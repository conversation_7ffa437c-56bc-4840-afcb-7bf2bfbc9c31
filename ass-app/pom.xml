<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.pxb7.mall.trade</groupId>
        <artifactId>ass</artifactId>
        <version>${revision}</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>ass-app</artifactId>
    <packaging>jar</packaging>
    <name>ass-app</name>
    <properties>
        <maven.deploy.skip>true</maven.deploy.skip>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.pxb7.mall.components</groupId>
            <artifactId>auth-satoken-c-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pxb7.mall.im</groupId>
            <artifactId>im-c-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pxb7.mall.trade</groupId>
            <artifactId>intelligent-delivery-client</artifactId>
        </dependency>
        <!-- JSR 303 Validation -->
        <dependency>
            <groupId>org.hibernate.validator</groupId>
            <artifactId>hibernate-validator</artifactId>
        </dependency>

        <dependency>
            <groupId>jakarta.el</groupId>
            <artifactId>jakarta.el-api</artifactId>
        </dependency>
        <!-- JSR 303 Validation End-->

        <dependency>
            <groupId>com.pxb7.mall.trade</groupId>
            <artifactId>ass-domain</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pxb7.mall.user</groupId>
            <artifactId>user-c-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.pxb7.mall.promotion</groupId>
            <artifactId>promotion-c-client</artifactId>
        </dependency>
        <!-- powerJob -->
        <dependency>
            <groupId>tech.powerjob</groupId>
            <artifactId>powerjob-worker-spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.pxb7.mall.trade</groupId>
            <artifactId>order-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pxb7.mall.user</groupId>
            <artifactId>user-admin-client</artifactId>
        </dependency>
    </dependencies>
</project>
