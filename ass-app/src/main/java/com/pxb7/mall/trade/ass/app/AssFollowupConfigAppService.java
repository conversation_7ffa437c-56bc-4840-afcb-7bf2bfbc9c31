package com.pxb7.mall.trade.ass.app;


import java.util.List;

import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import com.pxb7.mall.trade.ass.app.dto.reqeust.AssFollowupConfigReqDTO;
import com.pxb7.mall.trade.ass.app.dto.response.AssFollowupConfigRespDTO;
import com.pxb7.mall.trade.ass.app.mapping.AssFollowupConfigAppMapping;
import com.pxb7.mall.trade.ass.domain.model.AssFollowupConfigReqBO;
import com.pxb7.mall.trade.ass.domain.model.AssFollowupConfigRespBO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pxb7.mall.trade.ass.domain.AssFollowupConfigDomainService;


/**
 * 跟进结果类型配置表app服务
 *
 * <AUTHOR>
 * @since 2025-07-30 13:44:04
 */
@Service
public class AssFollowupConfigAppService {

    @Resource
    private AssFollowupConfigDomainService assFollowupConfigDomainService;

    public boolean insert(AssFollowupConfigReqDTO.AddDTO param) {
        AssFollowupConfigReqBO.AddBO addBO = AssFollowupConfigAppMapping.INSTANCE.assFollowupConfigDTO2AddBO(param);
        return assFollowupConfigDomainService.insert(addBO);
    }

    public boolean update(AssFollowupConfigReqDTO.UpdateDTO param) {
        AssFollowupConfigReqBO.UpdateBO updateBO = AssFollowupConfigAppMapping.INSTANCE.assFollowupConfigDTO2UpdateBO(param);
        return assFollowupConfigDomainService.update(updateBO);
    }

    public boolean deleteById(AssFollowupConfigReqDTO.DelDTO param) {
        AssFollowupConfigReqBO.DelBO delBO = AssFollowupConfigAppMapping.INSTANCE.assFollowupConfigDTO2DelBO(param);
        return assFollowupConfigDomainService.deleteById(delBO);
    }

    public AssFollowupConfigRespDTO.DetailDTO findById(Long id) {
        AssFollowupConfigRespBO.DetailBO detailBO = assFollowupConfigDomainService.findById(id);
        return AssFollowupConfigAppMapping.INSTANCE.assFollowupConfigBO2DetailDTO(detailBO);
    }

    public List<AssFollowupConfigRespDTO.DetailDTO> list(AssFollowupConfigReqDTO.SearchDTO param) {
        AssFollowupConfigReqBO.SearchBO searchBO = AssFollowupConfigAppMapping.INSTANCE.assFollowupConfigDTO2SearchBO(param);
        List<AssFollowupConfigRespBO.DetailBO> list = assFollowupConfigDomainService.list(searchBO);
        return AssFollowupConfigAppMapping.INSTANCE.assFollowupConfigBO2ListDTO(list);
    }

    public Page<AssFollowupConfigRespDTO.DetailDTO> page(AssFollowupConfigReqDTO.PageDTO param) {
        AssFollowupConfigReqBO.PageBO pageBO = AssFollowupConfigAppMapping.INSTANCE.assFollowupConfigDTO2PageBO(param);
        Page<AssFollowupConfigRespBO.DetailBO> page = assFollowupConfigDomainService.page(pageBO);
        return AssFollowupConfigAppMapping.INSTANCE.assFollowupConfigBO2PageDTO(page);
    }

}

