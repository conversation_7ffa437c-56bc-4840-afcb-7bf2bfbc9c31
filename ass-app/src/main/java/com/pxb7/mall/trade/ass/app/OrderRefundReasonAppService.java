package com.pxb7.mall.trade.ass.app;

import java.util.List;
import java.util.Map;
import java.util.Objects;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.pxb7.mall.response.PxResponse;
import com.pxb7.mall.trade.ass.app.dto.reqeust.UserRefundReasonListReqDTO;
import com.pxb7.mall.trade.ass.app.dto.response.RefundReasonListRespDTO;
import com.pxb7.mall.trade.ass.infra.repository.db.RefundReasonRepository;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.RefundReason;

import jakarta.annotation.Resource;

@Service
public class OrderRefundReasonAppService {

    @Resource
    private RefundReasonRepository refundReasonRepository;

    /**
     * 用户申请退款，退款原因列表
     *
     */
    public PxResponse<List<RefundReasonListRespDTO>> userRefundReasonList() {
        List<RefundReason> list = refundReasonRepository.lambdaQuery().in(RefundReason::getReasonRange, List.of(1, 3))
            .eq(RefundReason::getRefundType, 1).list();
        return PxResponse.ok(constructResponseDTO(list));
    }

    /**
     * 客服申请退款，退款原因列表
     *
     * @param reqDTO
     * @return
     */
    public PxResponse<List<RefundReasonListRespDTO>> serviceRefundReasonList(UserRefundReasonListReqDTO reqDTO) {
        // todo 这边的 getReasonRange 看不懂, 数据库注释不是 适用范围:1用户端申请退款,2客户端申请退款,3全部
        List<RefundReason> list = refundReasonRepository.lambdaQuery().in(RefundReason::getReasonRange, List.of(2, 3))
            .eq(StringUtils.isNotBlank(reqDTO.getReasonRefundId()), RefundReason::getPid, reqDTO.getReasonRefundId())
            .eq(reqDTO.getRefundType() != null, RefundReason::getRefundType, reqDTO.getRefundType()).list();
        return PxResponse.ok(constructResponseDTO(list));
    }

    private List<RefundReasonListRespDTO> constructResponseDTO(List<RefundReason> list) {
        Map<String, RefundReasonListRespDTO> respDTOMap = Maps.newHashMap();
        list.forEach(e -> {
            if (Objects.equals(1, e.getReasonLevel())) { // 一级原因
                if (respDTOMap.get(e.getReasonRefundId()) == null) {
                    RefundReasonListRespDTO refundReasonListRespDTO = new RefundReasonListRespDTO()
                        .setSecondReason(Lists.newArrayList()).setReasonRefundId(e.getReasonRefundId())
                        .setReasonRefundContent(e.getReasonRefundContent());
                    respDTOMap.put(e.getReasonRefundId(), refundReasonListRespDTO);
                } else {
                    respDTOMap.get(e.getReasonRefundId()).setReasonRefundId(e.getReasonRefundId())
                        .setReasonRefundContent(e.getReasonRefundContent());
                }
            } else if (Objects.equals(2, e.getReasonLevel())) { // 二级原因
                if (respDTOMap.get(e.getPid()) == null) {
                    RefundReasonListRespDTO emptyParent =
                        new RefundReasonListRespDTO().setSecondReason(Lists.newArrayList());
                    respDTOMap.put(e.getPid(), emptyParent);
                }
                RefundReasonListRespDTO second = new RefundReasonListRespDTO().setSecondReason(Lists.newArrayList())
                    .setReasonRefundId(e.getReasonRefundId()).setReasonRefundContent(e.getReasonRefundContent());
                respDTOMap.get(e.getPid()).getSecondReason().add(second);
            }
        });
        return Lists.newArrayList(respDTOMap.values());
    }
}