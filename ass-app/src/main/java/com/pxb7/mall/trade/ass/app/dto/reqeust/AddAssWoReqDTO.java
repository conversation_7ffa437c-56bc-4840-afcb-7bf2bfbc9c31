package com.pxb7.mall.trade.ass.app.dto.reqeust;

import java.io.Serial;
import java.io.Serializable;

import lombok.Data;

/**
 * 创建售后工单
 *
 * <AUTHOR>
 * @since: 2024-08-19 17:42
 **/
@Data
public class AddAssWoReqDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = 8303339650120230348L;
    /**
     * 订单ID
     */
    private String orderItemId;
    /**
     * 申请人 1:用户 2:客服
     */
    private Integer proposerType;
    /**
     * 申请人ID
     */
    private String proposerUserId;
    /**
     * 商品编码
     */
    private String productUniqueNo;
    /**
     * 游戏ID
     */
    private String gameId;
}
