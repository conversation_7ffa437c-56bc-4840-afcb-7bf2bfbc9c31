package com.pxb7.mall.trade.ass.app;


import java.util.List;

import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import com.pxb7.mall.trade.ass.app.dto.reqeust.AssRetrieveAssistanceConfigReqDTO;
import com.pxb7.mall.trade.ass.app.dto.response.AssRetrieveAssistanceConfigRespDTO;
import com.pxb7.mall.trade.ass.app.mapping.AssRetrieveAssistanceConfigAppMapping;
import com.pxb7.mall.trade.ass.domain.model.AssRetrieveAssistanceConfigReqBO;
import com.pxb7.mall.trade.ass.domain.model.AssRetrieveAssistanceConfigRespBO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pxb7.mall.trade.ass.domain.AssRetrieveAssistanceConfigDomainService;


/**
 * 找回协助类型配置表app服务
 *
 * <AUTHOR>
 * @since 2025-07-30 13:59:49
 */
@Service
public class AssRetrieveAssistanceConfigAppService {

    @Resource
    private AssRetrieveAssistanceConfigDomainService assRetrieveAssistanceConfigDomainService;

    public boolean insert(AssRetrieveAssistanceConfigReqDTO.AddDTO param) {
        AssRetrieveAssistanceConfigReqBO.AddBO addBO = AssRetrieveAssistanceConfigAppMapping.INSTANCE.assRetrieveAssistanceConfigDTO2AddBO(param);
        return assRetrieveAssistanceConfigDomainService.insert(addBO);
    }

    public boolean update(AssRetrieveAssistanceConfigReqDTO.UpdateDTO param) {
        AssRetrieveAssistanceConfigReqBO.UpdateBO updateBO = AssRetrieveAssistanceConfigAppMapping.INSTANCE.assRetrieveAssistanceConfigDTO2UpdateBO(param);
        return assRetrieveAssistanceConfigDomainService.update(updateBO);
    }

    public boolean deleteById(AssRetrieveAssistanceConfigReqDTO.DelDTO param) {
        AssRetrieveAssistanceConfigReqBO.DelBO delBO = AssRetrieveAssistanceConfigAppMapping.INSTANCE.assRetrieveAssistanceConfigDTO2DelBO(param);
        return assRetrieveAssistanceConfigDomainService.deleteById(delBO);
    }

    public AssRetrieveAssistanceConfigRespDTO.DetailDTO findById(Long id) {
        AssRetrieveAssistanceConfigRespBO.DetailBO detailBO = assRetrieveAssistanceConfigDomainService.findById(id);
        return AssRetrieveAssistanceConfigAppMapping.INSTANCE.assRetrieveAssistanceConfigBO2DetailDTO(detailBO);
    }

    public List<AssRetrieveAssistanceConfigRespDTO.DetailDTO> list(AssRetrieveAssistanceConfigReqDTO.SearchDTO param) {
        AssRetrieveAssistanceConfigReqBO.SearchBO searchBO = AssRetrieveAssistanceConfigAppMapping.INSTANCE.assRetrieveAssistanceConfigDTO2SearchBO(param);
        List<AssRetrieveAssistanceConfigRespBO.DetailBO> list = assRetrieveAssistanceConfigDomainService.list(searchBO);
        return AssRetrieveAssistanceConfigAppMapping.INSTANCE.assRetrieveAssistanceConfigBO2ListDTO(list);
    }

    public Page<AssRetrieveAssistanceConfigRespDTO.DetailDTO> page(AssRetrieveAssistanceConfigReqDTO.PageDTO param) {
        AssRetrieveAssistanceConfigReqBO.PageBO pageBO = AssRetrieveAssistanceConfigAppMapping.INSTANCE.assRetrieveAssistanceConfigDTO2PageBO(param);
        Page<AssRetrieveAssistanceConfigRespBO.DetailBO> page = assRetrieveAssistanceConfigDomainService.page(pageBO);
        return AssRetrieveAssistanceConfigAppMapping.INSTANCE.assRetrieveAssistanceConfigBO2PageDTO(page);
    }

}

