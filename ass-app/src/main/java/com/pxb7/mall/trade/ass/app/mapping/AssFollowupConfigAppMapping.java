package com.pxb7.mall.trade.ass.app.mapping;

import com.pxb7.mall.trade.ass.app.dto.reqeust.AssFollowupConfigReqDTO;
import com.pxb7.mall.trade.ass.app.dto.response.AssFollowupConfigRespDTO;
import com.pxb7.mall.trade.ass.domain.model.AssFollowupConfigReqBO;
import com.pxb7.mall.trade.ass.domain.model.AssFollowupConfigRespBO;

import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;


@Mapper
public interface AssFollowupConfigAppMapping {

    AssFollowupConfigAppMapping INSTANCE = Mappers.getMapper(AssFollowupConfigAppMapping.class);


    AssFollowupConfigReqBO.AddBO assFollowupConfigDTO2AddBO(AssFollowupConfigReqDTO.AddDTO source);

    AssFollowupConfigReqBO.UpdateBO assFollowupConfigDTO2UpdateBO(AssFollowupConfigReqDTO.UpdateDTO source);

    AssFollowupConfigReqBO.DelBO assFollowupConfigDTO2DelBO(AssFollowupConfigReqDTO.DelDTO source);

    AssFollowupConfigReqBO.SearchBO assFollowupConfigDTO2SearchBO(AssFollowupConfigReqDTO.SearchDTO source);

    AssFollowupConfigReqBO.PageBO assFollowupConfigDTO2PageBO(AssFollowupConfigReqDTO.PageDTO source);

    AssFollowupConfigRespDTO.DetailDTO assFollowupConfigBO2DetailDTO(AssFollowupConfigRespBO.DetailBO source);

    List<AssFollowupConfigRespDTO.DetailDTO> assFollowupConfigBO2ListDTO(List<AssFollowupConfigRespBO.DetailBO> source);

    Page<AssFollowupConfigRespDTO.DetailDTO> assFollowupConfigBO2PageDTO(Page<AssFollowupConfigRespBO.DetailBO> source);

}


