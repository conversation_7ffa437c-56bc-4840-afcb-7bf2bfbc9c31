package com.pxb7.mall.trade.ass.app.dto.reqeust;

import java.io.Serializable;

import com.pxb7.mall.trade.ass.client.enums.AssScheduleNode;
import com.pxb7.mall.trade.ass.client.enums.AssScheduleSourceTypeEnums;
import com.pxb7.mall.trade.ass.client.enums.AssWoType;

import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2024/8/12
 */
@Data
@Accessors(chain = true)
public class AssScheduleReqDTO implements Serializable {

    /**
     * 售后类型1找回2纠纷
     */
    @NotNull(message = "售后类型不能为空")
    private AssWoType assType;

    /**
     * 接待客服id
     */
    private String recvCustomerId;

    /**
     * 审核客服id
     */
    private String auditCustomerId;

    /**
     * 房间id
     */
    @NotNull(message = "房间id不能为空")
    private String roomId;

    /**
     * 节点
     */
    @NotNull(message = "节点不能为空")
    private AssScheduleNode node;

    /**
     * 纠纷处理结果
     */
    private String disputeResult;

    /**
     * 证据图片
     */
    private String data;
    /**
     * 来源1:c端用户 2:客服 3:admin用户
     *
     * @see AssScheduleSourceTypeEnums
     */
    private Integer sourceType;

    /**
     * 拒绝原因
     */
    private String rejectReason;

}
