package com.pxb7.mall.trade.ass.app.mapping;

import com.pxb7.mall.trade.ass.app.dto.reqeust.ListAssQuestionClassifyRespDTO;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.AssQuestionClassify;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 售后问题归类配置
 *
 * <AUTHOR>
 * @since: 2024-10-01 20:28
 **/
@Mapper
public interface AssQuestionClassifyMapping {
    AssQuestionClassifyMapping INSTANCE = Mappers.getMapper(AssQuestionClassifyMapping.class);

    List<ListAssQuestionClassifyRespDTO> toRespDTO(List<AssQuestionClassify> list);
}
