package com.pxb7.mall.trade.ass.app.dto.response;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.ToString;


/**
 * 售后找回游戏配置明细表(AssRetrieveGameConfigDetail)实体类
 *
 * <AUTHOR>
 * @since 2025-07-30 13:59:50
 */
public class AssRetrieveGameConfigDetailRespDTO {


    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class DetailDTO {
        /**
         * 自增id
         */
        private Long id;
        /**
         * 业务主键
         */
        private String detailId;
        /**
         * ass_retrieve_game_config.game_config_id
         */
        private String gameConfigId;
        /**
         * 找回处理时间单位天
         */
        private Integer retrieveDays;
        /**
         * 游戏id
         */
        private String gameId;
        /**
         * 用户端进群开关 1:已开启 0:未开启
         */
        private Boolean groupOpen;
    }
}

