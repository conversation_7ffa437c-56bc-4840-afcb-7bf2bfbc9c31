package com.pxb7.mall.trade.ass.app.dto.reqeust;

import java.util.List;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

/**
 * 添加证据
 *
 * <AUTHOR>
 * @since: 2024-06-12 15:07
 **/
@Data
public class AddProofReqDTO {
    /**
     * 售后工单id
     */
    @NotBlank(message = "售后工单id不能为空")
    private String roomId;
    /**
     * 证据列表
     */
    @Valid
    @NotEmpty(message = "证据列表不能为空")
    private List<ProofDTO> list;

}
