package com.pxb7.mall.trade.ass.app.dto.response;

import lombok.Data;

import java.util.List;

@Data
public class RefundReasonRespDTO {

    /**
     * 订单id
     */
    private String reasonRefundId;
    /**
     * 退款原因
     */
    private String reasonRefundContent;
    /**
     * 拦截退款，挽留
     */
    private Integer reasonIntercept;
    /**
     * 商品自动上架 1:是,0:否
     */
    private Integer autoGrounding;
    /**
     * 适用范围:1用户端申请退款,2客户端申请退款,3全部
     */
    private Integer reasonRange;
    /**
     * 层级
     */
    private Integer reasonLevel;

    private String pid;

    private List<RefundReasonRespDTO> child;
}
