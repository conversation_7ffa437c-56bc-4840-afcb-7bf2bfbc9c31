package com.pxb7.mall.trade.ass.app.dto.response;

import java.time.LocalDateTime;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.Data;

@Data
public class RefundDetailRespDTO {
    /**
     * 充值套餐，充值订单有
     */
    private String rechargePackage;
    /**
     * 商品数量
     */
    private Integer productQuantity;
    /**
     * 制单客服名称
     */
    private String serviceName;
    /**
     * 包赔服务
     */
    private List<String> indemnityNameList;
    /**
     * 商品编号
     */
    private String productUniqueNo;
    /**
     * 商品id
     */
    private String productId;
    /**
     * 商品销售价格(单价)(求降价之后的价格)
     */
    private Long productSalePrice;
    /**
     * 商品优惠金额, 是号价优惠了多少钱
     */
    private Long productCouponAmount;
    /**
     * 商品应付价格(实际支付的号价)(是商品不包含包赔)
     */
    private Long productPayAmount;
    /**
     * 商品名称
     */
    private String productName;
    /**
     * 商品图片
     */
    private String productPic;
    /**
     * 游戏名称
     */
    private String gameName;
    /**
     * 区服
     */
    private String productAttr;
    /**
     * 退款单id
     */
    private String refundVoucherId;
    /**
     * 订单行id
     */
    private String orderItemId;
    /**
     * 商品类型: 1 账号 2充值 3 金币 4 装备 5初始号 20 诚心卖曝光劵 21 诚心卖服务
     */
    private Integer productType;
    /**
     * 退款状态 1待审核, 2退款中, 3退款成功, 4退款失败, 5退款关闭
     */
    private Integer refundStatus;
    /**
     * 退款原因id
     */
    private String orderRefundReasonId;
    /**
     * 退款类型 1 整单退款 2.退商品差价
     */
    private Integer wholeRefund;
    /**
     * 退款类型 1线上原路返回 2线下打款
     */
    private Integer refundType;
    /**
     * 退款金额
     */
    private Long refundAmount;
    /**
     * 实际退款金额
     */
    private Long actualRefundAmount;
    /**
     * 提交人
     */
    private String submitter;
    /**
     * 1-用户,2-客服
     */
    private Integer submitterType;
    /**
     * 商品号价退款金额
     */
    private Long productAmount;
    /**
     * 包赔金额
     */
    private Long indemnityAmount;
    /**
     * 手续费金额
     */
    private Long feeAmount;
    /**
     * 优惠卷id
     */
    private String couponId;
    /**
     * 优惠卷描述
     */
    private String couponDesc;
    /**
     * 退款原因
     */
    private String orderRefundReason;
    /**
     * 申请时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime applyTime;
    /**
     * 退款完成时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime finishTime;
    /**
     * 财务审核人审核备注
     */
    private String auditRemark;
    /**
     * 1支付宝 2微信 3银行卡
     */
    private Integer refundChannel;
    /**
     * 买家收款账户
     */
    private String refundAccount;
    /**
     * 买家姓名
     */
    private String buyerName;
    /**
     * 审核状态 1待客服审核,2待提交打款信息(线下打款才有),3待财务审核,4审核成功,5审核失败
     */
    private Integer auditStatus;
    /**
     * 财务审核截图
     */
    private String auditImg;
    /**
     * 打款人用户id
     */
    private String executeUser;
    /**
     * 打款账号名称
     */
    private String accountName;
    /**
     * 打款账号
     */
    private String companyAccount;
    /**
     * 客服拒绝原因
     */
    private String rejectReason;

    /**
     * 违约单id （允许不存在值）
     */
    private String  violateId;
}
