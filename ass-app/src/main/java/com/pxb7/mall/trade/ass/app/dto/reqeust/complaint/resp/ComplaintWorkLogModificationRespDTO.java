package com.pxb7.mall.trade.ass.app.dto.reqeust.complaint.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * Copyright (C), 2002-2024, 螃蟹游戏服务网
 *
 * @fileName: ComplaintWorkLogRespDTO.java
 * @description: 客诉工单日志
 * @author: guominqiang
 * @email: <EMAIL>
 * @date: 2024/9/21 17:52
 * @history: //修改记录
 * <author> <time> <version> <desc>
 * 修改人姓名 修改时间 版本号 描述
 */
@Data
@AllArgsConstructor
public class ComplaintWorkLogModificationRespDTO implements Serializable {

    /**
     * 字段名称
     */
    private String field;
    /**
     * 修改前的值
     */
    private String before;

    /**
     * 修改后的值
     */
    private String after;
}
