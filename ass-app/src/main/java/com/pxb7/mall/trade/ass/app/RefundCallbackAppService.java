package com.pxb7.mall.trade.ass.app;

import com.alibaba.cola.extension.BizScenario;
import com.alibaba.cola.extension.ExtensionExecutor;
import com.pxb7.mall.trade.ass.domain.PayCompanyAccountDomainService;
import com.pxb7.mall.trade.ass.domain.refund.RefundSyncDomainService;
import com.pxb7.mall.trade.ass.domain.refund.invoke3rd.PayRefundExtPt;
import com.pxb7.mall.trade.ass.domain.refund.model.RefundCallBackBO;
import com.pxb7.mall.trade.ass.domain.refund.model.RefundCallBackReqBO;
import com.pxb7.mall.trade.ass.infra.model.mq.RefundCallbackMessage;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Objects;
import java.util.function.Function;

/**
 * 退款回调 app service
 */
@Slf4j
@Service
public class RefundCallbackAppService {

    @Resource
    private ExtensionExecutor extensionExecutor;
    @Resource
    private RefundSyncDomainService refundSyncDomainService;

    /**
     * 退款回调处理
     */
    public boolean RefundCallbackHandler(RefundCallbackMessage message) {
        if (message == null || message.getCallbackData() == null || message.getChannel() == null) {
            log.error("【退款回调】参数非法:{}", message);
            return true;
        }
        Function<PayRefundExtPt, RefundCallBackBO> function = refundExt -> {
            try {
                //退款回调验签&解析，拿到回调数据
                RefundCallBackReqBO reqBO = new RefundCallBackReqBO().setBodyStr(message.getCallbackData());
                return refundExt.refundCallbackSign(reqBO);
            } catch (Exception e) {
                log.error("【退款回调】验签异常:{}", message, e);
                return null;
            }
        };
        RefundCallBackBO callBackBO = extensionExecutor.execute(PayRefundExtPt.class,
                BizScenario.valueOf(message.getChannel().getRefundBizNo()), function);
        log.info("【退款回调】回调结果:{}", callBackBO);
        if (Objects.nonNull(callBackBO)) {
            //若 回调结果解析成功，则同步操作
            refundSyncDomainService.handleRefundCallBack(callBackBO);
            return true;
        }
        return false;
    }

}
