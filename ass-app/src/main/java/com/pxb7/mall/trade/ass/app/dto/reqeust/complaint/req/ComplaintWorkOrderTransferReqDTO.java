package com.pxb7.mall.trade.ass.app.dto.reqeust.complaint.req;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.io.Serializable;

/**
 * Copyright (C), 2002-2024, 螃蟹游戏服务网
 *
 * @fileName: ComplaintWorkOrderTransferReqDTO.java
 * @description: 工单转交请求的DTO
 * @author: guo<PERSON><PERSON><PERSON>
 * @email: <EMAIL>
 * @date: 2024/9/20 22:09
 * @history: //修改记录
 * <author> <time> <version> <desc>
 * 修改人姓名 修改时间 版本号 描述
 */
@Data
public class ComplaintWorkOrderTransferReqDTO implements Serializable {
    /**
     * 房间id
     */
    @NotBlank(message = "房间id不能为空")
    private String roomId;

    /**
     * 工单id
     */
    @NotBlank(message = "工单id不能为空")
    private String complaintWorkId;

    /**
     * 订单id
     */
    private String orderItemId;

    /**
     * 被转交人id
     */
    @NotBlank(message = "被转交人id不能为空")
    private String transfereeId;
    /**
     * 备注
     */
    @NotBlank(message = "转交备注不能为空")
    private String note;
}
