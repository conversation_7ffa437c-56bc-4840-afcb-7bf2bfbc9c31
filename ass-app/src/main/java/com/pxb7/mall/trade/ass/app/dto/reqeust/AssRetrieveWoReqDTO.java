package com.pxb7.mall.trade.ass.app.dto.reqeust;

import java.io.Serial;
import java.io.Serializable;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 找回工单请求
 *
 * <AUTHOR>
 * @since: 2024-09-21 13:51
 **/
public class AssRetrieveWoReqDTO {

    /**
     * 关闭工单
     */
    @Data
    public static class CloseWorkOrderReqDTO implements Serializable {

        @Serial
        private static final long serialVersionUID = 6358288232546310837L;
        /**
         * 找回工单id
         */
        @NotBlank(message = "找回工单id不能为空")
        private String assRetrieveId;
        /**
         * 订单ID
         */
        @NotBlank(message = "订单ID不能为空")
        private String orderItemId;
    }

    @Data
    public static class SetDealUserReqDTO implements Serializable {
        @Serial
        private static final long serialVersionUID = 5496799991472460886L;

        /**
         * 房间ID
         */
        @NotBlank(message = "房间id不能为空")
        private String roomId;
        /**
         * 处理人员ID
         */
        @NotBlank(message = "处理人员ID不能为空")
        private String dealUserId;

        /**
         * 处理人员名称
         */
        private String dealUserName;
        /**
         * 转交备注
         */
        private String remark = "";
    }

    /**
     * 创建找回工单
     */
    @Data
    public static class AddAssRetrieveWoReqDTO implements Serializable {

        @Serial
        private static final long serialVersionUID = 8303339650120230348L;
        /**
         * 订单ID
         */
        @NotBlank(message = "订单ID不能为空")
        private String orderItemId;
        /**
         * 申请人 1:用户 2:客服
         */
        @NotNull(message = "申请人不能为空")
        private Integer proposerType;
        /**
         * 申请人ID
         */
        @NotBlank(message = "申请人ID不能为空")
        private String proposerUserId;
        /**
         * 商品编码
         */
        @NotBlank(message = "商品编码不能为空")
        private String productUniqueNo;
        /**
         * 游戏ID
         */
        @NotBlank(message = "游戏ID不能为空")
        private String gameId;
        /**
         * 接待客服ID
         */
        @NotBlank(message = "接待客服ID不能为空")
        private String recvCustomerId;
        /**
         * 审核客服id
         */
        @NotBlank(message = "审核客服id不能为空")
        private String auditCustomerId;
        /**
         * 保险0未购买1购买
         */
        @NotNull(message = "保险不能为空")
        private Integer insure;
    }
}
