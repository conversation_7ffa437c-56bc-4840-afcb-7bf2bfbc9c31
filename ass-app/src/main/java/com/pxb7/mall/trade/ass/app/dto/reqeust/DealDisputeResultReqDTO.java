package com.pxb7.mall.trade.ass.app.dto.reqeust;

import java.io.Serial;
import java.io.Serializable;

import org.hibernate.validator.constraints.Range;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 完结纠纷工单
 *
 * <AUTHOR>
 * @since: 2024-08-13 20:21
 **/
@Data
public class DealDisputeResultReqDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = -754062281366985219L;
    /**
     * 房间ID
     */
    @NotBlank(message = "房间ID不能为空")
    private String roomId;

    /**
     * 1正常完结2平台规则完结3赔付安抚完结
     */
    @NotNull(message = "处理结果不能为空")
    @Range(min = 1, max = 3, message = "处理结果范围1-3")
    private Integer disputeDealResult;
    /**
     * 问题归类一级目录
     */
    @NotBlank(message = "问题归类一级目录不能为空")
    private String classifyFirst;

    /**
     * 问题归类二级目录
     */
    @NotBlank(message = "问题归类二级目录不能为空")
    private String classifySecond;
}
