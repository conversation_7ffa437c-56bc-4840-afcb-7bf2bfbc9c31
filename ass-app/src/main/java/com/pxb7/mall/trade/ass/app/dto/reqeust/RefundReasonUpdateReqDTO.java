package com.pxb7.mall.trade.ass.app.dto.reqeust;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

@Data
public class RefundReasonUpdateReqDTO {

    /**
     * 退款原因id
     */
    @NotBlank(message = "退款原因id不能为空")
    private String reasonRefundId;
    /**
     * 退款原因
     */
    @NotBlank(message = "退款原因不能为空")
    private String reasonRefundContent;
    /**
     * 拦截退款，挽留
     */
    private Integer reasonIntercept;
    /**
     * 商品自动上架 1:是,0:否
     */
    private Integer autoGrounding;
    /**
     * 适用范围:1用户端申请退款,2客户端申请退款,3全部
     */
    @NotNull(message = "适用范围不能为空")
    private Integer reasonRange;

    /**
     * 退款详情信息
     */
    @Valid
    private List<RefundReasonAddReqDTO> child;
}
