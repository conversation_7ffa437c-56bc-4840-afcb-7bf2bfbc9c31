package com.pxb7.mall.trade.ass.app.dto.reqeust;

import java.time.*;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Min;
import lombok.ToString;


/**
 * 售后工单(AssWorkOrder)实体类
 *
 * <AUTHOR>
 * @since 2025-07-30 14:00:39
 */
public class AssWorkOrderReqDTO {

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class AddDTO {

        /**
         * 售后工单号
         */
        @NotBlank(message = "workOrderId不能为空")
        private String workOrderId;

        /**
         * 订单ID
         */
        @NotBlank(message = "orderItemId不能为空")
        private String orderItemId;

        /**
         * 订单ID
         */
        @NotBlank(message = "roomId不能为空")
        private String roomId;

        /**
         * 售后类型1找回2纠纷
         */
        @NotNull(message = "assType不能为空")
        private Integer assType;

        /**
         * 关联找回/纠纷工单号
         */
        @NotBlank(message = "relOrderId不能为空")
        private String relOrderId;

        /**
         * 0:处理中 1:已完成 2:已取消
         */
        @NotNull(message = "assStatus不能为空")
        private Integer assStatus;
        /**
         * 售后工单状态备注
         */
        private String assStatusMemo;
        /**
         * 发起售后申请时间
         */
        @NotNull(message = "applyTime不能为空")
        private LocalDateTime applyTime;

        /**
         * 预计完成时间
         */
        @NotNull(message = "expectedTime不能为空")
        private LocalDateTime expectedTime;

        /**
         * 完成时间
         */
        @NotNull(message = "completeTime不能为空")
        private LocalDateTime completeTime;

        /**
         * 是否已读 1:已读 0:未读
         */
        @NotNull(message = "readFlag不能为空")
        private Boolean readFlag;

    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class UpdateDTO {

        /**
         * 自增id
         */
        @NotNull(message = "id不能为空")
        private Long id;


        /**
         * 售后工单号
         */
        @NotBlank(message = "workOrderId不能为空")
        private String workOrderId;


        /**
         * 订单ID
         */
        @NotBlank(message = "orderItemId不能为空")
        private String orderItemId;


        /**
         * 订单ID
         */
        @NotBlank(message = "roomId不能为空")
        private String roomId;


        /**
         * 售后类型1找回2纠纷
         */
        @NotNull(message = "assType不能为空")
        private Integer assType;


        /**
         * 关联找回/纠纷工单号
         */
        @NotBlank(message = "relOrderId不能为空")
        private String relOrderId;


        /**
         * 0:处理中 1:已完成 2:已取消
         */
        @NotNull(message = "assStatus不能为空")
        private Integer assStatus;

        /**
         * 售后工单状态备注
         */
        private String assStatusMemo;


        /**
         * 发起售后申请时间
         */
        @NotNull(message = "applyTime不能为空")
        private LocalDateTime applyTime;


        /**
         * 预计完成时间
         */
        @NotNull(message = "expectedTime不能为空")
        private LocalDateTime expectedTime;


        /**
         * 完成时间
         */
        @NotNull(message = "completeTime不能为空")
        private LocalDateTime completeTime;


        /**
         * 是否已读 1:已读 0:未读
         */
        @NotNull(message = "readFlag不能为空")
        private Boolean readFlag;


    }


    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class DelDTO {
        @NotNull(message = "id不能为空")
        private Long id;
    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class SearchDTO {
        /**
         * 售后工单号
         */
        @NotBlank(message = "workOrderId不能为空")
        private String workOrderId;

        /**
         * 订单ID
         */
        @NotBlank(message = "orderItemId不能为空")
        private String orderItemId;

        /**
         * 订单ID
         */
        @NotBlank(message = "roomId不能为空")
        private String roomId;

        /**
         * 售后类型1找回2纠纷
         */
        @NotNull(message = "assType不能为空")
        private Integer assType;

        /**
         * 关联找回/纠纷工单号
         */
        @NotBlank(message = "relOrderId不能为空")
        private String relOrderId;

        /**
         * 0:处理中 1:已完成 2:已取消
         */
        @NotNull(message = "assStatus不能为空")
        private Integer assStatus;

        /**
         * 发起售后申请时间
         */
        @NotNull(message = "applyTime不能为空")
        private LocalDateTime applyTime;

        /**
         * 预计完成时间
         */
        @NotNull(message = "expectedTime不能为空")
        private LocalDateTime expectedTime;

        /**
         * 完成时间
         */
        @NotNull(message = "completeTime不能为空")
        private LocalDateTime completeTime;

        /**
         * 是否已读 1:已读 0:未读
         */
        @NotNull(message = "readFlag不能为空")
        private Boolean readFlag;

    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class PageDTO {

        /**
         * 售后工单号
         */
        @NotBlank(message = "workOrderId不能为空")
        private String workOrderId;

        /**
         * 订单ID
         */
        @NotBlank(message = "orderItemId不能为空")
        private String orderItemId;

        /**
         * 订单ID
         */
        @NotBlank(message = "roomId不能为空")
        private String roomId;

        /**
         * 售后类型1找回2纠纷
         */
        @NotNull(message = "assType不能为空")
        private Integer assType;

        /**
         * 关联找回/纠纷工单号
         */
        @NotBlank(message = "relOrderId不能为空")
        private String relOrderId;

        /**
         * 0:处理中 1:已完成 2:已取消
         */
        @NotNull(message = "assStatus不能为空")
        private Integer assStatus;

        /**
         * 发起售后申请时间
         */
        @NotNull(message = "applyTime不能为空")
        private LocalDateTime applyTime;

        /**
         * 预计完成时间
         */
        @NotNull(message = "expectedTime不能为空")
        private LocalDateTime expectedTime;

        /**
         * 完成时间
         */
        @NotNull(message = "completeTime不能为空")
        private LocalDateTime completeTime;

        /**
         * 是否已读 1:已读 0:未读
         */
        @NotNull(message = "readFlag不能为空")
        private Boolean readFlag;


        /**
         * 页码，从1开始
         */
        @NotNull(message = "pageIndex不能为空")
        @Min(value = 1, message = "pageIndex不能小于1")
        private Integer pageIndex;
        /**
         * 每页数量
         */
        @NotNull(message = "pageSize不能为空")
        private Integer pageSize;

    }


    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class PageOrderDTO extends BasePageDTO{
        /**
         * 下单时间
         * 周期 1本周内 2 当月 3 三个月内 4半年内 5 一年内
         */
        private Integer cycle;

        /**
         * 售后单申请时间
         * 周期 1本周内 2 当月 3 三个月内 4半年内 5 一年内
         */
        private Integer assCycle;

        /**
         * 商品类型
         */
        private Integer productType;

        /**
         * 游戏id
         */
        private String gameId;

        /**
         * 关键词（模糊查询商品标题/订单编号/商品编号）
         */
        private String keyWords;



        /***********号商**************/

        /**
         * 是否是号商(为true表示号商身份查询)
         */
        @NotNull(message = "是否是号商不能为空")
        private Boolean isMerchant;


        /**
         * 订单编号
         */

        private String orderItemId;

        /**
         * 手机号（子账号）
         */
        private String telephone;

        /**
         * 商品名称
         */
        private String productName;

        /**
         * 商品ID
         */
        private String productId;

        /**
         * 商品编号
         */
        private String productUniqueNo;


    }

}

