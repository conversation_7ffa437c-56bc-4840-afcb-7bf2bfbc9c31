package com.pxb7.mall.trade.ass.app;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.cola.exception.Assert;
import com.alibaba.cola.exception.BizException;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.Maps;
import com.pxb7.mall.auth.c.util.LoginUserUtil;
import com.pxb7.mall.components.redis.redisson.RLockUtils;
import com.pxb7.mall.components.redis.redisson.RedissonUtils;
import com.pxb7.mall.im.client.api.SendCommonMsgServiceI;
import com.pxb7.mall.im.client.api.SendMsgServiceI;
import com.pxb7.mall.im.client.dto.request.SetExpansionDTO;
import com.pxb7.mall.im.client.dto.request.card.SendFormMsgReqDTO;
import com.pxb7.mall.im.client.dto.request.card.SendRichTextMsgReqDTO;
import com.pxb7.mall.im.client.dto.request.card.SendTableColumnMsgReqDTO;
import com.pxb7.mall.im.client.dto.request.card.SendTextMsgReqDTO;
import com.pxb7.mall.im.client.dto.request.card.richtext.CustomercareNoticeContent;
import com.pxb7.mall.im.client.dto.request.card.richtext.RichTextContent;
import com.pxb7.mall.im.client.dto.request.card.table.BaseTableColumnMsgContent;
import com.pxb7.mall.im.client.dto.request.card.table.TableColumnContent;
import com.pxb7.mall.im.client.dto.request.card.watermark.WaterMarkYQX;
import com.pxb7.mall.im.client.dto.request.card.watermark.WaterMarkYTJ;
import com.pxb7.mall.promotion.client.client.api.SinceritySellServiceI;
import com.pxb7.mall.promotion.client.client.dto.model.sinceritysell.ExposureCouponCompleteReqDTO;
import com.pxb7.mall.response.PxPageResponse;
import com.pxb7.mall.response.PxResponse;
import com.pxb7.mall.trade.ass.app.common.UserIdUtils;
import com.pxb7.mall.trade.ass.app.dto.reqeust.ApplyRefundReqDTO;
import com.pxb7.mall.trade.ass.app.dto.reqeust.CalculateRefundAmountReqDTO;
import com.pxb7.mall.trade.ass.app.dto.reqeust.CancelRefundReqDTO;
import com.pxb7.mall.trade.ass.app.dto.reqeust.ExposureCouponRefundReqDTO;
import com.pxb7.mall.trade.ass.app.dto.reqeust.ImRoomIdReqDTO;
import com.pxb7.mall.trade.ass.app.dto.reqeust.OrderRefundReqDTO;
import com.pxb7.mall.trade.ass.app.dto.reqeust.RefundPageReqDTO;
import com.pxb7.mall.trade.ass.app.dto.reqeust.RefundUserInfoDTO;
import com.pxb7.mall.trade.ass.app.dto.reqeust.RefundViolateDetailReqDTO;
import com.pxb7.mall.trade.ass.app.dto.reqeust.ServiceSubmitRefundReqDTO;
import com.pxb7.mall.trade.ass.app.dto.reqeust.SinceritySellOrderApplyRefundReqDTO;
import com.pxb7.mall.trade.ass.app.dto.reqeust.SupportRefundRejectReqDTO;
import com.pxb7.mall.trade.ass.app.dto.response.CalculateRefundAmountRespDTO;
import com.pxb7.mall.trade.ass.app.dto.response.RefundDetailRespDTO;
import com.pxb7.mall.trade.ass.app.dto.response.RefundInitInfoRespDTO;
import com.pxb7.mall.trade.ass.app.dto.response.RefundOrderItemPageRespDTO;
import com.pxb7.mall.trade.ass.app.dto.response.RefundPageRespDTO;
import com.pxb7.mall.trade.ass.app.mapping.RefundMapping;
import com.pxb7.mall.trade.ass.client.dto.model.order.OrderItemAmountInfo;
import com.pxb7.mall.trade.ass.client.dto.model.orderitemimdemnity.OrderItemIndemnityDTO;
import com.pxb7.mall.trade.ass.client.dto.request.refund.IndemnityChangeServiceRefundReqDTO;
import com.pxb7.mall.trade.ass.client.dto.response.refund.IndemnityChangeServiceRefundRespDTO;
import com.pxb7.mall.trade.ass.client.enums.violate.ViolateUserTypeEnum;
import com.pxb7.mall.trade.ass.domain.model.OrderItemAmountInfoBO;
import com.pxb7.mall.trade.ass.domain.refund.RefundDomainService;
import com.pxb7.mall.trade.ass.domain.refund.message.provider.RefundMessageService;
import com.pxb7.mall.trade.ass.domain.refund.message.provider.SinceritySellOrderRefundMessageService;
import com.pxb7.mall.trade.ass.domain.refund.model.RefundAuditMessage;
import com.pxb7.mall.trade.ass.domain.refund.model.RefundOrderItemPageRespBO;
import com.pxb7.mall.trade.ass.domain.refund.model.ServiceSubmitRefundBO;
import com.pxb7.mall.trade.ass.domain.refund.model.SinceritySellOrderRefundMessage;
import com.pxb7.mall.trade.ass.domain.refund.utils.RefundImCardUtil;
import com.pxb7.mall.trade.ass.domain.violate.ViolateOrderDomainService;
import com.pxb7.mall.trade.ass.domain.violate.mq.ViolateMessageService;
import com.pxb7.mall.trade.ass.domain.violate.mq.message.ViolateApplyMessage;
import com.pxb7.mall.trade.ass.infra.config.nacos.TSRefundConfig;
import com.pxb7.mall.trade.ass.infra.constant.PayConstant;
import com.pxb7.mall.trade.ass.infra.constant.RedisConstants;
import com.pxb7.mall.trade.ass.infra.enums.*;
import com.pxb7.mall.trade.ass.infra.model.Money;
import com.pxb7.mall.trade.ass.infra.model.RefundVoucherSearchPO;
import com.pxb7.mall.trade.ass.infra.model.UserBaseInfo;
import com.pxb7.mall.trade.ass.infra.remote.dubbo.OrderItemRpcGateway;
import com.pxb7.mall.trade.ass.infra.remote.model.refund.OrderItemReceiptVoucherPO;
import com.pxb7.mall.trade.ass.infra.repository.db.*;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.*;
import com.pxb7.mall.trade.ass.infra.repository.es.entity.OrderItemAggregation;
import com.pxb7.mall.trade.ass.infra.repository.es.entity.OrderItemIndemnityDoc;
import com.pxb7.mall.trade.ass.infra.repository.es.entity.RefundVoucherDoc;
import com.pxb7.mall.trade.ass.infra.repository.es.impl.EsOrderItemSearchService;
import com.pxb7.mall.trade.ass.infra.repository.es.impl.EsRefundSearchServiceI;
import com.pxb7.mall.trade.ass.infra.util.StringUtil;
import com.pxb7.mall.trade.ofs.delivery.client.api.DeliveryFlowServiceI;
import com.pxb7.mall.trade.ofs.delivery.client.dto.request.OrderItemIdReqDTO;
import com.pxb7.mall.trade.order.client.api.OrderImDubboServiceI;
import com.pxb7.mall.trade.order.client.constants.BaseConstant;
import com.pxb7.mall.trade.order.client.dto.request.orderim.request.ImInfoReqDTO;
import com.pxb7.mall.trade.order.client.dto.response.order.dubbo.OrderInfoDubboRespDTO;
import com.pxb7.mall.trade.order.client.enums.order.OrderItemStatusEnum;
import com.pxb7.mall.trade.order.client.enums.order.OrderOperateTypeEnum;
import com.pxb7.mall.trade.order.client.enums.order.OrderOptUserTypeEnum;
import com.pxb7.mall.trade.order.client.enums.order.ProductTypeEnum;
import com.pxb7.mall.trade.order.client.enums.order.RefundStatusEnum;
import com.pxb7.mall.trade.order.client.enums.order.RefundTypeEnum;
import com.pxb7.mall.trade.order.client.enums.order.ResponsibleEnum;
import com.pxb7.mall.trade.order.client.enums.order.WholeRefundTypeEnum;
import com.pxb7.mall.trade.order.client.enums.pay.PayBusinessTypeEnum;
import com.pxb7.mall.trade.order.client.enums.pay.PayStatusEnum;
import com.pxb7.mall.trade.order.client.enums.pay.TradeModeEnum;
import com.pxb7.mall.trade.order.client.lock.OrderItemLockConstant;
import com.pxb7.mall.user.admin.dto.response.sysuser.GetSysUserRespDTO;
import com.pxb7.mall.user.api.SysUserServiceI;
import com.pxb7.mall.user.dto.response.sys.CustomerCareInfoRespDTO;
import jakarta.annotation.Nullable;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.curator.shaded.com.google.common.collect.Sets;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.apache.dubbo.common.utils.MD5Utils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchPage;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.pxb7.mall.trade.ass.infra.enums.ErrorCode.*;

@Slf4j
@Service
public class RefundAppService {
    @Resource
    private RefundDomainService refundDomainService;
    @Resource
    private OrderItemRepository orderItemRepository;
    @Resource
    private RefundVoucherRepository refundVoucherRepository;
    @DubboReference
    private DeliveryFlowServiceI deliveryFlowServiceI;
    @DubboReference
    private SendMsgServiceI sendMsgServiceI;
    @DubboReference
    private SendCommonMsgServiceI sendCommonMsgServiceI;
    @DubboReference
    private OrderImDubboServiceI orderImDubboServiceI;
    @Resource
    private OrderItemExtendRepository orderItemExtendRepository;
    @Resource
    private RefundReasonRepository refundReasonRepository;
    @Resource
    private EsOrderItemSearchService esOrderItemSearchService;
    @Resource
    private EsRefundSearchServiceI esRefundSearchServiceI;
    @Resource
    private RefundVoucherDetailRepository refundVoucherDetailRepository;
    @Resource
    private RefundMessageService refundMessageService;
    @Resource
    private SinceritySellOrderRefundMessageService ssOrderRefundMessageService;
    @DubboReference(providedBy = "user-c")
    private SysUserServiceI sysUserServiceI;
    @DubboReference
    private SinceritySellServiceI sinceritySellServiceI;
    @Resource
    private ViolateMessageService violateMessageService;
    @Resource
    private com.pxb7.mall.user.admin.api.SysUserServiceI adminSysUserServiceI;

    @Resource
    private OrderItemRpcGateway orderItemRpcGateway;
    @Resource
    private ViolateOrderDomainService violateOrderDomainService;

    @Resource
    private PaymentRepository paymentRepository;

    @Resource
    private PayLogRepository payLogRepository;

    @Resource
    private TSRefundConfig tsRefundConfig;

    @Nullable
    private static OrderItemAmountInfo buildApplyRefundAmountInfo(ServiceSubmitRefundReqDTO serviceSubmitRefundReqDTO) {
        if (Objects.equals(serviceSubmitRefundReqDTO.getWholeRefund(), RefundWholeEnum.WHOLE.getValue())) {
            // 整单退款不需要退款申请金额信息
            return null;
        }
        OrderItemAmountInfo applyRefundAmountInfo = new OrderItemAmountInfo();
        if (Objects.equals(RefundCalculateTypeEnum.BY_INPUT_AMOUNT.getValue(),
                serviceSubmitRefundReqDTO.getCalculateType())) {
            // 客服输入方式，refundProductAmount,refundFeeAmount,refundIndemnityAmount三个字段不能都为空
            if (serviceSubmitRefundReqDTO.getRefundProductAmount() == null
                    && serviceSubmitRefundReqDTO.getRefundFeeAmount() == null
                    && serviceSubmitRefundReqDTO.getRefundIndemnityAmount() == null) {
                throw new BizException(REFUND_AMOUNT_ERROR.getErrCode(), ErrorCode.REFUND_AMOUNT_ERROR.getErrDesc());
            }
            // 客服输入方式，使用refundProductAmount,refundFeeAmount,refundIndemnityAmount三个字段
            if (serviceSubmitRefundReqDTO.getRefundProductAmount() != null) {
                applyRefundAmountInfo.setProductAmount(serviceSubmitRefundReqDTO.getRefundProductAmount());
            }
            if (serviceSubmitRefundReqDTO.getRefundFeeAmount() != null) {
                applyRefundAmountInfo.setFeeAmount(serviceSubmitRefundReqDTO.getRefundFeeAmount());
            }
            if (serviceSubmitRefundReqDTO.getRefundIndemnityAmount() != null) {
                applyRefundAmountInfo.setIndemnityAmount(serviceSubmitRefundReqDTO.getRefundIndemnityAmount());
            }
        } else {
            // 退号价方式，refundAmount代表号价金额，必传
            if (serviceSubmitRefundReqDTO.getRefundAmount() == null) {
                throw new BizException(REFUND_AMOUNT_ERROR.getErrCode(), ErrorCode.REFUND_AMOUNT_ERROR.getErrDesc());
            }
            // 退号价方式，使用refundAmount代表号价金额
            applyRefundAmountInfo.setProductAmount(serviceSubmitRefundReqDTO.getRefundAmount());
        }

        return applyRefundAmountInfo;
    }

    /**
     * 获取退款类型（全额退还是部分退）
     */
    private static Integer getRefundWhole(long alreadyRefundAmount, Long refundAmount, OrderItem orderItem) {
        long totalRefundAmount = alreadyRefundAmount + refundAmount;
        // 用成功退款金额和orderItem的实付金额比较
        if (totalRefundAmount > orderItem.getOrderItemActualPayAmount()) {
            throw new BizException(AMOUNT_INVALID_FAIL.getErrCode(), AMOUNT_INVALID_FAIL.getErrDesc());
        }

        // 判断是全退还是部分退款 订单金额是否和退款金额一致
        Integer refundWhole;
        if (Objects.equals(totalRefundAmount, orderItem.getOrderItemActualPayAmount())) {
            refundWhole = RefundWholeEnum.WHOLE.getValue();
        } else {
            refundWhole = RefundWholeEnum.PART.getValue();
        }

        return refundWhole;
    }

    /**
     * 用户提交账号申请
     */
    public PxResponse<Boolean> applyRefund(ApplyRefundReqDTO applyRefundReqDTO, UserBaseInfo userBaseInfo) {
        Assert.isTrue(StringUtils.isNotBlank(userBaseInfo.getUserId()), GET_USER_BASE_INFO_ERROR.getErrCode(),
                GET_USER_BASE_INFO_ERROR.getErrDesc());
        // 校验orderItem，同一时刻只能有一笔单据进行
        OrderItem orderItem = orderItemRepository.getOrderItem(applyRefundReqDTO.getOrderItemId());
        refundDomainService.checkOrderItem(orderItem);

        // 用户发起退款，需在订单创建成功6小时之后，未到6小时，点击申请退款提示“订单6小时后可以申请退款”
        Assert.isTrue(LocalDateTime.now().minusHours(6).isAfter(orderItem.getCreateTime()),
                REFUND_TIME_LIMIT_ERROR.getErrCode(), REFUND_TIME_LIMIT_ERROR.getErrDesc());

        // 检查im房间是否拉群成功
        OrderItemExtend orderItemExtend = checkDeliveryRoomId(applyRefundReqDTO.getOrderItemId());

        // 校验交付节点必须在卖家提供账号之前
        // checkExpose(applyRefundReqDTO.getOrderItemId());

        // // 根据退款原因来判断是否触发退款挽留
        // doRefundIntercept(applyRefundReqDTO);

        // 查询成功的退款单
        List<RefundVoucher> successRefundVoucher =
                refundVoucherRepository.getSuccessRefund(applyRefundReqDTO.getOrderItemId());
        // 查询成功收款单, 并校验是否仍有退款余额
        List<OrderItemReceiptVoucherPO> successReceiptVoucherDetail = refundDomainService
                .getSuccessReceiptVoucherDetail(applyRefundReqDTO.getOrderItemId(), successRefundVoucher);

        // 计算全额退款金额，并校验退款金额是否充足
        OrderItemAmountInfoBO refundAmountInfo = refundDomainService.getWholeRefundAmountInfo(
                applyRefundReqDTO.getOrderItemId(), successRefundVoucher, successReceiptVoucherDetail);
        // 计算支持的退款方式
        OrderInfoDubboRespDTO order = orderItemRpcGateway.getOrderInfo(applyRefundReqDTO.getOrderItemId());
        Set<Integer> supportRefundType =
                refundDomainService.supportRefundType(order, successReceiptVoucherDetail, refundAmountInfo.getTotalAmount());
        // 优选选择线上退款
        Integer refundType = supportRefundType.contains(RefundTypeEnum.ONLINE_REFUND.getValue())
                ? RefundTypeEnum.ONLINE_REFUND.getValue() : supportRefundType.iterator().next();

        if (Objects.equals(refundType, RefundTypeEnum.ONLINE_REFUND.getValue())) {
            // 校验是否允许在线退款
            refundDomainService.checkOrderCanRefund(orderItem);
        }

        // 保存申请数据, 先加分布式锁, 1锁 2判 3更新
        String lockKey = String.format(OrderItemLockConstant.PREFIX, applyRefundReqDTO.getOrderItemId());
        RefundVoucher refundVoucher = RLockUtils.of(lockKey, () -> {
                    // 锁后再次校验orderItem，同一时刻只能有一笔单据进行
                    OrderItem orderItemNew = orderItemRepository.getOrderItem(applyRefundReqDTO.getOrderItemId());
                    refundDomainService.checkOrderItem(orderItemNew);
                    return refundDomainService.saveUserRefundApply(ProductTypeEnum.ACCOUNT.getValue(),
                            applyRefundReqDTO.getOrderItemId(), applyRefundReqDTO.getRefundReasonId(), "", refundType,
                            refundAmountInfo, userBaseInfo.getUserId());
                }).withWaitTime(3).withTimeUnit(TimeUnit.SECONDS)
                .orElseThrow(() -> new BizException(ErrorCode.ORDER_HAVE_OTHER_REFUND_RECEIPT_FAIL.getErrCode(),
                        ErrorCode.ORDER_HAVE_OTHER_REFUND_RECEIPT_FAIL.getErrDesc()));

        // 同步到es
        // refundDomainService.syncOrderItem2Es(refundVoucher.getOrderItemId());

        // 通知im端客服进行审核
        sendServiceVerifyCard(orderItemExtend, userBaseInfo.getUserId());

        // 记录订单操作日志
        refundDomainService.addRefundOrderOperate(refundVoucher.getOrderItemId(), refundVoucher.getRefundVoucherId(),
                OrderOperateTypeEnum.CREATE_REFUND, OrderOptUserTypeEnum.BUYER, userBaseInfo.getUserId(),
                userBaseInfo.getUserName());
        return PxResponse.ok(true);
    }

    private OrderItemExtend checkDeliveryRoomId(String orderItemId) {
        OrderItemExtend orderItemExtend = doCheckDeliveryRoomId(orderItemId);
        if (orderItemExtend != null) {
            return orderItemExtend;
        }
        // 拉群失败，重新拉群
        log.info("下单拉群失败，调用重新拉群，orderItemId:{}", orderItemId);
        SingleResponse<String> response = null;
        try {
            ImInfoReqDTO imInfoReqDTO = new ImInfoReqDTO();
            imInfoReqDTO.setOrderItemId(orderItemId);
            response = orderImDubboServiceI.getImRoomId(imInfoReqDTO);
        } catch (Exception e) {
            log.error("调用重新拉群接口失败, orderItemId:{}", orderItemId, e);
        }
        Assert.isTrue(
                response != null && response.isSuccess() && StringUtils.isNotEmpty(response.getData())
                        && !Objects.equals("0", response.getData()),
                SERVICE_ROOM_NOT_FOUND_ERROR.getErrCode(), SERVICE_ROOM_NOT_FOUND_ERROR.getErrDesc());
        OrderItemExtend newOrderItemExtend = doCheckDeliveryRoomId(orderItemId);
        Assert.notNull(newOrderItemExtend, SERVICE_ROOM_NOT_FOUND_ERROR.getErrCode(),
                SERVICE_ROOM_NOT_FOUND_ERROR.getErrDesc());
        return newOrderItemExtend;
    }

    private OrderItemExtend doCheckDeliveryRoomId(String orderItemId) {
        OrderItemExtend orderItemExtend = orderItemExtendRepository.getOneByItemId(orderItemId);
        if (orderItemExtend == null) {
            log.error("orderItemExtend is null orderItemId:{}", orderItemId);
            throw new BizException(ORDER_ITEM_EXTEND_NOT_FOUND_ERROR.getErrCode(),
                    ORDER_ITEM_EXTEND_NOT_FOUND_ERROR.getErrDesc());
        }

        if (StringUtils.isNotEmpty(orderItemExtend.getDeliveryRoomId())
                && !Objects.equals("0", orderItemExtend.getDeliveryRoomId())) {
            return orderItemExtend;
        }
        return null;
    }


    /**
     * 客服发起的包赔变更退款申请
     */
    public IndemnityChangeServiceRefundRespDTO serviceSubmitIndemnityChangeRefund(IndemnityChangeServiceRefundReqDTO req) {

        // 不能有进行中的收退放
        boolean existTrading = orderItemRepository.existsProcessingTransfer(req.getOrderItemId());
        if (existTrading) {
            throw new BizException(ErrorCode.EXIST_PROCESSING_TRAD.getErrCode(), ErrorCode.EXIST_PROCESSING_TRAD.getErrDesc());
        }

        ServiceSubmitRefundReqDTO serviceSubmitRefundReq = new ServiceSubmitRefundReqDTO();
        serviceSubmitRefundReq.setOrderItemId(req.getOrderItemId());
        serviceSubmitRefundReq.setRefundIndemnityAmount(req.getRefundAmount());
        serviceSubmitRefundReq.setWholeRefund(RefundWholeEnum.INDEMNITY.getValue());

        // 卖家承担包赔，实际不打款，这里默认记为线上退款
        if (ResponsibleEnum.SELLER.eq(req.getResponseUser())) {
            serviceSubmitRefundReq.setRefundType(RefundTypeEnum.ONLINE_REFUND.getValue());
        } else {
            serviceSubmitRefundReq.setRefundType(req.getPayMode());
        }

        serviceSubmitRefundReq.setCalculateType(RefundCalculateTypeEnum.BY_INPUT_AMOUNT.getValue());
        serviceSubmitRefundReq.setDeliveryRoomId(req.getRoomId());
        serviceSubmitRefundReq.setIndemnityChangeServiceRefundReq(req);

        // 构建人员信息
        UserBaseInfo userBaseInfo = new UserBaseInfo();
        userBaseInfo.setUserId(req.getChangeUser());
        String userName = getAdminUserName(req.getChangeUser());
        userBaseInfo.setUserName(userName);


        OrderItem orderItem = orderItemRepository.getOrderItem(req.getOrderItemId());

        // 获取退款金额信息
        OrderItemAmountInfoBO refundAmountInfo = getRefundAmountInfo(req.getOrderItemId(), serviceSubmitRefundReq, userBaseInfo, orderItem);

        // 如果是卖家承担包赔，则退款单金额记为0.只在明细上记录具体金额
        if (ResponsibleEnum.SELLER.eq(req.getResponseUser())) {
            refundAmountInfo.setTotalAmount(0L);
        }

        String refundId = doSubmitRefund(serviceSubmitRefundReq, refundAmountInfo, userBaseInfo, orderItem);

        return IndemnityChangeServiceRefundRespDTO.builder().refundId(refundId).build();

    }

    private String getAdminUserName(String userId) {
        try {
            SingleResponse<GetSysUserRespDTO> response = adminSysUserServiceI.getUserInfoById(userId);
            if (response.isSuccess() && null != response.getData()) {
                return response.getData().getUserName();
            } else {
                return userId;
            }
        } catch (Exception ex) {
            log.error("通过userId获取用户信息失败, userId:{}", userId, ex);
            return userId;
        }
    }

    /**
     * 客服发起退款
     */
    public PxResponse<Boolean> serviceSubmitRefund(ServiceSubmitRefundReqDTO serviceSubmitRefundReqDTO,
                                                   UserBaseInfo userBaseInfo) {
        Assert.isTrue(StringUtils.isNotBlank(userBaseInfo.getUserId()), GET_USER_BASE_INFO_ERROR.getErrCode(),
                GET_USER_BASE_INFO_ERROR.getErrDesc());

        Integer productType;

        // 校验收款单里面是不是有泰山的, 查所有收的pay log
        checkTSPay(serviceSubmitRefundReqDTO);

        if (StringUtils.isBlank(serviceSubmitRefundReqDTO.getRefundVoucherId())) {
            // 重新发起退款时，校验是否存在别的正在进行中的退款，放款，支付单
            OrderItem orderItem = orderItemRepository.getOrderItem(serviceSubmitRefundReqDTO.getOrderItemId());
            refundDomainService.checkOrderItem(orderItem);
            productType = orderItem.getProductType();

        } else {
            // 审核时校验审核状态
            RefundVoucher refundVoucher =
                    refundVoucherRepository.getByRefundId(serviceSubmitRefundReqDTO.getRefundVoucherId());
            Assert.notNull(refundVoucher, REFUND_NOT_FOUND_ERROR.getErrCode(), REFUND_NOT_FOUND_ERROR.getErrDesc());
            Assert.isTrue(Objects.equals(refundVoucher.getOrderItemId(), serviceSubmitRefundReqDTO.getOrderItemId()),
                    REFUND_VOUCHER_ORDER_ITEM_ERROR.getErrCode(), REFUND_VOUCHER_ORDER_ITEM_ERROR.getErrDesc());
            Assert.isTrue(
                    Objects.equals(refundVoucher.getRefundStatus(), RefundStatusEnum.WAIT_APPLY.getValue()) && Objects
                            .equals(refundVoucher.getAuditStatus(), RefundAuditStatusEnum.WAIT_SERVICE_AUDIT.getValue()),
                    REFUND_AUDIT_STATUS_ERROR.getErrCode(), REFUND_AUDIT_STATUS_ERROR.getErrDesc());
            productType = refundVoucher.getProductType();
        }
        Assert.isTrue(Objects.equals(productType, ProductTypeEnum.ACCOUNT.getValue()),
                REFUND_PRODUCT_TYPE_ERROR.getErrCode(), REFUND_PRODUCT_TYPE_ERROR.getErrDesc());

        // // 校验交付节点必须在卖家提供账号之前
        // checkExpose(serviceSubmitRefundReqDTO.getOrderItemId());

        OrderItem orderItem = orderItemRepository.getOrderItem(serviceSubmitRefundReqDTO.getOrderItemId());

        OrderItemAmountInfoBO refundAmountInfo =
                getRefundAmountInfo(serviceSubmitRefundReqDTO.getOrderItemId(), serviceSubmitRefundReqDTO, userBaseInfo, orderItem);

        doSubmitRefund(serviceSubmitRefundReqDTO, refundAmountInfo, userBaseInfo, orderItem);
        //
        return PxResponse.ok(true);
    }

    private void checkTSPay(ServiceSubmitRefundReqDTO serviceSubmitRefundReqDTO) {
        boolean exists = false;
        if (StringUtils.isBlank(serviceSubmitRefundReqDTO.getOrderItemId())) {
            return;
        }

        if (org.springframework.util.CollectionUtils.isEmpty(tsRefundConfig.getTsIdList()) || tsRefundConfig.getTsIdList().contains("0")) {
            return;
        }

        try {
            List<Payment> orderItemPaymentList = paymentRepository.lambdaQuery()
                    .eq(Payment::getTradeMode, TradeModeEnum.ONLINE.getValue())
                    .eq(Payment::getTradeStatus, TradeStatusEnum.SUCCESS.getValue())
                    .eq(Payment::getBusinessType, PayBusinessTypeEnum.PAY.getValue())
                    .eq(Payment::getOrderId, serviceSubmitRefundReqDTO.getOrderItemId())
                    .last("limit 20" )
                    .list();
            if (CollectionUtils.isEmpty(orderItemPaymentList)) {
                return;
            }
            List<String> paymentList = orderItemPaymentList.stream()
                    .map(Payment::getPaymentId)
                    .toList();

            if (CollectionUtils.isEmpty(paymentList)) {
                return;
            }

            exists = payLogRepository.lambdaQuery()
                    .in(PayLog::getPaymentId, paymentList)
                    .eq(PayLog::getTradeMode, TradeModeEnum.ONLINE.getValue())
                    .eq(PayLog::getPayStatus, PayStatusEnum.PAY_SUCCESS.getValue())
                    .in(PayLog::getPayMerchantId, tsRefundConfig.getTsIdList())
                    .last("limit 20" )
                    .exists();
        } catch (Exception e) {
            log.warn("[退款查询异常] 参数: {}", serviceSubmitRefundReqDTO, e);
        }

        if (!exists) {
            return;
        }

        if (Objects.equals(1, serviceSubmitRefundReqDTO.getRefundType())) {
            throw new BizException("500", "请选择线下退款!!!");
        }


    }

    /**
     * 这里未改变原有逻辑，只是抽出独立方法
     */
    private String doSubmitRefund(ServiceSubmitRefundReqDTO serviceSubmitRefundReqDTO,
                                  OrderItemAmountInfoBO refundAmountInfo,
                                  UserBaseInfo userBaseInfo,
                                  OrderItem orderItem) {

        // 是否为卖家发起的包赔变更
        boolean sellerIndemnityChange = RefundWholeEnum.INDEMNITY.getValue().equals(serviceSubmitRefundReqDTO.getWholeRefund())
                && ResponsibleEnum.SELLER.eq(serviceSubmitRefundReqDTO.getIndemnityChangeServiceRefundReq().getResponseUser());

        ServiceSubmitRefundBO serviceSubmitRefundBO =
                RefundMapping.INSTANCE.toServiceSubmitRefundBO(serviceSubmitRefundReqDTO);
        if (sellerIndemnityChange) {
            serviceSubmitRefundBO.setResponseUser(ResponsibleEnum.SELLER.getValue());
        }

        // 计算下一个审批节点
        Integer nextAuditStatus;
        if (sellerIndemnityChange) {
            nextAuditStatus = RefundAuditStatusEnum.PASS.getValue();

        } else if (Objects.equals(RefundTypeEnum.OFFLINE_PAYOUT.getValue(), serviceSubmitRefundReqDTO.getRefundType())) {
            // 线下退款，还需要收集账号信息
            nextAuditStatus = RefundAuditStatusEnum.WAIT_COLLECT_ACCOUNT_INFO.getValue();
        } else {
            nextAuditStatus = RefundAuditStatusEnum.WAIT_FINANCE_AUDIT.getValue();
        }


        // 保存数据库
        String refundVoucherId = serviceSubmitRefundReqDTO.getRefundVoucherId();
        if (StringUtils.isNotBlank(serviceSubmitRefundReqDTO.getRefundVoucherId())) { // 有退款单id，更新
            refundDomainService.updateRefundSubmit(serviceSubmitRefundReqDTO.getRefundVoucherId(),
                    serviceSubmitRefundReqDTO.getRefundReasonId(), serviceSubmitRefundReqDTO.getWholeRefund(),
                    serviceSubmitRefundReqDTO.getRefundType(), refundAmountInfo, nextAuditStatus, userBaseInfo.getUserId(),
                    RefundAuditStatusEnum.WAIT_SERVICE_AUDIT.getValue());
        } else { // 无退款单id，新建; 先加分布式锁, 1锁 2判 3更新
            if (!sellerIndemnityChange && Objects.equals(RefundTypeEnum.ONLINE_REFUND.getValue(), serviceSubmitRefundReqDTO.getRefundType())) {
                refundDomainService.checkOrderCanRefund(serviceSubmitRefundReqDTO.getOrderItemId());
            }

            String lockKey = String.format(OrderItemLockConstant.PREFIX, serviceSubmitRefundReqDTO.getOrderItemId());
            refundVoucherId = RLockUtils.of(lockKey, () -> {
                        // 锁后再次校验orderItem，同一时刻只能有一笔单据进行
//                        OrderItem orderItem = orderItemRepository.getOrderItem(serviceSubmitRefundReqDTO.getOrderItemId());

                        // 包赔变更只需要检验不存在进行中的收退放即可
                        if (!Objects.equals(serviceSubmitRefundReqDTO.getWholeRefund(), RefundWholeEnum.INDEMNITY.getValue())) {
                            refundDomainService.checkOrderItem(orderItem);
                        }

                        // 更新orderItem退款状态
                        return refundDomainService.saveServiceRefundSubmit(serviceSubmitRefundBO, refundAmountInfo,
                                nextAuditStatus, userBaseInfo.getUserId());
                    }).withWaitTime(3).withTimeUnit(TimeUnit.SECONDS)
                    .orElseThrow(() -> new BizException(ErrorCode.ORDER_HAVE_OTHER_REFUND_RECEIPT_FAIL.getErrCode(),
                            ErrorCode.ORDER_HAVE_OTHER_REFUND_RECEIPT_FAIL.getErrDesc()));

        }

        // 发送违约金MQ
        if (refundAmountInfo.isSendViolateMQFlag()) {
            sendViolateMQ(orderItem, serviceSubmitRefundReqDTO, userBaseInfo, refundVoucherId);
        }

        // 同步到es
        // refundDomainService.syncOrderItem2Es(serviceSubmitRefundReqDTO.getOrderItemId());

        // 记录订单操作日志
        refundDomainService.addRefundOrderOperate(serviceSubmitRefundReqDTO.getOrderItemId(), refundVoucherId,
                StringUtils.isEmpty(serviceSubmitRefundReqDTO.getRefundVoucherId()) ? OrderOperateTypeEnum.CREATE_REFUND
                        : OrderOperateTypeEnum.AUDIT_REFUND_PASS,
                OrderOptUserTypeEnum.SERVICE, userBaseInfo.getUserId(), userBaseInfo.getUserName());


        // 发送订单退款申请消息：退包赔有特殊格式，单独实现
        if (RefundWholeEnum.INDEMNITY.getValue().equals(serviceSubmitRefundReqDTO.getWholeRefund())) {
            sendIndemnityChangeVerifyCard(serviceSubmitRefundReqDTO, userBaseInfo.getUserId(), orderItem);
        } else {
            OrderItemExtend orderItemExtend = checkDeliveryRoomId(serviceSubmitRefundReqDTO.getOrderItemId());
            sendServiceVerifyCard(orderItemExtend, userBaseInfo.getUserId());
        }

        if (Objects.equals(RefundTypeEnum.OFFLINE_PAYOUT.getValue(), serviceSubmitRefundReqDTO.getRefundType())) {
            // 线下收款发送，收集账号信息弹窗
            sendCollectRefundInfoCard(refundVoucherId, userBaseInfo);
        } else if (!sellerIndemnityChange
                && (Objects.equals(RefundTypeEnum.ONLINE_REFUND.getValue(), serviceSubmitRefundReqDTO.getRefundType())
                || Objects.equals(RefundTypeEnum.CREDIT.getValue(), serviceSubmitRefundReqDTO.getRefundType()))) {
            // 线上或挂账发送自动审核延迟mq消息
            refundMessageService.sendRefundAuditMessage(new RefundAuditMessage().setRefundVoucherId(refundVoucherId));
        }

        return refundVoucherId;
    }


    /**
     * 原有逻辑，此处仅将其抽成方法
     */
    private OrderItemAmountInfoBO getRefundAmountInfo(String orderItemId,
                                                      ServiceSubmitRefundReqDTO serviceSubmitRefundReqDTO,
                                                      UserBaseInfo userBaseInfo,
                                                      OrderItem orderItem) {
        // 查询成功的退款单, 并校验是否仍有退款余额
        List<RefundVoucher> successRefundVoucher = refundVoucherRepository.getSuccessRefund(orderItemId);
        // 查询成功收款单
        List<OrderItemReceiptVoucherPO> successReceiptVoucherDetail = refundDomainService
                .getSuccessReceiptVoucherDetail(orderItemId, successRefundVoucher);

        // 根据退款方式计算获取退款金额,并且校验申请退款金额是否存在异常
        OrderItemAmountInfo applyRefundAmountInfo = buildApplyRefundAmountInfo(serviceSubmitRefundReqDTO);
        OrderItemAmountInfoBO refundAmountInfo =
                refundDomainService.getRefundAmountInfo(serviceSubmitRefundReqDTO.getOrderItemId(), successRefundVoucher,
                        successReceiptVoucherDetail, serviceSubmitRefundReqDTO.getWholeRefund(), applyRefundAmountInfo,
                        serviceSubmitRefundReqDTO.getCalculateType());

        // 若有违约金
        boolean sendViolateMQFlag = false;
        if (Boolean.TRUE.equals(serviceSubmitRefundReqDTO.getIsViolate())
                && ObjectUtil.isNotEmpty(serviceSubmitRefundReqDTO.getRefundViolateDetail())) {
            // 校验
            RefundViolateDetailReqDTO violateDetail = serviceSubmitRefundReqDTO.getRefundViolateDetail();

            checkViolateParam(serviceSubmitRefundReqDTO.getWholeRefund(), violateDetail, refundAmountInfo.getTotalAmount(), violateDetail.getUserType(), orderItem);

            if (ViolateUserTypeEnum.BUYER.eq(violateDetail.getUserType())) {
                // 若有买家违约金 可退金额减掉
                refundAmountInfo.setTotalAmount(refundAmountInfo.getTotalAmount() - violateDetail.getViolateAmount());
                refundAmountInfo.setBuyerViolateAmount(violateDetail.getViolateAmount());
                Assert.isTrue(refundAmountInfo.getTotalAmount() > 0, AMOUNT_INVALID_FAIL.getErrCode(), AMOUNT_INVALID_FAIL.getErrDesc());
            }
            // 退款申请-发送违约金MQ
            sendViolateMQFlag = true;
        }

        refundAmountInfo.setSendViolateMQFlag(sendViolateMQFlag);

        log.info("[order refund] userBaseInfo:{},successReceiptVoucherDetail:{},refundAmountInfo:{},reqDTO:{},sendViolateMQFlag:{}", userBaseInfo,
                successReceiptVoucherDetail, refundAmountInfo, serviceSubmitRefundReqDTO, sendViolateMQFlag);

        // 是否为卖家发的包赔变更
        boolean sellerIndemnityChange = serviceSubmitRefundReqDTO.getWholeRefund().equals(WholeRefundTypeEnum.INDEMNITY.getValue())
                && null != serviceSubmitRefundReqDTO.getIndemnityChangeServiceRefundReq()
                && ResponsibleEnum.SELLER.eq(serviceSubmitRefundReqDTO.getIndemnityChangeServiceRefundReq().getResponseUser());

        // 计算退款方式,并校验是否包含请求的退款方式
        // 卖家承担包赔，不用校验付款方式的合理性
        if (!sellerIndemnityChange) {
            OrderInfoDubboRespDTO order = orderItemRpcGateway.getOrderInfo(serviceSubmitRefundReqDTO.getOrderItemId());
            Set<Integer> supportRefundType = refundDomainService.supportRefundType(order, successReceiptVoucherDetail, refundAmountInfo.getTotalAmount());
            if (!supportRefundType.contains(serviceSubmitRefundReqDTO.getRefundType())) {
                throw new BizException(REFUND_TYPE_VALID_FAIL_ERROR.getErrCode(),
                        REFUND_TYPE_VALID_FAIL_ERROR.getErrDesc());
            }
        } else {
            // 卖家承担包赔
            refundAmountInfo.setIndemnityResponseUser(ResponsibleEnum.SELLER.getValue());
        }

        return refundAmountInfo;
    }


    /**
     * 如果当前发起人不是订单的交付客服，不能申请退款
     */
    private void checkServiceId(ServiceSubmitRefundReqDTO serviceSubmitRefundReqDTO, String serviceId) {
        OrderItemExtend orderItemExtend =
                orderItemExtendRepository.getOneByItemId(serviceSubmitRefundReqDTO.getOrderItemId());
        if (StringUtils.isEmpty(serviceId) || !Objects.equals(orderItemExtend.getDeliveryCustomerId(), serviceId)) {
            throw new BizException(DELIVERY_CUSTOMER_NOT_MATCH_ERROR.getErrCode(),
                    DELIVERY_CUSTOMER_NOT_MATCH_ERROR.getErrDesc());
        }
    }

    private void doRefundIntercept(ApplyRefundReqDTO applyRefundReqDTO) {
        // todo 退款原因来判断是否触发退款挽留
    }

    /**
     * 校验交付节点必须在卖家提供账号之前
     */
    private void checkExpose(String orderItemId) {
        OrderItemIdReqDTO orderItemIdReqDTO = new OrderItemIdReqDTO();
        orderItemIdReqDTO.setOrderItemId(orderItemId);
        SingleResponse<Boolean> sellerAccountExpose = deliveryFlowServiceI.sellerAccountExpose(orderItemIdReqDTO);

        // 卖家账号信息已暴露，不允许买家申请退款
        if (sellerAccountExpose.isSuccess() && Boolean.TRUE.equals(sellerAccountExpose.getData())) {
            throw new BizException(SELLER_ACCOUNT_EXPOSE_FAIL.getErrCode(), SELLER_ACCOUNT_EXPOSE_FAIL.getErrDesc());
        }
    }

    /**
     * 发送退款申请通知
     */
    public void sendServiceVerifyCard(OrderItemExtend orderItemExtend, String userId) {
        CustomercareNoticeContent noticeMsgDTO =
                new CustomercareNoticeContent(String.format(BaseConstant.REFUND_APPLY_FORMAT,
                        orderItemExtend.getProductUniqueNo(), orderItemExtend.getOrderItemId()));
        SendRichTextMsgReqDTO msgReqDTO = new SendRichTextMsgReqDTO().setTargetId(orderItemExtend.getDeliveryRoomId())
                .setFromUserId(userId).setTitle("订单申请退款").setCustomercareNoticeContent(noticeMsgDTO)
                .setMentionedIds(List.of(orderItemExtend.getDeliveryCustomerId()))
                .setContent(List.of(new RichTextContent("订单编号: " + orderItemExtend.getOrderItemId()),
                        new RichTextContent("商品编号: " + orderItemExtend.getProductUniqueNo())));
        SingleResponse<String> response = sendCommonMsgServiceI.sendRichTextMsg(msgReqDTO);
        log.info("sendServiceVerifyCard request:{} , response:{}", JSONObject.toJSONString(msgReqDTO),
                JSONObject.toJSONString(response));
        if (!response.isSuccess()) {
            log.error("sendServiceVerifyCard fail request:{} , response:{}", JSONObject.toJSONString(msgReqDTO),
                    JSONObject.toJSONString(response));
        } else {
            response.getData();
        }
    }

    /**
     * 发送变更包赔退款打款卡片
     */
    public void sendIndemnityChangeVerifyCard(ServiceSubmitRefundReqDTO req, String userId, OrderItem orderItem) {

        // 构建通知内容
        List<BaseTableColumnMsgContent> content = buildIndemnityChangeRefundCardContent(req.getIndemnityChangeServiceRefundReq());

        String priceStr = new Money(req.getIndemnityChangeServiceRefundReq().getRefundAmount()).getYuan();

        CustomercareNoticeContent noticeMsgDTO = new CustomercareNoticeContent("向买家退款:" + PayConstant.RMB + priceStr);

        // 如果是卖家承担，则通知三方。买家就通知客服和买家即可。
        List<String> targetUserIds = ResponsibleEnum.SELLER.eq(req.getIndemnityChangeServiceRefundReq().getResponseUser())
                ? List.of(req.getIndemnityChangeServiceRefundReq().getResponseUserId(), userId, orderItem.getBuyerId())
                : List.of(req.getIndemnityChangeServiceRefundReq().getResponseUserId(), userId);


        SendTableColumnMsgReqDTO msgReqDTO = new SendTableColumnMsgReqDTO()
                .setTargetId(req.getDeliveryRoomId())
                .setFromUserId(userId)
                .setTitle("包赔变更退款")
                .setCustomercareNoticeContent(noticeMsgDTO)
                .setMentionedIds(targetUserIds)
                .setTargetUserIds(targetUserIds)
                .setContent(content);

        SingleResponse<String> response = sendCommonMsgServiceI.sendTableColumnCardMsg(msgReqDTO);
        log.info("sendIndemnityChangeVerifyCard request:{} , response:{}", JSONObject.toJSONString(msgReqDTO),
                JSONObject.toJSONString(response));
        if (!response.isSuccess()) {
            log.error("sendIndemnityChangeVerifyCard fail request:{} , response:{}", JSONObject.toJSONString(msgReqDTO),
                    JSONObject.toJSONString(response));
        } else {
            response.getData();
        }
    }

    /**
     * 客服拒绝买家退款请求,向买家发送私信
     */
    public PxResponse<Boolean> supportRefundReject(SupportRefundRejectReqDTO supportRefundRejectReqDTO,
                                                   UserBaseInfo userBaseInfo) {
        Assert.isTrue(StringUtils.isNotBlank(userBaseInfo.getUserId()), GET_USER_BASE_INFO_ERROR.getErrCode(),
                GET_USER_BASE_INFO_ERROR.getErrDesc());
        RefundVoucher refundVoucher =
                refundVoucherRepository.getByRefundId(supportRefundRejectReqDTO.getRefundVoucherId());
        Assert.notNull(refundVoucher, REFUND_NOT_FOUND_ERROR.getErrCode(), REFUND_NOT_FOUND_ERROR.getErrDesc());
        Assert.isTrue(Objects.equals(refundVoucher.getProductType(), ProductTypeEnum.ACCOUNT.getValue()),
                REFUND_PRODUCT_TYPE_ERROR.getErrCode(), REFUND_PRODUCT_TYPE_ERROR.getErrDesc());
        // 更新orderItem退款状态
        boolean suc = orderItemRepository.updateRefundStatus(refundVoucher.getOrderItemId(),
                RefundStatusEnum.WAIT_APPLY.getValue(), RefundStatusEnum.CLOSE.getValue());
        Assert.isTrue(suc, REFUND_DB_SAVE_ERROR.getErrCode(), REFUND_DB_SAVE_ERROR.getErrDesc());
        // 客服拒绝买家退款，关闭退款
        RefundVoucher toUpdate = new RefundVoucher().setRefundVoucherId(supportRefundRejectReqDTO.getRefundVoucherId())
                .setRefundStatus(RefundStatusEnum.CLOSE.getValue()).setAuditStatus(RefundAuditStatusEnum.REJECT.getValue())
                .setCloseType(RefundActionTypeEnum.SERVICE.getValue()).setSubmitter(userBaseInfo.getUserId())
                .setUpdateUserId(userBaseInfo.getUserId()).setRejectReason(supportRefundRejectReqDTO.getRejectReason())
                .setFinishTime(LocalDateTime.now());
        boolean refundVoucherSuc = refundVoucherRepository.updateByRefundVoucherId(toUpdate);
        Assert.isTrue(refundVoucherSuc, REFUND_DB_SAVE_ERROR.getErrCode(), REFUND_DB_SAVE_ERROR.getErrDesc());

        // 同步到es
        // refundDomainService.syncOrderItem2Es(refundVoucher.getOrderItemId());

        // 记录订单操作日志
        refundDomainService.addRefundOrderOperate(refundVoucher.getOrderItemId(), refundVoucher.getRefundVoucherId(),
                OrderOperateTypeEnum.AUDIT_REFUND_REJECT, OrderOptUserTypeEnum.SERVICE, userBaseInfo.getUserId(),
                userBaseInfo.getUserName());

        OrderItemExtend orderItemExtend = orderItemExtendRepository.getOneByItemId(refundVoucher.getOrderItemId());
        // 向买家发送告知退款原因
        SendTextMsgReqDTO sendTextMsgReqDTO = new SendTextMsgReqDTO()
                .setFromUserId(orderItemExtend.getDeliveryCustomerId()).setTargetId(orderItemExtend.getDeliveryRoomId())
                .setContent("退款已拒绝：" + supportRefundRejectReqDTO.getRejectReason());
        SingleResponse<Boolean> booleanSingleResponse = sendMsgServiceI.sendTextMsg(sendTextMsgReqDTO);
        log.info("supportRefundReject sendTextMsg request:{} , response:{}", JSONObject.toJSONString(sendTextMsgReqDTO),
                JSONObject.toJSONString(booleanSingleResponse));

        return PxResponse.ok(true);
    }

    /**
     * 发送收集买家退款账号信息卡片
     */
    public PxResponse<Boolean> sendCollectRefundInfoCard(String refundVoucherId, UserBaseInfo userBaseInfo) {
        Assert.isTrue(StringUtils.isNotBlank(userBaseInfo.getUserId()), GET_USER_BASE_INFO_ERROR.getErrCode(),
                GET_USER_BASE_INFO_ERROR.getErrDesc());
        RefundVoucher refundVoucher = refundVoucherRepository.getByRefundId(refundVoucherId);
        Assert.notNull(refundVoucher, REFUND_NOT_FOUND_ERROR.getErrCode(), REFUND_NOT_FOUND_ERROR.getErrDesc());
        String orderItemId = refundVoucher.getOrderItemId();
        OrderItemExtend orderItemExtend = orderItemExtendRepository.getOneByItemId(orderItemId);
        Assert.notNull(orderItemExtend, ORDER_ITEM_EXTEND_NOT_FOUND_ERROR.getErrCode(),
                ORDER_ITEM_EXTEND_NOT_FOUND_ERROR.getErrDesc());
        OrderItem orderItem = orderItemRepository.getOrderItem(orderItemId);
        Assert.notNull(orderItem, ORDER_ITEM_NOT_FOUND_ERROR.getErrCode(), ORDER_ITEM_NOT_FOUND_ERROR.getErrDesc());

        // 记录订单操作日志
        refundDomainService.addRefundOrderOperate(refundVoucher.getOrderItemId(), refundVoucher.getRefundVoucherId(),
                OrderOperateTypeEnum.COLLECT_REFUND_ACCOUNT, OrderOptUserTypeEnum.SERVICE, userBaseInfo.getUserId(),
                userBaseInfo.getUserName());

        SendFormMsgReqDTO msgReqDTO = RefundImCardUtil.buildFormDataMsg(refundVoucherId, orderItemExtend, orderItem);

        SingleResponse<String> response = sendCommonMsgServiceI.sendFormCardMsg(msgReqDTO);
        log.info("sendCollectRefundInfoCard request:{} , response:{}", JSONObject.toJSONString(msgReqDTO),
                JSONObject.toJSONString(response));
        if (!response.isSuccess()) {
            throw new BizException(SEND_BUYER_COLLECT_CARD_ERROR.getErrCode(),
                    SEND_BUYER_COLLECT_CARD_ERROR.getErrDesc());
        }
        return PxResponse.ok(true);
    }

    /**
     * 买家取消退款
     */
    public PxResponse<Boolean> cancelRefund(CancelRefundReqDTO refundReqDTO, UserBaseInfo userBaseInfo,
                                            RefundActionTypeEnum cancelRole) {
        Assert.isTrue(StringUtils.isNotBlank(userBaseInfo.getUserId()), GET_USER_BASE_INFO_ERROR.getErrCode(),
                GET_USER_BASE_INFO_ERROR.getErrDesc());
        // 更新退款单状态
        RefundVoucher refundVoucher = refundVoucherRepository.getByRefundId(refundReqDTO.getRefundVoucherId());
        Assert.notNull(refundVoucher, REFUND_NOT_FOUND_ERROR.getErrCode(), REFUND_NOT_FOUND_ERROR.getErrDesc());
        Assert.isTrue(
                Objects.equals(refundVoucher.getRefundStatus(), RefundStatusEnum.WAIT_APPLY.getValue()) && (Objects
                        .equals(refundVoucher.getAuditStatus(), RefundAuditStatusEnum.WAIT_COLLECT_ACCOUNT_INFO.getValue())
                        || Objects.equals(refundVoucher.getAuditStatus(), RefundAuditStatusEnum.WAIT_FINANCE_AUDIT.getValue())),
                REFUND_BUYER_CANCEL_ERROR.getErrCode(), REFUND_BUYER_CANCEL_ERROR.getErrDesc());

        refundDomainService.userCancelRefund(refundVoucher.getRefundVoucherId(),
                refundVoucher.getOrderItemId(),
                cancelRole,
                refundVoucher.getWholeRefund()
        );

        // 同步到es
        // refundDomainService.syncOrderItem2Es(refundVoucher.getOrderItemId());

        // 记录订单操作日志
        OrderOptUserTypeEnum role = Objects.equals(cancelRole, RefundActionTypeEnum.SERVICE)
                ? OrderOptUserTypeEnum.SERVICE : OrderOptUserTypeEnum.BUYER;
        refundDomainService.addRefundOrderOperate(refundVoucher.getOrderItemId(), refundVoucher.getRefundVoucherId(),
                OrderOperateTypeEnum.CANCEL_REFUND, role, userBaseInfo.getUserId(), userBaseInfo.getUserName());

        if (StringUtils.isNotEmpty(refundReqDTO.getMsgUid())) {
            OrderItemExtend orderItemExtend = orderItemExtendRepository.getOneByItemId(refundVoucher.getOrderItemId());
            // 更改卡片扩展信息状态
            SetExpansionDTO sellerCard = new SetExpansionDTO().setMsgUID(refundReqDTO.getMsgUid())
                    .setFormUserId(orderItemExtend.getDeliveryCustomerId()).setTargetId(orderItemExtend.getDeliveryRoomId())
                    .setWaterMark(new WaterMarkYQX());
            SingleResponse<Boolean> booleanSingleResponse = sendMsgServiceI.setExpansion(sellerCard);
            log.info("cancelRefund setExpansion request:{} , response:{}", JSONObject.toJSONString(sellerCard),
                    JSONObject.toJSONString(booleanSingleResponse));
        }
        return PxResponse.ok(true);
    }

    /**
     * 提交买家打款信息
     */
    public PxResponse<Boolean> submitUserRefundInfo(RefundUserInfoDTO refundUserInfoDTO, UserBaseInfo userBaseInfo) {
        Assert.isTrue(StringUtils.isNotBlank(userBaseInfo.getUserId()), GET_USER_BASE_INFO_ERROR.getErrCode(),
                GET_USER_BASE_INFO_ERROR.getErrDesc());
        // 将买家信息补充到退款单内
        RefundVoucher refundVoucher = refundVoucherRepository.getByRefundId(refundUserInfoDTO.getRefundVoucherId());
        Assert.notNull(refundVoucher, REFUND_NOT_FOUND_ERROR.getErrCode(), REFUND_NOT_FOUND_ERROR.getErrDesc());
        // 校验退款状态和审核状态
        Assert.isTrue(
                Objects.equals(refundVoucher.getAuditStatus(), RefundAuditStatusEnum.WAIT_COLLECT_ACCOUNT_INFO.getValue())
                        && Objects.equals(refundVoucher.getRefundStatus(), RefundStatusEnum.WAIT_APPLY.getValue()),
                REFUND_BUYER_ACCOUNT_EXIST.getErrCode(), REFUND_BUYER_ACCOUNT_EXIST.getErrDesc());
        RefundVoucher toUpdate = new RefundVoucher().setRefundVoucherId(refundUserInfoDTO.getRefundVoucherId())
                .setBuyerName(refundUserInfoDTO.getBuyerName()).setRefundAccount(refundUserInfoDTO.getRefundAccount())
                .setRefundChannel(refundUserInfoDTO.getRefundChannel())
                .setAuditStatus(RefundAuditStatusEnum.WAIT_FINANCE_AUDIT.getValue());
        // 更新时再次检查上一个退款审核状态
        boolean suc = refundVoucherRepository.updateByRefundId(toUpdate,
                RefundAuditStatusEnum.WAIT_COLLECT_ACCOUNT_INFO.getValue());
        Assert.isTrue(suc, REFUND_BUYER_ACCOUNT_EXIST.getErrCode(), REFUND_BUYER_ACCOUNT_EXIST.getErrDesc());

        // 记录订单操作日志
        refundDomainService.addRefundOrderOperate(refundVoucher.getOrderItemId(), refundVoucher.getRefundVoucherId(),
                OrderOperateTypeEnum.SUBMIT_RECEIPT_ACCOUNT, OrderOptUserTypeEnum.BUYER, userBaseInfo.getUserId(),
                userBaseInfo.getUserName());

        OrderItemExtend orderItemExtend = orderItemExtendRepository.getOneByItemId(refundVoucher.getOrderItemId());
        // 更改卡片扩展信息状态
        SetExpansionDTO sellerCard = new SetExpansionDTO().setMsgUID(refundUserInfoDTO.getMsgUid())
                .setFormUserId(orderItemExtend.getDeliveryCustomerId()).setTargetId(orderItemExtend.getDeliveryRoomId())
                .setWaterMark(new WaterMarkYTJ()).setContent(refundUserInfoDTO.getContent());

        SingleResponse<Boolean> booleanSingleResponse = sendMsgServiceI.setExpansion(sellerCard);
        log.info("submitUserRefundInfo request:{} , response:{}", JSONObject.toJSONString(sellerCard),
                JSONObject.toJSONString(booleanSingleResponse));
        return PxResponse.ok(true);
    }

    /**
     * 退款列表分页查询
     */
    public PxPageResponse<RefundPageRespDTO> refundPage(RefundPageReqDTO refundPageReqDTO, String userId) {
        Assert.isTrue(StringUtils.isNotBlank(userId), GET_USER_BASE_INFO_ERROR.getErrCode(),
                GET_USER_BASE_INFO_ERROR.getErrDesc());
        RefundVoucherSearchPO refundVoucherSearchPO = RefundMapping.INSTANCE.toRefundVoucherSearchPO(refundPageReqDTO);
        refundVoucherSearchPO.setUserId(userId);
        SearchPage<RefundVoucherDoc> searchHits = esRefundSearchServiceI.searchRefundPage(refundVoucherSearchPO);

        if (searchHits == null || CollectionUtils.isEmpty(searchHits.getContent())) {
            return PxPageResponse.of(List.of(), 0, refundPageReqDTO.getPageSize(), refundPageReqDTO.getPageIndex());
        }
        List<SearchHit<RefundVoucherDoc>> refundVoucherDocList = searchHits.getContent();

        // 查询补全订单信息
        List<SearchHit<OrderItemAggregation>> orderHits = esOrderItemSearchService.orderItemSearchByOrderItemIds(
                refundVoucherDocList.stream().map(e -> e.getContent().getOrderItemId()).toList());
        Map<String, OrderItemAggregation> orderItemAggregationMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(orderHits)) {
            for (SearchHit<OrderItemAggregation> hit : orderHits) {
                orderItemAggregationMap.put(hit.getContent().getOrderItemId(), hit.getContent());
            }
        }

        // 查询补全detail信息
        List<RefundVoucherDetail> details = refundVoucherDetailRepository.getByRefundVoucherIds(
                refundVoucherDocList.stream().map(e -> e.getContent().getRefundVoucherId()).toList());
        Map<String, RefundVoucherDetail> detailMap =
                details.stream().collect(Collectors.toMap(RefundVoucherDetail::getRefundVoucherId, Function.identity()));

        List<RefundPageRespDTO> result = refundVoucherDocList.stream().map(e -> {
            RefundVoucherDoc refundVoucherDoc = e.getContent();
            OrderItemAggregation orderItemAggregation = orderItemAggregationMap.get(refundVoucherDoc.getOrderItemId());
            RefundPageRespDTO refundPageRespDTO =
                    RefundMapping.INSTANCE.toRefundPageRespDTO(refundVoucherDoc, orderItemAggregation);
            if (Objects.nonNull(orderItemAggregation)
                    && CollectionUtils.isNotEmpty(orderItemAggregation.getOrderItemIndemnityDocList())) {
                List<OrderItemIndemnityDoc> orderItemIndemnityDocList =
                        orderItemAggregation.getOrderItemIndemnityDocList();
                if (CollectionUtils.isNotEmpty(orderItemIndemnityDocList)) {
                    refundPageRespDTO.setIndemnityNameList(
                            orderItemIndemnityDocList.stream().map(OrderItemIndemnityDoc::getIndemnityName).toList());
                }
            }

            // 清除返回手机号
            if (Objects.equals(refundPageReqDTO.getIdentity(), RefundPageReqDTO.IDENTITY_BUYER)) {
                refundPageRespDTO.setSellerPhone("");
            } else if (Objects.equals(refundPageReqDTO.getIdentity(), RefundPageReqDTO.IDENTITY_SELLER)) {
                refundPageRespDTO.setBuyerPhone("");
            } else {
                refundPageRespDTO.setSellerPhone("");
                refundPageRespDTO.setBuyerPhone("");
            }
            RefundVoucherDetail refundVoucherDetail = detailMap.get(refundVoucherDoc.getRefundVoucherId());
            if (refundVoucherDetail != null) {
                refundPageRespDTO.setProductAmount(refundVoucherDetail.getProductAmount());
                refundPageRespDTO.setFeeAmount(refundVoucherDetail.getFeeAmount());
                refundPageRespDTO.setIndemnityAmount(refundVoucherDetail.getIndemnityAmount());
            }
            return refundPageRespDTO;
        }).toList();

        return PxPageResponse.of(result, (int) searchHits.getTotalElements(), refundPageReqDTO.getPageSize(),
                refundPageReqDTO.getPageIndex());
    }

    /**
     * 客服点击审核通过，获取用户发起退款的信息
     */
    public RefundInitInfoRespDTO getRefundInitInfo(ImRoomIdReqDTO dto) {
        RefundInitInfoRespDTO refundInitInfoRespDTO = new RefundInitInfoRespDTO();

        // 从es获取房间内订单聚合信息
        OrderItemAggregation orderItemAggregation = esOrderItemSearchService.findOneByRoomId(dto.getRoomId());
        Assert.isTrue(StringUtils.isNotEmpty(orderItemAggregation.getOrderItemId()),
                ORDER_ITEM_NOT_FOUND_ERROR.getErrCode(), ORDER_ITEM_NOT_FOUND_ERROR.getErrDesc());

        String orderItermId = orderItemAggregation.getOrderItemId();

        // 判断退款方式
        List<RefundVoucher> successRefundVoucher =
                refundVoucherRepository.getSuccessRefund(orderItemAggregation.getOrderItemId());
        List<OrderItemReceiptVoucherPO> successReceiptVoucherDetail =
                refundDomainService.getSuccessReceiptVoucherDetail(orderItermId, successRefundVoucher);

        // 计算全额退款金额，并校验退款金额是否充足
        OrderItemAmountInfoBO wholeRefundAmountInfo = refundDomainService.getWholeRefundAmountInfo(orderItermId,
                successRefundVoucher, successReceiptVoucherDetail);

        OrderInfoDubboRespDTO order = orderItemRpcGateway.getOrderInfo(orderItermId);
        Set<Integer> supportRefundType =
                refundDomainService.supportRefundType(order, successReceiptVoucherDetail, wholeRefundAmountInfo.getTotalAmount());

        // 违约金额
        long defaultViolateAmount = (long) (order.getProductSalePrice() * 0.05);

        // 返回退款页面的初始化数据
        refundInitInfoRespDTO.setOrderItemId(orderItemAggregation.getOrderItemId());
        refundInitInfoRespDTO.setRefundAmount(wholeRefundAmountInfo.getTotalAmount());
        refundInitInfoRespDTO.setMaxProductAmount(wholeRefundAmountInfo.getProductAmount());
        refundInitInfoRespDTO.setMaxFeeAmount(wholeRefundAmountInfo.getFeeAmount());
        refundInitInfoRespDTO.setMaxIndemnityAmount(wholeRefundAmountInfo.getIndemnityAmount());
        refundInitInfoRespDTO.setPayModels(supportRefundType);
        refundInitInfoRespDTO
                .setWholeRefund(Sets.newHashSet(RefundWholeEnum.PART.getValue(), RefundWholeEnum.WHOLE.getValue()));
        refundInitInfoRespDTO.setDefaultViolateAmount(defaultViolateAmount);

        return refundInitInfoRespDTO;
    }

    /**
     * 计算退款金额，退款方式为全额退款时，可以不用传入金额，后台计算该订单剩余可退金额,退款方式不为全额退款时，需要传入退款金额
     */
    public CalculateRefundAmountRespDTO calculateRefundAmount(CalculateRefundAmountReqDTO dto) {
        if (Objects.equals(dto.getWholeRefund(), RefundWholeEnum.PART.getValue()) && dto.getRefundAmount() == null) {
            throw new BizException(PARAM_VAL_ERR.getErrCode(), PARAM_VAL_ERR.getErrDesc());
        }
        // 查询成功的退款单
        List<RefundVoucher> successRefundVoucher = refundVoucherRepository.getSuccessRefund(dto.getOrderItemId());
        // 查询成功收款单
        List<OrderItemReceiptVoucherPO> successReceiptVoucherDetail =
                refundDomainService.getSuccessReceiptVoucherDetail(dto.getOrderItemId(), successRefundVoucher);
        return RefundMapping.INSTANCE.toCalculateRefundAmountRespDTO(
                refundDomainService.getRefundAmountInfo(
                        dto.getOrderItemId(), successRefundVoucher, successReceiptVoucherDetail, dto.getWholeRefund(),
                        new OrderItemAmountInfo().setProductAmount(dto.getRefundAmount() == null ? 0 : dto.getRefundAmount()),
                        RefundCalculateTypeEnum.BY_PRODUCT_AMOUNT.getValue()
                )
        );
    }

    public PxResponse<RefundDetailRespDTO> getRefundDetail(String refundVoucherId) {
        RefundVoucher refundVoucher = refundVoucherRepository.getByRefundId(refundVoucherId);
        RefundVoucherDetail refundVoucherDetail = refundVoucherDetailRepository.getOneByRefundId(refundVoucherId);
        Assert.notNull(refundVoucher, REFUND_NOT_FOUND_ERROR.getErrCode(), REFUND_NOT_FOUND_ERROR.getErrDesc());
        Assert.notNull(refundVoucherDetail, REFUND_NOT_FOUND_ERROR.getErrCode(), REFUND_NOT_FOUND_ERROR.getErrDesc());

        // 校验权限：只能看到自己的单据
        if (!checkPermission(refundVoucher.getOrderItemId())) {
            return PxResponse.ok(null);
        }

        RefundDetailRespDTO refundDetailRespDTO =
                RefundMapping.INSTANCE.toRefundDetailRespDTO(refundVoucher, refundVoucherDetail);
        // 补全制单客服
        if (!Objects.equals(refundVoucher.getAuditStatus(), RefundAuditStatusEnum.WAIT_SERVICE_AUDIT.getValue())) {
            try {
                SingleResponse<CustomerCareInfoRespDTO> response =
                        sysUserServiceI.getSysUserByAccount(refundVoucher.getSubmitter());
                if (response != null && response.isSuccess()) {
                    String serviceName = StringUtils.isNotEmpty(response.getData().getNickName())
                            ? response.getData().getNickName() : response.getData().getUserName();
                    refundDetailRespDTO.setServiceName(serviceName);
                }
            } catch (Exception e) {
                log.error("getServiceName userId:{} 获取用户信息失败:{}", refundVoucher.getSubmitter(), e.getMessage());
            }
        }
        // 补全原因
        if (StringUtils.isNotEmpty(refundVoucher.getRefundReason())) {
            // 充值、诚心卖不进退款原因，直接使用文本 TODO 后续 账号订单退款也会保存 refundReason 字段
            refundDetailRespDTO.setOrderRefundReason(refundVoucher.getRefundReason());
        } else if (StringUtils.isNotEmpty(refundVoucher.getRefundReasonId())) {
            // 根据 业务ID 获取 退款原因Content （包括已经删除的）
            String reasonContent = refundReasonRepository.getReasonContentIncludeDeleted(refundVoucher.getRefundReasonId());
            refundDetailRespDTO.setOrderRefundReason(reasonContent);
        }

        // 补全订单信息
        List<SearchHit<OrderItemAggregation>> searchHits =
                esOrderItemSearchService.orderItemSearchByOrderItemIds(List.of(refundVoucher.getOrderItemId()));
        if (CollectionUtils.isNotEmpty(searchHits)) {
            OrderItemAggregation orderItemAggregation = searchHits.get(0).getContent();
            refundDetailRespDTO.setProductName(orderItemAggregation.getProductName());
            refundDetailRespDTO.setProductPic(orderItemAggregation.getProductPic());
            refundDetailRespDTO.setGameName(orderItemAggregation.getGameName());
            refundDetailRespDTO.setProductAttr(orderItemAggregation.getProductAttr());
            refundDetailRespDTO.setProductId(orderItemAggregation.getProductId());
            refundDetailRespDTO.setProductSalePrice(orderItemAggregation.getProductSalePrice());
            refundDetailRespDTO.setProductCouponAmount(orderItemAggregation.getProductCouponAmount());
            refundDetailRespDTO.setProductPayAmount(orderItemAggregation.getProductPayAmount());
            refundDetailRespDTO.setProductUniqueNo(orderItemAggregation.getProductUniqueNo());
            refundDetailRespDTO.setProductQuantity(orderItemAggregation.getProductQuantity());
            refundDetailRespDTO.setRechargePackage(orderItemAggregation.getRechargePackage());
            refundDetailRespDTO.setViolateId(orderItemAggregation.getViolateId());
            if (CollectionUtils.isNotEmpty(orderItemAggregation.getOrderItemIndemnityDocList())) {
                refundDetailRespDTO.setIndemnityNameList(orderItemAggregation.getOrderItemIndemnityDocList().stream()
                        .map(OrderItemIndemnityDoc::getIndemnityName).toList());
            }
        }
        return PxResponse.ok(refundDetailRespDTO);
    }

    public PxResponse<List<RefundOrderItemPageRespDTO>> getOrderRefund(OrderRefundReqDTO orderRefundReqDTO) {
        // 校验权限：只能看到自己的订单
        if (!checkPermission(orderRefundReqDTO.getOrderItemId())) {
            return PxResponse.ok(List.of());
        }

        List<RefundOrderItemPageRespBO> orderRefundList = refundDomainService.getOrderRefundList(orderRefundReqDTO.getOrderItemId());
        /*List<RefundVoucher> itemRefundList =
                refundVoucherRepository.getItemRefundList(orderRefundReqDTO.getOrderItemId());
        List<RefundVoucherDetail> byOrderItemId =
                refundVoucherDetailRepository.getByOrderItemId(orderRefundReqDTO.getOrderItemId());
        Map<String, RefundVoucherDetail> refundId2OrderRefundVoucher =
                byOrderItemId.stream().collect(Collectors.toMap(RefundVoucherDetail::getRefundVoucherId, e -> e));
        List<RefundOrderItemPageRespDTO> rlt = Lists.newArrayList();
        itemRefundList.forEach(refund -> {
            RefundOrderItemPageRespDTO respDTO = RefundMapping.INSTANCE.toRefundOrderItemPageRespDTO(refund,
                    refundId2OrderRefundVoucher.get(refund.getRefundVoucherId()));
            rlt.add(respDTO);
        });*/
        return PxResponse.ok(RefundMapping.INSTANCE.toRefundOrderItemPageRespDTOList(orderRefundList));
    }

    /**
     * 校验权限是否通过
     *
     * @param orderItemId
     * @return 通过返回true、 不通过返回false
     */
    public Boolean checkPermission(String orderItemId) {
        if (UserIdUtils.isAdmin()) {
            // 后台 或者 客服 直接放行
            return true;
        }
        if (UserIdUtils.isMerchant()) {
            OrderItemExtend orderItemExtend = orderItemExtendRepository.getOneByItemId(orderItemId);
            // 商家 校验部门
            String merchantId = UserIdUtils.getMerchantId();
            if (!Objects.equals(orderItemExtend.getBuyerMerchantId(), merchantId)
                    && !Objects.equals(orderItemExtend.getSellerMerchantId(), merchantId)) {
                log.warn("checkPermission no permission, buyerMerchantId:{}, sellerMerchantId:{},  merchantId:{}",
                        orderItemExtend.getBuyerMerchantId(), orderItemExtend.getSellerMerchantId(), merchantId);
                return false;
            }
        }
        if (UserIdUtils.isUser()) {
            OrderItem orderItem = orderItemRepository.getOrderItem(orderItemId);
            String userId = LoginUserUtil.getUserId();
            // 散户 校验本人
            if (!Objects.equals(orderItem.getBuyerId(), userId) && !Objects.equals(orderItem.getSellerId(), userId)) {
                log.warn("checkPermission no permission, buyerId:{}, sellerId:{},  nowUserId:{}",
                        orderItem.getBuyerId(), orderItem.getSellerId(), userId);
                return false;
            }
        }

        return true;
    }

    /**
     * 诚心卖订单部分退款
     */
    @Transactional
    public Boolean sinceritySellOrderRefund(ExposureCouponRefundReqDTO req) {

        log.info("营销域发起曝光券退款 参数: {} ", JSONObject.toJSONString(req));

        // 10秒内相同参数redis锁
        String paramString = req.toString();
        String key = RedisConstants.SINCERITY_SELL_ORDER_REFUND_LOCK + new MD5Utils().getMd5(paramString);
        log.info("serviceOrder refund, keys: {} , {}", paramString, key);
        if (RedissonUtils.isExistsObject(key)) {
            log.info("serviceOrder refund in exist 10s, param: {} ", JSONObject.toJSONString(req));
            return true;
        }

        RedissonUtils.setCacheObject(key, "1");
        RedissonUtils.expire(key, 10L);

        // 订单行id
        String orderItemId = req.getOrderItemId();
        // 退款金额
        Long sumApportionAmount = req.getSumApportionAmount();
        // 扩展字段
        String extend = req.getUserCouponIds();
        // 发起退款来源
        Integer refundSource = req.getRefundSource();
        String systemUserId = "";
        if (Objects.equals(refundSource, 2)) {
            // 客服退款id
            systemUserId = req.getSystemUserId();
        }

        OrderItem orderItem = orderItemRepository.getOrderItem(orderItemId);

        try {
            // 发起退款
            SinceritySellOrderApplyRefundReqDTO sinceritySellOrderApplyRefundReqDTO =
                    new SinceritySellOrderApplyRefundReqDTO();
            sinceritySellOrderApplyRefundReqDTO.setRefundAmount(sumApportionAmount);
            sinceritySellOrderApplyRefundReqDTO.setOrderItemId(orderItemId);
            sinceritySellOrderApplyRefundReqDTO.setExtend(extend);
            sinceritySellOrderApplyRefundReqDTO.setUserId(systemUserId);
            sinceritySellOrderApplyRefund(sinceritySellOrderApplyRefundReqDTO);
        } catch (BizException e) {
            log.error("sinceritySellOrderRefund item id {} 退款失败:{}", orderItemId, e.getMessage());
            refundFailCallback(orderItem, extend, e.getMessage());
        } catch (Exception e) {
            log.error("sinceritySellOrderRefund item id {} 退款失败:{}", orderItemId, e.getMessage());
            refundFailCallback(orderItem, extend, "诚心卖退款失败");
        }
        return true;
    }

    private List<BaseTableColumnMsgContent> buildIndemnityChangeRefundCardContent(IndemnityChangeServiceRefundReqDTO req) {
        List<BaseTableColumnMsgContent> response = new ArrayList<>();

        // 退订的包赔项
        List<OrderItemIndemnityDTO> canceledIndemnity = req.getCanceledIndemnityList();
        if (CollectionUtils.isNotEmpty(canceledIndemnity)) {
            TableColumnContent cancelTitle = new TableColumnContent("退订", "", true, true);
            response.add(cancelTitle);
            canceledIndemnity.forEach(item -> {
                        String priceStr = new Money(item.getIndemnityRealAmount()).getYuan();
                        TableColumnContent indemnityCancel = new TableColumnContent("\t" + item.getIndemnityName(), PayConstant.RMB + priceStr);
                        response.add(indemnityCancel);
                    }
            );
        }

        // 追加的包赔项
        List<OrderItemIndemnityDTO> addedIndemnity = req.getAddedIndemnityList();
        if (CollectionUtils.isNotEmpty(addedIndemnity)) {
            TableColumnContent addTitle = new TableColumnContent("新购", "", true, true);
            response.add(addTitle);
            addedIndemnity.forEach(item -> {
                String priceStr = new Money(item.getIndemnityRealAmount()).getYuan();
                TableColumnContent indemnityAdd = new TableColumnContent(item.getIndemnityName(), PayConstant.RMB + priceStr);
                response.add(indemnityAdd);
            });
        }

        // 汇总项
        String totalPriceStr = new Money(req.getRefundAmount()).getYuan();
        response.add(new TableColumnContent("向买家退款", PayConstant.RMB + totalPriceStr, true, true));

        return response;
    }

    private void refundFailCallback(OrderItem orderItem, String extend, String refundResult) {
        // 通知退款失败
        ExposureCouponCompleteReqDTO exposureCouponCompleteReqDTO = new ExposureCouponCompleteReqDTO();
        exposureCouponCompleteReqDTO.setUserCouponIds(extend);
        exposureCouponCompleteReqDTO.setUserId(orderItem.getBuyerId());
        exposureCouponCompleteReqDTO.setStatus(2);
        exposureCouponCompleteReqDTO.setRefundResult(refundResult);
        sinceritySellServiceI.userExposureCouponRefundComplete(exposureCouponCompleteReqDTO).getData();
    }

    /**
     * 诚心卖退款（单订单itemId退款）
     */
    public void sinceritySellOrderApplyRefund(SinceritySellOrderApplyRefundReqDTO req) {
        log.info("诚心卖退款开始 参数: {} ", JSONObject.toJSONString(req));

        String orderItemId = req.getOrderItemId();
        Long refundAmount = req.getRefundAmount();
        String extend = req.getExtend();
        String userId = req.getUserId();

        // 校验orderItem，同一时刻只能有一笔单据进行
        OrderItem orderItem = orderItemRepository.getOrderItem(orderItemId);
        log.info("测试生成退款单item:{}", JSONObject.toJSONString(orderItem));
        // 同orderItem只同时一次在退款
        refundDomainService.checkSinceritySellOrderItem(orderItem);
        // 查询成功的退款单
        List<RefundVoucher> successRefundVoucher = refundVoucherRepository.getSuccessRefund(orderItemId);

        // 校验金额
        // 已经退款的金额
        long alreadyRefundAmount = successRefundVoucher.stream().mapToLong(RefundVoucher::getActualRefundAmount).sum();
        Integer refundWhole = getRefundWhole(alreadyRefundAmount, refundAmount, orderItem);

        // 生成退款单
        Integer refundActionType;
        String buyerId = orderItem.getBuyerId();

        if (StringUtils.isBlank(userId)) {
            // 去查询订单的买家
            userId = buyerId;
            refundActionType = RefundActionTypeEnum.USER.getValue();
        } else {
            refundActionType = RefundActionTypeEnum.SERVICE.getValue();
        }

        // 生成退款单
        // 先加分布式锁, 1锁 2判 3更新
        String lockKey = String.format(OrderItemLockConstant.PREFIX, orderItemId);
        String finalUserId = userId;
        RefundVoucher refundVoucher = RLockUtils
                .of(lockKey,
                        () -> refundDomainService.saveSinceritySellRefundApply(orderItemId, orderItem.getProductType(),
                                refundWhole, refundAmount, refundActionType, finalUserId, buyerId, extend))
                .withWaitTime(3).withTimeUnit(TimeUnit.SECONDS)
                .orElseThrow(() -> new BizException(ErrorCode.ORDER_HAVE_OTHER_REFUND_RECEIPT_FAIL.getErrCode(),
                        ErrorCode.ORDER_HAVE_OTHER_REFUND_RECEIPT_FAIL.getErrDesc()));
        log.info("测试生成退款单:{}", JSONObject.toJSONString(refundVoucher));

        // 记录订单操作日志
        refundDomainService.addRefundOrderOperate(refundVoucher.getOrderItemId(), refundVoucher.getRefundVoucherId(),
                OrderOperateTypeEnum.CANCEL_REFUND, OrderOptUserTypeEnum.BUYER, userId, "");

        // 去三方支付退款
        ssOrderRefundMessageService.sendRefundAuditMessage(
                new SinceritySellOrderRefundMessage().setRefundVoucherId(refundVoucher.getRefundVoucherId()));

        // 同步到es
        // refundDomainService.syncOrderItem2Es(orderItemId);
    }

    // 发起创建违约金
    public void sendViolateMQ(OrderItem orderItem, ServiceSubmitRefundReqDTO serviceSubmitRefundReqDTO,
                              UserBaseInfo userBaseInfo, String refundVoucherId) {
        RefundViolateDetailReqDTO refundViolateDetail = serviceSubmitRefundReqDTO.getRefundViolateDetail();

        String violateUserId = ViolateUserTypeEnum.BUYER.eq(refundViolateDetail.getUserType()) ? orderItem.getBuyerId() : orderItem.getSellerId();
        ViolateApplyMessage violateApplyMessage = new ViolateApplyMessage()
                .setOrderItemId(serviceSubmitRefundReqDTO.getOrderItemId())
                .setRefundVoucherId(refundVoucherId)
                .setViolateAmount(refundViolateDetail.getViolateAmount())
                .setPromiseAmount(refundViolateDetail.getPromiseAmount())
                .setViolateUserType(refundViolateDetail.getUserType())
                .setViolateUserId(violateUserId)
                .setCreateUserId(userBaseInfo.getUserId())
                .setCreateUserName(userBaseInfo.getUserName());
        violateMessageService.sendCreateViolate(violateApplyMessage);
    }

    private void checkViolateParam(Integer isWholeRefund, RefundViolateDetailReqDTO violateDetail, long refundTotalAmount, Integer violateUserType, OrderItem orderItem) {
        Assert.isTrue(RefundWholeEnum.WHOLE.getValue().equals(isWholeRefund)
                , VIOLATE_NOT_WHOLE_ERROR.getErrCode()
                , VIOLATE_NOT_WHOLE_ERROR.getErrDesc());

        Long violateAmount = Optional.ofNullable(violateDetail)
                .map(RefundViolateDetailReqDTO::getViolateAmount)
                .orElse(0L);
        Long promiseAmount = Optional.ofNullable(violateDetail)
                .map(RefundViolateDetailReqDTO::getPromiseAmount)
                .orElse(0L);

        Assert.isTrue(violateAmount > 0
                , VIOLATE_PARAM_LACK_ERROR.getErrCode(), VIOLATE_PARAM_LACK_ERROR.getErrDesc());

        Assert.isTrue(violateAmount >= promiseAmount
                , VIOLATE_PROMISE_AMOUNT_ERROR.getErrCode(), VIOLATE_PROMISE_AMOUNT_ERROR.getErrDesc());

        if (ViolateUserTypeEnum.BUYER.eq(violateUserType)) {
            Assert.isTrue(refundTotalAmount > violateAmount
                    , VIOLATE_AMOUNT_EXCESS_ERROR.getErrCode(), VIOLATE_AMOUNT_EXCESS_ERROR.getErrDesc());
        }

        if (ViolateUserTypeEnum.SELLER.eq(violateUserType)) {
            Assert.isTrue(orderItem.getProductSalePrice() >= violateAmount
                    , VIOLATE_AMOUNT_EXCESS_ERROR.getErrCode(), VIOLATE_AMOUNT_EXCESS_ERROR.getErrDesc());
        }

        // 订单是否已有有效违约单
        Assert.isFalse(violateOrderDomainService.hasViolateByOrderIdAndRefundId(orderItem.getOrderItemId(), null)
                , VIOLATE_EXIST_ERROR.getErrCode(), VIOLATE_EXIST_ERROR.getErrDesc());
    }

    /**
     * 获取违约金初始化信息
     */
    public RefundInitInfoRespDTO getViolateInitInfo(ImRoomIdReqDTO dto) {
        RefundInitInfoRespDTO refundInitInfoRespDTO = new RefundInitInfoRespDTO();

        // 从es获取房间内订单聚合信息
        OrderItemAggregation orderItemAggregation = esOrderItemSearchService.findOneByRoomId(dto.getRoomId());
        Assert.isTrue(StringUtils.isNotEmpty(orderItemAggregation.getOrderItemId()),
                ORDER_ITEM_NOT_FOUND_ERROR.getErrCode(), ORDER_ITEM_NOT_FOUND_ERROR.getErrDesc());

        String orderItermId = orderItemAggregation.getOrderItemId();

        OrderItem orderItem = orderItemRepository.getOrderItem(orderItermId);

        // 违约金额
        long defaultViolateAmount = (long) (orderItem.getProductSalePrice() * 0.05);

        // 若订单已退款,则获取上一次退款信息
        if (OrderItemStatusEnum.REFUND_CANCEL.eq(orderItem.getOrderItemStatus())) {
            RefundVoucher refundVoucher = refundVoucherRepository.lastWholeByOrderItemId(orderItem.getOrderItemId());
            Assert.isTrue(ObjectUtil.isNotEmpty(refundVoucher), REFUND_NOT_FOUND_ERROR.getErrCode(), REFUND_NOT_FOUND_ERROR.getErrDesc());
            refundInitInfoRespDTO.setRefundAmount(refundVoucher.getRefundAmount());

        } else {
            // 判断退款方式
            List<RefundVoucher> successRefundVoucher =
                    refundVoucherRepository.getSuccessRefund(orderItemAggregation.getOrderItemId());
            List<OrderItemReceiptVoucherPO> successReceiptVoucherDetail =
                    refundDomainService.getSuccessReceiptVoucherDetail(orderItermId, successRefundVoucher);

            // 计算全额退款金额，并校验退款金额是否充足
            OrderItemAmountInfoBO wholeRefundAmountInfo = refundDomainService.getWholeRefundAmountInfo(orderItermId,
                    successRefundVoucher, successReceiptVoucherDetail);
            refundInitInfoRespDTO.setRefundAmount(wholeRefundAmountInfo.getTotalAmount());
        }

        // 返回退款页面的初始化数据
        refundInitInfoRespDTO.setOrderItemId(orderItemAggregation.getOrderItemId());
        refundInitInfoRespDTO.setMaxProductAmount(orderItem.getProductSalePrice());
        refundInitInfoRespDTO.setDefaultViolateAmount(defaultViolateAmount);

        return refundInitInfoRespDTO;
    }
}
