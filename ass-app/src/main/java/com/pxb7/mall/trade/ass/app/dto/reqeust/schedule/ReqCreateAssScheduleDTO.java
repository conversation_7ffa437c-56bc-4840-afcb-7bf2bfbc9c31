package com.pxb7.mall.trade.ass.app.dto.reqeust.schedule;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class ReqCreateAssScheduleDTO {
    /**
     * 订单ID
     */
    @NotBlank(message = "订单ID不能为空")
    private String orderNo;
    /**
     * 售后类型1找回2纠纷
     */
    @NotNull(message = "售后类型不能为空")
    private Integer assType;

}
