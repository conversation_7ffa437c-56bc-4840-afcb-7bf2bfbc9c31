package com.pxb7.mall.trade.ass.app.dto.response;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * Copyright (C), 2002-2025, 螃蟹游戏服务网
 *
 * @fileName: AfcQuestionConfRespDTO.java
 * @description: 售后问题配置响应对象
 * @author: g<PERSON><PERSON><PERSON><PERSON>
 * @email: <EMAIL>
 * @date: 2025/4/11 14:09
 * @history: //修改记录
 * <author> <time> <version> <desc>
 * 修改人姓名 修改时间 版本号 描述
 */
@Data
@Accessors(chain = true)
public class AfcQuestionConfRespDTO implements Serializable {
    /**
     * 问题id
     */
    private String questionId;
    /**
     * 问题名称
     */
    private String questionName;
    /**
     * 问题类型 1:找回 2:纠纷
     */
    private Integer questionType;
    /**
     * 排序值
     */
    private Integer sort;
}
