package com.pxb7.mall.trade.ass.app.mapping;

import com.pxb7.mall.trade.ass.app.dto.reqeust.AssRetrieveAssistanceConfigReqDTO;
import com.pxb7.mall.trade.ass.app.dto.response.AssRetrieveAssistanceConfigRespDTO;
import com.pxb7.mall.trade.ass.domain.model.AssRetrieveAssistanceConfigReqBO;
import com.pxb7.mall.trade.ass.domain.model.AssRetrieveAssistanceConfigRespBO;

import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;


@Mapper
public interface AssRetrieveAssistanceConfigAppMapping {

    AssRetrieveAssistanceConfigAppMapping INSTANCE = Mappers.getMapper(AssRetrieveAssistanceConfigAppMapping.class);


    AssRetrieveAssistanceConfigReqBO.AddBO assRetrieveAssistanceConfigDTO2AddBO(AssRetrieveAssistanceConfigReqDTO.AddDTO source);

    AssRetrieveAssistanceConfigReqBO.UpdateBO assRetrieveAssistanceConfigDTO2UpdateBO(AssRetrieveAssistanceConfigReqDTO.UpdateDTO source);

    AssRetrieveAssistanceConfigReqBO.DelBO assRetrieveAssistanceConfigDTO2DelBO(AssRetrieveAssistanceConfigReqDTO.DelDTO source);

    AssRetrieveAssistanceConfigReqBO.SearchBO assRetrieveAssistanceConfigDTO2SearchBO(AssRetrieveAssistanceConfigReqDTO.SearchDTO source);

    AssRetrieveAssistanceConfigReqBO.PageBO assRetrieveAssistanceConfigDTO2PageBO(AssRetrieveAssistanceConfigReqDTO.PageDTO source);

    AssRetrieveAssistanceConfigRespDTO.DetailDTO assRetrieveAssistanceConfigBO2DetailDTO(AssRetrieveAssistanceConfigRespBO.DetailBO source);

    List<AssRetrieveAssistanceConfigRespDTO.DetailDTO> assRetrieveAssistanceConfigBO2ListDTO(List<AssRetrieveAssistanceConfigRespBO.DetailBO> source);

    Page<AssRetrieveAssistanceConfigRespDTO.DetailDTO> assRetrieveAssistanceConfigBO2PageDTO(Page<AssRetrieveAssistanceConfigRespBO.DetailBO> source);

}


