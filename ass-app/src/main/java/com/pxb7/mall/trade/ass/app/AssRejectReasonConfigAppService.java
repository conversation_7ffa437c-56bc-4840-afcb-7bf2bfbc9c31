package com.pxb7.mall.trade.ass.app;


import java.util.List;

import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import com.pxb7.mall.trade.ass.app.dto.reqeust.AssRejectReasonConfigReqDTO;
import com.pxb7.mall.trade.ass.app.dto.response.AssRejectReasonConfigRespDTO;
import com.pxb7.mall.trade.ass.app.mapping.AssRejectReasonConfigAppMapping;
import com.pxb7.mall.trade.ass.domain.model.AssRejectReasonConfigReqBO;
import com.pxb7.mall.trade.ass.domain.model.AssRejectReasonConfigRespBO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pxb7.mall.trade.ass.domain.AssRejectReasonConfigDomainService;


/**
 * 驳回原因配置表app服务
 *
 * <AUTHOR>
 * @since 2025-07-30 13:59:49
 */
@Service
public class AssRejectReasonConfigAppService {

    @Resource
    private AssRejectReasonConfigDomainService assRejectReasonConfigDomainService;

    public boolean insert(AssRejectReasonConfigReqDTO.AddDTO param) {
        AssRejectReasonConfigReqBO.AddBO addBO = AssRejectReasonConfigAppMapping.INSTANCE.assRejectReasonConfigDTO2AddBO(param);
        return assRejectReasonConfigDomainService.insert(addBO);
    }

    public boolean update(AssRejectReasonConfigReqDTO.UpdateDTO param) {
        AssRejectReasonConfigReqBO.UpdateBO updateBO = AssRejectReasonConfigAppMapping.INSTANCE.assRejectReasonConfigDTO2UpdateBO(param);
        return assRejectReasonConfigDomainService.update(updateBO);
    }

    public boolean deleteById(AssRejectReasonConfigReqDTO.DelDTO param) {
        AssRejectReasonConfigReqBO.DelBO delBO = AssRejectReasonConfigAppMapping.INSTANCE.assRejectReasonConfigDTO2DelBO(param);
        return assRejectReasonConfigDomainService.deleteById(delBO);
    }

    public AssRejectReasonConfigRespDTO.DetailDTO findById(Long id) {
        AssRejectReasonConfigRespBO.DetailBO detailBO = assRejectReasonConfigDomainService.findById(id);
        return AssRejectReasonConfigAppMapping.INSTANCE.assRejectReasonConfigBO2DetailDTO(detailBO);
    }

    public List<AssRejectReasonConfigRespDTO.DetailDTO> list(AssRejectReasonConfigReqDTO.SearchDTO param) {
        AssRejectReasonConfigReqBO.SearchBO searchBO = AssRejectReasonConfigAppMapping.INSTANCE.assRejectReasonConfigDTO2SearchBO(param);
        List<AssRejectReasonConfigRespBO.DetailBO> list = assRejectReasonConfigDomainService.list(searchBO);
        return AssRejectReasonConfigAppMapping.INSTANCE.assRejectReasonConfigBO2ListDTO(list);
    }



    public List<AssRejectReasonConfigRespDTO.DetailDTO> getListByType(Integer assType) {
        List<AssRejectReasonConfigRespBO.DetailBO> list = assRejectReasonConfigDomainService.getListByType(assType);
        return AssRejectReasonConfigAppMapping.INSTANCE.assRejectReasonConfigBO2ListDTO(list);
    }


    public Page<AssRejectReasonConfigRespDTO.DetailDTO> page(AssRejectReasonConfigReqDTO.PageDTO param) {
        AssRejectReasonConfigReqBO.PageBO pageBO = AssRejectReasonConfigAppMapping.INSTANCE.assRejectReasonConfigDTO2PageBO(param);
        Page<AssRejectReasonConfigRespBO.DetailBO> page = assRejectReasonConfigDomainService.page(pageBO);
        return AssRejectReasonConfigAppMapping.INSTANCE.assRejectReasonConfigBO2PageDTO(page);
    }

}

