package com.pxb7.mall.trade.ass.app.mapping;

import com.pxb7.mall.trade.ass.app.dto.reqeust.DealDisputeResultReqDTO;
import com.pxb7.mall.trade.ass.client.dto.response.afc.DisputeWORespDTO;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.AssDisputeWo;
import com.pxb7.mall.trade.ass.infra.repository.db.model.request.DealDisputeResultReqPO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * 售后纠纷工单
 *
 * <AUTHOR>
 * @since: 2024-10-01 20:41
 **/
@Mapper
public interface AssDisputeWoMapping {
    AssDisputeWoMapping INSTANCE = Mappers.getMapper(AssDisputeWoMapping.class);

    DealDisputeResultReqPO toReqPO(DealDisputeResultReqDTO reqDTO);

    @Mappings({@Mapping(source = "assDisputeId", target = "workOrderId")})
    DisputeWORespDTO toRespDTO(AssDisputeWo assDisputeWo);
}
