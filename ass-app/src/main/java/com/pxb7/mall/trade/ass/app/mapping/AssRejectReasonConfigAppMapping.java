package com.pxb7.mall.trade.ass.app.mapping;

import com.pxb7.mall.trade.ass.app.dto.reqeust.AssRejectReasonConfigReqDTO;
import com.pxb7.mall.trade.ass.app.dto.response.AssRejectReasonConfigRespDTO;
import com.pxb7.mall.trade.ass.domain.model.AssRejectReasonConfigReqBO;
import com.pxb7.mall.trade.ass.domain.model.AssRejectReasonConfigRespBO;

import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;


@Mapper
public interface AssRejectReasonConfigAppMapping {

    AssRejectReasonConfigAppMapping INSTANCE = Mappers.getMapper(AssRejectReasonConfigAppMapping.class);


    AssRejectReasonConfigReqBO.AddBO assRejectReasonConfigDTO2AddBO(AssRejectReasonConfigReqDTO.AddDTO source);

    AssRejectReasonConfigReqBO.UpdateBO assRejectReasonConfigDTO2UpdateBO(AssRejectReasonConfigReqDTO.UpdateDTO source);

    AssRejectReasonConfigReqBO.DelBO assRejectReasonConfigDTO2DelBO(AssRejectReasonConfigReqDTO.DelDTO source);

    AssRejectReasonConfigReqBO.SearchBO assRejectReasonConfigDTO2SearchBO(AssRejectReasonConfigReqDTO.SearchDTO source);

    AssRejectReasonConfigReqBO.PageBO assRejectReasonConfigDTO2PageBO(AssRejectReasonConfigReqDTO.PageDTO source);

    AssRejectReasonConfigRespDTO.DetailDTO assRejectReasonConfigBO2DetailDTO(AssRejectReasonConfigRespBO.DetailBO source);

    List<AssRejectReasonConfigRespDTO.DetailDTO> assRejectReasonConfigBO2ListDTO(List<AssRejectReasonConfigRespBO.DetailBO> source);

    Page<AssRejectReasonConfigRespDTO.DetailDTO> assRejectReasonConfigBO2PageDTO(Page<AssRejectReasonConfigRespBO.DetailBO> source);

}


