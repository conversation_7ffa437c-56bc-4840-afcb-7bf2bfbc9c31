package com.pxb7.mall.trade.ass.app;


import java.util.List;

import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import com.pxb7.mall.trade.ass.app.dto.reqeust.AssRetrieveGameConfigDetailReqDTO;
import com.pxb7.mall.trade.ass.app.dto.response.AssRetrieveGameConfigDetailRespDTO;
import com.pxb7.mall.trade.ass.app.mapping.AssRetrieveGameConfigDetailAppMapping;
import com.pxb7.mall.trade.ass.domain.model.AssRetrieveGameConfigDetailReqBO;
import com.pxb7.mall.trade.ass.domain.model.AssRetrieveGameConfigDetailRespBO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pxb7.mall.trade.ass.domain.AssRetrieveGameConfigDetailDomainService;


/**
 * 售后找回游戏配置明细表app服务
 *
 * <AUTHOR>
 * @since 2025-07-30 13:59:50
 */
@Service
public class AssRetrieveGameConfigDetailAppService {

    @Resource
    private AssRetrieveGameConfigDetailDomainService assRetrieveGameConfigDetailDomainService;

    public boolean insert(AssRetrieveGameConfigDetailReqDTO.AddDTO param) {
        AssRetrieveGameConfigDetailReqBO.AddBO addBO = AssRetrieveGameConfigDetailAppMapping.INSTANCE.assRetrieveGameConfigDetailDTO2AddBO(param);
        return assRetrieveGameConfigDetailDomainService.insert(addBO);
    }

    public boolean update(AssRetrieveGameConfigDetailReqDTO.UpdateDTO param) {
        AssRetrieveGameConfigDetailReqBO.UpdateBO updateBO = AssRetrieveGameConfigDetailAppMapping.INSTANCE.assRetrieveGameConfigDetailDTO2UpdateBO(param);
        return assRetrieveGameConfigDetailDomainService.update(updateBO);
    }

    public boolean deleteById(AssRetrieveGameConfigDetailReqDTO.DelDTO param) {
        AssRetrieveGameConfigDetailReqBO.DelBO delBO = AssRetrieveGameConfigDetailAppMapping.INSTANCE.assRetrieveGameConfigDetailDTO2DelBO(param);
        return assRetrieveGameConfigDetailDomainService.deleteById(delBO);
    }

    public AssRetrieveGameConfigDetailRespDTO.DetailDTO findById(Long id) {
        AssRetrieveGameConfigDetailRespBO.DetailBO detailBO = assRetrieveGameConfigDetailDomainService.findById(id);
        return AssRetrieveGameConfigDetailAppMapping.INSTANCE.assRetrieveGameConfigDetailBO2DetailDTO(detailBO);
    }

    public List<AssRetrieveGameConfigDetailRespDTO.DetailDTO> list(AssRetrieveGameConfigDetailReqDTO.SearchDTO param) {
        AssRetrieveGameConfigDetailReqBO.SearchBO searchBO = AssRetrieveGameConfigDetailAppMapping.INSTANCE.assRetrieveGameConfigDetailDTO2SearchBO(param);
        List<AssRetrieveGameConfigDetailRespBO.DetailBO> list = assRetrieveGameConfigDetailDomainService.list(searchBO);
        return AssRetrieveGameConfigDetailAppMapping.INSTANCE.assRetrieveGameConfigDetailBO2ListDTO(list);
    }

    public Page<AssRetrieveGameConfigDetailRespDTO.DetailDTO> page(AssRetrieveGameConfigDetailReqDTO.PageDTO param) {
        AssRetrieveGameConfigDetailReqBO.PageBO pageBO = AssRetrieveGameConfigDetailAppMapping.INSTANCE.assRetrieveGameConfigDetailDTO2PageBO(param);
        Page<AssRetrieveGameConfigDetailRespBO.DetailBO> page = assRetrieveGameConfigDetailDomainService.page(pageBO);
        return AssRetrieveGameConfigDetailAppMapping.INSTANCE.assRetrieveGameConfigDetailBO2PageDTO(page);
    }

}

