package com.pxb7.mall.trade.ass.app.dto.response.assSchedule;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 售后流程节点
 *
 * <AUTHOR>
 * @since: 2024-09-23 11:21
 **/
@Data
@Accessors(chain = true)
public class AssScheduleRespMobileDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = 6107445735991653251L;
    /**
     * 是否完结
     */
    private Boolean finish;
    /**
     * 房间ID
     */
    private String roomId;
    /**
     * 节点数据
     */
    private List<RespAssScheduleNodeDTO> nodes;
}
