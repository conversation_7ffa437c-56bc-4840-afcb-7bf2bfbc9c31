package com.pxb7.mall.trade.ass.app.dto.response;

import java.util.List;

import com.pxb7.mall.trade.ass.client.enums.AssWoType;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/8/13
 */
@Data
public class AssScheduleRespDTO {

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 工单id
     */
    private String workOrderId;

    /**
     * 售后类型1找回2纠纷
     */
    private AssWoType assType;

    /**
     * 接待客服id
     */
    private String recvCustomerId;

    /**
     * 审核客服id
     */
    private String auditCustomerId;

    /**
     * 房间id
     */
    private String roomId;

    /**
     * 是否完结
     */
    private Boolean finish;

    /**
     * 流转详情
     */
    private List<AssScheduleLogRespDTO> logs;

    /**
     * 下级节点
     */
    private List<AssScheduleNodeDTO> nextNode;

}
