package com.pxb7.mall.trade.ass.app.dto.reqeust.complaint.resp;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * Copyright (C), 2002-2024, 螃蟹游戏服务网
 *
 * @fileName: ComplaintEmployeeConfigRespDTO.java
 * @description:
 * @author: guo<PERSON><PERSON><PERSON>
 * @email: <EMAIL>
 * @date: 2024/9/20 23:05
 * @history: //修改记录
 * <author> <time> <version> <desc>
 * 修改人姓名 修改时间 版本号 描述
 */
@Data
@Builder
public class ComplaintEmployeeConfigRespDTO implements Serializable {
    /**
     * 员工id
     */
    private String userId;
    /**
     * 员工姓名
     */
    private String userName;

    /**
     * 手机号
     */
    private String phone;

}
