package com.pxb7.mall.trade.ass.app;


import java.util.List;

import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import com.pxb7.mall.trade.ass.app.dto.reqeust.AssRetrieveGameConfigReqDTO;
import com.pxb7.mall.trade.ass.app.dto.response.AssRetrieveGameConfigRespDTO;
import com.pxb7.mall.trade.ass.app.mapping.AssRetrieveGameConfigAppMapping;
import com.pxb7.mall.trade.ass.domain.model.AssRetrieveGameConfigReqBO;
import com.pxb7.mall.trade.ass.domain.model.AssRetrieveGameConfigRespBO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pxb7.mall.trade.ass.domain.AssRetrieveGameConfigDomainService;


/**
 * 售后找回游戏配置app服务
 *
 * <AUTHOR>
 * @since 2025-08-01 14:12:52
 */
@Service
public class AssRetrieveGameConfigAppService {

    @Resource
    private AssRetrieveGameConfigDomainService assRetrieveGameConfigDomainService;

    public boolean insert(AssRetrieveGameConfigReqDTO.AddDTO param) {
        AssRetrieveGameConfigReqBO.AddBO addBO = AssRetrieveGameConfigAppMapping.INSTANCE.assRetrieveGameConfigDTO2AddBO(param);
        return assRetrieveGameConfigDomainService.insert(addBO);
    }

    public boolean update(AssRetrieveGameConfigReqDTO.UpdateDTO param) {
        AssRetrieveGameConfigReqBO.UpdateBO updateBO = AssRetrieveGameConfigAppMapping.INSTANCE.assRetrieveGameConfigDTO2UpdateBO(param);
        return assRetrieveGameConfigDomainService.update(updateBO);
    }

    public boolean deleteById(AssRetrieveGameConfigReqDTO.DelDTO param) {
        AssRetrieveGameConfigReqBO.DelBO delBO = AssRetrieveGameConfigAppMapping.INSTANCE.assRetrieveGameConfigDTO2DelBO(param);
        return assRetrieveGameConfigDomainService.deleteById(delBO);
    }

    public AssRetrieveGameConfigRespDTO.DetailDTO findById(Long id) {
        AssRetrieveGameConfigRespBO.DetailBO detailBO = assRetrieveGameConfigDomainService.findById(id);
        return AssRetrieveGameConfigAppMapping.INSTANCE.assRetrieveGameConfigBO2DetailDTO(detailBO);
    }

    public List<AssRetrieveGameConfigRespDTO.DetailDTO> list(AssRetrieveGameConfigReqDTO.SearchDTO param) {
        AssRetrieveGameConfigReqBO.SearchBO searchBO = AssRetrieveGameConfigAppMapping.INSTANCE.assRetrieveGameConfigDTO2SearchBO(param);
        List<AssRetrieveGameConfigRespBO.DetailBO> list = assRetrieveGameConfigDomainService.list(searchBO);
        return AssRetrieveGameConfigAppMapping.INSTANCE.assRetrieveGameConfigBO2ListDTO(list);
    }

    public Page<AssRetrieveGameConfigRespDTO.DetailDTO> page(AssRetrieveGameConfigReqDTO.PageDTO param) {
        AssRetrieveGameConfigReqBO.PageBO pageBO = AssRetrieveGameConfigAppMapping.INSTANCE.assRetrieveGameConfigDTO2PageBO(param);
        Page<AssRetrieveGameConfigRespBO.DetailBO> page = assRetrieveGameConfigDomainService.page(pageBO);
        return AssRetrieveGameConfigAppMapping.INSTANCE.assRetrieveGameConfigBO2PageDTO(page);
    }

}

