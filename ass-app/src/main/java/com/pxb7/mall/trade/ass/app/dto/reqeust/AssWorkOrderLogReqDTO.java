package com.pxb7.mall.trade.ass.app.dto.reqeust;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Min;
import lombok.ToString;


/**
 * 售后工单日志明细(AssWorkOrderLog)实体类
 *
 * <AUTHOR>
 * @since 2025-07-31 10:22:26
 */
public class AssWorkOrderLogReqDTO {

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class AddDTO {

        /**
         * 售后工单号关联ass_work_order.work_order_id
         */
        @NotBlank(message = "workOrderId不能为空")
        private String workOrderId;

        /**
         * 日志id
         */
        @NotBlank(message = "workOrderLogId不能为空")
        private String workOrderLogId;

        /**
         * 订单ID
         */
        @NotBlank(message = "orderItemId不能为空")
        private String orderItemId;

        /**
         * 标题
         */
        @NotBlank(message = "title不能为空")
        private String title;

        /**
         * 售后类型1找回2纠纷
         */
        @NotNull(message = "assType不能为空")
        private Integer assType;

        /**
         * 0:全展示 1:用户端展示 2:后台展示
         */
        @NotNull(message = "showType不能为空")
        private Integer showType;

        /**
         * 用户端展示操作内容
         */
        @NotBlank(message = "content不能为空")
        private String content;

        /**
         * admin展示操作内容
         */
        @NotBlank(message = "adminContent不能为空")
        private String adminContent;

        /**
         * 日志添加方式1系统自动产生的日志 2手动添加的日志
         */
        @NotNull(message = "addWay不能为空")
        private Integer addWay;

        /**
         * 节点id
         */
        @NotBlank(message = "nodeId不能为空")
        private String nodeId;

        /**
         * 当前节点状态描述
         */
        @NotBlank(message = "nodeDesc不能为空")
        private String nodeDesc;


        /**
         * 通知类型 0:不通知 1:追回账号,待卖家换绑2:追回号款,待卖家提供收款账号3:售后到期,待买家接受赔付
         */
        private Integer noticeType;

        /**
         * 进群提醒文案
         */
        private String joinGroupMsg;

    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class UpdateDTO {

        /**
         * 自增id
         */
        @NotNull(message = "id不能为空")
        private Long id;


        /**
         * 售后工单号关联ass_work_order.work_order_id
         */
        @NotBlank(message = "workOrderId不能为空")
        private String workOrderId;


        /**
         * 日志id
         */
        @NotBlank(message = "workOrderLogId不能为空")
        private String workOrderLogId;


        /**
         * 订单ID
         */
        @NotBlank(message = "orderItemId不能为空")
        private String orderItemId;


        /**
         * 标题
         */
        @NotBlank(message = "title不能为空")
        private String title;


        /**
         * 售后类型1找回2纠纷
         */
        @NotNull(message = "assType不能为空")
        private Integer assType;


        /**
         * 0:全展示 1:用户端展示 2:后台展示
         */
        @NotNull(message = "showType不能为空")
        private Integer showType;


        /**
         * 用户端展示操作内容
         */
        @NotBlank(message = "content不能为空")
        private String content;


        /**
         * admin展示操作内容
         */
        @NotBlank(message = "adminContent不能为空")
        private String adminContent;


        /**
         * 日志添加方式1系统自动产生的日志 2手动添加的日志
         */
        @NotNull(message = "addWay不能为空")
        private Integer addWay;


        /**
         * 节点id
         */
        @NotBlank(message = "nodeId不能为空")
        private String nodeId;


        /**
         * 当前节点状态描述
         */
        @NotBlank(message = "nodeDesc不能为空")
        private String nodeDesc;


    }


    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class DelDTO {
        @NotNull(message = "id不能为空")
        private Long id;
    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class SearchDTO {
        /**
         * 售后工单号关联ass_work_order.work_order_id
         */
        @NotBlank(message = "workOrderId不能为空")
        private String workOrderId;

        /**
         * 日志id
         */
        @NotBlank(message = "workOrderLogId不能为空")
        private String workOrderLogId;

        /**
         * 订单ID
         */
        @NotBlank(message = "orderItemId不能为空")
        private String orderItemId;

        /**
         * 标题
         */
        @NotBlank(message = "title不能为空")
        private String title;

        /**
         * 售后类型1找回2纠纷
         */
        @NotNull(message = "assType不能为空")
        private Integer assType;

        /**
         * 0:全展示 1:用户端展示 2:后台展示
         */
        @NotNull(message = "showType不能为空")
        private Integer showType;

        /**
         * 用户端展示操作内容
         */
        @NotBlank(message = "content不能为空")
        private String content;

        /**
         * admin展示操作内容
         */
        @NotBlank(message = "adminContent不能为空")
        private String adminContent;

        /**
         * 日志添加方式1系统自动产生的日志 2手动添加的日志
         */
        @NotNull(message = "addWay不能为空")
        private Integer addWay;

        /**
         * 节点id
         */
        @NotBlank(message = "nodeId不能为空")
        private String nodeId;

        /**
         * 当前节点状态描述
         */
        @NotBlank(message = "nodeDesc不能为空")
        private String nodeDesc;

    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class PageDTO {

        /**
         * 售后工单号关联ass_work_order.work_order_id
         */
        @NotBlank(message = "workOrderId不能为空")
        private String workOrderId;

        /**
         * 日志id
         */
        @NotBlank(message = "workOrderLogId不能为空")
        private String workOrderLogId;

        /**
         * 订单ID
         */
        @NotBlank(message = "orderItemId不能为空")
        private String orderItemId;

        /**
         * 标题
         */
        @NotBlank(message = "title不能为空")
        private String title;

        /**
         * 售后类型1找回2纠纷
         */
        @NotNull(message = "assType不能为空")
        private Integer assType;

        /**
         * 0:全展示 1:用户端展示 2:后台展示
         */
        @NotNull(message = "showType不能为空")
        private Integer showType;

        /**
         * 用户端展示操作内容
         */
        @NotBlank(message = "content不能为空")
        private String content;

        /**
         * admin展示操作内容
         */
        @NotBlank(message = "adminContent不能为空")
        private String adminContent;

        /**
         * 日志添加方式1系统自动产生的日志 2手动添加的日志
         */
        @NotNull(message = "addWay不能为空")
        private Integer addWay;

        /**
         * 节点id
         */
        @NotBlank(message = "nodeId不能为空")
        private String nodeId;

        /**
         * 当前节点状态描述
         */
        @NotBlank(message = "nodeDesc不能为空")
        private String nodeDesc;


        /**
         * 页码，从1开始
         */
        @NotNull(message = "pageIndex不能为空")
        @Min(value = 1, message = "pageIndex不能小于1")
        private Integer pageIndex;
        /**
         * 每页数量
         */
        @NotNull(message = "pageSize不能为空")
        private Integer pageSize;

    }

}

