package com.pxb7.mall.trade.ass.app;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.pxb7.mall.common.client.response.censor.CensorRespDTO;
import com.pxb7.mall.components.redis.redisson.RedissonUtils;
import com.pxb7.mall.trade.ass.app.dto.reqeust.ApplyAfcReqDTO;
import com.pxb7.mall.trade.ass.app.mapping.AfcApplyRecordMapping;
import com.pxb7.mall.trade.ass.domain.AfcRecordDomainService;
import com.pxb7.mall.trade.ass.domain.model.assSchedule.ReqCreateAssScheduleBO;
import com.pxb7.mall.trade.ass.domain.model.reqeust.ApplyAfcRecordReqBO;
import com.pxb7.mall.trade.ass.infra.constant.RedisConstant;
import com.pxb7.mall.trade.ass.infra.enums.ErrorCode;
import com.pxb7.mall.trade.ass.infra.exception.BusinessException;
import com.pxb7.mall.trade.ass.infra.remote.dubbo.CommonSupportRpcGateway;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.Duration;

/**
 * Copyright (C), 2002-2025, 螃蟹游戏服务网
 *
 * @fileName: AfcRecordAppService.java
 * @description: 售后记录申请服务
 * @author: guominqiang
 * @email: <EMAIL>
 * @date: 2025/4/11 14:37
 * @history: //修改记录
 * <author> <time> <version> <desc>
 * 修改人姓名 修改时间 版本号 描述
 */
@Service
@Slf4j
public class AfcRecordAppService {

    @Resource
    private AfcRecordDomainService afcRecordDomainService;

    @Resource
    private CommonSupportRpcGateway commonSupportRpcGateway;

    /**
     * 用户申请售后
     *
     * @param applyAfcReqDTO 申请售后请求
     * @param userId         用户id
     * @return {@link String 房间id}
     */
    public String applyAfc(ApplyAfcReqDTO applyAfcReqDTO, String userId) {
        log.info("用户申请售后: userId:{} apply:{}", userId, JSON.toJSONString(applyAfcReqDTO));
        //todo 新增订单越权校验，后续需要校验当前登录态用户是否拥有此订单权限
        String applyAfcKey = StrUtil.format(RedisConstant.APPLY_AFC_KEY, userId, applyAfcReqDTO.getOrderItemId());
        boolean b = RedissonUtils.setObjectIfAbsent(applyAfcKey, "1", Duration.ofSeconds(5));
        if (!b) {
            throw new BusinessException(ErrorCode.REPEAT_SUBMIT);
        }
        if (StrUtil.isNotBlank(applyAfcReqDTO.getRemark())) {
            CensorRespDTO censorRespDTO = commonSupportRpcGateway.censorSingleText(applyAfcReqDTO.getRemark());
            if (censorRespDTO != null && !StrUtil.equalsIgnoreCase(censorRespDTO.getResult(), "pass")) {
                throw new BusinessException(ErrorCode.TEXT_CENSOR_NO_PASS);
            }
        }
        ApplyAfcRecordReqBO applyAfcRecordReqBO = AfcApplyRecordMapping.INSTANCE.applyAfcReqDTO2ApplyAfcRecordReqBO(applyAfcReqDTO);

        ReqCreateAssScheduleBO applyAfcScheduleBO = AfcApplyRecordMapping.INSTANCE.applyAfcRecordReqDTO2AfcScheduleBO(applyAfcReqDTO);
        return afcRecordDomainService.applyAfc(applyAfcRecordReqBO, applyAfcScheduleBO, userId);
    }
}
