package com.pxb7.mall.trade.ass.app;

import java.util.List;

import org.springframework.stereotype.Service;

import com.pxb7.mall.trade.ass.app.dto.reqeust.ListAssQuestionClassifyRespDTO;
import com.pxb7.mall.trade.ass.app.mapping.AssQuestionClassifyMapping;
import com.pxb7.mall.trade.ass.infra.repository.db.AssQuestionClassifyRepository;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.AssQuestionClassify;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

/**
 * 售后问题归类配置
 *
 * <AUTHOR>
 **/
@Slf4j
@Service
public class AssQuestionClassifyAppService {
    @Resource
    private AssQuestionClassifyRepository assQuestionClassifyRepository;

    public List<ListAssQuestionClassifyRespDTO> list(Integer assType) {
        List<AssQuestionClassify> list = assQuestionClassifyRepository.selectList(assType);
        return AssQuestionClassifyMapping.INSTANCE.toRespDTO(list);
    }
}
