package com.pxb7.mall.trade.ass.app.dto.reqeust;

import java.io.Serial;
import java.io.Serializable;

import lombok.Data;

/**
 * 售后问题归类配置列表
 *
 * <AUTHOR>
 * @since: 2024-08-14 13:48
 **/
@Data
public class ListAssQuestionClassifyRespDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = 6197522834217423650L;
    /**
     * 业务主键ID
     */
    private String classifyId;

    /**
     * 一级目录
     */
    private String firstLevelDirectory;

    /**
     * 二级目录
     */
    private String secondLevelDirectory;

    /**
     * 排序
     */
    private Integer sortIndex;
}
