package com.pxb7.mall.trade.ass.app.dto.response;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.ToString;


/**
 * 找回协助类型配置表(AssRetrieveAssistanceConfig)实体类
 *
 * <AUTHOR>
 * @since 2025-07-30 13:59:49
 */
public class AssRetrieveAssistanceConfigRespDTO {


    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class DetailDTO {
        /**
         * 自增id
         */
        private Long id;
        /**
         * 业务主键
         */
        private String assistanceConfigId;
        /**
         * 找回协助类型
         */
        private String assistanceDesc;
        /**
         * 用户测文案
         */
        private String userDesc;
        /**
         * 排序
         */
        private Integer sort;
    }
}

