package com.pxb7.mall.trade.ass.app;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.DefaultTransactionDefinition;
import org.springframework.util.CollectionUtils;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.exception.BizException;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import com.google.common.collect.Maps;
import com.pxb7.mall.trade.ass.app.dto.reqeust.complaint.req.ComplaintWorkOrderFinishReqDTO;
import com.pxb7.mall.trade.ass.app.dto.reqeust.complaint.req.ComplaintWorkOrderReqDTO;
import com.pxb7.mall.trade.ass.app.dto.reqeust.complaint.req.ComplaintWorkOrderTransferReqDTO;
import com.pxb7.mall.trade.ass.app.dto.reqeust.complaint.resp.*;
import com.pxb7.mall.trade.ass.app.mapping.ComplaintWorkOrderAppMapping;
import com.pxb7.mall.trade.ass.client.dto.response.afc.ComplaintWORespDTO;
import com.pxb7.mall.trade.ass.client.enums.AssWoType;
import com.pxb7.mall.trade.ass.domain.ComplaintWorkOrderDomainService;
import com.pxb7.mall.trade.ass.domain.model.response.ComplaintWorkOrderDetailRespBO;
import com.pxb7.mall.trade.ass.domain.model.response.ComplaintWorkOrderLogRespBO;
import com.pxb7.mall.trade.ass.domain.model.response.ComplaintWorkOrderRespBO;
import com.pxb7.mall.trade.ass.domain.provider.WorkOrderStatusChangeMessageProvider;
import com.pxb7.mall.trade.ass.infra.constant.RMQConstant;
import com.pxb7.mall.trade.ass.infra.enums.ErrorCode;
import com.pxb7.mall.trade.ass.infra.enums.complaint.WorkOrderStatus;
import com.pxb7.mall.trade.ass.infra.exception.BusinessException;
import com.pxb7.mall.trade.ass.infra.repository.db.ComplaintDepartmentConfigRepository;
import com.pxb7.mall.trade.ass.infra.repository.db.ComplaintEmployeeConfigRepository;
import com.pxb7.mall.trade.ass.infra.repository.db.ComplaintWorkRepository;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.ComplaintDepartmentConfig;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.ComplaintEmployeeConfig;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.ComplaintWork;
import com.pxb7.mall.trade.ass.infra.util.DubboResultAssert;
import com.pxb7.mall.trade.ass.infra.util.IdGenUtil;
import com.pxb7.mall.trade.ass.infra.util.OptionalUtil;
import com.pxb7.mall.user.api.SysUserServiceI;
import com.pxb7.mall.user.api.UserServiceI;
import com.pxb7.mall.user.dto.request.user.DubboUserSearchReqDTO;
import com.pxb7.mall.user.dto.response.sys.SysUserRespDTO;
import com.pxb7.mall.user.dto.response.user.UserShortInfoDTO;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

/**
 * Copyright (C), 2002-2024, 螃蟹游戏服务网
 *
 * @fileName: ComplaintWorkOrderAppService.java
 * @description: 客诉工单服务
 * @author: guominqiang
 * @email: <EMAIL>
 * @date: 2024/9/20 17:10
 * @history: //修改记录 <author> <time> <version> <desc> 修改人姓名 修改时间 版本号 描述
 */
@Service
@Slf4j
public class ComplaintWorkOrderAppService {

    @Resource
    private ComplaintWorkOrderDomainService complaintWorkOrderDomainService;
    @Resource
    private ComplaintWorkRepository complaintWorkRepository;
    @Resource
    private ComplaintDepartmentConfigRepository complaintDepartmentConfigRepository;
    @Resource
    private ComplaintEmployeeConfigRepository complaintEmployeeConfigRepository;

    @DubboReference(providedBy = "user-c")
    private UserServiceI userServiceI;

    @DubboReference(providedBy = "user-c")
    private SysUserServiceI sysUserServiceI;
    @Resource
    private DataSourceTransactionManager transactionManager;
    @Resource
    private WorkOrderStatusChangeMessageProvider workOrderStatusChangeMessageProvider;

    public Boolean create(ComplaintWorkOrderReqDTO complaintWorkOrderReqDTO, String userId) {
        ComplaintWork complaintWork = ComplaintWorkOrderAppMapping.INSTANCE.toComplaintWork(complaintWorkOrderReqDTO);
        var complaintWorkId = IdGenUtil.generateId();
        var createTime = LocalDateTime.now();
        var roomId = complaintWorkOrderReqDTO.getRoomId();
        complaintWork.setComplaintWorkId(complaintWorkId);
        complaintWork.setUserId(complaintWorkOrderReqDTO.getUserId());
        complaintWork.setCreateUserId(userId);
        complaintWork.setCurrentProcessorId(userId);
        complaintWork.setWorkOrderStatus(WorkOrderStatus.WORK_STATUS_PROCESSING.getCode());
        complaintWork.setCreateTime(createTime);
        complaintWork.setComplaintSource(2);

        DefaultTransactionDefinition defaultTransactionDefinition = new DefaultTransactionDefinition();
        defaultTransactionDefinition.setTimeout(6);
        TransactionStatus status = transactionManager.getTransaction(defaultTransactionDefinition);
        try {
            complaintWorkRepository.save(complaintWork);
            complaintWorkOrderDomainService.saveComplaintWorkOrderLog(complaintWorkId, roomId, 1, userId, "", "");
            transactionManager.commit(status);
        } catch (Exception e) {
            log.error("[创建客诉工单失败], workOrderId: {},roomId:{}", complaintWorkId, roomId, e);
            transactionManager.rollback(status);
            throw new BusinessException(ErrorCode.COMPLAINT_WO_CREATE_ERROR);
        } finally {
            if (!status.isCompleted()) {
                transactionManager.rollback(status);
            }
        }
        workOrderStatusChangeMessageProvider.asyncSendCreateWOMessage(complaintWorkId, roomId, "", AssWoType.COMPLAINT.getCode(), RMQConstant.AFC_COMPLAINT_WORK_ORDER_STATUS_CHANGE_TAG);
        return Boolean.TRUE;
    }

    public ComplaintWorkOrderDetailRespDTO detail(String complaintWorkId) {
        ComplaintWorkOrderDetailRespBO complaintWorkOrderDetailRespBO = complaintWorkOrderDomainService.searchComplaintWorkOrderDetail(complaintWorkId);
        ComplaintWorkOrderDetailRespDTO respDTO = ComplaintWorkOrderAppMapping.INSTANCE.toComplaintWorkOrderDetailRespDTO(complaintWorkOrderDetailRespBO);
        respDTO.setPhoneStatus(checkPhoneStatusCode(respDTO.getComplaintPhone()));
        return respDTO;
    }

    public Boolean finish(ComplaintWorkOrderFinishReqDTO complaintWorkOrderFinishReqDTO, String userId) {
        var complaintWorkId = complaintWorkOrderFinishReqDTO.getComplaintWorkId();
        var roomId = complaintWorkOrderFinishReqDTO.getRoomId();
        ComplaintWorkOrderDetailRespBO complaintWorkOrderDetailRespBO = complaintWorkOrderDomainService.searchComplaintWorkOrderDetail(complaintWorkId);
        if (Objects.equals(complaintWorkOrderDetailRespBO.getWorkOrderStatus(), WorkOrderStatus.WORK_STATUS_FINISH.getCode())) {
            throw new BizException(ErrorCode.COMPLAINT_WORK_ORDER_ALREADY_FINISH.getErrDesc());

        }
        complaintWorkOrderDetailRespBO.setDealResult(complaintWorkOrderFinishReqDTO.getDealResult());
        complaintWorkOrderDetailRespBO.setNote(complaintWorkOrderFinishReqDTO.getNote());
        complaintWorkOrderDetailRespBO.setDepartmentName(complaintWorkOrderFinishReqDTO.getDepartmentName());
        complaintWorkOrderDetailRespBO.setQuestionType(complaintWorkOrderFinishReqDTO.getQuestionType());
        complaintWorkOrderDetailRespBO.setQuestionLevel(complaintWorkOrderFinishReqDTO.getQuestionLevel());
        complaintWorkOrderDetailRespBO.setResponsiblePerson(complaintWorkOrderFinishReqDTO.getResponsiblePerson());
        complaintWorkOrderDetailRespBO.setResponsibility(complaintWorkOrderFinishReqDTO.getResponsibility());

        DefaultTransactionDefinition defaultTransactionDefinition = new DefaultTransactionDefinition();
        defaultTransactionDefinition.setTimeout(6);
        TransactionStatus status = transactionManager.getTransaction(defaultTransactionDefinition);
        try {
            complaintWorkOrderDomainService.finishComplaintWorkOrder(userId, complaintWorkOrderDetailRespBO);
            complaintWorkOrderDomainService.saveComplaintWorkOrderLog(complaintWorkId, roomId, 3, userId, "", "");
            transactionManager.commit(status);
        } catch (Exception e) {
            log.error("[完结客诉工单失败], workOrderId: {},roomId:{}", complaintWorkId, roomId, e);
            transactionManager.rollback(status);
            throw new BusinessException(ErrorCode.COMPLAINT_WO_FINISHED_ERROR);
        } finally {
            if (!status.isCompleted()) {
                transactionManager.rollback(status);
            }
        }
        workOrderStatusChangeMessageProvider.asyncSendFinishWOMessage(complaintWorkId, roomId, "", AssWoType.COMPLAINT.getCode(), RMQConstant.AFC_COMPLAINT_WORK_ORDER_STATUS_CHANGE_TAG);
        return Boolean.TRUE;
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean transfer(ComplaintWorkOrderTransferReqDTO complaintWorkOrderTransferReqDTO, String userId) {

        var complaintWorkId = complaintWorkOrderTransferReqDTO.getComplaintWorkId();
        var roomId = complaintWorkOrderTransferReqDTO.getRoomId();
        var transfereeId = complaintWorkOrderTransferReqDTO.getTransfereeId();
        ComplaintWorkOrderDetailRespBO complaintWorkOrderDetailRespBO = complaintWorkOrderDomainService.searchComplaintWorkOrderDetail(complaintWorkId);
        if (Objects.equals(complaintWorkOrderDetailRespBO.getWorkOrderStatus(), WorkOrderStatus.WORK_STATUS_FINISH.getCode())) {
            throw new BizException(ErrorCode.COMPLAINT_WORK_ORDER_ALREADY_FINISH.getErrDesc());
        }
        if (StrUtil.equals(transfereeId, complaintWorkOrderDetailRespBO.getTransfereeId())) {
            throw new BizException(ErrorCode.COMPLAINT_WO_CANNOT_TRANSFERRED_ONESELF.getErrDesc());
        }
        ComplaintEmployeeConfig complaintEmployeeConfig = complaintEmployeeConfigRepository.queryEmployeeByUserId(transfereeId);
        if (ObjUtil.isEmpty(complaintEmployeeConfig)) {
            throw new BizException(ErrorCode.TRANSFEREE_NOT_EXISTS.getErrDesc());
        }
        complaintWorkOrderDetailRespBO.setCurrentProcessorId(transfereeId);
        complaintWorkOrderDetailRespBO.setTransfereeId(transfereeId);
        boolean isSuccess = complaintWorkOrderDomainService.transferComplaintWorkOrder(complaintWorkOrderDetailRespBO);
        if (isSuccess) {
            return complaintWorkOrderDomainService.saveComplaintWorkOrderLog(complaintWorkId, roomId, 2, userId, transfereeId, complaintWorkOrderTransferReqDTO.getNote());
        }
        return Boolean.TRUE;
    }

    public List<ComplaintDepartmentConfigRespDTO> departmentList() {
        List<ComplaintDepartmentConfigRespDTO> respList = new ArrayList<>();
        List<ComplaintDepartmentConfig> complaintDepartmentConfigList = complaintDepartmentConfigRepository.queryDepartmentList();

        List<String> departmentIdList = Optional.of(complaintDepartmentConfigList)
            .orElseThrow(() -> new BizException(ErrorCode.COMPLAINT_NOT_CONFIG_DEPARTMENT.getErrDesc()))
            .stream()
            .map(ComplaintDepartmentConfig::getComplaintDepartmentId)
            .toList();
        List<ComplaintDepartmentConfigRespDTO> complaintDepartmentConfigRespDTOList = ComplaintWorkOrderAppMapping.INSTANCE.toComplaintDepartmentConfigRespDTOList(complaintDepartmentConfigList);
        Map<String, ComplaintDepartmentConfigRespDTO> deptMap = complaintDepartmentConfigRespDTOList.stream()
            .collect(Collectors.toMap(ComplaintDepartmentConfigRespDTO::getComplaintDepartmentId, val -> val));

        List<ComplaintEmployeeConfig> complaintEmployeeConfigList = complaintEmployeeConfigRepository.queryEmployeeList(departmentIdList);
        Optional.of(complaintEmployeeConfigList)
            .orElseThrow(() -> new BizException(ErrorCode.COMPLAINT_NOT_CONFIG_EMPLOYEE.getErrDesc()))
            .stream()
            .collect(Collectors.groupingBy(ComplaintEmployeeConfig::getComplaintDepartmentId))
            .forEach((departmentId, employeeList) -> {
                ComplaintDepartmentConfigRespDTO complaintDepartmentConfig = deptMap.get(departmentId);
                List<ComplaintEmployeeConfigRespDTO> complaintEmployeeList = new ArrayList<>();
                employeeList.forEach(employee -> {
                    ComplaintEmployeeConfigRespDTO complaintEmployeeConfigRespDTO = ComplaintEmployeeConfigRespDTO.builder()
                        .phone(employee.getMobile())
                        .userName(employee.getUserName())
                        .userId(employee.getUserId())
                        .build();
                    complaintEmployeeList.add(complaintEmployeeConfigRespDTO);
                });
                complaintDepartmentConfig.setComplaintEmployeeList(complaintEmployeeList);
                respList.add(complaintDepartmentConfig);
            });
        if (CollectionUtils.isEmpty(respList)) {
            return Collections.emptyList();
        }
        return respList.stream()
            .sorted(Comparator.comparing(ComplaintDepartmentConfigRespDTO::getQuestionSort))
            .toList();
    }

    public List<ComplaintWorkLogRespDTO> viewLog(String complaintWorkId) {
        List<ComplaintWorkOrderLogRespBO> complaintWorkLogList = complaintWorkOrderDomainService.queryByWorkId(complaintWorkId);
        List<ComplaintWorkLogRespDTO> respList = complaintWorkLogList.stream().map(item -> {
            ComplaintWorkLogRespDTO respDTO = new ComplaintWorkLogRespDTO();
            respDTO.setComplaintWorkId(item.getComplaintWorkId());
            respDTO.setLogType(item.getLogType());
            respDTO.setCurrentProcessorId(item.getCurrentProcessorId());
            respDTO.setTransfereeId(item.getTransfereeId());
            respDTO.setTransferNote(item.getTransferNote());
            if (StrUtil.isNotBlank(item.getModification())) {
                List<ComplaintWorkLogModificationRespDTO> list = JSONObject.parseObject(item.getModification(), new TypeReference<>() {});
                respDTO.setModification(list);
            }
            respDTO.setCreateTime(item.getCreateTime());
            return respDTO;
        }).collect(Collectors.toList());
        List<String> userIds = respList.stream()
            .flatMap(item -> Stream.of(item.getCurrentProcessorId(), item.getTransfereeId()))
            .filter(StringUtils::isNotBlank)
            .toList();
        Map<String, String> userMap = getSysUserInfo(userIds);
        respList.forEach(item -> {
            item.setCurrentProcessorName(userMap.get(item.getCurrentProcessorId()));
            item.setTransfereeName(userMap.get(item.getTransfereeId()));
        });
        return respList;
    }

    /**
     * 查询当前用户的工单列表
     *
     * @param userId
     * @return
     */
    public List<ComplaintWorkOrderRespDTO> list(String customerId, String userId) {
        List<ComplaintWorkOrderRespBO> complaintWorkOrderList = complaintWorkOrderDomainService.list(customerId, userId);
        List<ComplaintWorkOrderRespDTO> resp = ComplaintWorkOrderAppMapping.INSTANCE.toComplaintWorkOrderRespDTOList(complaintWorkOrderList);
        List<String> userIds = resp.stream()
            .flatMap(item -> Stream.of(item.getCurrentProcessorId(), item.getFinisherId()))
            .filter(StringUtils::isNotBlank)
            .toList();
        Map<String, String> userMap = getSysUserInfo(userIds);
        resp.forEach(item -> {
            switch (item.getWorkOrderStatus()) {
                case 1 -> item.setCurrentProcessorName(userMap.get(item.getCurrentProcessorId()));
                case 2 -> item.setFinisherName(userMap.get(item.getFinisherId()));
            }
        });
        return resp;
    }

    /**
     * 查询系统用户信息
     *
     * @param userIds 用户列表
     * @return 个人用户认证的信息
     */
    private Map<String, String> getSysUserInfo(List<String> userIds) {
        return OptionalUtil.ofEmpty(userIds)
            .map(o -> DubboResultAssert.wrapException(() -> sysUserServiceI.listByUserIdContainsDeletion(o),
                ErrorCode.RPC_GET_SYS_USER_INFO_ERROR))
            .map(MultiResponse::getData)
            .filter(CollUtil::isNotEmpty)
            .map(o -> o.stream()
                .collect(Collectors.toMap(SysUserRespDTO::getUserId, SysUserRespDTO::getUserName, (o1, o2) -> o1)))
            .orElse(Maps.newHashMap());
    }

    /**
     * 查询手机号是否注销
     *
     * @param phone 待查验手机号
     * @return 状态 false未注册或正常用户 true注销用户
     */
    public Boolean checkPhoneStatus(String phone) {
        if (StringUtils.isEmpty(phone)) {
            throw new BizException(ErrorCode.PHONE_NOT_EXISTS.getErrDesc());
        }
        int code = checkPhoneStatusCode(phone);
        return code == 2;
    }

    /**
     * 手机号状态查询
     *
     * @param phone 待查验手机号
     * @return 状态 0未注册 1正常用户 2注销用户
     */
    public Integer checkPhoneStatusCode(String phone) {
        int status = 0;
        DubboUserSearchReqDTO reqDTO = new DubboUserSearchReqDTO();
        reqDTO.setPhone(phone);
        reqDTO.setUserType(1);
        MultiResponse<UserShortInfoDTO> userResp = userServiceI.searchUser(reqDTO);
        if (!userResp.isSuccess()) {
            throw new BizException(ErrorCode.GET_USER_PHONE_STATUS_ERROR.getErrDesc());
        }
        List<UserShortInfoDTO> userList = userResp.getData();
        if (userList != null && !userList.isEmpty()) {
            // 有查询结果，最少是注册过，只要有一个正常状态的数据就是正常用户，其它数据为注销数据
            status = 2;
            for (UserShortInfoDTO userShortInfoDTO : userList) {
                if (userShortInfoDTO.getUserStatus() == 0 || userShortInfoDTO.getUserStatus() == 1) {
                    status = 1;
                }
            }
        }
        return status;
    }

    public ComplaintWORespDTO searchComplaintWOById(String workOrderId) {
        return ComplaintWorkOrderAppMapping.INSTANCE.toComplaintWORespDTO(complaintWorkRepository.queryById(workOrderId));
    }
}
