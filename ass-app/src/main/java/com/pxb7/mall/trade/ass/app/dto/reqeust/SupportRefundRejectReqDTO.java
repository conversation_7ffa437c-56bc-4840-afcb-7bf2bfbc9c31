package com.pxb7.mall.trade.ass.app.dto.reqeust;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;

@Data
public class SupportRefundRejectReqDTO {

    @NotBlank(message = "退款单id不能为空")
    private String refundVoucherId;

    @NotBlank(message = "拒绝原因不能为空")
    @Size(max = 200, message = "拒绝原因长度不能超过200")
    private String rejectReason;
}
