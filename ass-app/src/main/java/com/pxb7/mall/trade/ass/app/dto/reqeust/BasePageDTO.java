package com.pxb7.mall.trade.ass.app.dto.reqeust;

/**
 * <AUTHOR>
 * @version : BasePageDTO.java, v 0.1 2025年07月30日 16:29 yang.xuexi Exp $
 */

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>功能描述:</p>
 * 作者：xuexiyang
 * 创建日期：2025/07/30
 * 公司名称：金华博淳网络科技有限公司
 * 域名： www.pxb7.com
 */
@Data
public class BasePageDTO implements Serializable {

    @NotNull(message = "当前页不能为空")
    @Min(value = 1, message = "当前页最小值为1")
    private Integer pageIndex;

    @NotNull(message = "页面大小不能为空")
    // todo 待确认每个场景的
    @Min(value = 1, message = "页面大小最小值为1")
    @Max(value = 100, message = "页面大小最大值为100")
    private Integer pageSize;
}
