package com.pxb7.mall.trade.ass.app.dto.response;

import java.time.LocalDateTime;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.Data;

@Data
public class RefundPageRespDTO {
    /**
     * 充值套餐，充值订单有
     */
    private String rechargePackage;
    /**
     * 商品成交价 商品销售价格(单价)(求降价之后的价格)
     */
    private String productSalePrice;
    /**
     * 商品数量
     */
    private Integer productQuantity;

    /**
     * 商品名称
     */
    private String productName;
    /**
     * 商品图片
     */
    private String productPic;
    /**
     * 退款单id
     */
    private String refundVoucherId;

    /**
     * 申请时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:Ss")
    private LocalDateTime applyTime;
    /**
     * 退款类型 1 整单退款 2.退商品差价
     */
    private Integer wholeRefund;

    /**
     * 退款状态 1待审核, 2退款中, 3退款成功, 4退款失败, 5退款关闭
     */
    private Integer refundStatus;
    /**
     * 审核状态 1待客服审核,2待提交打款信息(线下打款才有),3待财务审核,4审核成功,5审核失败
     */
    private Integer auditStatus;
    /**
     * 退款金额
     */
    private Long refundAmount;
    /**
     * 实际退款金额，退款完成后写入
     */
    private Long actualRefundAmount;
    /**
     * 商品号价退款金额
     */
    private Long productAmount;
    /**
     * 包赔金额
     */
    private Long indemnityAmount;
    /**
     * 手续费金额
     */
    private Long feeAmount;
    /**
     * 客服拒绝原因
     */
    private String rejectReason;
    /**
     * im房间id
     */
    private String imRoomId;
    /**
     * 订单id
     */
    private String orderId;
    /**
     * 订单行id
     */
    private String orderItemId;
    /**
     * 商品类型
     */
    private String productType;
    /**
     * 买家手机号
     */
    private String buyerPhone;
    /**
     * 卖家手机号
     */
    private String sellerPhone;
    /**
     * 游戏名称
     */
    private String gameName;

    /**
     * 区服
     */
    private String productAttr;

    /**
     * 包赔服务
     */
    private List<String> indemnityNameList;

    /**
     * 买商品的时候,它是否有生效的诚心卖履约服务, true有 false/null 没有
     */
    private Boolean promiseSell;
}
