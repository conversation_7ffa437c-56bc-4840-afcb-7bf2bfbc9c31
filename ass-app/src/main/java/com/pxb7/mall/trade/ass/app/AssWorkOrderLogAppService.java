package com.pxb7.mall.trade.ass.app;


import java.util.List;

import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import com.pxb7.mall.trade.ass.app.dto.reqeust.AssWorkOrderLogReqDTO;
import com.pxb7.mall.trade.ass.app.dto.response.AssWorkOrderLogRespDTO;
import com.pxb7.mall.trade.ass.app.mapping.AssWorkOrderLogAppMapping;
import com.pxb7.mall.trade.ass.domain.model.AssWorkOrderLogReqBO;
import com.pxb7.mall.trade.ass.domain.model.AssWorkOrderLogRespBO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pxb7.mall.trade.ass.domain.AssWorkOrderLogDomainService;


/**
 * 售后工单日志明细app服务
 *
 * <AUTHOR>
 * @since 2025-07-31 10:22:33
 */
@Service
public class AssWorkOrderLogAppService {

    @Resource
    private AssWorkOrderLogDomainService assWorkOrderLogDomainService;

    public boolean insert(AssWorkOrderLogReqDTO.AddDTO param) {
        AssWorkOrderLogReqBO.AddBO addBO = AssWorkOrderLogAppMapping.INSTANCE.assWorkOrderLogDTO2AddBO(param);
        return assWorkOrderLogDomainService.insert(addBO);
    }

    public boolean update(AssWorkOrderLogReqDTO.UpdateDTO param) {
        AssWorkOrderLogReqBO.UpdateBO updateBO = AssWorkOrderLogAppMapping.INSTANCE.assWorkOrderLogDTO2UpdateBO(param);
        return assWorkOrderLogDomainService.update(updateBO);
    }


    public AssWorkOrderLogRespDTO.DetailDTO findById(Long id) {
        AssWorkOrderLogRespBO.DetailBO detailBO = assWorkOrderLogDomainService.findById(id);
        return AssWorkOrderLogAppMapping.INSTANCE.assWorkOrderLogBO2DetailDTO(detailBO);
    }

    public List<AssWorkOrderLogRespDTO.DetailDTO> list(AssWorkOrderLogReqDTO.SearchDTO param) {
        AssWorkOrderLogReqBO.SearchBO searchBO = AssWorkOrderLogAppMapping.INSTANCE.assWorkOrderLogDTO2SearchBO(param);
        List<AssWorkOrderLogRespBO.DetailBO> list = assWorkOrderLogDomainService.list(searchBO);
        return AssWorkOrderLogAppMapping.INSTANCE.assWorkOrderLogBO2ListDTO(list);
    }

    public Page<AssWorkOrderLogRespDTO.DetailDTO> page(AssWorkOrderLogReqDTO.PageDTO param) {
        AssWorkOrderLogReqBO.PageBO pageBO = AssWorkOrderLogAppMapping.INSTANCE.assWorkOrderLogDTO2PageBO(param);
        Page<AssWorkOrderLogRespBO.DetailBO> page = assWorkOrderLogDomainService.page(pageBO);
        return AssWorkOrderLogAppMapping.INSTANCE.assWorkOrderLogBO2PageDTO(page);
    }

}

