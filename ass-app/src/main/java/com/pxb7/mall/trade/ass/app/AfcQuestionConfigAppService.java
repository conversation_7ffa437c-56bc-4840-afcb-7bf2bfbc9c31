package com.pxb7.mall.trade.ass.app;

import com.pxb7.mall.trade.ass.app.dto.response.AfcQuestionConfRespDTO;
import com.pxb7.mall.trade.ass.app.mapping.AfcQuestionConfigMapping;
import com.pxb7.mall.trade.ass.infra.repository.db.AfcQuestionConfRepository;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.AfcQuestionConf;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Copyright (C), 2002-2025, 螃蟹游戏服务网
 *
 * @fileName: AfcQuestionConfigAppService.java
 * @description: 售后问题配置服务
 * @author: guominqiang
 * @email: <EMAIL>
 * @date: 2025/4/11 14:13
 * @history: //修改记录
 * <author> <time> <version> <desc>
 * 修改人姓名 修改时间 版本号 描述
 */
@Service
@Slf4j
public class AfcQuestionConfigAppService {

    @Resource
    private AfcQuestionConfRepository afcQuestionConfRepository;

    public List<AfcQuestionConfRespDTO> getAfcQuestionConfList(String userId) {
        List<AfcQuestionConf> afcQuestionConfList = afcQuestionConfRepository.findAfcQuestionConfList(userId);
        return AfcQuestionConfigMapping.INSTANCE.toAfcQuestionConfRespDTOList(afcQuestionConfList);
    }
}
