package com.pxb7.mall.trade.ass.app.dto.reqeust;

import java.io.Serial;
import java.io.Serializable;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 证据列表
 *
 * <AUTHOR>
 * @since: 2024-06-12 15:06
 **/
@Data
public class ProofDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = 8404192042630196520L;
    /**
     * 名称
     */
    @NotBlank(message = "名称不能为空")
    private String name;
    /**
     * 链接
     */
    @NotBlank(message = "链接不能为空")
    private String linkUrl;
}
