package com.pxb7.mall.trade.ass.app.mapping;

import com.pxb7.mall.trade.ass.app.dto.reqeust.AssWorkOrderLogReqDTO;
import com.pxb7.mall.trade.ass.app.dto.response.AssWorkOrderLogRespDTO;
import com.pxb7.mall.trade.ass.domain.model.AssWorkOrderLogReqBO;
import com.pxb7.mall.trade.ass.domain.model.AssWorkOrderLogRespBO;

import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;


@Mapper
public interface AssWorkOrderLogAppMapping {

    AssWorkOrderLogAppMapping INSTANCE = Mappers.getMapper(AssWorkOrderLogAppMapping.class);


    AssWorkOrderLogReqBO.AddBO assWorkOrderLogDTO2AddBO(AssWorkOrderLogReqDTO.AddDTO source);

    AssWorkOrderLogReqBO.UpdateBO assWorkOrderLogDTO2UpdateBO(AssWorkOrderLogReqDTO.UpdateDTO source);

    AssWorkOrderLogReqBO.DelBO assWorkOrderLogDTO2DelBO(AssWorkOrderLogReqDTO.DelDTO source);

    AssWorkOrderLogReqBO.SearchBO assWorkOrderLogDTO2SearchBO(AssWorkOrderLogReqDTO.SearchDTO source);

    AssWorkOrderLogReqBO.PageBO assWorkOrderLogDTO2PageBO(AssWorkOrderLogReqDTO.PageDTO source);

    AssWorkOrderLogRespDTO.DetailDTO assWorkOrderLogBO2DetailDTO(AssWorkOrderLogRespBO.DetailBO source);

    List<AssWorkOrderLogRespDTO.DetailDTO> assWorkOrderLogBO2ListDTO(List<AssWorkOrderLogRespBO.DetailBO> source);

    Page<AssWorkOrderLogRespDTO.DetailDTO> assWorkOrderLogBO2PageDTO(Page<AssWorkOrderLogRespBO.DetailBO> source);

}


