package com.pxb7.mall.trade.ass.app.dto.reqeust.violate;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.Data;

@Data
public class ViolateUpdateDTO {

    /**
     * 违约单Id
     */
    @NotNull(message = "违约金id不可为空")
    private String violateId;

    /**
     * 违约金额，分
     */
    @NotNull(message = "违约金额不可为空")
    @Positive(message = "违约金额必须大于0")
    private Long violateAmount;

    /**
     * 守约金额，分
     */
    private Long promiseAmount;
}
