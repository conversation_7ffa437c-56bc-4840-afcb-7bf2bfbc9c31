package com.pxb7.mall.trade.ass.app.dto.reqeust;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class RefundUserInfoDTO {

    /**
     * 退款单id
     */
    @NotBlank(message = "退款单id不能为空")
    private String refundVoucherId;

    /**
     * 买家姓名
     */
    @NotBlank(message = "买家姓名不能为空")
    private String buyerName;

    /**
     * 退款渠道, 1支付宝 2 微信 3银行卡
     */
    @NotNull(message = "退款渠道不能为空， 1支付宝 2微信 3银行卡")
    private Integer refundChannel;

    /**
     * 退款账户
     */
    @NotBlank(message = "退款账户不能为空")
    private String refundAccount;

    /**
     * 消息id
     */
    @NotBlank(message = "消息id不能为空")
    private String msgUid;

    /**
     * 表单回写内容
     */
    @NotBlank(message = "表单回写内容不能为空")
    private String content;
}
