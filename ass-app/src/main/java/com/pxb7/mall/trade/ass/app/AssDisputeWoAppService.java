package com.pxb7.mall.trade.ass.app;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.cola.exception.BizException;
import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Lists;
import com.pxb7.mall.components.redis.redisson.RedissonUtils;
import com.pxb7.mall.im.client.api.AdminImUserServiceI;
import com.pxb7.mall.im.client.dto.request.CustomerCareLastFeishuAuthReqDTO;
import com.pxb7.mall.im.client.dto.response.CustomerCareLastFeishuAuthRespDTO;
import com.pxb7.mall.trade.ass.app.dto.reqeust.DealDisputeResultReqDTO;
import com.pxb7.mall.trade.ass.app.mapping.AssDisputeWoMapping;
import com.pxb7.mall.trade.ass.client.dto.response.afc.DisputeWORespDTO;
import com.pxb7.mall.trade.ass.client.enums.*;
import com.pxb7.mall.trade.ass.domain.*;
import com.pxb7.mall.trade.ass.domain.model.AssWorkOrderLogReqBO;
import com.pxb7.mall.trade.ass.domain.model.AssWorkOrderReqBO;
import com.pxb7.mall.trade.ass.domain.model.AssWorkOrderRespBO;
import com.pxb7.mall.trade.ass.domain.model.reqeust.RecordBO;
import com.pxb7.mall.trade.ass.domain.provider.WorkOrderStatusChangeMessageProvider;
import com.pxb7.mall.trade.ass.infra.constant.AssWoConstant;
import com.pxb7.mall.trade.ass.infra.constant.RMQConstant;
import com.pxb7.mall.trade.ass.infra.enums.ErrorCode;
import com.pxb7.mall.trade.ass.infra.exception.BusinessException;
import com.pxb7.mall.trade.ass.infra.exception.Ensure;
import com.pxb7.mall.trade.ass.infra.repository.db.AssClaimRepository;
import com.pxb7.mall.trade.ass.infra.repository.db.AssDisputeWoRepository;
import com.pxb7.mall.trade.ass.infra.repository.db.AssScheduleRepository;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.AssDisputeWo;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.AssSchedule;
import com.pxb7.mall.trade.ass.infra.repository.db.model.request.DealDisputeResultReqPO;
import com.pxb7.mall.trade.ass.infra.util.AssNoUtil;
import com.pxb7.mall.trade.ass.infra.util.IdGenUtil;
import com.pxb7.mall.trade.order.client.dto.response.order.dubbo.OrderInfoDubboRespDTO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 售后纠纷工单
 *
 * <AUTHOR>
 **/
@Slf4j
@Service
public class AssDisputeWoAppService {

    @Resource
    private AssClaimRepository assClaimRepository;
    @DubboReference
    private AdminImUserServiceI adminImUserServiceI;
    @Resource
    private AssScheduleRepository assScheduleRepository;
    @Resource
    private AssDisputeWoRepository assDisputeWoRepository;
    @Resource
    private AssScheduleDomainService assScheduleDomainService;
    @Resource
    private AssScheduleLogDomainService assScheduleLogDomainService;
    @Resource
    private AssFollowupLogDomainService assFollowupLogDomainService;

    @Resource
    private DataSourceTransactionManager transactionManager;
    @Resource
    private WorkOrderStatusChangeMessageProvider workOrderStatusChangeMessageProvider;

    @Resource
    private AssWorkOrderDomainService assWorkOrderDomainService;

    @Transactional(rollbackFor = Exception.class)
    public String createDisputeWo(String userId, AssSchedule schedule, OrderInfoDubboRespDTO orderInfo,
        AssWoType assWoType, CustomerCareLastFeishuAuthRespDTO authRespDTO) {
        String workOrderId = IdGenUtil.generateId();
        LocalDateTime expectedTime = LocalDateTime.now().plusDays(AssWoConstant.EXPECTED_TIME);
        AssDisputeWo disputeWo = new AssDisputeWo();
        disputeWo.setAssDisputeId(workOrderId);
        disputeWo.setWorkOrderNo(AssNoUtil.generateAssNo(AssWoType.DISPUTE));
        disputeWo.setCompleteTime(expectedTime);
        disputeWo.setOrderItemId(schedule.getOrderItemId());
        disputeWo.setProductId(orderInfo.getProductId());
        disputeWo.setProductUniqueNo(orderInfo.getProductUniqueNo());
        disputeWo.setProductType(orderInfo.getProductType());
        disputeWo.setGameId(orderInfo.getGameId());
        disputeWo.setProposerUserId(schedule.getCreateUserId());
        disputeWo.setSponsor(schedule.getSourceType());
        disputeWo.setDisputeUserId(userId);
        disputeWo.setDataSource(AssDataSource.DEFAULT.getCode());
        disputeWo.setSourceType(AssSourceType.WEB.getCode());
        disputeWo.setCreateUserId(userId);
        disputeWo.setUpdateUserId(userId);
        String feiShuName = Objects.nonNull(authRespDTO)
            ? (authRespDTO.getFeishuName() + "(" + authRespDTO.getFeishuEmployeeNo() + ")") : StringUtils.EMPTY;
        disputeWo.setFeishuName(feiShuName);
        disputeWo.setDataCreateSource(AssDataCreateSource.IM.getCode());
        // 保存数据库数据
        boolean flag = assDisputeWoRepository.save(disputeWo);
        // 工单关联流程节点
        assScheduleDomainService.updateWorkOrderIdAndAssTypeAndFinishById(schedule.getId(), workOrderId,
            assWoType.getCode(), AssScheduleNode.CREATE_WORK_ORDER.isFinish(), userId);
        assScheduleLogDomainService.add(schedule.getScheduleId(), AssScheduleNode.CREATE_WORK_ORDER, "", assWoType,
            userId, "");
        // 记录日志
        RecordBO recordBO = new RecordBO().setWorkOrderId(disputeWo.getAssDisputeId())
            .setAssType(AssWoType.DISPUTE.getCode()).setOperatorId(disputeWo.getCreateUserId())
            .setTitle(AssOperatorLog.ADD_WORD_ORDER.getDesc())
            .setContent(AssOperatorLog.ADD_WORD_ORDER.getDesc() + disputeWo.getWorkOrderNo()).setFeiShuName(feiShuName);
        assFollowupLogDomainService.recordLog(recordBO);
        assWorkOrderDomainService.createWorkOrder(recordBO, schedule,workOrderId);
        return flag ? workOrderId : null;
    }





    public String completeDispute(DealDisputeResultReqDTO reqDTO, String userId) {
        log.info("completeDispute:userId:{},reqDTO:{}", userId, JSON.toJSONString(reqDTO));
        // 校验重复提交
        String strKey = String.format("completedispute:%s:%s", userId, reqDTO.getRoomId());
        Ensure.that(RedissonUtils.setObjectIfAbsent(strKey, "1", Duration.ofSeconds(2)))
            .isTrue(ErrorCode.REPEAT_SUBMIT);
        DealDisputeResultReqPO resultRep = AssDisputeWoMapping.INSTANCE.toReqPO(reqDTO);
        AssSchedule schedule = assScheduleRepository.getByRoomId(reqDTO.getRoomId());
        Ensure.that(schedule).isNotNull(ErrorCode.ROOM_NOT_EXISTS);
        resultRep.setAssDisputeId(schedule.getWorkOrderId());
        AssDisputeWo disputeWo = assDisputeWoRepository.getById(resultRep.getAssDisputeId());
        Ensure.that(disputeWo).isNotNull(ErrorCode.WORD_ORDER_NOT_EXIST);
        // 校验赔付
        if (Objects.equals(AssDisputeDealResult.CLAIM.getCode(), reqDTO.getDisputeDealResult())) {
            // 查询当前工单是否存在赔付
            long l = assClaimRepository.countByWorkOrderIdAndStatus(resultRep.getAssDisputeId(), Lists.newArrayList(AssClaimStatus.CLAIM.getCode(), AssClaimStatus.FINANCE.getCode(), AssClaimStatus.ONLINE.getCode()));
            if (l > 0) {
                throw new BizException(ErrorCode.EXIST_CLAIM_INFO.getErrDesc());
            }
        }
        // 已完成工单不允许处理
        if (Objects.equals(AssStatus.FINISH.getCode(), disputeWo.getStatus())) {
            throw new BizException(ErrorCode.DISPUTE_ORDER_FINISH_ERROR.getErrDesc());
        }
        AssDisputeDealResult dealResult = AssDisputeDealResult.fromCode(reqDTO.getDisputeDealResult());
        // 记录日志
        // 获取im飞书信息
        CustomerCareLastFeishuAuthRespDTO authRespDTO =
            Optional.ofNullable(userId).map(o -> CustomerCareLastFeishuAuthReqDTO.builder().userId(o).build())
                .map(adminImUserServiceI::customerCareLastFeishuAuth).map(SingleResponse::getData).orElse(null);
        String feiShuName = Objects.nonNull(authRespDTO)
            ? (authRespDTO.getFeishuName() + "(" + authRespDTO.getFeishuEmployeeNo() + ")") : StringUtils.EMPTY;
        RecordBO recordBO =
            new RecordBO().setWorkOrderId(schedule.getWorkOrderId()).setAssType(AssWoType.DISPUTE.getCode())
                .setOperatorId(userId).setTitle(AssOperatorLog.COMPLETE_WO.getDesc())
                .setContent(AssOperatorLog.COMPLETE_WO.getDesc() + dealResult.getDesc()).setFeiShuName(feiShuName);
        DefaultTransactionDefinition defaultTransactionDefinition = new DefaultTransactionDefinition();
        defaultTransactionDefinition.setTimeout(6);
        TransactionStatus status = transactionManager.getTransaction(defaultTransactionDefinition);
        try {
        // 更新数据库
        assDisputeWoRepository.completeDispute(resultRep);
        assFollowupLogDomainService.recordLog(recordBO);
        // 添加节点日志
        AssScheduleNode node = AssScheduleNode.DISPUTE_AFTER_SALE;
        assScheduleDomainService.updateFinishByScheduleId(schedule.getScheduleId(), node);
        assScheduleLogDomainService.record(schedule.getScheduleId(), dealResult.getDesc(), node, userId,
            AssScheduleSourceTypeEnums.SourceType_2.getCode(), "");
        assWorkOrderDomainService.completeWorkOrder(schedule);
            transactionManager.commit(status);
        } catch (Exception e) {
            log.error("[完结纠纷工单失败], workOrderId: {},orderItemId:{}", resultRep.getAssDisputeId(), disputeWo.getOrderItemId(), e);
            transactionManager.rollback(status);
            throw new BusinessException(ErrorCode.COMPLAINT_WO_CREATE_ERROR);
        } finally {
            if (!status.isCompleted()) {
                transactionManager.rollback(status);
            }
        }
        workOrderStatusChangeMessageProvider.asyncSendFinishWOMessage(resultRep.getAssDisputeId(), reqDTO.getRoomId(), disputeWo.getOrderItemId(), AssWoType.DISPUTE.getCode(), RMQConstant.AFC_DISPUTE_WORK_ORDER_STATUS_CHANGE_TAG);
        return dealResult.getDesc();
    }

    public DisputeWORespDTO searchDisputeWOById(String workOrderId) {
        return AssDisputeWoMapping.INSTANCE.toRespDTO(assDisputeWoRepository.getById(workOrderId));
    }
}
