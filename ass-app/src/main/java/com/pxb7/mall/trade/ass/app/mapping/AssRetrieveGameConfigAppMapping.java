package com.pxb7.mall.trade.ass.app.mapping;

import com.pxb7.mall.trade.ass.app.dto.reqeust.AssRetrieveGameConfigReqDTO;
import com.pxb7.mall.trade.ass.app.dto.response.AssRetrieveGameConfigRespDTO;
import com.pxb7.mall.trade.ass.domain.model.AssRetrieveGameConfigReqBO;
import com.pxb7.mall.trade.ass.domain.model.AssRetrieveGameConfigRespBO;

import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;


@Mapper
public interface AssRetrieveGameConfigAppMapping {

    AssRetrieveGameConfigAppMapping INSTANCE = Mappers.getMapper(AssRetrieveGameConfigAppMapping.class);


    AssRetrieveGameConfigReqBO.AddBO assRetrieveGameConfigDTO2AddBO(AssRetrieveGameConfigReqDTO.AddDTO source);

    AssRetrieveGameConfigReqBO.UpdateBO assRetrieveGameConfigDTO2UpdateBO(AssRetrieveGameConfigReqDTO.UpdateDTO source);

    AssRetrieveGameConfigReqBO.DelBO assRetrieveGameConfigDTO2DelBO(AssRetrieveGameConfigReqDTO.DelDTO source);

    AssRetrieveGameConfigReqBO.SearchBO assRetrieveGameConfigDTO2SearchBO(AssRetrieveGameConfigReqDTO.SearchDTO source);

    AssRetrieveGameConfigReqBO.PageBO assRetrieveGameConfigDTO2PageBO(AssRetrieveGameConfigReqDTO.PageDTO source);

    AssRetrieveGameConfigRespDTO.DetailDTO assRetrieveGameConfigBO2DetailDTO(AssRetrieveGameConfigRespBO.DetailBO source);

    List<AssRetrieveGameConfigRespDTO.DetailDTO> assRetrieveGameConfigBO2ListDTO(List<AssRetrieveGameConfigRespBO.DetailBO> source);

    Page<AssRetrieveGameConfigRespDTO.DetailDTO> assRetrieveGameConfigBO2PageDTO(Page<AssRetrieveGameConfigRespBO.DetailBO> source);

}


