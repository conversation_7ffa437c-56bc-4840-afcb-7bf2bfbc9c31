package com.pxb7.mall.trade.ass.app;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.fastjson2.JSON;
import com.pxb7.mall.im.client.api.SendCommonMsgServiceI;
import com.pxb7.mall.im.client.api.SendMsgServiceI;
import com.pxb7.mall.im.client.dto.request.GroupNotifyMsgReqDTO;
import com.pxb7.mall.im.client.dto.request.card.SendRichTextMsgReqDTO;
import com.pxb7.mall.im.client.dto.request.card.button.ButtonCon;
import com.pxb7.mall.im.client.dto.request.card.button.ButtonConUrl;
import com.pxb7.mall.im.client.dto.request.card.button.clienttype.ClientAndroid;
import com.pxb7.mall.im.client.dto.request.card.button.clienttype.ClientPC;
import com.pxb7.mall.im.client.dto.request.card.button.clienttype.ClientWap;
import com.pxb7.mall.im.client.dto.request.card.button.targettype.TargetTypeWindow;
import com.pxb7.mall.im.client.dto.request.card.richtext.RichTextContent;
import com.pxb7.mall.trade.ass.app.dto.reqeust.TransferQuestionDataDTO;
import com.pxb7.mall.trade.ass.client.enums.AssScheduleNode;
import com.pxb7.mall.trade.ass.infra.enums.ErrorCode;
import com.pxb7.mall.trade.ass.infra.exception.Ensure;
import com.pxb7.mall.trade.ass.infra.repository.db.AssQuestionOptionRelationRepository;
import com.pxb7.mall.trade.ass.infra.repository.db.AssQuestionOptionRepository;
import com.pxb7.mall.trade.ass.infra.repository.db.AssScheduleRepository;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.AssQuestionOption;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.AssSchedule;
import com.pxb7.mall.trade.ass.infra.util.DubboResultAssert;
import com.pxb7.mall.trade.order.client.api.OrderInfoDubboServiceI;
import com.pxb7.mall.trade.order.client.dto.response.order.dubbo.OrderInfoDubboRespDTO;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

/**
 * 售后问答问题配置
 *
 * <AUTHOR>
 **/
@Slf4j
@Service
public class AssQuestionOptionAppService {

    @DubboReference
    private SendMsgServiceI sendMsgServiceI;
    @Resource
    private AssScheduleRepository assScheduleRepository;
    @DubboReference
    private SendCommonMsgServiceI sendCommonMsgServiceI;
    @DubboReference
    private OrderInfoDubboServiceI orderInfoDubboServiceI;
    @Resource
    private AssQuestionOptionRepository assQuestionOptionRepository;
    @Resource
    private AssQuestionOptionRelationRepository assQuestionOptionRelationRepository;

    public boolean sendQuestionByGame(AssSchedule assSchedule, String userId) {
        String orderItemId = assSchedule.getOrderItemId();
        Ensure.that(orderItemId).isNotBlank(ErrorCode.PARAM_EMPTY);
        SingleResponse<OrderInfoDubboRespDTO> orderInfo = orderInfoDubboServiceI.getOrderInfo(orderItemId);
        AssQuestionOption questionOption = Optional.ofNullable(orderInfo).map(SingleResponse::getData)
            .map(OrderInfoDubboRespDTO::getGameId).map(assQuestionOptionRelationRepository::getOptionId)
            .map(assQuestionOptionRepository::findOneByOptionId).orElse(null);
        Ensure.that(questionOption).isNotNull(ErrorCode.QUESTION_NOT_CONFIG);
        assert questionOption != null;
        return sendQuestionCard(assSchedule.getRoomId(), userId, orderInfo.getData().getBuyerId(),
            questionOption.getQuestionList());
    }

    public boolean sendQuestionCard(String roomId, String customerId, String buyerId, String questionList) {
        // 封装按钮信息
        List<TransferQuestionDataDTO> questionDataList = JSON.parseArray(questionList, TransferQuestionDataDTO.class);
        ButtonConUrl h5Url = new ButtonConUrl().setClientType(new ClientWap()).setUrl("BasicAfterSalesIssuesPopupRef")
            .setTargetType(new TargetTypeWindow("BasicAfterSalesIssuesPopupRef"));
        ButtonConUrl pcUrl = new ButtonConUrl().setClientType(new ClientPC()).setUrl("BasicAfterSalesIssuesPopupRef")
            .setTargetType(new TargetTypeWindow("BasicAfterSalesIssuesPopupRef"));
        ButtonConUrl mobileUrl =
            new ButtonConUrl().setClientType(new ClientAndroid()).setUrl("BasicAfterSalesIssuesPopupRef")
                .setTargetType(new TargetTypeWindow("BasicAfterSalesIssuesPopupRef"));
        ButtonCon buttonContent = new ButtonCon().setParam(questionDataList).setLabel("去回答")
            .setButtonUrl(Arrays.asList(h5Url, pcUrl, mobileUrl));
        SendRichTextMsgReqDTO cardReqDTO = new SendRichTextMsgReqDTO().setTitle("售后基础信息").setFromUserId(customerId)
            .setTargetId(roomId).setOperation("ass_question").setButtons(Collections.singletonList(buttonContent))
            .setOperatorUserIds(Collections.singletonList(buyerId)).setMentionedIds(Collections.singletonList(buyerId))
            .setContent(Collections.singletonList(new RichTextContent(AssScheduleNode.SEND_QUESTION.getMsg())));
        DubboResultAssert.wrapException(() -> sendCommonMsgServiceI.sendRichTextMsg(cardReqDTO), ErrorCode.RPC_ERROR);

        // 发融云消息
        GroupNotifyMsgReqDTO msgDto = new GroupNotifyMsgReqDTO();
        msgDto.setGroupId(roomId);
        msgDto.setFromUserId(customerId);
        msgDto.setContent(AssScheduleNode.SEND_QUESTION.getId());
        msgDto.setOperation("after_sale_node_message");
        sendMsgServiceI.sendGroupNotify(msgDto);
        return true;

    }

}
