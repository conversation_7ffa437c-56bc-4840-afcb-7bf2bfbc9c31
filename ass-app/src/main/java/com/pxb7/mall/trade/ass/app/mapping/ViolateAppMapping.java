package com.pxb7.mall.trade.ass.app.mapping;

import com.pxb7.mall.trade.ass.app.dto.response.violate.ViolateOrderRespDTO;
import com.pxb7.mall.trade.ass.domain.violate.model.ViolateOrderBO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface ViolateAppMapping {

    ViolateAppMapping INSTANCE = Mappers.getMapper(ViolateAppMapping.class);

    @Mapping(target = "refundApplyTime",source = "createTime")
    @Mapping(target = "createUserName",source = "createUsername")
    ViolateOrderRespDTO toViolateOrderRespDTO(ViolateOrderBO violateOrderBO);

    List<ViolateOrderRespDTO> toViolateOrderRespDTOList(List<ViolateOrderBO> violateOrderBOList);
}
