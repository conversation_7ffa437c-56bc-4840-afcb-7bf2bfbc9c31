package com.pxb7.mall.trade.ass.app.dto.response;

import java.util.Set;

import lombok.Data;

@Data
public class RefundInitInfoRespDTO {

    /**
     * 子订单id
     */
    private String orderItemId;

    /**
     * 最大退款金额
     */
    private Long refundAmount;

    /**
     * 最大退款号价金额
     */
    private Long maxProductAmount;

    /**
     * 最大退款包赔金额
     */
    private Long maxIndemnityAmount;

    /**
     * 最大退款手续费金额
     */
    private Long maxFeeAmount;

    /**
     * 默认违约金额:订单商品的5%
     */
    private Long defaultViolateAmount;

    /**
     * 订单支付方式，用于帮助前端限制退款方式，实际情况仍需后台校验 支付模式 1线上支付 2线下支付 3挂账
     *
     */
    private Set<Integer> payModels;

    /**
     * 用于帮助前端限制退款类型 1 整单退款 2.退商品差价
     */
    private Set<Integer> wholeRefund;

}
