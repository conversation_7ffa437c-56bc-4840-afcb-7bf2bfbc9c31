package com.pxb7.mall.trade.ass.app.dto.response.assSchedule;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;


@Data
public class RespAssScheduleNodeDTO {

    /**
     * 节点id
     */
    private String nodeId;

    /**
     * 当前节点状态描述
     */
    private String nodeDesc;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    /**
     * 创建用户编号
     */
    private String createUserId;

    /**
     * 其他数据
     */
    private String data;

    /**
     * 节点颜色1绿色2黄色3红色
     */
    private Integer color;
    /**
     * 描述(绿色字体)
     */
    private String desc;
}
