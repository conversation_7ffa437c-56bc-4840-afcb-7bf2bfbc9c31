package com.pxb7.mall.trade.ass.app.dto.response;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.ToString;


/**
 * 售后找回游戏配置(AssRetrieveGameConfig)实体类
 *
 * <AUTHOR>
 * @since 2025-08-01 14:12:48
 */
public class AssRetrieveGameConfigRespDTO {


    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class DetailDTO {
        /**
         * 自增id
         */
        private Long id;
        /**
         * 业务主键
         */
        private String gameConfigId;
        /**
         * 找回处理时间单位天
         */
        private Integer retrieveDays;
        /**
         * 是否开启群通知 1:已开启 0:未开启
         */
        private Boolean groupOpen;
    }
}

