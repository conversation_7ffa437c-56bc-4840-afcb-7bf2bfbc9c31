package com.pxb7.mall.trade.ass.app;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.cola.exception.BizException;
import com.pxb7.mall.components.redis.redisson.RLockUtils;
import com.pxb7.mall.trade.ass.app.dto.reqeust.violate.ViolateApplyDTO;
import com.pxb7.mall.trade.ass.app.dto.reqeust.violate.ViolateQueryDTO;
import com.pxb7.mall.trade.ass.app.dto.reqeust.violate.ViolateUpdateDTO;
import com.pxb7.mall.trade.ass.app.dto.response.violate.ViolateOrderRespDTO;
import com.pxb7.mall.trade.ass.app.mapping.ViolateAppMapping;
import com.pxb7.mall.trade.ass.client.lock.ViolateLockConstant;
import com.pxb7.mall.trade.ass.domain.order.OrderItemDomainService;
import com.pxb7.mall.trade.ass.domain.violate.ViolateDomainService;
import com.pxb7.mall.trade.ass.domain.violate.ViolateOrderDomainService;
import com.pxb7.mall.trade.ass.domain.violate.manager.ViolateApplyManager;
import com.pxb7.mall.trade.ass.domain.violate.model.ViolateOrderBO;
import com.pxb7.mall.trade.ass.domain.violate.request.ViolateApplyReqBO;
import com.pxb7.mall.trade.ass.domain.violate.request.ViolateUpdateReqBO;
import com.pxb7.mall.trade.ass.infra.enums.ErrorCode;
import com.pxb7.mall.trade.ass.infra.exception.BusinessException;
import com.pxb7.mall.trade.ass.infra.model.UserBaseInfo;
import com.pxb7.mall.trade.ass.infra.repository.db.OrderItemRepository;
import com.pxb7.mall.trade.ass.infra.repository.db.RefundVoucherRepository;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.OrderItem;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.ViolateOrder;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class ViolateAppService {
    @Resource
    private ViolateApplyManager violateApplyManager;
    @Resource
    private OrderItemDomainService orderItemDomainService;
    @Resource
    private ViolateOrderDomainService violateOrderDomainService;
    @Resource
    private OrderItemRepository orderItemRepository;
    @Resource
    private ViolateDomainService violateDomainService;
    @Resource
    private RefundVoucherRepository refundVoucherRepository;

    public void update(ViolateUpdateDTO violateUpdateDTO, UserBaseInfo userBaseInfo) {
        // 订单
        ViolateOrder violateOrder = violateOrderDomainService.getDetailById(violateUpdateDTO.getViolateId());
        if (ObjectUtil.isEmpty(violateOrder)) {
            throw new BusinessException(ErrorCode.VIOLATE_EDIT_ERROR);
        }
        String lockKey = ViolateLockConstant.getOrderLockKey(violateOrder.getOrderItemId());
        ViolateUpdateReqBO bo = new ViolateUpdateReqBO()
                .setViolateId(violateUpdateDTO.getViolateId())
                .setViolateAmount(violateUpdateDTO.getViolateAmount())
                .setPromiseAmount(violateUpdateDTO.getPromiseAmount())
                .setCreateUserId(userBaseInfo.getUserId())
                .setCreateUserName(userBaseInfo.getUserName());
        RLockUtils.of(lockKey, () -> violateApplyManager.violateEdit(bo, userBaseInfo))
                .withWaitTime(2).withTimeUnit(TimeUnit.SECONDS)
                .orElseThrow(() -> new BizException(ErrorCode.REPEAT_OPERATION.getErrCode(), ErrorCode.REPEAT_OPERATION.getErrDesc()));
    }

    public List<ViolateOrderRespDTO> list(ViolateQueryDTO queryDTO, UserBaseInfo userInfo) {
        String orderItemId = queryDTO.getOrderItemId();
        orderItemDomainService.checkPermission(orderItemId, userInfo);
        List<ViolateOrderBO> violateOrderBOList = violateOrderDomainService.getEffectiveListByOrderItemId(orderItemId);
        return ViolateAppMapping.INSTANCE.toViolateOrderRespDTOList(violateOrderBOList);
    }

    /**
     * Initiate violation payment collection
     *
     * @param applyDTO violation application parameters
     * @param userInfo user information
     * @return violation order
     */
    public ViolateOrder apply(ViolateApplyDTO applyDTO, UserBaseInfo userInfo) {
        log.info("[违约收款-发起开始] applyDTO:{}, userInfo:{}", applyDTO, userInfo);

        // 构建请求参数
        ViolateApplyReqBO bo = new ViolateApplyReqBO()
                .setOrderItemId(applyDTO.getOrderItemId())
                .setViolateUserType(applyDTO.getUserType())
                .setViolateAmount(applyDTO.getViolateAmount())
                .setPromiseAmount(applyDTO.getPromiseAmount())
                .setCreateUserId(userInfo.getUserId())
                .setCreateUserName(userInfo.getUserName());

        String lockKey = ViolateLockConstant.getOrderLockKey(applyDTO.getOrderItemId());
        ViolateOrder violateOrder = RLockUtils.of(lockKey, () -> violateApplyManager.createViolateAndDeal(bo))
                .withWaitTime(2)
                .withTimeUnit(TimeUnit.SECONDS)
                .orElseThrow(() -> new BizException(ErrorCode.REPEAT_OPERATION.getErrCode(),
                        ErrorCode.REPEAT_OPERATION.getErrDesc()));

        log.info("[违约收款-发起成功] orderItemId:{}, violateId:{}", applyDTO.getOrderItemId(), violateOrder.getViolateId());
        return violateOrder;
    }

    public boolean getViolateByOrder(String orderItemId) {
        OrderItem orderItem = orderItemRepository.getOrderItem(orderItemId);
        if (ObjectUtil.isEmpty(orderItem)) {
            return false;
        }
        return !ObjectUtil.isNotEmpty(violateOrderDomainService.violateDealingByOrder(orderItemId));
    }
}