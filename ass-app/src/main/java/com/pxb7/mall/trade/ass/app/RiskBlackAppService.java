package com.pxb7.mall.trade.ass.app;

import com.pxb7.mall.trade.ass.domain.violate.ViolateDomainService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class RiskBlackAppService {

    @Resource
    private ViolateDomainService violateDomainService;

    public void addViolateBlackRecord(String violateId) {
        violateDomainService.addViolateBlackRecord(violateId);
    }


    public void removeViolateBlackRecord(String violateId) {
        violateDomainService.removeViolateBlackRecord(violateId);
    }


}
