package com.pxb7.mall.trade.ass.app.dto.reqeust.complaint.req;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.io.Serializable;

/**
 * Copyright (C), 2002-2024, 螃蟹游戏服务网
 *
 * @fileName: ComplaintWorkOrderFinishReqDTO.java
 * @description: 工单完结请求的DTO
 * @author: guominqiang
 * @email: <EMAIL>
 * @date: 2024/9/20 21:28
 * @history: //修改记录
 * <author> <time> <version> <desc>
 * 修改人姓名 修改时间 版本号 描述
 */
@Data
public class ComplaintWorkOrderFinishReqDTO implements Serializable {

    /**
     * 房间id
     */
    @NotBlank(message = "房间id不能为空")
    private String roomId;

    /**
     * 工单id
     */
    @NotBlank(message = "工单id不能为空")
    private String complaintWorkId;

    /**
     * 订单id
     */
    //@NotBlank(message = "订单id不能为空")
    private String orderItemId;

    /**
     * 处理结果
     */
    @NotBlank(message = "处理结果必填")
    @Length(max = 500, message = "不能超过500字")
    private String dealResult;

    /**
     * 备注
     */
    @Length(max = 500, message = "不能超过500字")
    private String note;

    /**
     * 问题部门名称
     */
    @NotBlank(message = "问题部门必传")
    private String departmentName;

    /**
     * 问题类型
     */
    @NotBlank(message = "问题类型必传")
    private String questionType;

    /**
     * 问题级别:1-6 1:一级 2:二级 ...6:六级
     */
    @NotNull(message = "问题级别必传")
    private Integer questionLevel;

    /**
     * 责任人
     */
    @NotBlank(message = "责任人必传")
    private String responsiblePerson;

    /**
     * 是否有责 0:否 1是
     */
    @NotNull(message = "是否有责必传")
    private Boolean responsibility;

}
