package com.pxb7.mall.trade.ass.app.mapping;

import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import com.pxb7.mall.trade.ass.app.dto.response.RoomRefundListRespDTO;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.RefundVoucher;

@Mapper
public interface OrderRefundAppMapping {

    OrderRefundAppMapping INSTANCE = Mappers.getMapper(OrderRefundAppMapping.class);

    List<RoomRefundListRespDTO> toRoomRefundListRespDTO(List<RefundVoucher> refundReceipt);

}
