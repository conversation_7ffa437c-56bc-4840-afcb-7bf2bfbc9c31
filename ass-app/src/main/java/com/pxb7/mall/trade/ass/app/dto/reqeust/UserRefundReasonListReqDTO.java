package com.pxb7.mall.trade.ass.app.dto.reqeust;

import lombok.Data;

@Data
public class UserRefundReasonListReqDTO {

    /**
     * 退款原因id
     */
    private String reasonRefundId;

    /**
     * 退款类型，1，全额退款，2部分退款
     */
    private Integer refundType;
    /**
     * 挽留策略，1: 自动推荐替换商品，2员工撮合卖家降价，3不挽留
     */
    private Integer reasonIntercept;
    /**
     * 商品自动上架1:是,0:否
     */
    private Boolean autoGrounding;
    /**
     * 适用范围:1用户端申请退款,2客户端申请退款,3全部
     */
    private Integer reasonRange;
}
