package com.pxb7.mall.trade.ass.app.dto.reqeust;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.Data;

@Data
public class RefundViolateDetailReqDTO {

    /**
     * 违约方:1买家 2卖家
     */
    @NotNull(message = "违约方userType不能为空")
    private Integer userType;

    /**
     * 违约金
     */
    @NotNull(message = "违约金额必填")
    @Positive(message = "违约金额必须大于0")
    private Long violateAmount;

    /**
     * 守约金
     */
    private Long promiseAmount = 0L;

}
