package com.pxb7.mall.trade.ass.app.dto.response.violate;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class ViolateOrderRespDTO {

    private String orderItemId;

    /**
     *  退单时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime refundApplyTime;

    /**
     *  违约单Id
     */
    private String violateId;


    /**
     *  关联退款编号
     */
    private String refundVoucherId;

    /**
     * 创建人Id (制单客服Id)
     */
    private String createUserId;

    /**
     * 创建人名称 (制单客服名称)
     */
    private String createUserName;

    /**
     * 违约方:1买家、2卖家
     */
    private String violateUserType;

    /**
     * 违约金,分
     */
    private Integer violateAmount;

    /**
     * 守约金,分
     */
    private Integer promiseAmount;

    /**
     * 平台金额,分
     */
    private Integer platformAmount;

    /**
     * 违约单聚合状态
     */
    private Integer violateAggregationStatus;

    /**
     * 违约单状态:1待执行、2执行中、3执行成功、4执行失败、5取消
     */
    private Integer  violateStatus;
    /**
     * 收款状态:1待缴款、2缴款成功、3缴款失败、4缴款中断、5缴款取消
     */
    private Integer  receiptStatus;
    /**
     * 打款状态:1打款中、2打款成功、3打款失败
     */
    private Integer  transferStatus;













}
