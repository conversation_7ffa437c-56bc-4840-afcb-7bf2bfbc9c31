package com.pxb7.mall.trade.ass.app.dto.response;

import java.io.Serializable;
import java.time.*;
import java.util.List;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.ToString;
import lombok.experimental.FieldNameConstants;
import org.springframework.data.elasticsearch.annotations.DateFormat;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;


/**
 * 售后工单(AssWorkOrder)实体类
 *
 * <AUTHOR>
 * @since 2025-07-30 14:00:39
 */
public class AssWorkOrderRespDTO {


    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class DetailDTO {
        /**
         * 自增id
         */
        private Long id;
        /**
         * 售后工单号
         */
        private String workOrderId;

        /**
         * 订单ID
         */
        private String orderItemId;
        /**
         * 订单ID
         */
        private String roomId;
        /**
         * 售后类型1找回2纠纷
         */
        private Integer assType;
        /**
         * 关联找回/纠纷工单号
         */
        private String relOrderId;
        /**
         * 0:处理中 1:已完成 2:已取消
         */
        private Integer assStatus;
        /**
         * 售后工单状态备注
         */
        private String assStatusMemo;
        /**
         * 发起售后申请时间
         */
        private LocalDateTime applyTime;
        /**
         * 预计完成时间
         */
        private LocalDateTime expectedTime;
        /**
         * 完成时间
         */
        private LocalDateTime completeTime;
        /**
         * 是否已读 1:已读 0:未读
         */
        private Boolean readFlag;
    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class PageDetailDTO {
        /**
         * 售后单ID
         */
        private String assWorkOrderId;

        /*************************************************订单信息****************************************************/
        /**
         * 订单ID
         */
        private String orderItemId;
        /**
         * 商品id
         */
        private String productId;
        /**
         * 卖家id
         */
        private String buyerUserId;
        /**
         * 游戏ID
         */
        private String gameId;
        /**
         * 商品类型: 1账号 2充值 3金币 4装备 5初始号 20诚心卖
         */
        private Integer productType;
        /**
         * 1待付款 2交易中 3待结算 4已成交 5已取消 (待支付是买家, 其他都是卖家状态) 6退款已取消
         */
        private Integer orderItemStatus;
        /**
         * 订单行金额
         */
        private Long orderItemAmount;
        /**
         * 订单行应付款金额
         */
        private Long orderItemPayAmount;
        /**
         * 订单行实付金额
         */
        private Long orderItemActualPayAmount;
        /**
         * 购买的商品数量
         */
        private Integer productQuantity;
        /**
         * 订单完结时间
         */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime completeTime;
        /**
         * 订单取消时间
         */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime cancelTime;

        // 订单创建时间
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime createTime;
        /**
         * 商品名称
         */
        private String productName;
        /**
         * 商品短标
         */
        private String productShortName;
        /**
         * 商品图片
         */
        private String productPic;
        /**
         * 商品属性
         */
        private String productAttr;
        /**
         * 账号信息
         */
        private String gameAccount;

        private String productUniqueNo;
        /**
         * 游戏名称
         */
        private String gameName;
        /**
         * 游戏名称
         */
        private String gameAttr;
        /**
         * 交付群聊房间id, 智能交付是新群交付, 中介订单是原群交付
         */
        private String deliveryRoomId;

        /**
         * 最新售后工单日志
         */
        private AssWorkOrderLogDTO assLatestWorkOrderLog;

        /**
         * 售后类型: 1:找回 2:纠纷
         */
        private Integer assType;

        /**
         * 售后状态: 0:处理中 1:已完成 2:已取消
         */
        private Integer assStatus;

        /**
         * 售后工单状态备注
         */
        private String assStatusMemo;

        /**
         * 售后子订单id
         */
        private String assSubOrderId;
        /**
         * 售后完成时间
         */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime assCompleteTime;
        /**
         * 售后期望完成时间
         */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime assExpectedTime;

        /**
         * 售后工单是否已读 0:未读 1:已读
         */
        private Integer readFlag;

        /**
         * 售后申请时间
         */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime assApplyTime;


        /**
         * 售后单更新时间
         */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime assUpdateTime;

        /**
         * 售后创建人id
         */
        private String assCreateUserId;

        /**
         * 是否显示加入群聊按钮
         */
        private Boolean showJoinGroup;
    }

    @Setter
    @Getter
    @Accessors(chain = true)
    public static class AssWorkOrderLogDTO implements Serializable{
        /**
         * 日志id
         */
        private String workOrderLogId;

        /**
         * 日志标题
         */
        private String title;

        /**
         * 售后类型1找回2纠纷
         */
        private Integer assType;
        /**
         * 用户端展示操作内容
         */
        private String content;
        /**
         * 日志添加方式1系统自动产生的日志 2手动添加的日志
         */
        private Integer addWay;

        /**
         * 创建人id
         */
        private String createUserId;
        /**
         * 记录创建时间
         */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime createTime;

        /**
         * 提醒用户进群文案
         */
        private String joinGroupMsg;

    }


    /**
     *售后工单详情
     */
    @Setter
    @Getter
    @Accessors(chain = true)
    public static class AssWorkOrderDetailDTO implements Serializable {

        /**
         * 售后工单id
         */
        private String workOrderId;

        /**
         * 售后申请时间
         */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime assApplyTime;

        /**
         * 售后工单找回处理天数
         */
        private Integer retrieveDays;

        /**
         * 售后工单状态
         * 0:处理中 1:已完成 2:已取消
         */
        private Integer assStatus;

        /**
         * 售后工单状态备注
         */
        private String assStatusMemo;

        /**
         * 售后类型1找回2纠纷
         */
        private Integer assType;


        /**
         * 售后工单进度日志
         */
        private List<AssWorkOrderLogDTO> logList;


        /**
         * 订单ID
         */
        private String orderItemId;
        /**
         * 商品id
         */
        private String productId;
        /**
         * 卖家id
         */
        private String buyerUserId;
        /**
         * 游戏ID
         */
        private String gameId;
        /**
         * 商品类型: 1账号 2充值 3金币 4装备 5初始号 20诚心卖
         */
        private Integer productType;
        /**
         * 1待付款 2交易中 3待结算 4已成交 5已取消 (待支付是买家, 其他都是卖家状态) 6退款已取消
         */
        private Integer orderItemStatus;
        /**
         * 订单行金额
         */
        private Long orderItemAmount;
        /**
         * 订单行应付款金额
         */
        private Long orderItemPayAmount;
        /**
         * 订单行实付金额
         */
        private Long orderItemActualPayAmount;
        /**
         * 购买的商品数量
         */
        private Integer productQuantity;
        /**
         * 订单完结时间
         */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime completeTime;
        /**
         * 订单取消时间
         */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime cancelTime;

        // 订单创建时间
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime createTime;

        /**
         * 商品名称
         */
        private String productName;
        /**
         * 商品短标
         */
        private String productShortName;
        /**
         * 商品图片
         */
        private String productPic;
        /**
         * 商品属性
         */
        private String productAttr;
        /**
         * 账号信息
         */
        private String gameAccount;

        private String productUniqueNo;
        /**
         * 游戏名称
         */
        private String gameName;
        /**
         * 游戏属性
         */
        private String gameAttr;
        /**
         * 交付群聊房间id, 智能交付是新群交付, 中介订单是原群交付
         */
        private String deliveryRoomId;


        /**
         * 包赔服务名称
         */
        private String indemnityNameStr;

        /**
         * 是否是协助服务
         */
        private Boolean isAssistant;

        /**
         * 包赔不生效原因
         */
        private String indemnityReasonDesc;

    }
}

