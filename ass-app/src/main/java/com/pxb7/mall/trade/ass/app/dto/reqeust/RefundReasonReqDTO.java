package com.pxb7.mall.trade.ass.app.dto.reqeust;

import java.util.List;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

public class RefundReasonReqDTO {
    @Data
    public static class InsertDTO {
        /**
         * 一级退款原因
         */
        @NotNull(message = "退款原因不能为空")
        private String reasonRefundContent;
        /**
         * 退款类型，1，全额退款，2部分退款
         */
        private Integer refundType;
        /**
         * 适用范围，二级以一级为准: 1用户端申请退款,2客户端申请退款,3全部
         */
        private Integer reasonRange;
        /**
         * 挽留策略，1: 自动推荐替换商品，2员工撮合卖家降价，3不挽留
         */
        private Integer reasonIntercept;
        /**
         * 商品自动上架1:是,0:否
         */
        private Boolean autoGrounding;
        /**
         * 二集群退款原因，没有二级可为空
         */
        private List<InsertDTO> secondReasonList;
    }
}
