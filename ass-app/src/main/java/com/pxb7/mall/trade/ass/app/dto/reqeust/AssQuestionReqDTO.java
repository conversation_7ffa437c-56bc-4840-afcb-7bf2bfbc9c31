package com.pxb7.mall.trade.ass.app.dto.reqeust;

import java.io.Serial;
import java.io.Serializable;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 售后发送问题
 *
 * <AUTHOR>
 * @since: 2024-09-29 17:19
 **/
@Data
public class AssQuestionReqDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = -3897791388712239035L;
    /**
     * 客服ID
     */
    @NotBlank(message = "客服ID不能为空")
    private String customerId;
    /**
     * 房间号
     */
    @NotBlank(message = "房间号不能为空")
    private String roomId;
}
