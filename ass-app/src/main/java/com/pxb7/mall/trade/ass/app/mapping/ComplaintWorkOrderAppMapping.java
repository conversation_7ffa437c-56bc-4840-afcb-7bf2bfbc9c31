package com.pxb7.mall.trade.ass.app.mapping;

import com.pxb7.mall.trade.ass.app.dto.reqeust.complaint.req.ComplaintWorkOrderFinishReqDTO;
import com.pxb7.mall.trade.ass.app.dto.reqeust.complaint.req.ComplaintWorkOrderReqDTO;
import com.pxb7.mall.trade.ass.app.dto.reqeust.complaint.req.ComplaintWorkOrderTransferReqDTO;
import com.pxb7.mall.trade.ass.app.dto.reqeust.complaint.resp.ComplaintDepartmentConfigRespDTO;
import com.pxb7.mall.trade.ass.app.dto.reqeust.complaint.resp.ComplaintWorkLogRespDTO;
import com.pxb7.mall.trade.ass.app.dto.reqeust.complaint.resp.ComplaintWorkOrderDetailRespDTO;
import com.pxb7.mall.trade.ass.app.dto.reqeust.complaint.resp.ComplaintWorkOrderRespDTO;
import com.pxb7.mall.trade.ass.client.dto.request.CreateWorkOrderReqDTO;
import com.pxb7.mall.trade.ass.client.dto.request.FinishWorkOrderReqDTO;
import com.pxb7.mall.trade.ass.client.dto.request.TransferWorkOrderReqDTO;
import com.pxb7.mall.trade.ass.client.dto.response.DepartmentConfigRespDTO;
import com.pxb7.mall.trade.ass.client.dto.response.WorkOrderDetailRespDTO;
import com.pxb7.mall.trade.ass.client.dto.response.WorkOrderLogRespDTO;
import com.pxb7.mall.trade.ass.client.dto.response.afc.ComplaintWORespDTO;
import com.pxb7.mall.trade.ass.domain.model.response.ComplaintWorkOrderDetailRespBO;
import com.pxb7.mall.trade.ass.domain.model.response.ComplaintWorkOrderLogRespBO;
import com.pxb7.mall.trade.ass.domain.model.response.ComplaintWorkOrderRespBO;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.ComplaintDepartmentConfig;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.ComplaintWork;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.ComplaintWorkLog;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * Copyright (C), 2002-2024, 螃蟹游戏服务网
 *
 * @fileName: ComplaintWorkOrderAppMapping.java
 * @description: 客诉工单Mapping
 * @author: guominqiang
 * @email: <EMAIL>
 * @date: 2024/9/20 17:32
 * @history: //修改记录
 * <author> <time> <version> <desc>
 * 修改人姓名 修改时间 版本号 描述
 */
@Mapper
public interface ComplaintWorkOrderAppMapping {
    ComplaintWorkOrderAppMapping INSTANCE = Mappers.getMapper(ComplaintWorkOrderAppMapping.class);

    ComplaintWork toComplaintWork(ComplaintWorkOrderReqDTO complaintWorkOrderReqDTO);

    ComplaintWorkOrderDetailRespDTO toComplaintWorkOrderDetailRespDTO(ComplaintWorkOrderDetailRespBO complaintWorkOrderDetailRespBO);

    List<ComplaintDepartmentConfigRespDTO> toComplaintDepartmentConfigRespDTOList(List<ComplaintDepartmentConfig> complaintDepartmentConfigList);

    List<ComplaintWorkOrderRespDTO> toComplaintWorkOrderRespDTOList(List<ComplaintWorkOrderRespBO> complaintWorkOrderList);

    ComplaintWorkOrderReqDTO toComplaintWorkOrderReqDTO(CreateWorkOrderReqDTO createWorkOrderReqDTO);

    ComplaintWorkOrderFinishReqDTO toComplaintWorkOrderFinishReqDTO(FinishWorkOrderReqDTO finishWorkOrderReqDTO);

    ComplaintWorkOrderTransferReqDTO toComplaintWorkOrderTransferReqDTO(TransferWorkOrderReqDTO transferWorkOrderReqDTO);

    WorkOrderDetailRespDTO toWorkOrderDetailRespDTO(ComplaintWorkOrderDetailRespDTO complaintWorkOrderDetailRespDTO);

    List<WorkOrderLogRespDTO> toWorkOrderLogRespDTOList(List<ComplaintWorkLogRespDTO> complaintWorkLogRespDTOList);

    List<DepartmentConfigRespDTO> toDepartmentConfigRespDTOList(List<ComplaintDepartmentConfigRespDTO> complaintDepartmentConfigRespDTOList);

    @Mappings({@Mapping(source = "complaintWorkId", target = "workOrderId")})
    ComplaintWORespDTO toComplaintWORespDTO(ComplaintWork complaintWork);
}
