package com.pxb7.mall.trade.ass.app.dto.reqeust;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;

/**
 * Copyright (C), 2002-2025, 螃蟹游戏服务网
 *
 * @fileName: ApplyAfcReqDTO.java
 * @description: 用户申请售后请求参数
 * @author: guo<PERSON><PERSON><PERSON>
 * @email: <EMAIL>
 * @date: 2025/4/10 13:58
 * @history: //修改记录
 * <author> <time> <version> <desc>
 * 修改人姓名 修改时间 版本号 描述
 */
@Data
@Slf4j
public class ApplyAfcReqDTO implements Serializable {
    /**
     * 订单ID
     */
    @NotBlank(message = "订单id不能为空")
    private String orderItemId;

    /**
     * 游戏id
     */
    @NotBlank(message = "游戏id不能为空")
    private String gameId;

    /**
     * 游戏名称
     */
    @NotBlank(message = "游戏名称不能为空")
    private String gameName;
    /**
     * 商品id
     */
    @NotBlank(message = "商品id不能为空")
    private String productId;

    /**
     * 商品编码
     */
    @NotBlank(message = "商品编码不能为空")
    private String productUniqueNo;

    /**
     * 售后问题id
     */
    @NotBlank(message = "售后问题id不能为空")
    private String questionId;

    /**
     * 交付房间id(订单列表中有的必传)
     */
    private String deliveryRoomId;
    /**
     * 备注(其他问题，用户输入的内容)
     */
    private String remark;
}
