package com.pxb7.mall.trade.ass.app.dto.reqeust.violate;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.Data;

@Data
public class ViolateApplyDTO {

    /**
     * 订单Id
     */
    @NotNull(message = "订单id不可为空")
    private String orderItemId;

    /**
     * 退款单Id
     */
    private String refundVoucherId;

    /**
     * 违约方:1买家 2卖家
     */
    @NotNull(message = "违约方类型不可为空")
    private Integer userType;

    /**
     * 违约金额，分
     */
    @NotNull(message = "违约金额不可为空")
    @Positive(message = "违约金额必须大于0")
    private Long violateAmount;

    /**
     * 守约金额，分
     */
    private Long promiseAmount;
}
