package com.pxb7.mall.trade.ass.app.dto.reqeust;

import java.io.Serial;
import java.io.Serializable;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 售后问题回答
 *
 * <AUTHOR>
 * @since: 2024-08-13 17:22
 **/
@Data
public class AddAssAnswerReqDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = 4630038962075007492L;
    /**
     * 答案 [{"label":"","type":"","value":""}]
     */
    @NotBlank(message = "答案不能为空")
    private String answer;
    /**
     * 房间号
     */
    @NotBlank(message = "房间号不能为空")
    private String roomId;
}
