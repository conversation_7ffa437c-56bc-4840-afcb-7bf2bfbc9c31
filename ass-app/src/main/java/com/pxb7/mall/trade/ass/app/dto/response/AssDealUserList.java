package com.pxb7.mall.trade.ass.app.dto.response;

import java.io.Serial;
import java.io.Serializable;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 处理人员列表
 *
 * <AUTHOR>
 * @since: 2024-10-01 15:46
 **/
@Data
@Accessors(chain = true)
public class AssDealUserList implements Serializable {

    @Serial
    private static final long serialVersionUID = -570295648247457570L;
    private String userId;
    private String userName;
}
