package com.pxb7.mall.trade.ass.app;


import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.cola.exception.BizException;
import com.alibaba.fastjson2.JSON;
import com.pxb7.mall.auth.c.util.MerchantUserUtil;
import com.pxb7.mall.components.redis.redisson.RLockUtils;
import com.pxb7.mall.response.PxPageResponse;
import com.pxb7.mall.response.PxResponse;
import com.pxb7.mall.trade.ass.app.common.UserIdUtils;
import com.pxb7.mall.trade.ass.client.enums.AssOperatorLog;
import com.pxb7.mall.trade.ass.client.enums.AssScheduleNode;
import com.pxb7.mall.trade.ass.client.enums.AssWoType;
import com.pxb7.mall.trade.ass.client.enums.AssWorkOrderStatusEnum;
import com.pxb7.mall.trade.ass.domain.*;
import com.pxb7.mall.trade.ass.domain.model.AssWorkOrderLogReqBO;
import com.pxb7.mall.trade.ass.domain.model.reqeust.RecordBO;
import com.pxb7.mall.trade.ass.infra.constant.AfcConstant;
import com.pxb7.mall.trade.ass.infra.enums.ErrorCode;
import com.pxb7.mall.trade.ass.infra.exception.Ensure;
import com.pxb7.mall.trade.ass.infra.repository.db.AssDisputeWoRepository;
import com.pxb7.mall.trade.ass.infra.repository.db.AssRetrieveWoRepository;
import com.pxb7.mall.trade.ass.infra.util.IdGenUtil;
import jakarta.annotation.Resource;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Service;

import com.pxb7.mall.trade.ass.app.dto.reqeust.AssWorkOrderReqDTO;
import com.pxb7.mall.trade.ass.app.dto.response.AssWorkOrderRespDTO;
import com.pxb7.mall.trade.ass.app.mapping.AssWorkOrderAppMapping;
import com.pxb7.mall.trade.ass.domain.model.AssWorkOrderReqBO;
import com.pxb7.mall.trade.ass.domain.model.AssWorkOrderRespBO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.transaction.support.TransactionTemplate;


/**
 * 售后工单app服务
 *
 * <AUTHOR>
 * @since 2025-07-30 14:00:39
 */
@Slf4j
@Service
public class AssWorkOrderAppService {

    @Resource
    private AssWorkOrderDomainService assWorkOrderDomainService;

    @Resource
    private AssScheduleDomainService assScheduleDomainService;

    @Resource
    private AssScheduleLogDomainService assScheduleLogDomainService;

    @Resource
    private AssWorkOrderLogDomainService workOrderLogDomainService;
    @Resource
    private AssDisputeWoRepository assDisputeWoRepository;
    @Resource
    private AssRetrieveWoRepository assRetrieveWoRepository;

    @Resource
    private AssFollowupLogDomainService assFollowupLogDomainService;

    @Resource
    private TransactionTemplate transactionTemplate;


    public PxPageResponse<AssWorkOrderRespDTO.PageDetailDTO> page(AssWorkOrderReqDTO.PageOrderDTO param) {
        AssWorkOrderReqBO.PageAssOrderBO paramBo = AssWorkOrderAppMapping.INSTANCE.assWorkOrderDTO2PageAssOrderBO(param);
        if (BooleanUtil.isTrue(param.getIsMerchant())) {
            String userId = MerchantUserUtil.getUserId();
            Ensure.that(StrUtil.isNotBlank(userId)).isTrue(ErrorCode.ASS_USER_GET_FAIL);
            paramBo.setMerchantUserId(userId);
        } else {
            String userId = UserIdUtils.getUserId();
            Ensure.that(StrUtil.isNotBlank(userId)).isTrue(ErrorCode.ASS_USER_GET_FAIL);
            paramBo.setUserIdList(Collections.singletonList(userId));
        }
        log.info("page ass param:{}", JSON.toJSONString(paramBo));
        PxPageResponse<AssWorkOrderRespBO.AssInfoPageDetailBO> response = assWorkOrderDomainService.pageAssInfoFromEs(paramBo);
        List<AssWorkOrderRespDTO.PageDetailDTO> list = AssWorkOrderAppMapping.INSTANCE.assInfoPageDetailBO2PageDetailDTO(response.getData());
        return PxPageResponse.of(list, response.getTotalCount(), param.getPageSize(), param.getPageIndex());
    }


    public boolean insert(AssWorkOrderReqDTO.AddDTO param) {
        AssWorkOrderReqBO.AddBO addBO = AssWorkOrderAppMapping.INSTANCE.assWorkOrderDTO2AddBO(param);
        return assWorkOrderDomainService.insert(addBO);
    }

    public boolean update(AssWorkOrderReqDTO.UpdateDTO param) {
        AssWorkOrderReqBO.UpdateBO updateBO = AssWorkOrderAppMapping.INSTANCE.assWorkOrderDTO2UpdateBO(param);
        return assWorkOrderDomainService.update(updateBO);
    }

    public List<AssWorkOrderRespDTO.DetailDTO> list(AssWorkOrderReqDTO.SearchDTO param) {
        AssWorkOrderReqBO.SearchBO searchBO = AssWorkOrderAppMapping.INSTANCE.assWorkOrderDTO2SearchBO(param);
        List<AssWorkOrderRespBO.DetailBO> list = assWorkOrderDomainService.list(searchBO);
        return AssWorkOrderAppMapping.INSTANCE.assWorkOrderBO2ListDTO(list);
    }

    public Page<AssWorkOrderRespDTO.DetailDTO> page(AssWorkOrderReqDTO.PageDTO param) {
        AssWorkOrderReqBO.PageBO pageBO = AssWorkOrderAppMapping.INSTANCE.assWorkOrderDTO2PageBO(param);
        Page<AssWorkOrderRespBO.DetailBO> page = assWorkOrderDomainService.page(pageBO);
        return AssWorkOrderAppMapping.INSTANCE.assWorkOrderBO2PageDTO(page);
    }

    public PxResponse<AssWorkOrderRespDTO.AssWorkOrderDetailDTO> findOrderDetail(String orderNo) {
        return PxResponse.ok(null);
    }

    public Boolean cancel(String orderNo, String userId) {
        log.info("cancel,orderNo:{},userId:{}", orderNo, userId);
        Ensure.that(StrUtil.isNotBlank(orderNo)).isTrue(ErrorCode.NO_ORDER);
        return RLockUtils.of(AfcConstant.getAfcOrderLockKey(orderNo), () -> {
                    //根据交易订单号查询最新的售后工单
                    AssWorkOrderRespBO.DetailBO workOrder = assWorkOrderDomainService.findByOrderItemId(orderNo);
                    if (workOrder == null) {
                        log.warn("cancel 未查询到售后工单orderNo,{} userId:{}", orderNo, userId);
                        return false;
                    }
                    Ensure.that(Objects.equals(workOrder.getAssStatus(), AssWorkOrderStatusEnum.PROCESS.getCode()))
                            .isTrue(ErrorCode.ASS_CANCEL_UN_VALID);
                    return doCancel(userId, workOrder);
                }).withWaitTime(3).withTimeUnit(TimeUnit.SECONDS)
                .orElseThrow(() -> new BizException(ErrorCode.ASS_HAVE_OTHER_FAIL.getErrCode(),
                        ErrorCode.ASS_HAVE_OTHER_FAIL.getErrDesc()));

    }

    private Boolean doCancel(String userId, AssWorkOrderRespBO.DetailBO workOrder) {
        return transactionTemplate.execute(transactionStatus -> {
            boolean result = assWorkOrderDomainService.updateWithOpt(workOrder.getId(), AssWorkOrderStatusEnum.PROCESS.getCode(), AssWorkOrderStatusEnum.CANCELED.getCode(), "用户取消");
            if (!result) {
                return false;
            }

            AssWoType assWoType = AssWoType.fromCode(workOrder.getAssType());
            // 更新工单状态
            String scheduleId = workOrder.getScheduleId();
            if (StrUtil.isNotBlank(scheduleId)) {
                assScheduleDomainService.updateFinishByScheduleId(scheduleId, AssScheduleNode.CANCEL_WORK_ORDER);
            }

            String relOrderId = workOrder.getRelOrderId();
            if (StrUtil.isBlank(relOrderId)) {
                return true;
            }

            if (Objects.equals(AssWoType.RETRIEVE, assWoType)) {
                //取消找回子单据
                assRetrieveWoRepository.userCancel(relOrderId);
            }

            if (Objects.equals(AssWoType.DISPUTE, assWoType)) {
                //取消纠纷子单据
                assDisputeWoRepository.userCancel(relOrderId);
            }
            RecordBO recordBO = new RecordBO()
                    .setWorkOrderId(relOrderId)
                    .setAssType(assWoType.getCode())
                    .setTitle(AssOperatorLog.COMPLETE_WO.getDesc())
                    .setContent(AssOperatorLog.COMPLETE_WO.getDesc())
                    .setOperatorId(userId);
            assFollowupLogDomainService.recordLog(recordBO);
            return true;
        });
    }


    public int unreadCount(String userId) {
        if (StrUtil.isBlank(userId)) {
            return 0;
        }

        return 0;
    }
}

