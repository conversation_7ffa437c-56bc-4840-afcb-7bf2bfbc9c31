package com.pxb7.mall.trade.ass.app;


import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.cola.exception.BizException;
import com.alibaba.fastjson2.JSON;
import com.pxb7.mall.auth.c.util.MerchantUserUtil;
import com.pxb7.mall.components.redis.redisson.RLockUtils;
import com.pxb7.mall.response.PxPageResponse;
import com.pxb7.mall.response.PxResponse;
import com.pxb7.mall.trade.ass.app.common.UserIdUtils;
import com.pxb7.mall.trade.ass.client.enums.*;
import com.pxb7.mall.trade.ass.domain.*;
import com.pxb7.mall.trade.ass.domain.model.AssWorkOrderLogReqBO;
import com.pxb7.mall.trade.ass.domain.model.AssWorkOrderLogRespBO;
import com.pxb7.mall.trade.ass.domain.model.reqeust.RecordBO;
import com.pxb7.mall.trade.ass.infra.constant.AfcConstant;
import com.pxb7.mall.trade.ass.infra.enums.ErrorCode;
import com.pxb7.mall.trade.ass.infra.exception.Ensure;
import com.pxb7.mall.trade.ass.infra.remote.dubbo.OrderItemRpcGateway;
import com.pxb7.mall.trade.ass.infra.repository.db.AssDisputeWoRepository;
import com.pxb7.mall.trade.ass.infra.repository.db.AssRetrieveGameConfigDetailRepository;
import com.pxb7.mall.trade.ass.infra.repository.db.AssRetrieveWoRepository;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.AssRetrieveGameConfigDetail;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.AssRetrieveWo;
import com.pxb7.mall.trade.ass.infra.util.IdGenUtil;
import com.pxb7.mall.trade.ass.infra.util.OptionalUtil;
import com.pxb7.mall.trade.order.client.dto.request.orderItemIndemnity.OrderItemIndemnityDubboDTO;
import com.pxb7.mall.trade.order.client.dto.response.order.dubbo.OrderInfoDubboRespDTO;
import jakarta.annotation.Resource;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Service;

import com.pxb7.mall.trade.ass.app.dto.reqeust.AssWorkOrderReqDTO;
import com.pxb7.mall.trade.ass.app.dto.response.AssWorkOrderRespDTO;
import com.pxb7.mall.trade.ass.app.mapping.AssWorkOrderAppMapping;
import com.pxb7.mall.trade.ass.domain.model.AssWorkOrderReqBO;
import com.pxb7.mall.trade.ass.domain.model.AssWorkOrderRespBO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.transaction.support.TransactionTemplate;


/**
 * 售后工单app服务
 *
 * <AUTHOR>
 * @since 2025-07-30 14:00:39
 */
@Slf4j
@Service
public class AssWorkOrderAppService {

    @Resource
    private AssWorkOrderDomainService assWorkOrderDomainService;

    @Resource
    private AssScheduleDomainService assScheduleDomainService;

    @Resource
    private AssScheduleLogDomainService assScheduleLogDomainService;

    @Resource
    private AssWorkOrderLogDomainService workOrderLogDomainService;
    @Resource
    private AssDisputeWoRepository assDisputeWoRepository;
    @Resource
    private AssRetrieveWoRepository assRetrieveWoRepository;

    @Resource
    private AssFollowupLogDomainService assFollowupLogDomainService;

    @Resource
    private TransactionTemplate transactionTemplate;

    @Resource
    private OrderItemRpcGateway orderItemRpcGateway;

    @Resource
    private AssRetrieveGameConfigDetailRepository assRetrieveGameConfigDetailRepository ;

    public PxPageResponse<AssWorkOrderRespDTO.PageDetailDTO> page(AssWorkOrderReqDTO.PageOrderDTO param) {
        AssWorkOrderReqBO.PageAssOrderBO paramBo = AssWorkOrderAppMapping.INSTANCE.assWorkOrderDTO2PageAssOrderBO(param);
        if (BooleanUtil.isTrue(param.getIsMerchant())) {
            String userId = MerchantUserUtil.getUserId();
            Ensure.that(StrUtil.isNotBlank(userId)).isTrue(ErrorCode.ASS_USER_GET_FAIL);
            paramBo.setMerchantUserId(userId);
        } else {
            String userId = UserIdUtils.getUserId();
            Ensure.that(StrUtil.isNotBlank(userId)).isTrue(ErrorCode.ASS_USER_GET_FAIL);
            paramBo.setUserIdList(Collections.singletonList(userId));
        }
        log.info("page ass param:{}", JSON.toJSONString(paramBo));
        PxPageResponse<AssWorkOrderRespBO.AssInfoPageDetailBO> response = assWorkOrderDomainService.pageAssInfoFromEs(paramBo);
        List<AssWorkOrderRespDTO.PageDetailDTO> list = AssWorkOrderAppMapping.INSTANCE.assInfoPageDetailBO2PageDetailDTO(response.getData());
        //查询游戏是否开启进群配置
        if (CollUtil.isNotEmpty(list)) {
            Set<String> gameIdSet = list.stream()
                    .filter(detail -> Objects.equals(detail.getAssType(), AssWoType.RETRIEVE.getCode()))
                    .map(AssWorkOrderRespDTO.PageDetailDTO::getGameId).collect(Collectors.toSet());
            //批量查询游戏是否开启进群提醒
            Map<String, Boolean> gameIdGroupOpenMap = OptionalUtil.of(assRetrieveGameConfigDetailRepository.getListBy(gameIdSet))
                    .stream()
                    .collect(Collectors.toMap(AssRetrieveGameConfigDetail::getGameId, AssRetrieveGameConfigDetail::getGroupOpen));
            list.forEach(e -> e.setShowJoinGroup(gameIdGroupOpenMap.getOrDefault(e.getGameId(), false)));
        }
        return PxPageResponse.of(list, response.getTotalCount(), param.getPageSize(), param.getPageIndex());
    }


    public boolean insert(AssWorkOrderReqDTO.AddDTO param) {
        AssWorkOrderReqBO.AddBO addBO = AssWorkOrderAppMapping.INSTANCE.assWorkOrderDTO2AddBO(param);
        return assWorkOrderDomainService.insert(addBO);
    }

    public boolean update(AssWorkOrderReqDTO.UpdateDTO param) {
        AssWorkOrderReqBO.UpdateBO updateBO = AssWorkOrderAppMapping.INSTANCE.assWorkOrderDTO2UpdateBO(param);
        return assWorkOrderDomainService.update(updateBO);
    }

    public List<AssWorkOrderRespDTO.DetailDTO> list(AssWorkOrderReqDTO.SearchDTO param) {
        AssWorkOrderReqBO.SearchBO searchBO = AssWorkOrderAppMapping.INSTANCE.assWorkOrderDTO2SearchBO(param);
        List<AssWorkOrderRespBO.DetailBO> list = assWorkOrderDomainService.list(searchBO);
        return AssWorkOrderAppMapping.INSTANCE.assWorkOrderBO2ListDTO(list);
    }

    public Page<AssWorkOrderRespDTO.DetailDTO> page(AssWorkOrderReqDTO.PageDTO param) {
        AssWorkOrderReqBO.PageBO pageBO = AssWorkOrderAppMapping.INSTANCE.assWorkOrderDTO2PageBO(param);
        Page<AssWorkOrderRespBO.DetailBO> page = assWorkOrderDomainService.page(pageBO);
        return AssWorkOrderAppMapping.INSTANCE.assWorkOrderBO2PageDTO(page);
    }

    public AssWorkOrderRespDTO.AssWorkOrderDetailDTO findOrderDetail(String orderNo) {
        AssWorkOrderRespBO.AssInfoPageDetailBO assInfo = assWorkOrderDomainService.getAssInfoByOrderItemId(orderNo);
        if (Objects.isNull(assInfo)) {
            return null;
        }
        String assWorkOrderId = assInfo.getAssWorkOrderId();
        //查询日志进度信息
        List<AssWorkOrderLogRespBO.DetailBO> logList = workOrderLogDomainService.list(new AssWorkOrderLogReqBO.SearchBO()
                .setWorkOrderId(assWorkOrderId).setShowTypeList(AssShowType.getUserShowTypes()));
        List<OrderItemIndemnityDubboDTO> indemnityList = orderItemRpcGateway.getEffectiveIndemnityListByOrderItemId(orderNo);
        //查询是否是协助工单
        AssRetrieveWo assRetrieveWo = getAssistanceInfo(assInfo);
        return AssWorkOrderAppMapping.INSTANCE.assInfoPageDetailBO2AssWorkOrderDetailDTO(assInfo, indemnityList, assRetrieveWo, logList);
    }

    private AssRetrieveWo getAssistanceInfo(AssWorkOrderRespBO.AssInfoPageDetailBO assInfo) {
        AssWoType assWoType = AssWoType.fromCode(assInfo.getAssType());
        if (Objects.equals(AssWoType.RETRIEVE, assWoType) && StrUtil.isNotBlank(assInfo.getAssSubOrderId())) {
            AssRetrieveWo assRetrieveWo = assRetrieveWoRepository.getByAssRetrieveId(assInfo.getAssSubOrderId());
            return assRetrieveWo;
        }
        return null;
    }

    public Boolean cancel(String orderNo, String userId) {
        log.info("cancel,orderNo:{},userId:{}", orderNo, userId);
        Ensure.that(StrUtil.isNotBlank(orderNo)).isTrue(ErrorCode.NO_ORDER);
        return RLockUtils.of(AfcConstant.getAfcOrderLockKey(orderNo), () -> {
                    //根据交易订单号查询最新的售后工单
                    AssWorkOrderRespBO.DetailBO workOrder = assWorkOrderDomainService.findByOrderItemId(orderNo);
                    if (workOrder == null) {
                        log.warn("cancel 未查询到售后工单orderNo,{} userId:{}", orderNo, userId);
                        return false;
                    }
                    Ensure.that(Objects.equals(workOrder.getAssStatus(), AssWorkOrderStatusEnum.PROCESS.getCode()))
                            .isTrue(ErrorCode.ASS_CANCEL_UN_VALID);
                    return doCancel(userId, workOrder);
                }).withWaitTime(3).withTimeUnit(TimeUnit.SECONDS)
                .orElseThrow(() -> new BizException(ErrorCode.ASS_HAVE_OTHER_FAIL.getErrCode(),
                        ErrorCode.ASS_HAVE_OTHER_FAIL.getErrDesc()));

    }

    private Boolean doCancel(String userId, AssWorkOrderRespBO.DetailBO workOrder) {
        return transactionTemplate.execute(transactionStatus -> {
            boolean result = assWorkOrderDomainService.updateWithOpt(workOrder.getId(), AssWorkOrderStatusEnum.PROCESS.getCode(), AssWorkOrderStatusEnum.CANCELED.getCode(), AssUserOperatorLog.USER_CLOSE_ORDER.getUserDesc());
            if (!result) {
                return false;
            }

            AssWoType assWoType = AssWoType.fromCode(workOrder.getAssType());
            // 更新工单状态
            String scheduleId = workOrder.getScheduleId();
            if (StrUtil.isNotBlank(scheduleId)) {
                assScheduleDomainService.updateFinishByScheduleId(scheduleId, AssScheduleNode.CANCEL_WORK_ORDER);
            }

            String relOrderId = workOrder.getRelOrderId();
            if (StrUtil.isBlank(relOrderId)) {
                return true;
            }

            if (Objects.equals(AssWoType.RETRIEVE, assWoType)) {
                //取消找回子单据
                assRetrieveWoRepository.userCancel(relOrderId);
            }

            if (Objects.equals(AssWoType.DISPUTE, assWoType)) {
                //取消纠纷子单据
                assDisputeWoRepository.userCancel(relOrderId);
            }
            RecordBO recordBO = new RecordBO()
                    .setWorkOrderId(relOrderId)
                    .setAssType(assWoType.getCode())
                    .setTitle(AssOperatorLog.COMPLETE_WO.getDesc())
                    .setContent(AssOperatorLog.COMPLETE_WO.getDesc())
                    .setOperatorId(userId);
            assFollowupLogDomainService.recordLog(recordBO);
            return true;
        });
    }


    public Long unreadCount(Boolean isMerchant) {
        AssWorkOrderReqBO.PageAssOrderBO param = new AssWorkOrderReqBO.PageAssOrderBO();
        if (BooleanUtil.isTrue(isMerchant)) {
            String userId = MerchantUserUtil.getUserId();
            Ensure.that(StrUtil.isNotBlank(userId)).isTrue(ErrorCode.ASS_USER_GET_FAIL);
            param.setMerchantUserId(userId);
        } else {
            String userId = UserIdUtils.getUserId();
            Ensure.that(StrUtil.isNotBlank(userId)).isTrue(ErrorCode.ASS_USER_GET_FAIL);
            param.setUserIdList(Collections.singletonList(userId));
        }
        param.setReadFlag(NumberUtils.INTEGER_ZERO);
        log.info(">>>>>>>unreadCount: param {}", param);
        return assWorkOrderDomainService.count(param);
    }
}

