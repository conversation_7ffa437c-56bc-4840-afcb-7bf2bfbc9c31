package com.pxb7.mall.trade.ass.app;


import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.cola.exception.BizException;
import com.alibaba.fastjson2.JSON;
import com.pxb7.mall.auth.c.util.MerchantUserUtil;
import com.pxb7.mall.components.redis.redisson.RLockUtils;
import com.pxb7.mall.response.PxPageResponse;
import com.pxb7.mall.trade.ass.app.common.UserIdUtils;
import com.pxb7.mall.trade.ass.app.dto.response.assSchedule.AssScheduleRespMobileDTO;
import com.pxb7.mall.trade.ass.app.dto.response.assSchedule.RespAssScheduleNodeDTO;
import com.pxb7.mall.trade.ass.client.enums.*;
import com.pxb7.mall.trade.ass.domain.*;
import com.pxb7.mall.trade.ass.domain.helper.RocketMqMessageHelper;
import com.pxb7.mall.trade.ass.domain.model.*;
import com.pxb7.mall.trade.ass.domain.model.reqeust.RecordBO;
import com.pxb7.mall.trade.ass.domain.model.reqeust.mq.AfcWoStatusChange;
import com.pxb7.mall.trade.ass.infra.config.nacos.AssWorkOrderInitConfig;
import com.pxb7.mall.trade.ass.infra.constant.AfcConstant;
import com.pxb7.mall.trade.ass.infra.constant.RMQConstant;
import com.pxb7.mall.trade.ass.infra.enums.ErrorCode;
import com.pxb7.mall.trade.ass.infra.exception.Ensure;
import com.pxb7.mall.trade.ass.infra.remote.dubbo.OrderItemRpcGateway;
import com.pxb7.mall.trade.ass.infra.repository.db.*;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.AssRetrieveGameConfigDetail;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.AssRetrieveWo;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.AssSchedule;
import com.pxb7.mall.trade.ass.infra.util.IdGenUtil;
import com.pxb7.mall.trade.ass.infra.util.ObjectMapperUtil;
import com.pxb7.mall.trade.ass.infra.util.OptionalUtil;
import com.pxb7.mall.trade.order.client.dto.request.orderItemIndemnity.OrderItemIndemnityDubboDTO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Service;
import com.pxb7.mall.trade.ass.app.dto.reqeust.AssWorkOrderReqDTO;
import com.pxb7.mall.trade.ass.app.dto.response.AssWorkOrderRespDTO;
import com.pxb7.mall.trade.ass.app.mapping.AssWorkOrderAppMapping;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.transaction.support.TransactionTemplate;


/**
 * 售后工单app服务
 *
 * <AUTHOR>
 * @since 2025-07-30 14:00:39
 */
@Slf4j
@Service
public class AssWorkOrderAppService {

    @Resource
    private AssWorkOrderDomainService assWorkOrderDomainService;

    @Resource
    private AssScheduleDomainService assScheduleDomainService;
    @Resource
    private AssScheduleLogDomainService assScheduleLogDomainService;
    @Resource
    private AssWorkOrderLogDomainService workOrderLogDomainService;
    @Resource
    private AssDisputeWoRepository assDisputeWoRepository;
    @Resource
    private AssRetrieveWoRepository assRetrieveWoRepository;

    @Resource
    private AssFollowupLogDomainService assFollowupLogDomainService;

    @Resource
    private TransactionTemplate transactionTemplate;

    @Resource
    private OrderItemRpcGateway orderItemRpcGateway;

    @Resource
    private AssRetrieveGameConfigDetailRepository assRetrieveGameConfigDetailRepository;

    @Resource
    private AssScheduleRepository assScheduleRepository;

    @Resource
    private AssWorkOrderInitConfig assWorkOrderInitConfig;

    @Resource
    private AssScheduleLogAppService assScheduleLogAppService;

    @Resource
    private RocketMqMessageHelper rocketMqMessageHelper;

    public PxPageResponse<AssWorkOrderRespDTO.PageDetailDTO> page(AssWorkOrderReqDTO.PageOrderDTO param) {
        AssWorkOrderReqBO.PageAssOrderBO paramBo = AssWorkOrderAppMapping.INSTANCE.assWorkOrderDTO2PageAssOrderBO(param);
        if (BooleanUtil.isTrue(param.getIsMerchant())) {
            String userId = MerchantUserUtil.getUserId();
            Ensure.that(StrUtil.isNotBlank(userId)).isTrue(ErrorCode.ASS_USER_GET_FAIL);
            paramBo.setMerchantUserId(userId);
        } else {
            String userId = UserIdUtils.getUserId();
            Ensure.that(StrUtil.isNotBlank(userId)).isTrue(ErrorCode.ASS_USER_GET_FAIL);
            paramBo.setUserIdList(Collections.singletonList(userId));
        }
        return getPageDetailDTOPxPageResponse(param, paramBo);
    }

    @NotNull
    private PxPageResponse<AssWorkOrderRespDTO.PageDetailDTO> getPageDetailDTOPxPageResponse(AssWorkOrderReqDTO.PageOrderDTO param, AssWorkOrderReqBO.PageAssOrderBO paramBo) {
        log.info("page ass param:{}", JSON.toJSONString(paramBo));
        PxPageResponse<AssWorkOrderRespBO.AssInfoPageDetailBO> response = assWorkOrderDomainService.pageAssInfoFromEs(paramBo);
        List<AssWorkOrderRespDTO.PageDetailDTO> list = AssWorkOrderAppMapping.INSTANCE.assInfoPageDetailBO2PageDetailDTO(response.getData());
        //查询游戏是否开启进群配置
        if (CollUtil.isNotEmpty(list)) {
            Set<String> gameIdSet = list.stream()
                    .map(AssWorkOrderRespDTO.PageDetailDTO::getGameId).collect(Collectors.toSet());
            //批量查询游戏是否开启进群提醒
            Map<String, Boolean> gameIdGroupOpenMap = OptionalUtil.of(assRetrieveGameConfigDetailRepository.getListBy(gameIdSet))
                    .stream()
                    .collect(Collectors.toMap(AssRetrieveGameConfigDetail::getGameId, AssRetrieveGameConfigDetail::getGroupOpen));
            list.forEach(e -> e.setShowJoinGroup(gameIdGroupOpenMap.getOrDefault(e.getGameId(), false)));
        }
        return PxPageResponse.of(list, response.getTotalCount(), param.getPageSize(), param.getPageIndex());
    }


    public boolean insert(AssWorkOrderReqDTO.AddDTO param) {
        AssWorkOrderReqBO.AddBO addBO = AssWorkOrderAppMapping.INSTANCE.assWorkOrderDTO2AddBO(param);
        return assWorkOrderDomainService.insert(addBO);
    }

    public boolean update(AssWorkOrderReqDTO.UpdateDTO param) {
        AssWorkOrderReqBO.UpdateBO updateBO = AssWorkOrderAppMapping.INSTANCE.assWorkOrderDTO2UpdateBO(param);
        return assWorkOrderDomainService.update(updateBO);
    }

    public List<AssWorkOrderRespDTO.DetailDTO> list(AssWorkOrderReqDTO.SearchDTO param) {
        AssWorkOrderReqBO.SearchBO searchBO = AssWorkOrderAppMapping.INSTANCE.assWorkOrderDTO2SearchBO(param);
        List<AssWorkOrderRespBO.DetailBO> list = assWorkOrderDomainService.list(searchBO);
        return AssWorkOrderAppMapping.INSTANCE.assWorkOrderBO2ListDTO(list);
    }

    public Page<AssWorkOrderRespDTO.DetailDTO> page(AssWorkOrderReqDTO.PageDTO param) {
        AssWorkOrderReqBO.PageBO pageBO = AssWorkOrderAppMapping.INSTANCE.assWorkOrderDTO2PageBO(param);
        Page<AssWorkOrderRespBO.DetailBO> page = assWorkOrderDomainService.page(pageBO);
        return AssWorkOrderAppMapping.INSTANCE.assWorkOrderBO2PageDTO(page);
    }

    public AssWorkOrderRespDTO.AssWorkOrderDetailDTO findOrderDetail(String orderNo) {
        AssWorkOrderRespBO.AssInfoPageDetailBO assInfo = assWorkOrderDomainService.getAssInfoByOrderItemId(orderNo);
        Ensure.that(assInfo).isNotNull(ErrorCode.ASS_OTHER_WORK_ORDER_EXIST);
        String assWorkOrderId = assInfo.getAssWorkOrderId();
        //todo 新增订单越权校验，后续需要校验当前登录态用户是否拥有此订单权限
        AssWorkOrderRespBO.DetailBO workOrder = assWorkOrderDomainService.findByOrderItemId(orderNo);

        //查询日志进度信息
        List<AssWorkOrderLogRespBO.DetailBO> logList = workOrderLogDomainService.list(new AssWorkOrderLogReqBO.SearchBO()
                .setWorkOrderId(assWorkOrderId).setShowTypeList(AssShowType.getUserShowTypes()));
        List<OrderItemIndemnityDubboDTO> indemnityList = orderItemRpcGateway.getEffectiveIndemnityListByOrderItemId(orderNo);
        //查询是否是协助工单
        AssRetrieveWo assRetrieveWo = getAssistanceInfo(assInfo);
        //
        AssRetrieveGameConfigDetail gameConfig = getAssRetrieveGameConfigDetail(assInfo);

        AssWorkOrderRespDTO.AssWorkOrderDetailDTO workOrderDetailDTO = AssWorkOrderAppMapping.INSTANCE.assInfoPageDetailBO2AssWorkOrderDetailDTO(assInfo, indemnityList, assRetrieveWo, logList, gameConfig, workOrder);

        if (!Objects.equals(assInfo.getReadFlag(), NumberUtils.INTEGER_ONE)) {
            assWorkOrderDomainService.updateReadFlag(assInfo.getAssWorkOrderId(), NumberUtils.INTEGER_ONE);
        }
        return workOrderDetailDTO;
    }

    @Nullable
    private AssRetrieveGameConfigDetail getAssRetrieveGameConfigDetail(AssWorkOrderRespBO.AssInfoPageDetailBO assInfo) {
        if (StrUtil.isNotBlank(assInfo.getGameId())) {
            return assRetrieveGameConfigDetailRepository.getByGameId(assInfo.getGameId());
        }
        return null;
    }

    private AssRetrieveWo getAssistanceInfo(AssWorkOrderRespBO.AssInfoPageDetailBO assInfo) {
        AssWoType assWoType = AssWoType.fromCode(assInfo.getAssType());
        if (Objects.equals(AssWoType.RETRIEVE, assWoType) && StrUtil.isNotBlank(assInfo.getAssSubOrderId())) {
            AssRetrieveWo assRetrieveWo = assRetrieveWoRepository.getByAssRetrieveId(assInfo.getAssSubOrderId());
            return assRetrieveWo;
        }
        return null;
    }

    public Boolean cancel(String orderNo, String userId) {
        log.info("cancel,orderNo:{},userId:{}", orderNo, userId);
        //todo 新增订单越权校验，后续需要校验当前登录态用户是否拥有此订单权限
        Ensure.that(StrUtil.isNotBlank(orderNo)).isTrue(ErrorCode.NO_ORDER);
        return RLockUtils.of(AfcConstant.getAfcOrderLockKey(orderNo), () -> {
                    //根据交易订单号查询最新的售后工单
                    AssWorkOrderRespBO.DetailBO workOrder = assWorkOrderDomainService.findByOrderItemId(orderNo);
                    if (Objects.nonNull(workOrder) && !Objects.equals(workOrder.getAssStatus(), AssWorkOrderStatusEnum.PROCESS.getCode())) {
                        log.info("当前售后工单无须取消:{}", workOrder.getWorkOrderId());
                        return true;
                    }
                    if (workOrder == null) {
                        log.warn("cancel 未查询到售后工单走老逻辑orderNo,{} userId:{}", orderNo, userId);
                        return doCancelSchedule(userId, orderNo);
                    }
                    Ensure.that(Objects.equals(workOrder.getAssStatus(), AssWorkOrderStatusEnum.PROCESS.getCode()))
                            .isTrue(ErrorCode.ASS_CANCEL_UN_VALID);
                    return doCancel(userId, workOrder);
                }).withWaitTime(3).withTimeUnit(TimeUnit.SECONDS)
                .orElseThrow(() -> new BizException(ErrorCode.ASS_HAVE_OTHER_FAIL.getErrCode(),
                        ErrorCode.ASS_HAVE_OTHER_FAIL.getErrDesc()));

    }


    private Boolean doCancelSchedule(String userId, String orderNo) {
        AssSchedule assSchedule = assScheduleDomainService.getAssSchedule(orderNo);
        if (Objects.isNull(assSchedule) || Objects.equals(assSchedule.getFinish(), true)) {
            return true;
        }

        return transactionTemplate.execute(transactionStatus -> {
            AssWoType assWoType = AssWoType.fromCode(assSchedule.getAssType());
            // 更新工单状态
            String scheduleId = assSchedule.getScheduleId();
            if (StrUtil.isNotBlank(scheduleId)) {
                assScheduleDomainService.updateFinishByScheduleId(scheduleId, AssScheduleNode.USER_CANCEL);
                assScheduleLogDomainService.add(scheduleId, AssScheduleNode.USER_CANCEL, null, assWoType, userId, StrUtil.EMPTY);
            }

            String relOrderId = assSchedule.getWorkOrderId();
            if (StrUtil.isBlank(relOrderId)) {
                return true;
            }

            if (Objects.equals(AssWoType.RETRIEVE, assWoType)) {
                //取消找回子单据
                assRetrieveWoRepository.userCancel(relOrderId);
            }

            if (Objects.equals(AssWoType.DISPUTE, assWoType)) {
                //取消纠纷子单据
                assDisputeWoRepository.userCancel(relOrderId);
            }
            RecordBO recordBO = new RecordBO()
                    .setWorkOrderId(relOrderId)
                    .setAssType(assWoType.getCode())
                    .setTitle(AssOperatorLog.COMPLETE_WO.getDesc())
                    .setContent(AssOperatorLog.COMPLETE_WO.getDesc())
                    .setOperatorId(userId);
            assFollowupLogDomainService.recordLog(recordBO);

            notifyCloseMessage(relOrderId, assSchedule.getRoomId(), orderNo, assWoType);

            return true;
        });
    }


    public void notifyCloseMessage(String workOrderId, String roomId, String orderItemId, AssWoType assType) {
        AfcWoStatusChange afcWoStatusChange = AfcWoStatusChange.builder()
                .workOrderId(workOrderId)
                .roomId(roomId)
                .orderItemId(orderItemId)
                .workOrderStatus(AfcWOStatus.COMPLETED.getLabel())
                .workOrderType(assType.getCode())
                .build();
        String tag = Objects.equals(assType, AssWoType.RETRIEVE) ? RMQConstant.AFC_RETRIEVE_WORK_ORDER_STATUS_CHANGE_TAG : RMQConstant.AFC_DISPUTE_WORK_ORDER_STATUS_CHANGE_TAG;
        RocketMqMessageDTO messageDTO = RocketMqMessageDTO.builder()
                .topic(RMQConstant.AFC_WORK_ORDER_STATUS_CHANGE_TOPIC)
                .tag(tag)
                .isDelay(false)
                .content(ObjectMapperUtil.toJsonStr(afcWoStatusChange))
                .bizId(workOrderId)
                .isSharding(false)
                .build();
        // 本地消息
        rocketMqMessageHelper.saveAndSend(messageDTO);
        log.info("notifyCloseMessage:{},tag:{}", JSON.toJSONString(messageDTO), tag);
    }


    private Boolean doCancel(String userId, AssWorkOrderRespBO.DetailBO workOrder) {
        return transactionTemplate.execute(transactionStatus -> {
            boolean result = assWorkOrderDomainService.updateWithOpt(workOrder.getId(), AssWorkOrderStatusEnum.PROCESS.getCode(), AssWorkOrderStatusEnum.CANCELED.getCode(), AssUserOperatorLog.USER_CLOSE_ORDER.getUserDesc(), NumberUtils.INTEGER_ONE);
            if (!result) {
                return false;
            }

            AssWoType assWoType = AssWoType.fromCode(workOrder.getAssType());
            // 更新工单状态
            String scheduleId = workOrder.getScheduleId();
            if (StrUtil.isNotBlank(scheduleId)) {
                assScheduleDomainService.updateFinishByScheduleId(scheduleId, AssScheduleNode.USER_CANCEL);
                assScheduleLogDomainService.add(scheduleId, AssScheduleNode.USER_CANCEL, null, assWoType, userId, StrUtil.EMPTY);
            }

            String relOrderId = workOrder.getRelOrderId();
            if (StrUtil.isBlank(relOrderId)) {
                return true;
            }

            if (Objects.equals(AssWoType.RETRIEVE, assWoType)) {
                //取消找回子单据
                assRetrieveWoRepository.userCancel(relOrderId);
            }

            if (Objects.equals(AssWoType.DISPUTE, assWoType)) {
                //取消纠纷子单据
                assDisputeWoRepository.userCancel(relOrderId);
            }
            RecordBO recordBO = new RecordBO()
                    .setWorkOrderId(relOrderId)
                    .setAssType(assWoType.getCode())
                    .setTitle(AssOperatorLog.COMPLETE_WO.getDesc())
                    .setContent(AssOperatorLog.COMPLETE_WO.getDesc() + AssUserOperatorLog.USER_CLOSE_ORDER.getAdminDesc())
                    .setFeiShuName(AfcConstant.ASS_CANCEL_USER)
                    .setOperatorId(userId);
            assFollowupLogDomainService.recordLog(recordBO);
            return true;
        });
    }


    public Long unreadCount(Boolean isMerchant) {
        AssWorkOrderReqBO.PageAssOrderBO param = new AssWorkOrderReqBO.PageAssOrderBO();
        if (BooleanUtil.isTrue(isMerchant)) {
            String userId = MerchantUserUtil.getUserId();
            Ensure.that(StrUtil.isNotBlank(userId)).isTrue(ErrorCode.ASS_USER_GET_FAIL);
            param.setMerchantUserId(userId);
        } else {
            String userId = UserIdUtils.getUserId();
            Ensure.that(StrUtil.isNotBlank(userId)).isTrue(ErrorCode.ASS_USER_GET_FAIL);
            param.setUserIdList(Collections.singletonList(userId));
        }
        param.setReadFlag(NumberUtils.INTEGER_ZERO);
        param.setIsMerchant(isMerchant);
        log.info(">>>>>>>unreadCount: param {}", param);
        return assWorkOrderDomainService.count(param);
    }


    public void initWorkOrder(Long minId, Long maxId) {
        // 参数校验
        if (ObjectUtil.hasNull(minId, maxId) || minId > maxId) {
            log.warn("initWorkOrder param fail minId:{},maxId:{}", minId, maxId);
            return;
        }

        log.info("initWorkOrder started, minId:{}, maxId:{}", minId, maxId);

        // 批处理大小
        final int batchSize = 100;
        Long currentMinId = minId;
        int totalProcessed = 0;

        // 循环处理直到达到最大ID或配置开关关闭
        while (currentMinId < maxId && assWorkOrderInitConfig.isInitSwitch()) {
            // 计算当前批次的ID范围
            Long currentMaxId = Math.min(currentMinId + batchSize, maxId);

            try {
                // 查询未完成的任务
                List<AssSchedule> assSchedules = assScheduleRepository.listNoFinishAndMaxId(currentMinId, currentMaxId);
                log.info("Processing batch, minId:{} maxId:{}, found {} schedules", currentMinId, currentMaxId, assSchedules.size());

                if (CollUtil.isNotEmpty(assSchedules)) {
                    // 处理每个任务
                    for (AssSchedule assSchedule : assSchedules) {
                        try {
                            log.debug("Initializing work order for schedule:{}", assSchedule.getOrderItemId());
                            this.initSingleWorkOrder(assSchedule);
                            totalProcessed++;
                        } catch (Exception e) {
                            log.error("Failed to init work order for schedule:{}", assSchedule.getOrderItemId(), e);
                        }
                    }

                    // 更新下一批次的起始ID为最后一条记录的ID + 1
                    currentMinId = assSchedules.get(assSchedules.size() - 1).getId() + 1;
                } else {
                    // 如果当前批次没有数据，则移动到下一个批次
                    currentMinId = currentMaxId;
                }

            } catch (Exception e) {
                log.error("Error processing batch, minId:{}, maxId:{}", currentMinId, currentMaxId, e);
                // 发生异常时跳过当前批次，继续处理下一批
                currentMinId = currentMaxId;
            }
        }

        log.info("initWorkOrder completed, processed up to maxId:{}, total processed:{}", maxId, totalProcessed);
    }

    public void initSingleWorkOrder(AssSchedule assSchedule) {
        //先校验下是否存在
        Optional<AssWorkOrderRespBO.DetailBO> optional = assWorkOrderDomainService.findByScheduleId(assSchedule.getScheduleId());
        if (optional.isPresent()) {
            return;
        }
        AssWorkOrderReqBO.InitWorkOrderAndLogBO initWorkOrderAndLogBO = new AssWorkOrderReqBO.InitWorkOrderAndLogBO()
                .setWorkOrderId(IdGenUtil.generateId())
                .setOrderItemId(assSchedule.getOrderItemId())
                .setRoomId(assSchedule.getRoomId())
                .setCreateUserId(assSchedule.getCreateUserId())
                .setUpdateUserId(assSchedule.getUpdateUserId())
                .setRelOrderId(assSchedule.getWorkOrderId())
                .setAssStatus(AssWorkOrderStatusEnum.PROCESS.getCode())
                .setApplyTime(assSchedule.getCreateTime())
                .setScheduleId(assSchedule.getScheduleId())
                .setProposerType(1)
                .setReadFlag(true)
                .setAssType(assSchedule.getAssType());
        //查询ass_schedule_log
        AssScheduleRespMobileDTO assScheduleRespMobileDTO = assScheduleLogAppService.recordLogList(assSchedule.getOrderItemId());
        //为空或者已完结的直接不初始化
        if (Objects.isNull(assScheduleRespMobileDTO) || BooleanUtil.isTrue(assScheduleRespMobileDTO.getFinish())) {
            return;
        }
        List<AssWorkOrderReqBO.InitLogBO> initLogBOS = OptionalUtil.of(assScheduleRespMobileDTO.getNodes())
                .stream()
                .sorted(Comparator.comparing(RespAssScheduleNodeDTO::getCreateTime))
                .map(item -> {
                    AssWorkOrderReqBO.InitLogBO initLogBO = new AssWorkOrderReqBO.InitLogBO()
                            .setWorkOrderId(initWorkOrderAndLogBO.getWorkOrderId())
                            .setWorkOrderLogId(IdGenUtil.generateId())
                            .setOrderItemId(assSchedule.getOrderItemId())
                            .setTitle(item.getDesc())
                            .setNodeId(item.getNodeId())
                            .setNodeDesc(item.getNodeDesc())
                            .setAssType(assSchedule.getAssType())
                            .setShowType(AssShowType.ALL.getCode())
                            .setCreateUserId(item.getCreateUserId())
                            .setCreateTime(item.getCreateTime())
                            .setContent(item.getNodeDesc());
                    return initLogBO;
                }).collect(Collectors.toList());

        initWorkOrderAndLogBO.setLogList(initLogBOS);
        assWorkOrderDomainService.initWorkOrderAndLog(initWorkOrderAndLogBO);

    }
}
