package com.pxb7.mall.trade.ass.app;

import static com.pxb7.mall.trade.ass.infra.enums.ErrorCode.*;

import com.pxb7.mall.trade.ass.domain.model.OrderItemAmountInfoBO;
import com.pxb7.mall.trade.ass.infra.remote.dubbo.OrderItemRpcGateway;
import com.pxb7.mall.trade.order.client.dto.response.order.dubbo.OrderInfoDubboRespDTO;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;

import org.apache.commons.lang3.StringUtils;
import org.apache.curator.shaded.com.google.common.collect.Lists;
import org.springframework.stereotype.Service;

import com.alibaba.cola.exception.Assert;
import com.alibaba.cola.exception.BizException;
import com.pxb7.mall.components.redis.redisson.RLockUtils;
import com.pxb7.mall.response.PxResponse;
import com.pxb7.mall.trade.ass.app.dto.reqeust.ApplyRechargeRefundReqDTO;
import com.pxb7.mall.trade.ass.app.dto.reqeust.RechargeRefundAuditReqDTO;
import com.pxb7.mall.trade.ass.client.dto.model.order.OrderItemAmountInfo;
import com.pxb7.mall.trade.ass.domain.refund.RefundDomainService;
import com.pxb7.mall.trade.ass.domain.refund.message.provider.RefundMessageService;
import com.pxb7.mall.trade.ass.domain.refund.model.RefundAuditMessage;
import com.pxb7.mall.trade.ass.infra.enums.ErrorCode;
import com.pxb7.mall.trade.ass.infra.enums.RefundActionTypeEnum;
import com.pxb7.mall.trade.ass.infra.enums.RefundAuditStatusEnum;
import com.pxb7.mall.trade.ass.infra.model.UserBaseInfo;
import com.pxb7.mall.trade.ass.infra.remote.model.refund.OrderItemReceiptVoucherPO;
import com.pxb7.mall.trade.ass.infra.repository.db.OrderItemExtendRepository;
import com.pxb7.mall.trade.ass.infra.repository.db.OrderItemRepository;
import com.pxb7.mall.trade.ass.infra.repository.db.RefundVoucherRepository;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.OrderItem;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.OrderItemExtend;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.RefundVoucher;
import com.pxb7.mall.trade.order.client.enums.order.*;
import com.pxb7.mall.trade.order.client.lock.OrderItemLockConstant;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class RechargeRefundAppService {
    @Resource
    private RefundDomainService refundDomainService;
    @Resource
    private OrderItemRepository orderItemRepository;
    @Resource
    private OrderItemExtendRepository orderItemExtendRepository;
    @Resource
    private RefundVoucherRepository refundVoucherRepository;
    @Resource
    private RefundMessageService refundMessageService;

    @Resource
    private OrderItemRpcGateway orderItemRpcGateway;

    /**
     * 用户提交充值退款申请
     */
    public PxResponse<Boolean> applyRechargeRefund(ApplyRechargeRefundReqDTO applyRefundReqDTO, UserBaseInfo userInfo) {
        Assert.isTrue(userInfo != null && StringUtils.isNotBlank(userInfo.getUserId()),
            GET_USER_BASE_INFO_ERROR.getErrCode(), GET_USER_BASE_INFO_ERROR.getErrDesc());
        // 校验充值退款订单状态
        Consumer<String> rechargeCheckFunc = orderItemId -> {
            // 校验orderItem，同一时刻只能有一笔单据进行
            OrderItem orderItem = orderItemRepository.getOrderItem(applyRefundReqDTO.getOrderItemId());
            refundDomainService.checkOrderItem(orderItem);
            // 校验orderExtend,必须是代发货状态
            OrderItemExtend orderItemExtend = orderItemExtendRepository.getOneByItemId(orderItemId);
            Assert.isTrue(Objects.equals(orderItemExtend.getRechargeSellerStatus(), 1),
                RECHARGE_SELLER_STATUS_ERROR.getErrCode(), RECHARGE_SELLER_STATUS_ERROR.getErrDesc());
        };
        rechargeCheckFunc.accept(applyRefundReqDTO.getOrderItemId());

        // 查询成功收款单, 并校验是否仍有退款余额
        List<OrderItemReceiptVoucherPO> successReceiptVoucherDetail = refundDomainService
            .getSuccessReceiptVoucherDetail(applyRefundReqDTO.getOrderItemId(), Lists.newArrayList());

        // 计算全额退款金额，并校验退款金额是否充足
        OrderItemAmountInfoBO refundAmountInfo = refundDomainService.getWholeRefundAmountInfo(
            applyRefundReqDTO.getOrderItemId(), Lists.newArrayList(), successReceiptVoucherDetail);
        // 计算支持的退款方式
        OrderInfoDubboRespDTO order = orderItemRpcGateway.getOrderInfo(applyRefundReqDTO.getOrderItemId());

        Set<Integer> supportRefundType =
            refundDomainService.supportRefundType(order, successReceiptVoucherDetail, refundAmountInfo.getTotalAmount());

        // 校验是否支持线上退款
        Assert.isTrue(supportRefundType.contains(RefundTypeEnum.ONLINE_REFUND.getValue()),
            REFUND_TYPE_VALID_FAIL_ERROR.getErrCode(), REFUND_TYPE_VALID_FAIL_ERROR.getErrDesc());
        Integer refundType = RefundTypeEnum.ONLINE_REFUND.getValue();

        // 校验是否允许在线退款
        refundDomainService.checkOrderCanRefund(applyRefundReqDTO.getOrderItemId());

        // 保存申请数据, 先加分布式锁, 1锁 2判 3更新
        String lockKey = String.format(OrderItemLockConstant.PREFIX, applyRefundReqDTO.getOrderItemId());
        RefundVoucher refundVoucher = RLockUtils.of(lockKey, () -> {
            // 锁后再次校验orderItem，同一时刻只能有一笔单据进行
            rechargeCheckFunc.accept(applyRefundReqDTO.getOrderItemId());
            return refundDomainService.saveUserRefundApply(ProductTypeEnum.RECHARGE.getValue(),
                applyRefundReqDTO.getOrderItemId(), "", "未发货无理由退款", refundType, refundAmountInfo, userInfo.getUserId());
        }).withWaitTime(3).withTimeUnit(TimeUnit.SECONDS)
            .orElseThrow(() -> new BizException(ErrorCode.ORDER_HAVE_OTHER_REFUND_RECEIPT_FAIL.getErrCode(),
                ErrorCode.ORDER_HAVE_OTHER_REFUND_RECEIPT_FAIL.getErrDesc()));

        // 同步到es
        // refundDomainService.syncOrderItem2Es(refundVoucher.getOrderItemId());

        // 记录订单操作日志
        refundDomainService.addRefundOrderOperate(refundVoucher.getOrderItemId(), refundVoucher.getRefundVoucherId(),
            OrderOperateTypeEnum.CREATE_REFUND, OrderOptUserTypeEnum.BUYER, userInfo.getUserId(),
            userInfo.getUserName());
        return PxResponse.ok(true);
    }

    public PxResponse<Boolean> rejectRefund(RechargeRefundAuditReqDTO auditReqDTO, UserBaseInfo userBaseInfo) {
        RefundVoucher refundVoucher = refundVoucherRepository.getByRefundId(auditReqDTO.getRefundVoucherId());
        Assert.notNull(refundVoucher, REFUND_NOT_FOUND_ERROR.getErrCode(), REFUND_NOT_FOUND_ERROR.getErrDesc());
        Assert.isTrue(Objects.equals(refundVoucher.getProductType(), ProductTypeEnum.RECHARGE.getValue()),
            REFUND_PRODUCT_TYPE_ERROR.getErrCode(), REFUND_PRODUCT_TYPE_ERROR.getErrDesc());
        Assert.isTrue(
            Objects.equals(refundVoucher.getAuditStatus(), RefundAuditStatusEnum.WAIT_SERVICE_AUDIT.getValue()),
            REFUND_AUDIT_STATUS_ERROR.getErrCode(), REFUND_AUDIT_STATUS_ERROR.getErrDesc());
        // 更新orderItem退款状态
        boolean suc = orderItemRepository.updateRefundStatus(refundVoucher.getOrderItemId(),
            RefundStatusEnum.WAIT_APPLY.getValue(), RefundStatusEnum.CLOSE.getValue());
        Assert.isTrue(suc, REFUND_AUDIT_STATUS_ERROR.getErrCode(), REFUND_AUDIT_STATUS_ERROR.getErrDesc());
        // 客服拒绝买家退款，关闭退款
        RefundVoucher toUpdate = new RefundVoucher().setRefundVoucherId(auditReqDTO.getRefundVoucherId())
            .setRefundStatus(RefundStatusEnum.CLOSE.getValue()).setAuditStatus(RefundAuditStatusEnum.REJECT.getValue())
            .setCloseType(RefundActionTypeEnum.MERCHANT.getValue()).setUpdateUserId(userBaseInfo.getUserId())
            .setFinishTime(LocalDateTime.now());
        boolean refundVoucherSuc = refundVoucherRepository.updateByRefundVoucherId(toUpdate);
        Assert.isTrue(refundVoucherSuc, REFUND_AUDIT_STATUS_ERROR.getErrCode(), REFUND_AUDIT_STATUS_ERROR.getErrDesc());
        // 同步到es
        // refundDomainService.syncOrderItem2Es(refundVoucher.getOrderItemId());

        // 记录订单操作日志
        refundDomainService.addRefundOrderOperate(refundVoucher.getOrderItemId(), refundVoucher.getRefundVoucherId(),
            OrderOperateTypeEnum.AUDIT_REFUND_REJECT, OrderOptUserTypeEnum.SELLER, userBaseInfo.getUserId(),
            userBaseInfo.getUserName());
        return PxResponse.ok(true);
    }

    public PxResponse<Boolean> passRefund(RechargeRefundAuditReqDTO auditReqDTO, UserBaseInfo userBaseInfo) {
        // 审核时校验审核状态
        RefundVoucher refundVoucher = refundVoucherRepository.getByRefundId(auditReqDTO.getRefundVoucherId());
        Assert.notNull(refundVoucher, REFUND_NOT_FOUND_ERROR.getErrCode(), REFUND_NOT_FOUND_ERROR.getErrDesc());
        Assert.isTrue(Objects.equals(refundVoucher.getProductType(), ProductTypeEnum.RECHARGE.getValue()),
            REFUND_PRODUCT_TYPE_ERROR.getErrCode(), REFUND_PRODUCT_TYPE_ERROR.getErrDesc());
        Assert.isTrue(
            Objects.equals(refundVoucher.getRefundStatus(), RefundStatusEnum.WAIT_APPLY.getValue())
                && Objects.equals(refundVoucher.getAuditStatus(), RefundAuditStatusEnum.WAIT_SERVICE_AUDIT.getValue()),
            REFUND_AUDIT_STATUS_ERROR.getErrCode(), REFUND_AUDIT_STATUS_ERROR.getErrDesc());

        // 更新退款单状态
        refundDomainService.updateRefundSubmit(auditReqDTO.getRefundVoucherId(), null, null, null, null,
            RefundAuditStatusEnum.WAIT_FINANCE_AUDIT.getValue(), userBaseInfo.getUserId(),
            RefundAuditStatusEnum.WAIT_SERVICE_AUDIT.getValue());

        // 同步到es
        // refundDomainService.syncOrderItem2Es(refundVoucher.getOrderItemId());

        // 记录订单操作日志
        refundDomainService.addRefundOrderOperate(refundVoucher.getOrderItemId(), refundVoucher.getRefundVoucherId(),
            OrderOperateTypeEnum.AUDIT_REFUND_PASS, OrderOptUserTypeEnum.SELLER, userBaseInfo.getUserId(),
            userBaseInfo.getUserName());

        // 线上发送自动审核延迟mq消息
        refundMessageService
            .sendRefundAuditMessage(new RefundAuditMessage().setRefundVoucherId(auditReqDTO.getRefundVoucherId()));
        return PxResponse.ok(true);
    }
}
