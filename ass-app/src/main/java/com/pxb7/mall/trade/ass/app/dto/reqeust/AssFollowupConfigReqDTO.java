package com.pxb7.mall.trade.ass.app.dto.reqeust;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Min;
import lombok.ToString;


/**
 * 跟进结果类型配置表(AssFollowupConfig)实体类
 *
 * <AUTHOR>
 * @since 2025-07-30 13:44:02
 */
public class AssFollowupConfigReqDTO {

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class AddDTO {

        /**
         * 业务主键
         */
        @NotBlank(message = "followupConfigId不能为空")
        private String followupConfigId;

        /**
         * 售后类型1找回2纠纷
         */
        @NotNull(message = "assType不能为空")
        private Integer assType;

        /**
         * 跟进结果类型
         */
        @NotBlank(message = "resultDesc不能为空")
        private String resultDesc;

        /**
         * 售后进度展示文案
         */
        @NotBlank(message = "progressDesc不能为空")
        private String progressDesc;

        /**
         * 提醒用户进群文案
         */
        @NotBlank(message = "joinGroupDesc不能为空")
        private String joinGroupDesc;

        /**
         * 发送通知类型1:追回账号，待买家换绑2:追回号款，待买家提供收款账号3:售后到期，待买家接收赔付
         */
        @NotNull(message = "noticeType不能为空")
        private Integer noticeType;

        /**
         * 排序
         */
        @NotNull(message = "sort不能为空")
        private Integer sort;

    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class UpdateDTO {

        /**
         * 自增id
         */
        @NotNull(message = "id不能为空")
        private Long id;


        /**
         * 业务主键
         */
        @NotBlank(message = "followupConfigId不能为空")
        private String followupConfigId;


        /**
         * 售后类型1找回2纠纷
         */
        @NotNull(message = "assType不能为空")
        private Integer assType;


        /**
         * 跟进结果类型
         */
        @NotBlank(message = "resultDesc不能为空")
        private String resultDesc;


        /**
         * 售后进度展示文案
         */
        @NotBlank(message = "progressDesc不能为空")
        private String progressDesc;


        /**
         * 提醒用户进群文案
         */
        @NotBlank(message = "joinGroupDesc不能为空")
        private String joinGroupDesc;


        /**
         * 发送通知类型1:追回账号，待买家换绑2:追回号款，待买家提供收款账号3:售后到期，待买家接收赔付
         */
        @NotNull(message = "noticeType不能为空")
        private Integer noticeType;


        /**
         * 排序
         */
        @NotNull(message = "sort不能为空")
        private Integer sort;


    }


    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class DelDTO {
        @NotNull(message = "id不能为空")
        private Long id;
    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class SearchDTO {
        /**
         * 业务主键
         */
        @NotBlank(message = "followupConfigId不能为空")
        private String followupConfigId;

        /**
         * 售后类型1找回2纠纷
         */
        @NotNull(message = "assType不能为空")
        private Integer assType;

        /**
         * 跟进结果类型
         */
        @NotBlank(message = "resultDesc不能为空")
        private String resultDesc;

        /**
         * 售后进度展示文案
         */
        @NotBlank(message = "progressDesc不能为空")
        private String progressDesc;

        /**
         * 提醒用户进群文案
         */
        @NotBlank(message = "joinGroupDesc不能为空")
        private String joinGroupDesc;

        /**
         * 发送通知类型1:追回账号，待买家换绑2:追回号款，待买家提供收款账号3:售后到期，待买家接收赔付
         */
        @NotNull(message = "noticeType不能为空")
        private Integer noticeType;

        /**
         * 排序
         */
        @NotNull(message = "sort不能为空")
        private Integer sort;

    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class PageDTO {

        /**
         * 业务主键
         */
        @NotBlank(message = "followupConfigId不能为空")
        private String followupConfigId;

        /**
         * 售后类型1找回2纠纷
         */
        @NotNull(message = "assType不能为空")
        private Integer assType;

        /**
         * 跟进结果类型
         */
        @NotBlank(message = "resultDesc不能为空")
        private String resultDesc;

        /**
         * 售后进度展示文案
         */
        @NotBlank(message = "progressDesc不能为空")
        private String progressDesc;

        /**
         * 提醒用户进群文案
         */
        @NotBlank(message = "joinGroupDesc不能为空")
        private String joinGroupDesc;

        /**
         * 发送通知类型1:追回账号，待买家换绑2:追回号款，待买家提供收款账号3:售后到期，待买家接收赔付
         */
        @NotNull(message = "noticeType不能为空")
        private Integer noticeType;

        /**
         * 排序
         */
        @NotNull(message = "sort不能为空")
        private Integer sort;


        /**
         * 页码，从1开始
         */
        @NotNull(message = "pageIndex不能为空")
        @Min(value = 1, message = "pageIndex不能小于1")
        private Integer pageIndex;
        /**
         * 每页数量
         */
        @NotNull(message = "pageSize不能为空")
        private Integer pageSize;

    }

}

