package com.pxb7.mall.trade.ass.app.mapping;

import com.pxb7.mall.trade.ass.client.dto.response.afc.RetrieveWORespDTO;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.AssRetrieveWo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * 找回工单
 *
 * <AUTHOR>
 * @since: 2024-09-21 11:02
 **/
@Mapper
public interface AssRetrieveAppMapping {
    AssRetrieveAppMapping INSTANCE = Mappers.getMapper(AssRetrieveAppMapping.class);

    @Mapping(source = "assRetrieveId", target = "workOrderId")
    RetrieveWORespDTO toRetrieveWORespDTO(AssRetrieveWo assRetrieveWo);
}
