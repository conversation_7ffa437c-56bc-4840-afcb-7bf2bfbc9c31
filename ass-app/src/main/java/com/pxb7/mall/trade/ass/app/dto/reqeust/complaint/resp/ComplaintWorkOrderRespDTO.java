package com.pxb7.mall.trade.ass.app.dto.reqeust.complaint.resp;

import java.io.Serializable;

import lombok.Data;

/**
 * Copyright (C), 2002-2024, 螃蟹游戏服务网
 *
 * @fileName: ComplaintWorkOrderRespDTO.java
 * @description: 客诉工单列表返回的DTO
 * @author: guo<PERSON><PERSON><PERSON>
 * @email: <EMAIL>
 * @date: 2024/9/20 18:03
 * @history: //修改记录
 * <author> <time> <version> <desc>
 * 修改人姓名 修改时间 版本号 描述
 */
@Data
public class ComplaintWorkOrderRespDTO implements Serializable {
    /**
     * 工单id
     */
    private String complaintWorkId;
    /**
     * 投诉标题
     */
    private String complaintTitle;
    /**
     * 订单id
     */
    private String orderItemId;

    /**
     * 投诉文字内容
     */
    private String complaintContent;

    /**
     * 工单状态1待处理2已完成
     */
    private Integer workOrderStatus;

    /**
     * 处理结果
     */
    private String dealResult;

    /**
     * 当前处理人ID
     */
    private String currentProcessorId;

    /**
     * 当前处理人名称
     */
    private String currentProcessorName;

    /**
     * 完结人id
     */
    private String finisherId;

    /**
     * 完结人姓名
     */
    private String finisherName;
}
