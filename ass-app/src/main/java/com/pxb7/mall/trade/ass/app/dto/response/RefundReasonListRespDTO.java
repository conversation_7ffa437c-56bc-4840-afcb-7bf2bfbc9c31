package com.pxb7.mall.trade.ass.app.dto.response;

import java.util.List;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class RefundReasonListRespDTO {

    /**
     * 退款原因id
     */
    private String reasonRefundId;

    /**
     * 退款原因
     */
    private String reasonRefundContent;

    /**
     * 二级原因
     */
    private List<RefundReasonListRespDTO> secondReason;
}
