package com.pxb7.mall.trade.ass.app;

import static com.pxb7.mall.trade.ass.client.enums.AssScheduleNode.*;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.pxb7.mall.trade.ass.app.dto.response.assSchedule.AssScheduleRespMobileDTO;
import com.pxb7.mall.trade.ass.app.dto.response.assSchedule.RespAssScheduleNodeDTO;
import com.pxb7.mall.trade.ass.client.enums.AssScheduleNode;
import com.pxb7.mall.trade.ass.client.enums.AssWoType;
import com.pxb7.mall.trade.ass.domain.AssScheduleDomainService;
import com.pxb7.mall.trade.ass.domain.AssScheduleLogDomainService;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.AssSchedule;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.AssScheduleLog;

import cn.hutool.core.collection.CollUtil;
import groovy.util.logging.Slf4j;
import jakarta.annotation.Resource;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class AssScheduleLogAppService {

    @Resource
    private AssScheduleDomainService assScheduleDomainService;
    @Resource
    private AssScheduleLogDomainService assScheduleLogDomainService;

    public AssScheduleRespMobileDTO recordLogList(String orderNo) {
        AssSchedule assSchedule = assScheduleDomainService.getAssSchedule(orderNo);
        List<AssScheduleLog> logList = assScheduleLogDomainService.findListByScheduleId(assSchedule.getScheduleId());
        if (CollUtil.isEmpty(logList)) {
            return new AssScheduleRespMobileDTO().setRoomId(assSchedule.getRoomId()).setFinish(assSchedule.getFinish())
                .setNodes(CollUtil.newArrayList());
        }
        // 去除重复数据
        List<RespAssScheduleNodeDTO> respList = logList.stream().filter(distinctByKey(AssScheduleLog::getNodeId))
            .filter(o -> Objects.equals(INITIATE_AFTER_SALE.getId(), o.getNodeId())
                || Objects.equals(REJECT.getId(), o.getNodeId())
                || Objects.equals(CREATE_WORK_ORDER.getId(), o.getNodeId())
                || Objects.equals(UPLOAD_EVIDENCE.getId(), o.getNodeId())
                || Objects.equals(CLOSE_WORK_ORDER.getId(), o.getNodeId())
                || Objects.equals(AFFIRM_RETRIEVE.getId(), o.getNodeId())
                || Objects.equals(CREATE_CLAIM_ORDER.getId(), o.getNodeId())
                || Objects.equals(RETRIEVE_NO_FINISH.getId(), o.getNodeId())
                || Objects.equals(RETRIEVE_NO_FINISH_BACK_AMOUNT.getId(), o.getNodeId())
                || Objects.equals(RETRIEVE_FINISH_BACK_AMOUNT_CLAIM_FINISH.getId(), o.getNodeId())
                || Objects.equals(RETRIEVE_FINISH_BACK_ACCOUNT.getId(), o.getNodeId())
                || Objects.equals(DISPUTE_AFTER_SALE.getId(), o.getNodeId())
                || Objects.equals(DISPUTE_AFTER_CLAIM.getId(), o.getNodeId()))
            .map(AssScheduleLogAppService::convertToRespAssScheduleNodeDTO).filter(Objects::nonNull).toList();
        respList.forEach(item -> {
            item.setColor(2);
            AssWoType assWoType = AssWoType.fromCode(assSchedule.getAssType());
            if (AssWoType.RETRIEVE == assWoType) {
                AssScheduleNode scheduleNode = AssScheduleNode.getById(item.getNodeId());
                String retrieveMsg = Objects.nonNull(scheduleNode) ? scheduleNode.getRetrieveMsg() : "";
                item.setNodeDesc(StringUtils.isNotBlank(retrieveMsg) ? retrieveMsg : item.getNodeDesc());
            } else if (AssWoType.DISPUTE == assWoType) {
                AssScheduleNode scheduleNode = AssScheduleNode.getById(item.getNodeId());
                String disputeMsg = Objects.nonNull(scheduleNode) ? scheduleNode.getDisputeMsg() : "";
                item.setNodeDesc(StringUtils.isNotBlank(disputeMsg) ? disputeMsg : item.getNodeDesc());
            }
            if (assSchedule.getFinish()) {
                // 工单已完结
                item.setColor(1);
            }
            if (Objects.equals(REJECT.getId(), item.getNodeId())) {
                // 驳回
                item.setColor(3);
            }
        });
        return new AssScheduleRespMobileDTO().setRoomId(assSchedule.getRoomId()).setFinish(assSchedule.getFinish())
            .setNodes(respList);
    }

    static <T> Predicate<T> distinctByKey(Function<? super T, ?> keyExtractor) {
        Map<Object, Boolean> seen = new ConcurrentHashMap<>();
        return t -> seen.putIfAbsent(keyExtractor.apply(t), Boolean.TRUE) == null;
    }

    private static RespAssScheduleNodeDTO convertToRespAssScheduleNodeDTO(AssScheduleLog item) {
        if (item == null) {
            return null;
        }
        RespAssScheduleNodeDTO result = new RespAssScheduleNodeDTO();
        result.setNodeId(item.getNodeId());
        result.setNodeDesc(item.getNodeDesc());
        result.setCreateTime(item.getCreateTime());
        result.setCreateUserId(item.getCreateUserId());
        result.setData(item.getData());
        return result;
    }
}
