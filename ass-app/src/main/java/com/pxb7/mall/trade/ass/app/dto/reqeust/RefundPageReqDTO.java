package com.pxb7.mall.trade.ass.app.dto.reqeust;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.pxb7.mall.trade.order.client.dto.PageDTO;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class RefundPageReqDTO extends PageDTO {
    public static Integer IDENTITY_BUYER = 0;
    public static Integer IDENTITY_SELLER = 1;

    /**
     * 周期
     */
    private Integer cycle;

    /**
     * 商品类型
     */
    private Integer productType;

    /**
     * 游戏
     */
    private String gameId;

    /**
     * 关键词
     */
    private String keyWords;

    /**
     * 身份 0 买家(我买的) 1 卖家（我卖的）
     */
    @NotNull(message = "买卖身份不能为空")
    private Integer identity;
    /** 订单编号 */
    private String orderItemId;
    /** 手机号（子账号） */
    private String telephone;
    /** 商品名称 */
    private String productName;
    /** 商品id */
    private String productId;
    /** 商品编号 */
    private String productUniqueNo;
    /** 开始时间 */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS")
    private String startTime;
    /** 结束时间 */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS")
    private String endTime;
}
