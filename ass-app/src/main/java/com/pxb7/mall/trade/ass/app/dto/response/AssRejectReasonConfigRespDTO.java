package com.pxb7.mall.trade.ass.app.dto.response;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.ToString;


/**
 * 驳回原因配置表(AssRejectReasonConfig)实体类
 *
 * <AUTHOR>
 * @since 2025-07-30 13:59:49
 */
public class AssRejectReasonConfigRespDTO {


    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class DetailDTO {
        /**
         * 自增id
         */
        private Long id;
        /**
         * 业务主键
         */
        private String reasonConfigId;
        /**
         * 售后类型1找回2纠纷
         */
        private Integer assType;
        /**
         * 驳回原因文案
         */
        private String reason;
        /**
         * 用户测文案
         */
        private String userDesc;
        /**
         * 排序
         */
        private Integer sort;
    }
}

