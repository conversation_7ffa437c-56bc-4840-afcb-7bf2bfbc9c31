package com.pxb7.mall.trade.ass.app;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.pxb7.mall.im.client.api.AdminImUserServiceI;
import com.pxb7.mall.im.client.dto.request.CustomerCareLastFeishuAuthReqDTO;
import com.pxb7.mall.im.client.dto.response.CustomerCareLastFeishuAuthRespDTO;
import com.pxb7.mall.trade.ass.app.dto.response.AssDealUserList;
import com.pxb7.mall.trade.ass.app.mapping.AssRetrieveAppMapping;
import com.pxb7.mall.trade.ass.client.dto.response.afc.RetrieveWORespDTO;
import com.pxb7.mall.trade.ass.client.enums.*;
import com.pxb7.mall.trade.ass.domain.*;
import com.pxb7.mall.trade.ass.domain.model.reqeust.RecordBO;
import com.pxb7.mall.trade.ass.infra.constant.AssWoConstant;
import com.pxb7.mall.trade.ass.infra.repository.db.AssAnswerRepository;
import com.pxb7.mall.trade.ass.infra.repository.db.AssRetrieveWoRepository;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.AssRetrieveWo;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.AssSchedule;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.AssScheduleLog;
import com.pxb7.mall.trade.ass.infra.util.AssNoUtil;
import com.pxb7.mall.trade.ass.infra.util.IdGenUtil;
import com.pxb7.mall.trade.ass.infra.util.OptionalUtil;
import com.pxb7.mall.trade.order.client.api.InsuranceDubboService;
import com.pxb7.mall.trade.order.client.dto.response.order.dubbo.OrderInfoDubboRespDTO;
import com.pxb7.mall.user.api.SysUserServiceI;
import com.pxb7.mall.user.dto.request.sys.SysUserReqDTO;
import com.pxb7.mall.user.dto.response.sys.SysUserRespDTO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;

import static com.pxb7.mall.trade.ass.client.enums.AssAssistance.YES;

/**
 * 售后找回工单
 *
 * <AUTHOR>
 **/
@Slf4j
@Service
public class AssRetrieveWoAppService {

    @DubboReference
    private SysUserServiceI sysUserServiceI;
    @DubboReference
    private AdminImUserServiceI adminImUserServiceI;
    @Resource
    private AssAnswerRepository assAnswerRepository;
    @DubboReference
    private InsuranceDubboService insuranceDubboService;
    @Resource
    private AssRetrieveWoRepository assRetrieveWoRepository;
    @Resource
    private AssScheduleDomainService assScheduleDomainService;
    @Resource
    private AssFollowupLogDomainService assFollowupLogDomainService;
    @Resource
    private AssScheduleLogDomainService assScheduleLogDomainService;
    @Resource
    private AssWorkOrderDomainService assWorkOrderDomainService;

    public String createRetrieveWo(String userId,
                                   AssSchedule schedule,
                                   OrderInfoDubboRespDTO orderInfo,
                                   AssWoType assWoType,
                                   AssAssistance assAssistance,
                                   CustomerCareLastFeishuAuthRespDTO authRespDTO,
                                   String assistanceUserDesc
                                   ) {
        AssRetrieveWo assRetrieveWo = new AssRetrieveWo();
        LocalDateTime expectedTime = LocalDateTime.now().plusDays(AssWoConstant.EXPECTED_TIME);
        // 查询保险信息
        SingleResponse<Boolean> response = insuranceDubboService.queryInsurancePolicyBlnExist(schedule.getOrderItemId());
        boolean insure = response.getData();
        String feiShuName = Objects.nonNull(authRespDTO)
            ? (authRespDTO.getFeishuName() + "(" + authRespDTO.getFeishuEmployeeNo() + ")")
            : StringUtils.EMPTY;
        String workOrderId = IdGenUtil.generateId();
        assRetrieveWo.setAssRetrieveId(workOrderId);
        assRetrieveWo.setWorkOrderNo(AssNoUtil.generateAssNo(AssWoType.RETRIEVE));
        assRetrieveWo.setOrderItemId(schedule.getOrderItemId());
        assRetrieveWo.setProductId(orderInfo.getProductId());
        assRetrieveWo.setProductUniqueNo(orderInfo.getProductUniqueNo());
        assRetrieveWo.setProductType(orderInfo.getProductType());
        assRetrieveWo.setGameId(orderInfo.getGameId());
        assRetrieveWo.setProposerUserId(schedule.getCreateUserId());
        assRetrieveWo.setSponsor(schedule.getSourceType());
        String recvCustomerId = OptionalUtil.ofBlank(schedule.getScheduleId())
            .map(assScheduleLogDomainService::findListByScheduleId)
            .flatMap(o -> o.stream()
                .filter(s -> Objects.equals(s.getNodeId(), AssScheduleNode.SEND_QUESTION.name()))
                .min(Comparator.comparing(AssScheduleLog::getId))
                .map(AssScheduleLog::getCreateUserId))
            .orElse("");
        assRetrieveWo.setRecvCustomerId(recvCustomerId);
        // 获取im飞书信息
        CustomerCareLastFeishuAuthRespDTO recvAuthRespDTO = OptionalUtil.ofBlank(recvCustomerId)
            .map(o -> CustomerCareLastFeishuAuthReqDTO.builder().userId(o).build())
            .map(adminImUserServiceI::customerCareLastFeishuAuth)
            .map(SingleResponse::getData)
            .orElse(null);
        assRetrieveWo.setRecvFeishuName(Objects.nonNull(recvAuthRespDTO)
            ? (recvAuthRespDTO.getFeishuName() + "(" + recvAuthRespDTO.getFeishuEmployeeNo() + ")")
            : StringUtils.EMPTY);
        assRetrieveWo.setAuditCustomerId(userId);
        assRetrieveWo.setAuditFeishuName(feiShuName);
        assRetrieveWo.setDataSource(AssDataSource.DEFAULT.getCode());
        assRetrieveWo.setSourceType(AssSourceType.WEB.getCode());
        assRetrieveWo.setExpectedTime(expectedTime);
        assRetrieveWo.setStatus(AssStatus.DETERMINE.getCode());
        assRetrieveWo.setDealResult(AssRetrieveDealResult.DEFAULT.getCode());
        assRetrieveWo.setClaimStatus(AssWoClaimStatus.NONE.getCode());
        assRetrieveWo.setInsure(insure ? 1 : 0);
        assRetrieveWo.setCreateUserId(userId);
        assRetrieveWo.setCreateFeishuName(feiShuName);
        assRetrieveWo.setUpdateUserId(userId);
        assRetrieveWo.setAssistanceWorkOrder(assAssistance.getCode());
        assRetrieveWo.setAssistanceConfigDesc(assistanceUserDesc);
        assRetrieveWo.setDataCreateSource(AssDataCreateSource.IM.getCode());
        String extendDesc = "";
        if (YES == assAssistance) {
            extendDesc = assAssistance.getDesc();
        }

        // 保存数据库
        boolean flag = assRetrieveWoRepository.save(assRetrieveWo);
        // 工单关联流程节点
        assScheduleDomainService.updateWorkOrderIdAndAssTypeAndFinishById(schedule.getId(), workOrderId, assWoType.getCode(), AssScheduleNode.CREATE_WORK_ORDER.isFinish(), userId);
        assScheduleLogDomainService.add(schedule.getScheduleId(), AssScheduleNode.CREATE_WORK_ORDER, "", assWoType, userId, extendDesc);
        // 关联回答的问题
        assAnswerRepository.relationAssAnswer(workOrderId, schedule.getOrderItemId());
        // 记录操作日志
        RecordBO recordBO = new RecordBO().setWorkOrderId(assRetrieveWo.getAssRetrieveId())
            .setAssType(AssWoType.RETRIEVE.getCode())
            .setOperatorId(assRetrieveWo.getCreateUserId())
            .setTitle(AssOperatorLog.ADD_WORD_ORDER.getDesc())
            .setContent(AssOperatorLog.ADD_WORD_ORDER.getDesc() + assRetrieveWo.getWorkOrderNo())
            .setFeiShuName(feiShuName);
        assFollowupLogDomainService.recordLog(recordBO);
        assWorkOrderDomainService.createWorkOrder(recordBO, schedule, workOrderId);
        return flag ? workOrderId : null;
    }

    public List<AssDealUserList> dealUserList() {
        MultiResponse<SysUserRespDTO> response = sysUserServiceI.listByCondition(new SysUserReqDTO());
        return response.getData().stream()
            .map(item -> new AssDealUserList().setUserId(item.getUserId()).setUserName(item.getUserName())).toList();

    }

    public RetrieveWORespDTO searchRetrieveWOById(String workOrderId) {
        return AssRetrieveAppMapping.INSTANCE.toRetrieveWORespDTO(assRetrieveWoRepository.getById(workOrderId));
    }
}
