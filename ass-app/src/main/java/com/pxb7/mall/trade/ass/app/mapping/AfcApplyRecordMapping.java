package com.pxb7.mall.trade.ass.app.mapping;

import com.pxb7.mall.trade.ass.app.dto.reqeust.ApplyAfcReqDTO;
import com.pxb7.mall.trade.ass.domain.model.assSchedule.ReqCreateAssScheduleBO;
import com.pxb7.mall.trade.ass.domain.model.reqeust.ApplyAfcRecordReqBO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * Copyright (C), 2002-2025, 螃蟹游戏服务网
 *
 * @fileName: AfcApplyRecordMapping.java
 * @description: 售后申请记录对象映射
 * @author: guominqiang
 * @email: <EMAIL>
 * @date: 2025/4/11 15:09
 * @history: //修改记录
 * <author> <time> <version> <desc>
 * 修改人姓名 修改时间 版本号 描述
 */
@Mapper
public interface AfcApplyRecordMapping {

    AfcApplyRecordMapping INSTANCE = Mappers.getMapper(AfcApplyRecordMapping.class);

    @Mappings({@Mapping(source = "deliveryRoomId", target = "roomId")})
    ApplyAfcRecordReqBO applyAfcReqDTO2ApplyAfcRecordReqBO(ApplyAfcReqDTO applyAfcReqDTO);

    @Mappings({@Mapping(source = "orderItemId", target = "orderNo"),
        @Mapping(source = "deliveryRoomId", target = "roomId")})
    ReqCreateAssScheduleBO applyAfcRecordReqDTO2AfcScheduleBO(ApplyAfcReqDTO applyAfcReqDTO);
}
