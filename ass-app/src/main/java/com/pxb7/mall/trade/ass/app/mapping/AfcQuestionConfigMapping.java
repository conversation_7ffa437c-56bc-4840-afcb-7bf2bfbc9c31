package com.pxb7.mall.trade.ass.app.mapping;

import com.pxb7.mall.trade.ass.app.dto.response.AfcQuestionConfRespDTO;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.AfcQuestionConf;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * Copyright (C), 2002-2025, 螃蟹游戏服务网
 *
 * @fileName: AfcQuestionConfigMapping.java
 * @description: 售后问题配置
 * @author: guominqiang
 * @email: <EMAIL>
 * @date: 2025/4/11 14:29
 * @history: //修改记录
 * <author> <time> <version> <desc>
 * 修改人姓名 修改时间 版本号 描述
 */
@Mapper
public interface AfcQuestionConfigMapping {
    AfcQuestionConfigMapping INSTANCE = Mappers.getMapper(AfcQuestionConfigMapping.class);

    List<AfcQuestionConfRespDTO> toAfcQuestionConfRespDTOList(List<AfcQuestionConf> afcQuestionConfList);
}
