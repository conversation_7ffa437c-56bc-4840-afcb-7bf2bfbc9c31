package com.pxb7.mall.trade.ass.app.dto.reqeust.complaint.req;

import java.io.Serializable;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * Copyright (C), 2002-2024, 螃蟹游戏服务网
 *
 * @fileName: ComplaintWorkOrderReqDTO.java
 * @description: 创建客诉工单请求DTO
 * @author: guo<PERSON>qiang
 * @email: <EMAIL>
 * @date: 2024/9/20 17:14
 * @history: //修改记录
 * <author> <time> <version> <desc>
 * 修改人姓名 修改时间 版本号 描述
 */
@Data
public class ComplaintWorkOrderReqDTO implements Serializable {
    /**
     * 订单id
     */
    private String orderItemId;
    /**
     * 房间id
     */
    @NotBlank(message = "房间id不能为空")
    private String roomId;
    /**
     * 投诉标题
     */
    @NotBlank(message = "投诉标题不能为空")
    private String complaintTitle;

    /**
     * 投诉渠道 1:IM 2支付宝 3闲鱼 4:12315 5消费宝 6连连支付 7电话 8反诈邮箱 9外部门升级 10黑猫投诉 11工商局 12工信部
     */
    @NotNull(message = "投诉渠道不能为空")
    private Integer complaintChannel;

    /**
     * 投诉人手机号
     */
    @NotBlank(message = "投诉人手机号不能为空")
    private String complaintPhone;

    /**
     * 用户id
     */
    @NotBlank(message = "用户id不能为空")
    private String userId;

    /**
     * 投诉人身份1买家 2卖家 3其他
     */
    @NotNull(message = "投诉人身份不能为空")
    private Integer complaintRole;
    /**
     * 投诉级别: 1-6 1一级 2:二级 ...6:六级
     */
    @NotNull(message = "投诉级别不能为空")
    private Integer complaintLevel;
    /**
     * 投诉文字内容
     */
    @NotBlank(message = "投诉与建议内容不能为空")
    private String complaintContent;

    /**
     * 图片url，最多允许六张图片,逗号分隔
     */
    private String complaintImg;

    /**
     * 创建来源(1:系统,2:IM)
     */
    private Integer complaintSource = 2;

}
