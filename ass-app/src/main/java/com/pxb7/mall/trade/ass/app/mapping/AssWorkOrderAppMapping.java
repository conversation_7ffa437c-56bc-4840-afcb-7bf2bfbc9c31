package com.pxb7.mall.trade.ass.app.mapping;

import com.pxb7.mall.trade.ass.app.dto.reqeust.AssWorkOrderReqDTO;
import com.pxb7.mall.trade.ass.app.dto.response.AssWorkOrderRespDTO;
import com.pxb7.mall.trade.ass.domain.model.AssWorkOrderReqBO;
import com.pxb7.mall.trade.ass.domain.model.AssWorkOrderRespBO;

import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;


@Mapper
public interface AssWorkOrderAppMapping {

    AssWorkOrderAppMapping INSTANCE = Mappers.getMapper(AssWorkOrderAppMapping.class);


    AssWorkOrderReqBO.AddBO assWorkOrderDTO2AddBO(AssWorkOrderReqDTO.AddDTO source);

    AssWorkOrderReqBO.UpdateBO assWorkOrderDTO2UpdateBO(AssWorkOrderReqDTO.UpdateDTO source);

    AssWorkOrderReqBO.DelBO assWorkOrderDTO2DelBO(AssWorkOrderReqDTO.DelDTO source);

    AssWorkOrderReqBO.SearchBO assWorkOrderDTO2SearchBO(AssWorkOrderReqDTO.SearchDTO source);

    AssWorkOrderReqBO.PageBO assWorkOrderDTO2PageBO(AssWorkOrderReqDTO.PageDTO source);

    AssWorkOrderRespDTO.DetailDTO assWorkOrderBO2DetailDTO(AssWorkOrderRespBO.DetailBO source);

    List<AssWorkOrderRespDTO.DetailDTO> assWorkOrderBO2ListDTO(List<AssWorkOrderRespBO.DetailBO> source);

    Page<AssWorkOrderRespDTO.DetailDTO> assWorkOrderBO2PageDTO(Page<AssWorkOrderRespBO.DetailBO> source);

    AssWorkOrderReqBO.PageAssOrderBO assWorkOrderDTO2PageAssOrderBO(AssWorkOrderReqDTO.PageOrderDTO param);

    List<AssWorkOrderRespDTO.PageDetailDTO> assInfoPageDetailBO2PageDetailDTO(List<AssWorkOrderRespBO.AssInfoPageDetailBO> data);
}


