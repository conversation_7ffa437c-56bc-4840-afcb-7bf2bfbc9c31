package com.pxb7.mall.trade.ass.app.mapping;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.base.Joiner;
import com.pxb7.mall.trade.ass.app.dto.reqeust.AssWorkOrderReqDTO;
import com.pxb7.mall.trade.ass.app.dto.response.AssWorkOrderRespDTO;
import com.pxb7.mall.trade.ass.client.enums.AssWoType;
import com.pxb7.mall.trade.ass.domain.model.AssWorkOrderLogRespBO;
import com.pxb7.mall.trade.ass.domain.model.AssWorkOrderReqBO;
import com.pxb7.mall.trade.ass.domain.model.AssWorkOrderRespBO;

import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import com.pxb7.mall.trade.ass.infra.repository.db.entity.AssRetrieveGameConfigDetail;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.AssRetrieveWo;
import com.pxb7.mall.trade.order.client.dto.request.orderItemIndemnity.OrderItemIndemnityDubboDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;


@Mapper
public interface AssWorkOrderAppMapping {

    AssWorkOrderAppMapping INSTANCE = Mappers.getMapper(AssWorkOrderAppMapping.class);


    AssWorkOrderReqBO.AddBO assWorkOrderDTO2AddBO(AssWorkOrderReqDTO.AddDTO source);

    AssWorkOrderReqBO.UpdateBO assWorkOrderDTO2UpdateBO(AssWorkOrderReqDTO.UpdateDTO source);

    AssWorkOrderReqBO.DelBO assWorkOrderDTO2DelBO(AssWorkOrderReqDTO.DelDTO source);

    AssWorkOrderReqBO.SearchBO assWorkOrderDTO2SearchBO(AssWorkOrderReqDTO.SearchDTO source);

    AssWorkOrderReqBO.PageBO assWorkOrderDTO2PageBO(AssWorkOrderReqDTO.PageDTO source);

    AssWorkOrderRespDTO.DetailDTO assWorkOrderBO2DetailDTO(AssWorkOrderRespBO.DetailBO source);

    List<AssWorkOrderRespDTO.DetailDTO> assWorkOrderBO2ListDTO(List<AssWorkOrderRespBO.DetailBO> source);

    Page<AssWorkOrderRespDTO.DetailDTO> assWorkOrderBO2PageDTO(Page<AssWorkOrderRespBO.DetailBO> source);

    AssWorkOrderReqBO.PageAssOrderBO assWorkOrderDTO2PageAssOrderBO(AssWorkOrderReqDTO.PageOrderDTO param);

    List<AssWorkOrderRespDTO.PageDetailDTO> assInfoPageDetailBO2PageDetailDTO(List<AssWorkOrderRespBO.AssInfoPageDetailBO> data);


    @Mapping(source = "indemnityName", target = "indemnityNameStr")
    AssWorkOrderRespDTO.PageDetailDTO convertPageDetailDTO(AssWorkOrderRespBO.AssInfoPageDetailBO data);

    default AssWorkOrderRespDTO.AssWorkOrderDetailDTO assInfoPageDetailBO2AssWorkOrderDetailDTO(AssWorkOrderRespBO.AssInfoPageDetailBO assInfo,
                                                                                                List<OrderItemIndemnityDubboDTO> indemnityList,
                                                                                                AssRetrieveWo assRetrieveWo,
                                                                                                List<AssWorkOrderLogRespBO.DetailBO> logList,
                                                                                                AssRetrieveGameConfigDetail gameConfig,
                                                                                                AssWorkOrderRespBO.DetailBO workOrderBO
    ) {

        AssWorkOrderRespDTO.AssWorkOrderDetailDTO workOrder = new AssWorkOrderRespDTO.AssWorkOrderDetailDTO();
        workOrder.setAssType(workOrderBO.getAssType());
        workOrder.setWorkOrderId(workOrderBO.getWorkOrderId());
        workOrder.setAssApplyTime(workOrderBO.getApplyTime());
        workOrder.setAssStatus(workOrderBO.getAssStatus());
        workOrder.setAssStatusMemo(workOrderBO.getAssStatusMemo());

        //工单进度日志信息
        if (CollUtil.isNotEmpty(logList)) {
            List<AssWorkOrderRespDTO.AssWorkOrderLogDTO> logDTOList = logList.stream().map(log -> {
                        AssWorkOrderRespDTO.AssWorkOrderLogDTO assWorkOrderLogDTO = new AssWorkOrderRespDTO.AssWorkOrderLogDTO();
                        assWorkOrderLogDTO.setWorkOrderLogId(log.getWorkOrderLogId());
                        assWorkOrderLogDTO.setTitle(log.getTitle());
                        assWorkOrderLogDTO.setContent(log.getContent());
                        assWorkOrderLogDTO.setAssType(log.getAssType());
                        assWorkOrderLogDTO.setAddWay(log.getAddWay());
                        assWorkOrderLogDTO.setCreateUserId(log.getCreateUserId());
                        assWorkOrderLogDTO.setCreateTime(log.getCreateTime());
                        assWorkOrderLogDTO.setJoinGroupMsg(log.getJoinGroupMsg());
                        return assWorkOrderLogDTO;
                    }).sorted(Comparator.comparing(AssWorkOrderRespDTO.AssWorkOrderLogDTO::getCreateTime).reversed())
                    .collect(Collectors.toList());
            workOrder.setLogList(logDTOList);
        }
        //交易信息
        workOrder.setOrderItemId(assInfo.getOrderItemId());
        workOrder.setProductId(assInfo.getProductId());
        workOrder.setBuyerUserId(assInfo.getBuyerId());
        workOrder.setGameId(assInfo.getGameId());
        workOrder.setProductType(assInfo.getProductType());
        workOrder.setOrderItemStatus(assInfo.getOrderItemStatus());
        workOrder.setOrderItemAmount(assInfo.getOrderItemAmount());
        workOrder.setOrderItemPayAmount(assInfo.getOrderItemPayAmount());
        workOrder.setProductSalePrice(assInfo.getProductSalePrice());
        workOrder.setOrderItemActualPayAmount(assInfo.getOrderItemActualPayAmount());
        workOrder.setProductQuantity(assInfo.getProductQuantity());
        workOrder.setCompleteTime(assInfo.getCompleteTime());
        workOrder.setCancelTime(assInfo.getCancelTime());
        workOrder.setCreateTime(assInfo.getCreateTime());
        workOrder.setBuyerPhone(assInfo.getBuyerPhone());

        //商品信息
        workOrder.setProductName(assInfo.getProductName());
        workOrder.setProductShortName(assInfo.getProductShortName());
        workOrder.setProductPic(assInfo.getProductPic());
        workOrder.setProductAttr(assInfo.getProductAttr());
        workOrder.setGameAccount(assInfo.getGameAccount());
        workOrder.setProductUniqueNo(assInfo.getProductUniqueNo());
        workOrder.setGameName(assInfo.getGameName());
        workOrder.setGameAttr(assInfo.getGameAttr());

        AssWorkOrderRespDTO.DetailProductInfoRespDTO detailProductInfo = new AssWorkOrderRespDTO.DetailProductInfoRespDTO();
        workOrder.setDetailProductInfoRespDTO(detailProductInfo);

        detailProductInfo.setGameAttr(assInfo.getGameAttr());
        detailProductInfo.setGameId(assInfo.getGameId());
        detailProductInfo.setGameName(assInfo.getGameName());
        detailProductInfo.setProductAttr(assInfo.getProductAttr());
        detailProductInfo.setProductHighlight("");
        detailProductInfo.setProductId(assInfo.getProductId());
        detailProductInfo.setProductName(assInfo.getProductName());
        detailProductInfo.setProductOriginalPrice(null);
        detailProductInfo.setProductPayAmount(null);
        detailProductInfo.setProductPicture(assInfo.getProductPic());
        detailProductInfo.setProductQuantity(assInfo.getProductQuantity());
        detailProductInfo.setGameAttr(assInfo.getGameAttr());
        detailProductInfo.setProductType(assInfo.getProductType());

        //房间号
        workOrder.setDeliveryRoomId(assInfo.getDeliveryRoomId());

        if (Objects.nonNull(assRetrieveWo)) {
            workOrder.setIsAssistant(Objects.equals(assRetrieveWo.getAssistanceWorkOrder(), 1));
            if (BooleanUtil.isTrue(workOrder.getIsAssistant())) {
                workOrder.setIndemnityReasonDesc(assRetrieveWo.getAssistanceUserDesc());
            }
        }

        if (CollUtil.isNotEmpty(indemnityList)) {
            String indemnityNameStr = Joiner.on("/").join(indemnityList.stream()
                    .map(OrderItemIndemnityDubboDTO::getIndemnityName)
                    .collect(Collectors.toSet()));
            //包赔信息
            workOrder.setIndemnityNameStr(indemnityNameStr);
        }
        if (Objects.nonNull(gameConfig)) {
            if (Objects.equals(AssWoType.RETRIEVE.getCode(), workOrder.getAssType())) {
                workOrder.setRetrieveDays(gameConfig.getRetrieveDays());
            }
            workOrder.setShowJoinGroup(gameConfig.getGroupOpen());
        }

        if (Objects.equals(AssWoType.RETRIEVE.getCode(), workOrder.getAssType())) {
            if (Objects.nonNull(gameConfig) && Objects.nonNull(gameConfig.getRetrieveDays())) {
                workOrder.setRetrieveDays(gameConfig.getRetrieveDays());
                workOrder.setShowJoinGroup(gameConfig.getGroupOpen());
            } else {
                workOrder.setRetrieveDays(15);
                workOrder.setShowJoinGroup(false);
            }
        }
        return workOrder;
    }
}


