package com.pxb7.mall.trade.ass.app.common;

import cn.dev33.satoken.error.SaErrorCode;
import cn.dev33.satoken.exception.NotLoginException;
import com.pxb7.mall.auth.c.annotation.StpUserUtil;
import com.pxb7.mall.auth.c.util.AdminUserUtil;
import com.pxb7.mall.auth.c.util.ImUserUtil;
import com.pxb7.mall.auth.c.util.LoginUserUtil;
import com.pxb7.mall.auth.c.util.MerchantUserUtil;
import com.pxb7.mall.auth.dto.ImUserDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import static cn.dev33.satoken.exception.NotLoginException.NOT_TOKEN;
import static cn.dev33.satoken.exception.NotLoginException.NOT_TOKEN_MESSAGE;

@Slf4j
public class UserIdUtils {

    public static String getUserId() {
        String loginUserId = LoginUserUtil.getUserId();
        if (StringUtils.isNotBlank(loginUserId)) {
            return loginUserId;
        }
        String merchantUserId = MerchantUserUtil.getUserId();
        if (StringUtils.isNotBlank(merchantUserId)) {
            return merchantUserId;
        }
        String userId = AdminUserUtil.getUserId();
        if (StringUtils.isNotBlank(userId)) {
            return userId;
        }
        String imUserId = ImUserUtil.getUserId();
        if (StringUtils.isNotBlank(imUserId)) {
            return imUserId;
        }
        throw NotLoginException.newInstance(StpUserUtil.getLoginType(), NOT_TOKEN, NOT_TOKEN_MESSAGE, null)
            .setCode(SaErrorCode.CODE_11011);
    }

    public static String getUserName() {
        String loginUserName = LoginUserUtil.getUserName();
        if (StringUtils.isNotBlank(loginUserName)) {
            return loginUserName;
        }
        String merchantUserName = MerchantUserUtil.getUserName();
        if (StringUtils.isNotBlank(merchantUserName)) {
            return merchantUserName;
        }
        return ImUserUtil.getUserName();
    }

    public static Boolean isMerchant() {
        return StringUtils.isNotBlank(MerchantUserUtil.getMerchantId());
    }

    public static Boolean isUser() {
        return StringUtils.isNotBlank(LoginUserUtil.getUserId());
    }

    public static Boolean isAdmin() {
        String loginUserId = LoginUserUtil.getUserId();
        if (StringUtils.isNotBlank(loginUserId)) {
            return false;
        }
        String merchantUserId = MerchantUserUtil.getUserId();
        if (StringUtils.isNotBlank(merchantUserId)) {
            return false;
        }
        String userId = AdminUserUtil.getUserId();
        if (StringUtils.isNotBlank(userId)) {
            // 后台用户
            return true;
        }
        ImUserDTO imUser = ImUserUtil.getImUser();
        if (imUser != null) {
            // 1是客服，其他都不是
            return imUser.getUserType() == 1;
        }

        throw NotLoginException.newInstance(StpUserUtil.getLoginType(), NOT_TOKEN, NOT_TOKEN_MESSAGE, null)
                .setCode(SaErrorCode.CODE_11011);
    }

    public static String getMerchantId() {
        return MerchantUserUtil.getMerchantId();
    }
}
