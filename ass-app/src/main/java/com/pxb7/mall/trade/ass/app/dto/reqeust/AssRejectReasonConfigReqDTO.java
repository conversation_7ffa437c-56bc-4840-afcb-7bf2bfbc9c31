package com.pxb7.mall.trade.ass.app.dto.reqeust;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Min;
import lombok.ToString;


/**
 * 驳回原因配置表(AssRejectReasonConfig)实体类
 *
 * <AUTHOR>
 * @since 2025-07-30 13:59:49
 */
public class AssRejectReasonConfigReqDTO {

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class AddDTO {

        /**
         * 业务主键
         */
        @NotBlank(message = "reasonConfigId不能为空")
        private String reasonConfigId;

        /**
         * 售后类型1找回2纠纷
         */
        @NotNull(message = "assType不能为空")
        private Integer assType;

        /**
         * 驳回原因文案
         */
        @NotBlank(message = "reason不能为空")
        private String reason;

        /**
         * 用户测文案
         */
        @NotBlank(message = "userDesc不能为空")
        private String userDesc;

        /**
         * 排序
         */
        @NotNull(message = "sort不能为空")
        private Integer sort;

    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class UpdateDTO {

        /**
         * 自增id
         */
        @NotNull(message = "id不能为空")
        private Long id;


        /**
         * 业务主键
         */
        @NotBlank(message = "reasonConfigId不能为空")
        private String reasonConfigId;


        /**
         * 售后类型1找回2纠纷
         */
        @NotNull(message = "assType不能为空")
        private Integer assType;


        /**
         * 驳回原因文案
         */
        @NotBlank(message = "reason不能为空")
        private String reason;


        /**
         * 用户测文案
         */
        @NotBlank(message = "userDesc不能为空")
        private String userDesc;


        /**
         * 排序
         */
        @NotNull(message = "sort不能为空")
        private Integer sort;


    }


    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class DelDTO {
        @NotNull(message = "id不能为空")
        private Long id;
    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class SearchDTO {
        /**
         * 业务主键
         */
        @NotBlank(message = "reasonConfigId不能为空")
        private String reasonConfigId;

        /**
         * 售后类型1找回2纠纷
         */
        @NotNull(message = "assType不能为空")
        private Integer assType;

        /**
         * 驳回原因文案
         */
        @NotBlank(message = "reason不能为空")
        private String reason;

        /**
         * 用户测文案
         */
        @NotBlank(message = "userDesc不能为空")
        private String userDesc;

        /**
         * 排序
         */
        @NotNull(message = "sort不能为空")
        private Integer sort;

    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class PageDTO {

        /**
         * 业务主键
         */
        @NotBlank(message = "reasonConfigId不能为空")
        private String reasonConfigId;

        /**
         * 售后类型1找回2纠纷
         */
        @NotNull(message = "assType不能为空")
        private Integer assType;

        /**
         * 驳回原因文案
         */
        @NotBlank(message = "reason不能为空")
        private String reason;

        /**
         * 用户测文案
         */
        @NotBlank(message = "userDesc不能为空")
        private String userDesc;

        /**
         * 排序
         */
        @NotNull(message = "sort不能为空")
        private Integer sort;


        /**
         * 页码，从1开始
         */
        @NotNull(message = "pageIndex不能为空")
        @Min(value = 1, message = "pageIndex不能小于1")
        private Integer pageIndex;
        /**
         * 每页数量
         */
        @NotNull(message = "pageSize不能为空")
        private Integer pageSize;

    }

}

