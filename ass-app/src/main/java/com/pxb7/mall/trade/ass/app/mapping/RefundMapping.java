package com.pxb7.mall.trade.ass.app.mapping;

import com.pxb7.mall.trade.ass.domain.model.OrderItemAmountInfoBO;
import com.pxb7.mall.trade.ass.domain.refund.model.RefundOrderItemPageRespBO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import com.pxb7.mall.trade.ass.app.dto.reqeust.ExposureCouponRefundReqDTO;
import com.pxb7.mall.trade.ass.app.dto.reqeust.RefundPageReqDTO;
import com.pxb7.mall.trade.ass.app.dto.reqeust.ServiceSubmitRefundReqDTO;
import com.pxb7.mall.trade.ass.app.dto.response.CalculateRefundAmountRespDTO;
import com.pxb7.mall.trade.ass.app.dto.response.RefundDetailRespDTO;
import com.pxb7.mall.trade.ass.app.dto.response.RefundOrderItemPageRespDTO;
import com.pxb7.mall.trade.ass.app.dto.response.RefundPageRespDTO;
import com.pxb7.mall.trade.ass.client.dto.model.order.OrderItemAmountInfo;
import com.pxb7.mall.trade.ass.client.dto.request.UserExposureCouponRefundReqDTO;
import com.pxb7.mall.trade.ass.domain.refund.model.ServiceSubmitRefundBO;
import com.pxb7.mall.trade.ass.infra.model.RefundVoucherSearchPO;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.RefundVoucher;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.RefundVoucherDetail;
import com.pxb7.mall.trade.ass.infra.repository.es.entity.OrderItemAggregation;
import com.pxb7.mall.trade.ass.infra.repository.es.entity.RefundVoucherDoc;

import java.util.List;

@Mapper
public interface RefundMapping {

    RefundMapping INSTANCE = Mappers.getMapper(RefundMapping.class);

    RefundVoucherSearchPO toRefundVoucherSearchPO(RefundPageReqDTO refundPageReqDTO);

    ExposureCouponRefundReqDTO
        toExposureCouponRefundReqDTO(UserExposureCouponRefundReqDTO userExposureCouponRefundReqDTO);

    CalculateRefundAmountRespDTO toCalculateRefundAmountRespDTO(OrderItemAmountInfo refundAmountInfo);

    CalculateRefundAmountRespDTO toCalculateRefundAmountRespDTO(OrderItemAmountInfoBO refundAmountInfo);

    @Mapping(source = "refundVoucher.refundVoucherId", target = "refundVoucherId")
    @Mapping(source = "refundVoucher.orderItemId", target = "orderItemId")
    RefundDetailRespDTO toRefundDetailRespDTO(RefundVoucher refundVoucher, RefundVoucherDetail refundVoucherDetail);

    @Mapping(source = "refundVoucher.refundStatus", target = "refundStatus")
    @Mapping(source = "refundVoucher.buyerPhone", target = "buyerPhone")
    @Mapping(source = "refundVoucher.sellerPhone", target = "sellerPhone")
    @Mapping(source = "refundVoucher.productName", target = "productName")
    @Mapping(source = "orderItemAggregation.orderItemId", target = "orderItemId")
    @Mapping(source = "orderItemAggregation.productType", target = "productType")
    @Mapping(source = "orderItemAggregation.deliveryRoomId", target = "imRoomId")
    @Mapping(target = "feeAmount", ignore = true)
    RefundPageRespDTO toRefundPageRespDTO(RefundVoucherDoc refundVoucher, OrderItemAggregation orderItemAggregation);

    @Mapping(source = "refundVoucher.refundVoucherId", target = "refundVoucherId")
    @Mapping(source = "refundVoucher.orderItemId", target = "orderItemId")
    RefundOrderItemPageRespDTO toRefundOrderItemPageRespDTO(RefundVoucher refundVoucher,
        RefundVoucherDetail refundVoucherDetail);

    ServiceSubmitRefundBO toServiceSubmitRefundBO(ServiceSubmitRefundReqDTO serviceSubmitRefundReqDTO);

    List<RefundOrderItemPageRespDTO> toRefundOrderItemPageRespDTOList(List<RefundOrderItemPageRespBO> orderRefundList);
}
