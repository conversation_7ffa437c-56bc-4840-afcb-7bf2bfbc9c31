package com.pxb7.mall.trade.ass.app.dto.response;

import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/8/13
 */
@Data
public class AssScheduleLogRespDTO {

    /**
     * 节点id
     */
    private String nodeId;

    /**
     * 当前节点状态描述
     */
    private String nodeDesc;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 创建用户编号
     */
    @JsonIgnore
    private String createUserId;

    /**
     * 创建用户
     */
    private String createUser;

    /**
     * 其他数据
     */
    private String data;

}
