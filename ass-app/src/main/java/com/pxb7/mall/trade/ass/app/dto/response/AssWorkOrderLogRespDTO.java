package com.pxb7.mall.trade.ass.app.dto.response;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.ToString;


/**
 * 售后工单日志明细(AssWorkOrderLog)实体类
 *
 * <AUTHOR>
 * @since 2025-07-31 10:22:28
 */
public class AssWorkOrderLogRespDTO {


    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class DetailDTO {
        /**
         * 自增id
         */
        private Long id;
        /**
         * 售后工单号关联ass_work_order.work_order_id
         */
        private String workOrderId;
        /**
         * 日志id
         */
        private String workOrderLogId;
        /**
         * 订单ID
         */
        private String orderItemId;
        /**
         * 标题
         */
        private String title;
        /**
         * 售后类型1找回2纠纷
         */
        private Integer assType;
        /**
         * 0:全展示 1:用户端展示 2:后台展示
         */
        private Integer showType;
        /**
         * 用户端展示操作内容
         */
        private String content;
        /**
         * admin展示操作内容
         */
        private String adminContent;
        /**
         * 日志添加方式1系统自动产生的日志 2手动添加的日志
         */
        private Integer addWay;
        /**
         * 节点id
         */
        private String nodeId;
        /**
         * 当前节点状态描述
         */
        private String nodeDesc;
    }
}

