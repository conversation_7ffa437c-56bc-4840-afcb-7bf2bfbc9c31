package com.pxb7.mall.trade.ass.app.dto.reqeust;

import java.io.Serializable;

import com.pxb7.mall.trade.ass.client.dto.request.refund.IndemnityChangeServiceRefundReqDTO;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.apache.calcite.plan.Strong;

/**
 * 客服Im发起退款请求
 */
@Data
public class ServiceSubmitRefundReqDTO implements Serializable {
    /**
     * 订单行id
     */
    @NotBlank(message = "订单行id不能为空")
    private String orderItemId;

    /**
     * 退款单id，审核时传入，发起时为空
     */
    private String refundVoucherId;

    /**
     * 退款原因
     */
    @NotBlank(message = "退款原因不能为空")
    private String refundReasonId;

    /**
     * 退款号价金额, 部分退款时、退商品差价时输入退款号价金额
     */
    private Long refundAmount;

    /**
     * 退款类别，1，整单退款 2，部分退款
     */
    @NotNull(message = "退款类型不能为空")
    private Integer wholeRefund;

    /**
     * 退款计算方式，0 退商品差价, 1 手动输入退款金额 部分退款时传入 默认商品退差价
     */
    private int calculateType = 0;

    /**
     * 退款号价金额, 部分退款时、手动输入金额时输入退款号价金额
     */
    private Long refundProductAmount;

    /**
     * 退款包赔金额, 部分退款时、手动输入金额时输入退款包赔金额
     */
    private Long refundIndemnityAmount;

    /**
     * 退款手续费金额, 部分退款时、手动输入金额时输入退款手续费金额
     */
    private Long refundFeeAmount;

    /**
     * 退款类型，1，在线退款，2，线下退款，3，挂账
     */
    @NotNull(message = "退款方式不能为空")
    private Integer refundType;

    /**
     * 是否有违约金:true 是
     */
    private Boolean isViolate = false;

    /**
     * 违约金详情
     */
    @Valid
    private RefundViolateDetailReqDTO refundViolateDetail;

    /**
     * 包赔变更
     */
    private IndemnityChangeServiceRefundReqDTO indemnityChangeServiceRefundReq;

    /**
     * 交付房间ID
     */
    private String deliveryRoomId;

}
