package com.pxb7.mall.trade.ass.app;

import java.time.Duration;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import cn.hutool.core.util.StrUtil;
import com.pxb7.mall.trade.ass.infra.remote.dubbo.ImRpcGateway;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson2.JSON;
import com.pxb7.mall.components.redis.redisson.RedissonUtils;
import com.pxb7.mall.im.client.api.SendMsgServiceI;
import com.pxb7.mall.im.client.dto.request.GroupNotifyMsgReqDTO;
import com.pxb7.mall.im.client.dto.request.SetExpansionDTO;
import com.pxb7.mall.im.client.dto.request.card.SendTextMsgReqDTO;
import com.pxb7.mall.im.client.dto.request.card.button.ButtonCon;
import com.pxb7.mall.trade.ass.app.dto.reqeust.AddAssAnswerReqDTO;
import com.pxb7.mall.trade.ass.app.dto.reqeust.TransferQuestionDataDTO;
import com.pxb7.mall.trade.ass.client.enums.AssScheduleNode;
import com.pxb7.mall.trade.ass.infra.enums.ErrorCode;
import com.pxb7.mall.trade.ass.infra.exception.Ensure;
import com.pxb7.mall.trade.ass.infra.repository.db.AssAnswerRepository;
import com.pxb7.mall.trade.ass.infra.repository.db.AssScheduleLogRepository;
import com.pxb7.mall.trade.ass.infra.repository.db.AssScheduleRepository;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.AssAnswer;
import com.pxb7.mall.trade.ass.infra.util.DubboResultAssert;
import com.pxb7.mall.trade.ass.infra.util.IdGenUtil;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

/**
 * 售后问题回答
 *
 * <AUTHOR>
 **/
@Slf4j
@Service
public class AssAnswerAppService {

    @DubboReference
    private SendMsgServiceI sendMsgServiceI;
    @Resource
    private AssAnswerRepository assAnswerRepository;
    @Resource
    private AssScheduleRepository assScheduleRepository;
    @Resource
    private AssScheduleLogRepository assScheduleLogRepository;
    @Resource
    private ImRpcGateway imRpcGateway;

    public String addAssAnswer(AddAssAnswerReqDTO reqDTO, String userId) {
        log.info("addAssAnswer,userId:{},reqDTO:{}", userId, JSON.toJSONString(reqDTO));
        // 校验重复提交
        String strKey = String.format("addassanswer:%s:%s", userId, reqDTO.getRoomId());
        Ensure.that(RedissonUtils.setObjectIfAbsent(strKey, "1", Duration.ofSeconds(2)))
            .isTrue(ErrorCode.REPEAT_SUBMIT);
        // 消息体
        List<TransferQuestionDataDTO> answerList = JSON.parseArray(reqDTO.getAnswer(), TransferQuestionDataDTO.class);
        Ensure.that(answerList).isNotEmpty(ErrorCode.ANSWER_EMPTY);

        String answerId = IdGenUtil.generateId();
        var assSchedule = assScheduleRepository.getByRoomId(reqDTO.getRoomId());
        Ensure.that(assSchedule).isNotNull(ErrorCode.ROOM_NOT_EXISTS);
        var assAnswer = new AssAnswer().setAnswerId(answerId).setAnswer(reqDTO.getAnswer())
            .setOrderItemId(assSchedule.getOrderItemId());
        boolean save = assAnswerRepository.save(assAnswer);
        // 发送消息
        var builder = new StringBuilder();
        builder.append("问题已提交\n");
        answerList.forEach(dto -> {
            String label = dto.getLabel() == null ? "" : dto.getLabel();
            String value = dto.getValue() == null ? "" : dto.getValue();
            builder.append(label).append("【").append(value).append("】");
            builder.append("\n");
        });

        // 发消息
        SendTextMsgReqDTO msg = new SendTextMsgReqDTO().setContent(builder.toString()).setTargetId(reqDTO.getRoomId())
            .setMentionedInfo(Collections.singletonList(assSchedule.getRecvCustomerId()))
            .setFromUserId(userId).setTargetUserIds(Arrays.asList(userId, assSchedule.getRecvCustomerId()));
        String mentionedId = imRpcGateway.getOneAfterSaleCustomerCare(reqDTO.getRoomId(), assSchedule.getAssType());
        if (StrUtil.isNotBlank(mentionedId)) {
            msg.setMentionedInfo(Collections.singletonList(mentionedId));
        }
        sendMsgServiceI.sendTextMsg(msg).getData();
        // 更新节点信息
        assScheduleLogRepository.updateNode(assSchedule.getScheduleId(), AssScheduleNode.SEND_QUESTION.getId(),
            builder.toString());
        // 发融云消息
        GroupNotifyMsgReqDTO msgDto = new GroupNotifyMsgReqDTO();
        msgDto.setGroupId(reqDTO.getRoomId());
        msgDto.setFromUserId(assSchedule.getRecvCustomerId());
        msgDto.setContent(AssScheduleNode.SEND_QUESTION.getId());
        msgDto.setOperation("after_sale_node_message");
        sendMsgServiceI.sendGroupNotify(msgDto);
        // 更新去回答卡片为已回答
        updateToAnswerButton(reqDTO.getRoomId(), userId);
        return save ? answerId : null;
    }

    /**
     * 更新卡片去回答为已回答
     * 
     * @param roomId：
     * @param userId：
     */
    private void updateToAnswerButton(String roomId, String userId) {
        ButtonCon buttonContent = new ButtonCon().setLabel("已回答").setDisabled(true);
        // 可以为非发送卡片的人员
        SetExpansionDTO expansionDTO = new SetExpansionDTO().setTargetId(roomId).setFormUserId(userId)
            .setOperation("ass_question").setButtons(List.of(buttonContent));
        log.info("updateToAnswerButton.expansionDTO:{}", expansionDTO);
        DubboResultAssert.wrapException(() -> sendMsgServiceI.setExpansion(expansionDTO), "500", "im更新卡片异常");
    }

}
