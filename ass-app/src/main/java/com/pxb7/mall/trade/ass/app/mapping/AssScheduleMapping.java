package com.pxb7.mall.trade.ass.app.mapping;

import com.pxb7.mall.trade.ass.app.dto.reqeust.AssScheduleReqDTO;
import com.pxb7.mall.trade.ass.app.dto.reqeust.schedule.ReqCreateAssScheduleDTO;
import com.pxb7.mall.trade.ass.app.dto.response.AssScheduleLogRespDTO;
import com.pxb7.mall.trade.ass.app.dto.response.AssScheduleRespDTO;
import com.pxb7.mall.trade.ass.domain.model.assSchedule.ReqCreateAssScheduleBO;
import com.pxb7.mall.trade.ass.domain.model.reqeust.AssScheduleReqBO;
import com.pxb7.mall.trade.ass.domain.model.response.AssScheduleLogRespBO;
import com.pxb7.mall.trade.ass.domain.model.response.AssScheduleRespBO;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.AssSchedule;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.AssScheduleLog;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/12
 */
@Mapper
public interface AssScheduleMapping {

    AssScheduleMapping INSTANCE = Mappers.getMapper(AssScheduleMapping.class);

    AssScheduleReqBO toAssScheduleReqBO(AssScheduleReqDTO assScheduleReqDTO);

    AssScheduleRespDTO toAssScheduleRespDTO(AssScheduleRespBO assScheduleRespBO);
    List<AssScheduleLogRespDTO> toAssScheduleLogRespDTO(List<AssScheduleLogRespBO> assScheduleLogRespBOs);

    ReqCreateAssScheduleBO toReqCreateAssScheduleBO(ReqCreateAssScheduleDTO assScheduleReqDTO);
}
