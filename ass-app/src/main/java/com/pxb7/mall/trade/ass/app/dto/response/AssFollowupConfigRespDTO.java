package com.pxb7.mall.trade.ass.app.dto.response;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.ToString;


/**
 * 跟进结果类型配置表(AssFollowupConfig)实体类
 *
 * <AUTHOR>
 * @since 2025-07-30 13:44:04
 */
public class AssFollowupConfigRespDTO {


    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class DetailDTO {
        /**
         * 自增id
         */
        private Long id;
        /**
         * 业务主键
         */
        private String followupConfigId;
        /**
         * 售后类型1找回2纠纷
         */
        private Integer assType;
        /**
         * 跟进结果类型
         */
        private String resultDesc;
        /**
         * 售后进度展示文案
         */
        private String progressDesc;
        /**
         * 提醒用户进群文案
         */
        private String joinGroupDesc;
        /**
         * 发送通知类型1:追回账号，待买家换绑2:追回号款，待买家提供收款账号3:售后到期，待买家接收赔付
         */
        private Integer noticeType;
        /**
         * 排序
         */
        private Integer sort;
    }
}

