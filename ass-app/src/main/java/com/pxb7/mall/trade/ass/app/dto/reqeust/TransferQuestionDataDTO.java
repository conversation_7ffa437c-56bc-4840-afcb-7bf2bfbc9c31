package com.pxb7.mall.trade.ass.app.dto.reqeust;

import java.io.Serial;
import java.io.Serializable;

import lombok.Data;

/**
 * 售后问题解析
 *
 * <AUTHOR>
 * @since: 2024-09-25 13:38
 **/
@Data
public class TransferQuestionDataDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = -3857895516359176811L;
    /**
     * 问题
     */
    private String label;
    /**
     * 问题类型(是否radio, 文本textarea)
     */
    private String type;
    /**
     * 答案
     */
    private String value;
}
