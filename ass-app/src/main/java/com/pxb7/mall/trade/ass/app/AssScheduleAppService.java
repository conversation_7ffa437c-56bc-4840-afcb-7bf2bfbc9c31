package com.pxb7.mall.trade.ass.app;

import cn.hutool.core.collection.CollUtil;

import java.time.Duration;
import java.util.function.BinaryOperator;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.pxb7.mall.trade.ass.client.enums.*;
import com.pxb7.mall.trade.ass.domain.*;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.cola.exception.BizException;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.Lists;
import com.pxb7.mall.components.redis.redisson.RedissonUtils;
import com.pxb7.mall.im.client.api.AdminImUserServiceI;
import com.pxb7.mall.im.client.api.ImGroupMemberServiceI;
import com.pxb7.mall.im.client.api.ImGroupServiceI;
import com.pxb7.mall.im.client.api.SendCommonMsgServiceI;
import com.pxb7.mall.im.client.dto.request.AddAfterSaleCustomerCareReqDTO;
import com.pxb7.mall.im.client.dto.request.CustomerCareLastFeishuAuthReqDTO;
import com.pxb7.mall.im.client.dto.request.OderJumpGroupReqDTO;
import com.pxb7.mall.im.client.dto.request.card.SendRichTextMsgReqDTO;
import com.pxb7.mall.im.client.dto.request.card.richtext.CustomercareNoticeContent;
import com.pxb7.mall.im.client.dto.request.card.richtext.RichTextContent;
import com.pxb7.mall.im.client.dto.response.AddGroupMemberRespDTO;
import com.pxb7.mall.im.client.dto.response.CustomerCareLastFeishuAuthRespDTO;
import com.pxb7.mall.trade.ass.app.dto.reqeust.AddProofReqDTO;
import com.pxb7.mall.trade.ass.app.dto.reqeust.AssRetrieveWoReqDTO;
import com.pxb7.mall.trade.ass.app.dto.reqeust.AssScheduleReqDTO;
import com.pxb7.mall.trade.ass.app.dto.reqeust.schedule.ReqCreateAssScheduleDTO;
import com.pxb7.mall.trade.ass.app.dto.response.AssScheduleLogRespDTO;
import com.pxb7.mall.trade.ass.app.dto.response.AssScheduleNodeDTO;
import com.pxb7.mall.trade.ass.app.dto.response.AssScheduleRespDTO;
import com.pxb7.mall.trade.ass.app.mapping.AssScheduleMapping;
import com.pxb7.mall.trade.ass.client.dto.request.AssStatusReqDTO;
import com.pxb7.mall.trade.ass.client.dto.response.AssStatusRespDTO;
import com.pxb7.mall.trade.ass.domain.model.assSchedule.ReqCreateAssScheduleBO;
import com.pxb7.mall.trade.ass.domain.model.reqeust.AssScheduleReqBO;
import com.pxb7.mall.trade.ass.domain.model.reqeust.RecordBO;
import com.pxb7.mall.trade.ass.domain.model.response.AssScheduleLogRespBO;
import com.pxb7.mall.trade.ass.domain.model.response.AssScheduleRespBO;
import com.pxb7.mall.trade.ass.domain.provider.WorkOrderStatusChangeMessageProvider;
import com.pxb7.mall.trade.ass.infra.constant.RMQConstant;
import com.pxb7.mall.trade.ass.infra.enums.ErrorCode;
import com.pxb7.mall.trade.ass.infra.exception.Ensure;
import com.pxb7.mall.trade.ass.infra.remote.dubbo.UserRpcGateway;
import com.pxb7.mall.trade.ass.infra.repository.db.AssProofMaterialRepository;
import com.pxb7.mall.trade.ass.infra.repository.db.AssScheduleRepository;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.AssProofMaterial;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.AssRetrieveWo;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.AssSchedule;
import com.pxb7.mall.trade.ass.infra.util.DubboResultAssert;
import com.pxb7.mall.trade.ass.infra.util.IdGenUtil;
import com.pxb7.mall.trade.ass.infra.util.OptionalUtil;
import com.pxb7.mall.trade.order.client.api.OrderInfoDubboServiceI;
import com.pxb7.mall.trade.order.client.dto.response.ass.AssOrderInfoRespDTO;
import com.pxb7.mall.trade.order.client.dto.response.ass.StartAssInfo;
import com.pxb7.mall.trade.order.client.dto.response.order.dubbo.OrderInfoDubboRespDTO;
import com.pxb7.mall.trade.order.client.enums.order.OrderItemStatusEnum;
import com.pxb7.mall.user.admin.api.SysUserServiceI;
import com.pxb7.mall.user.admin.dto.request.sysuser.GetUserInfoReqDTO;
import com.pxb7.mall.user.admin.dto.response.sysuser.GetSysUserRespDTO;
import com.pxb7.mall.user.api.UserServiceI;
import com.pxb7.mall.user.dto.response.user.UserShortInfoDTO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class AssScheduleAppService {

    public static String ASS_APPLY_FORMAT = "售后申请: 售后类型: %s,商品编号:%s，订单编号:%s";

    @DubboReference
    private UserServiceI userServiceI;
    @DubboReference
    private SysUserServiceI sysUserServiceI;
    @DubboReference
    private ImGroupServiceI imGroupServiceI;
    @DubboReference
    private AdminImUserServiceI adminImUserServiceI;
    @DubboReference
    private SendCommonMsgServiceI sendCommonMsgServiceI;
    @DubboReference
    private ImGroupMemberServiceI imGroupMemberServiceI;
    @Resource
    private AssScheduleRepository assScheduleRepository;
    @DubboReference
    private OrderInfoDubboServiceI orderInfoDubboServiceI;
    @Resource
    private AssDisputeWoAppService assDisputeWoAppService;
    @Resource
    private AfcRecordDomainService afcRecordDomainService;
    @Resource
    private AssRetrieveWoAppService assRetrieveWoAppService;
    @Resource
    private AssScheduleDomainService assScheduleDomainService;
    @Resource
    private AssRetrieveWoDomainService assRetrieveWoDomainService;
    @Resource
    private AssProofMaterialRepository assProofMaterialRepository;
    @Resource
    private AssScheduleLogDomainService assScheduleLogDomainService;
    @Resource
    private AssFollowupLogDomainService assFollowupLogDomainService;
    @Resource
    private AssWorkOrderDomainService assWorkOrderDomainService;
    @Resource
    private AssSendMessageDomainService assSendMessageDomainService;

    @Resource
    private UserRpcGateway userRpcGateway;

    @Resource
    private WorkOrderStatusChangeMessageProvider workOrderStatusChangeMessageProvider;

    @Transactional
    public Boolean record(AssScheduleReqDTO dto, String userId) {
        log.info("record,userId:{},dto:{}", userId, JSON.toJSONString(dto));
        // 校验重复提交
        String strKey = String.format("record:%s:%s", userId, dto.getRoomId());
        Ensure.that(RedissonUtils.setObjectIfAbsent(strKey, "1", Duration.ofSeconds(2)))
            .isTrue(ErrorCode.REPEAT_SUBMIT);
        AssSchedule schedule = assScheduleRepository.getByRoomId(dto.getRoomId());
        if (AssScheduleNode.INITIATE_AFTER_SALE == dto.getNode()) {
            // 目前前端 发起售后、转接找回 转接纠纷都传INITIATE_AFTER_SALE 节点
            // 发起售后
            String orderItemId;
            if (ObjectUtils.isEmpty(schedule) || StringUtils.isBlank(schedule.getOrderItemId())) {
                SingleResponse<AssOrderInfoRespDTO> response = DubboResultAssert.wrapException(
                    () -> orderInfoDubboServiceI.selectOrderInfoByRoomId(dto.getRoomId()), ErrorCode.NO_ORDER);
                orderItemId = response.getData().getOrderItemId();
                Ensure.that(orderItemId).isNotBlank(ErrorCode.GROUP_ORDER_NOT_EXIST);
            } else {
                orderItemId = schedule.getOrderItemId();
            }
            AssScheduleReqBO bo = AssScheduleMapping.INSTANCE.toAssScheduleReqBO(dto);
            bo.setOrderNo(orderItemId);
            // 校验订单状态 纠纷:订单存在 找回：订单完结
            StartAssInfo orderStatusInfo = this.getOrderStatusInfo(orderItemId);
            Ensure.that(Objects.equals(AssWoType.RETRIEVE.getCode(), dto.getAssType().getCode())
                && !Objects.equals(OrderItemStatusEnum.DEAL_SUCCESS.getValue(), orderStatusInfo.getOrderItemStatus()))
                .isFalse(ErrorCode.ORDER_NO_DEAL);
            // 拉人进群
            AddGroupMemberRespDTO groupMemberDto =
                this.handlerAfterSale(orderItemId, dto.getRoomId(), dto.getAssType(), orderStatusInfo);
            bo.setRecvCustomerId(groupMemberDto.getCustomerCareId());
            // 更新节点信息
            String scheduleId = assScheduleDomainService.record(bo, userId);
            bo.setScheduleId(scheduleId);
            assScheduleLogDomainService.record(bo, userId);
            if (Objects.equals(AssWoType.RETRIEVE.getCode(), dto.getAssType().getCode())) {
                // 找回-自动发送问题
                afcRecordDomainService.sendQuestion(dto.getRoomId(), groupMemberDto.getCustomerCareId());
            }
            return Boolean.TRUE;
        }
        if (AssScheduleNode.TRANSFER_RETRIEVE == dto.getNode()) {
            Ensure.that(schedule).isNotNull(ErrorCode.ROOM_NOT_EXISTS);
            // 转接找回
            AssScheduleReqBO bo = AssScheduleMapping.INSTANCE.toAssScheduleReqBO(dto);
            bo.setOrderNo(schedule.getOrderItemId());
            // 拉人进群判断
            StartAssInfo orderStatusInfo = this.getOrderStatusInfo(schedule.getOrderItemId());
            // 校验订单完结
            Ensure
                .that(Objects.equals(OrderItemStatusEnum.DEAL_SUCCESS.getValue(), orderStatusInfo.getOrderItemStatus()))
                .isTrue(ErrorCode.ORDER_NO_DEAL);
            AddGroupMemberRespDTO groupMemberDto =
                this.handlerAfterSale(schedule.getOrderItemId(), dto.getRoomId(), AssWoType.DISPUTE, orderStatusInfo);
            bo.setRecvCustomerId(groupMemberDto.getCustomerCareId());
            assScheduleDomainService.updateById(schedule.getId(), bo, userId);
            assScheduleLogDomainService.add(schedule.getScheduleId(), AssScheduleNode.TRANSFER_RETRIEVE, dto.getData(),
                dto.getAssType(), userId, "");
            assWorkOrderDomainService.transfer(AssUserOperatorLog.TRANSFER_RETRIEVE, userId, schedule.getScheduleId());
            return Boolean.TRUE;
        }
        if (AssScheduleNode.TRANSFER_DISPUTE == dto.getNode()) {
            Ensure.that(schedule).isNotNull(ErrorCode.ROOM_NOT_EXISTS);
            // 转接纠纷
            AssScheduleReqBO bo = AssScheduleMapping.INSTANCE.toAssScheduleReqBO(dto);
            bo.setOrderNo(schedule.getOrderItemId());
            // 拉人进群判断
            StartAssInfo orderStatusInfo = this.getOrderStatusInfo(schedule.getOrderItemId());
            AddGroupMemberRespDTO groupMemberDto =
                this.handlerAfterSale(schedule.getOrderItemId(), dto.getRoomId(), AssWoType.DISPUTE, orderStatusInfo);
            bo.setRecvCustomerId(groupMemberDto.getCustomerCareId());
            assScheduleDomainService.updateById(schedule.getId(), bo, userId);
            assScheduleLogDomainService.add(schedule.getScheduleId(), AssScheduleNode.TRANSFER_DISPUTE, dto.getData(),
                dto.getAssType(), userId, "");
            assWorkOrderDomainService.transfer(AssUserOperatorLog.TRANSFER_DISPUTE, userId, schedule.getScheduleId());
            return Boolean.TRUE;
        }
        if (AssScheduleNode.REJECT == dto.getNode()) {
            // 驳回
            Ensure.that(schedule).isNotNull(ErrorCode.ROOM_NOT_EXISTS);
            assScheduleDomainService.updateFinishById(schedule.getId(), AssScheduleNode.REJECT.isFinish(), userId);
            assScheduleLogDomainService.add(schedule.getScheduleId(), AssScheduleNode.REJECT, dto.getData(),
                dto.getAssType(), userId, "");

            assWorkOrderDomainService.findByScheduleId(schedule.getScheduleId()).ifPresent(workOrder -> {
                assWorkOrderDomainService.updateWithOpt(workOrder.getId(), AssWorkOrderStatusEnum.PROCESS.getCode(), AssWorkOrderStatusEnum.CANCELED.getCode(), dto.getRejectReason());
            });

            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    /**
     * 获取订单信息
     *
     * @param orderItemId : 订单行id
     */
    private StartAssInfo getOrderStatusInfo(String orderItemId) {
        return OptionalUtil
            .ofBlank(orderItemId).map(o -> DubboResultAssert
                .wrapException(() -> orderInfoDubboServiceI.startAss(orderItemId), ErrorCode.CALL_FAIL))
            .filter(SingleResponse::isSuccess).map(SingleResponse::getData).orElse(null);
    }

    /**
     * 拉人进群
     *
     * @param orderItemId : 订单id
     * @param roomId : 房间id
     * @param assType : 售后类型
     */
    private AddGroupMemberRespDTO handlerAfterSale(String orderItemId, String roomId, AssWoType assType,
        StartAssInfo startAssInfo) {
        AddAfterSaleCustomerCareReqDTO reqDTO = new AddAfterSaleCustomerCareReqDTO();
        reqDTO.setGroupId(roomId);
        reqDTO.setGameId(startAssInfo.getGameId());
        reqDTO.setOrderId(orderItemId);
        reqDTO.setAfterSaleType(assType.getCode());
        log.info("record.拉人进群:{}", JSON.toJSONString(reqDTO));
        SingleResponse<AddGroupMemberRespDTO> singleResponse = DubboResultAssert
            .wrapException(() -> imGroupMemberServiceI.addAfterSaleCustomerCare(reqDTO), ErrorCode.PULL_CUSTOMER_ERROR);
        if (ObjectUtils.isEmpty(singleResponse.getData())
            || StringUtils.isBlank(singleResponse.getData().getCustomerCareId())) {
            log.warn("handlerAfterSale.拉人进群失败，reqDTO:{}，singleResponse:{}", JSON.toJSONString(reqDTO),
                JSON.toJSONString(singleResponse));
            throw new BizException(ErrorCode.PULL_CUSTOMER_ERROR.getErrDesc());
        }
        return singleResponse.getData();
    }

    private String handlerAfterSale(String deliveryRoomId, String gameId, String orderNo, Integer assType) {
        AddAfterSaleCustomerCareReqDTO reqDTO = new AddAfterSaleCustomerCareReqDTO();
        reqDTO.setGroupId(deliveryRoomId);
        reqDTO.setGameId(gameId);
        reqDTO.setOrderId(orderNo);
        reqDTO.setAfterSaleType(assType);
        SingleResponse<AddGroupMemberRespDTO> singleResponse = DubboResultAssert.wrapException(
            () -> imGroupMemberServiceI.addAfterSaleCustomerCare(reqDTO), ErrorCode.USER_PULL_CUSTOMER_ERROR);
        if (ObjectUtils.isEmpty(singleResponse.getData())
            || StringUtils.isBlank(singleResponse.getData().getCustomerCareId())) {
            log.warn("handlerAfterSale.拉人进群失败，reqDTO:{}，singleResponse:{}", JSON.toJSONString(reqDTO),
                JSON.toJSONString(singleResponse));
            throw new BizException(ErrorCode.USER_PULL_CUSTOMER_ERROR.getErrDesc());
        }
        return singleResponse.getData().getCustomerCareId();
    }

    /**
     * im端客服创建工单
     *
     * @param roomId : 群号
     * @param userId : im客服id
     */
    @Transactional
    public String createWorkOrder(String roomId, String userId, Integer assistance) {
        log.info("createWorkOrder,userId:{},roomId:{}", userId, roomId);
        // 校验重复提交
        String strKey = String.format("createworkorder:%s:%s", userId, roomId);
        Ensure.that(RedissonUtils.setObjectIfAbsent(strKey, "1", Duration.ofSeconds(5)))
            .isTrue(ErrorCode.REPEAT_SUBMIT);
        AssSchedule schedule = assScheduleRepository.getByRoomId(roomId);
        log.info("createWorkOrder.roomId:{},schedule:{}", roomId,JSON.toJSONString(schedule));
        // 1、校验节点信息
        Ensure.that(schedule).isNotNull(ErrorCode.ROOM_NOT_EXISTS);
        AssWoType assWoType = AssWoType.fromCode(schedule.getAssType());
        // 2、校验订单信息
        Ensure.that(schedule.getOrderItemId()).isNotBlank(ErrorCode.NO_DATA_MSG);
        OrderInfoDubboRespDTO orderInfo =
            Optional.ofNullable(schedule.getOrderItemId()).map(orderInfoDubboServiceI::getOrderInfo)
                .filter(SingleResponse::isSuccess).map(SingleResponse::getData).orElse(null);
        Ensure.that(orderInfo).isNotNull(ErrorCode.ORDER_NOT_EXIST);
        assert orderInfo != null;
        String workOrderId = "";
        // 获取im飞书信息
        CustomerCareLastFeishuAuthRespDTO authRespDTO =
            Optional.ofNullable(userId).map(o -> CustomerCareLastFeishuAuthReqDTO.builder().userId(o).build())
                .map(adminImUserServiceI::customerCareLastFeishuAuth).map(SingleResponse::getData).orElse(null);
        if (AssWoType.DISPUTE == assWoType) {
            // 纠纷
            workOrderId = assDisputeWoAppService.createDisputeWo(userId, schedule, orderInfo, assWoType, authRespDTO);
            workOrderStatusChangeMessageProvider.asyncSendCreateWOMessage(workOrderId, roomId, schedule.getOrderItemId(), AssWoType.DISPUTE.getCode(), RMQConstant.AFC_DISPUTE_WORK_ORDER_STATUS_CHANGE_TAG);
        } else if (AssWoType.RETRIEVE == assWoType) {
            // 找回
            // 增加是否为协助工单
            if (assistance < AssAssistance.NO.getCode() || assistance > AssAssistance.YES.getCode()) {
                throw new BizException(ErrorCode.PARAM_VAL_ERR.getErrDesc());
            }
            workOrderId = assRetrieveWoAppService.createRetrieveWo(userId, schedule, orderInfo, assWoType,
                Objects.requireNonNull(AssAssistance.fromCode(assistance)), authRespDTO);
        }
        // 发送通知
        assSendMessageDomainService.assRetrieveCreate(workOrderId, schedule.getOrderItemId(), "", "");

        return workOrderId;
    }

    /**
     * 添加证据材料
     */
    @Transactional
    public boolean addProof(AddProofReqDTO reqDTO, String userId) {
        log.info("addProof,userId:{},reqDTO:{}", userId, JSON.toJSONString(reqDTO));
        // 校验重复提交
        String strKey = String.format("addproof:%s:%s", userId, reqDTO.getRoomId());
        Ensure.that(RedissonUtils.setObjectIfAbsent(strKey, "1", Duration.ofSeconds(2)))
            .isTrue(ErrorCode.REPEAT_SUBMIT);
        AssSchedule schedule = assScheduleRepository.getByRoomId(reqDTO.getRoomId());
        // 1、校验参数信息
        Ensure.that(schedule).isNotNull(ErrorCode.ROOM_NOT_EXISTS);
        Ensure.that(schedule.getWorkOrderId()).isNotBlank(ErrorCode.WORKORDER_NOT_EXIST);
        // 2、添加证据
        List<AssProofMaterial> list = reqDTO.getList().stream().map(item -> {
            // 校验url
            Ensure.that(StringUtils.contains(item.getLinkUrl(), "pxb7")).isTrue(ErrorCode.LINK_URL_ERROR);
            return new AssProofMaterial().setProofId(IdGenUtil.generateId()).setName(item.getName())
                .setLinkUrl(item.getLinkUrl()).setAssType(schedule.getAssType())
                .setWorkOrderId(schedule.getWorkOrderId()).setCreateUserId(userId).setUpdateUserId(userId);
        }).toList();
        assProofMaterialRepository.saveBatch(list);
        // 3、添加流程信息
        assScheduleDomainService.updateWorkOrderIdAndAssTypeAndFinishById(schedule.getId(), schedule.getWorkOrderId(),
            AssWoType.RETRIEVE.getCode(), AssScheduleNode.UPLOAD_EVIDENCE.isFinish(), userId);
        assScheduleLogDomainService.add(schedule.getScheduleId(), AssScheduleNode.UPLOAD_EVIDENCE, "",
            AssWoType.RETRIEVE, userId, "");
        // 4、添加日志信息
        // 获取im飞书信息
        CustomerCareLastFeishuAuthRespDTO authRespDTO =
            Optional.ofNullable(userId).map(o -> CustomerCareLastFeishuAuthReqDTO.builder().userId(o).build())
                .map(adminImUserServiceI::customerCareLastFeishuAuth).map(SingleResponse::getData).orElse(null);
        String feiShuName = Objects.nonNull(authRespDTO)
            ? (authRespDTO.getFeishuName() + "(" + authRespDTO.getFeishuEmployeeNo() + ")") : StringUtils.EMPTY;
        String names = list.stream().map(AssProofMaterial::getName).collect(Collectors.joining(","));
        RecordBO recordBO = new RecordBO().setWorkOrderId(schedule.getWorkOrderId())
            .setAssType(AssWoType.RETRIEVE.getCode()).setOperatorId(userId).setTitle(AssOperatorLog.ADD_PROOF.getDesc())
            .setContent(AssOperatorLog.ADD_PROOF.getDesc() + names).setFeiShuName(feiShuName);
        assFollowupLogDomainService.recordLog(recordBO);
        assWorkOrderDomainService.addProof(recordBO, schedule);
        return true;
    }

    /**
     * 找回工单 分配处理人员
     *
     * @param reqDTO : 请求对象
     * @param userId : 用户id
     */
    @Transactional
    public Boolean setDealUser(AssRetrieveWoReqDTO.SetDealUserReqDTO reqDTO, String userId) {
        log.info("setDealUser,userId:{},reqDTO:{}", userId, JSON.toJSONString(reqDTO));
        // 校验重复提交
        String strKey = String.format("setdealuser:%s:%s", userId, reqDTO.getRoomId());
        Ensure.that(RedissonUtils.setObjectIfAbsent(strKey, "1", Duration.ofSeconds(2)))
            .isTrue(ErrorCode.REPEAT_SUBMIT);
        AssSchedule schedule = assScheduleRepository.getByRoomId(reqDTO.getRoomId());
        // 1、参数校验
        Ensure.that(schedule).isNotNull(ErrorCode.ROOM_NOT_EXISTS);
        Ensure.that(schedule.getWorkOrderId()).isNotBlank(ErrorCode.WORKORDER_NOT_EXIST);
        OptionalUtil.ofBlank(reqDTO.getDealUserId())
            .map(o -> DubboResultAssert.wrapException(() -> sysUserServiceI.getUserInfoById(reqDTO.getDealUserId()),
                ErrorCode.RPC_ERROR))
            .map(SingleResponse::getData).map(GetSysUserRespDTO::getUserName).ifPresent(reqDTO::setDealUserName);
        // 2、分配处理人员
        assRetrieveWoDomainService.setDealUser(schedule.getWorkOrderId(), reqDTO.getDealUserId());
        // 3、添加流程信息
        assScheduleDomainService.updateWorkOrderIdAndAssTypeAndFinishById(schedule.getId(), schedule.getWorkOrderId(),
            AssWoType.RETRIEVE.getCode(), AssScheduleNode.AFFIRM_RETRIEVE.isFinish(), userId);
        assScheduleLogDomainService.add(schedule.getScheduleId(), AssScheduleNode.AFFIRM_RETRIEVE, "",
            AssWoType.RETRIEVE, userId, reqDTO.getDealUserName());
        // 4、添加日志信息
        // 获取im飞书信息
        CustomerCareLastFeishuAuthRespDTO authRespDTO =
            Optional.ofNullable(userId).map(o -> CustomerCareLastFeishuAuthReqDTO.builder().userId(o).build())
                .map(adminImUserServiceI::customerCareLastFeishuAuth).map(SingleResponse::getData).orElse(null);
        String feiShuName = Objects.nonNull(authRespDTO)
            ? (authRespDTO.getFeishuName() + "(" + authRespDTO.getFeishuEmployeeNo() + ")") : StringUtils.EMPTY;
        RecordBO recordBO = new RecordBO().setWorkOrderId(schedule.getWorkOrderId())
            .setAssType(AssWoType.RETRIEVE.getCode()).setOperatorId(userId)
            .setTitle(AssOperatorLog.CONFIRM_RETRIEVE_SET_DEAL_USER.getDesc())
            .setContent(AssOperatorLog.SET_DEAL_USER.getDesc() + reqDTO.getDealUserName()).setFeiShuName(feiShuName);
        assFollowupLogDomainService.recordLog(recordBO);
        assWorkOrderDomainService.setDealUser(recordBO, schedule);
        workOrderStatusChangeMessageProvider.asyncSendCreateWOMessage(schedule.getWorkOrderId(), reqDTO.getRoomId(), schedule.getOrderItemId(), AssWoType.RETRIEVE.getCode(), RMQConstant.AFC_RETRIEVE_WORK_ORDER_STATUS_CHANGE_TAG);
        return Boolean.TRUE;
    }

    /**
     * 关闭工单
     *
     * @param roomId : 房间id
     * @param userId :用户id
     */
    @Transactional
    public Boolean closeRetrieveWo(String roomId, String userId) {
        log.info("closeRetrieveWo,userId:{},roomId:{}", userId, roomId);
        // 1、参数校验
        AssSchedule schedule = assScheduleRepository.getByRoomId(roomId);
        Ensure.that(schedule).isNotNull(ErrorCode.ROOM_NOT_EXISTS);
        Ensure.that(schedule.getWorkOrderId()).isNotBlank(ErrorCode.WORKORDER_NOT_EXIST);
        // 2、关闭工单
        AssRetrieveWo retrieveDetail = assRetrieveWoDomainService.findOneById(schedule.getWorkOrderId());
        assRetrieveWoDomainService.closeRetrieveWo(schedule.getWorkOrderId());
        // 3、添加流程信息
        assScheduleDomainService.updateWorkOrderIdAndAssTypeAndFinishById(schedule.getId(), schedule.getWorkOrderId(),
            AssWoType.RETRIEVE.getCode(), AssScheduleNode.CLOSE_WORK_ORDER.isFinish(), userId);
        assScheduleLogDomainService.add(schedule.getScheduleId(), AssScheduleNode.CLOSE_WORK_ORDER, "",
            AssWoType.RETRIEVE, userId, "");
        // 4、添加日志信息
        // 获取im飞书信息
        CustomerCareLastFeishuAuthRespDTO authRespDTO =
            Optional.ofNullable(userId).map(o -> CustomerCareLastFeishuAuthReqDTO.builder().userId(o).build())
                .map(adminImUserServiceI::customerCareLastFeishuAuth).map(SingleResponse::getData).orElse(null);
        String feiShuName = Objects.nonNull(authRespDTO)
            ? (authRespDTO.getFeishuName() + "(" + authRespDTO.getFeishuEmployeeNo() + ")") : StringUtils.EMPTY;
        RecordBO recordBO =
            new RecordBO().setWorkOrderId(schedule.getWorkOrderId()).setAssType(AssWoType.RETRIEVE.getCode())
                .setOperatorId(userId).setTitle(AssOperatorLog.DELETE_RETRIEVE_WO.getDesc())
                .setContent(AssOperatorLog.DELETE_RETRIEVE_WO.getDesc()
                    + (Objects.nonNull(retrieveDetail) ? retrieveDetail.getWorkOrderNo() : ""))
                .setFeiShuName(feiShuName);
        assFollowupLogDomainService.recordLog(recordBO);
        assWorkOrderDomainService.closeWorkOrder(recordBO, schedule);
        workOrderStatusChangeMessageProvider.asyncSendCloseWOMessage(retrieveDetail.getAssRetrieveId(), roomId, retrieveDetail.getOrderItemId(), AssWoType.RETRIEVE.getCode(), RMQConstant.AFC_RETRIEVE_WORK_ORDER_STATUS_CHANGE_TAG);
        return Boolean.TRUE;
    }

    /**
     * 发送问题
     *
     * @param roomId : 房间id
     * @param userId :用户id
     */
    @Transactional
    public Boolean sendQuestion(String roomId, String userId) {
        log.info("sendQuestion,userId:{},roomId:{}", userId, roomId);
        // 1、参数校验
        AssSchedule schedule = assScheduleRepository.getByRoomId(roomId);
        Ensure.that(schedule).isNotNull(ErrorCode.ROOM_NOT_EXISTS);
        // 2、添加流程信息
        assScheduleDomainService.updateFinishById(schedule.getId(), AssScheduleNode.SEND_QUESTION.isFinish(), userId);
        assScheduleLogDomainService.add(schedule.getScheduleId(), AssScheduleNode.SEND_QUESTION, "", AssWoType.RETRIEVE,
            userId, "");
        return Boolean.TRUE;
    }

    /**
     * c端用户发起售后
     *
     * @param param :请求对象
     * @param loginUserId :c端用户
     */
    public String mobileCreateAssSchedule(ReqCreateAssScheduleDTO param, String loginUserId) {
        log.info("mobileCreateAssSchedule,userId{},param:{}", loginUserId, JSON.toJSONString(param));
        // 校验重复提交
        String strKey = String.format("createassschedule:%s:%s", loginUserId, param.getOrderNo());
        Ensure.that(RedissonUtils.setObjectIfAbsent(strKey, "1", Duration.ofSeconds(2)))
            .isTrue(ErrorCode.REPEAT_SUBMIT);
        ReqCreateAssScheduleBO bo = new ReqCreateAssScheduleBO();
        bo.setAssType(param.getAssType());
        bo.setOrderNo(param.getOrderNo());
        bo.setLoginUserId(loginUserId);
        bo.setSourceType(AssScheduleSourceTypeEnums.SourceType_1.getCode());
        // 从订单查询交付群信息
        SingleResponse<StartAssInfo> response = orderInfoDubboServiceI.startAss(param.getOrderNo());
        StartAssInfo orderInfo = response.getData();
        Ensure.that(orderInfo).isNotNull(ErrorCode.ORDER_NOT_EXIST);
        // 校验订单完结
        Ensure.that(Objects.equals(OrderItemStatusEnum.DEAL_SUCCESS.getValue(), orderInfo.getOrderItemStatus()))
            .isTrue(ErrorCode.ORDER_NO_DEAL);
        // 校验交付群聊
        String deliveryRoomId = orderInfo.getDeliveryRoomId();
        Ensure.that(deliveryRoomId).isNotBlank(ErrorCode.ROOM_NOT_EXISTS);
        // 恢复群聊 每次进群都判断一下
        OderJumpGroupReqDTO renewGroup = new OderJumpGroupReqDTO();
        renewGroup.setGroupId(deliveryRoomId);
        renewGroup.setLoginUserId(loginUserId);
        try {
            imGroupServiceI.orderJumpGroup(renewGroup);
        } catch (Exception ex) {
            log.error("恢复群异常:{}", JSON.toJSONString(renewGroup), ex);
        }
        // 查询日志信息
        AssSchedule schedule = assScheduleRepository.findOneByOrderItemIdAndRoomId(bo.getOrderNo(), deliveryRoomId);
        if (Objects.nonNull(schedule)) {
            // 此次请求为重复发起售后
            Ensure.that(schedule.getFinish()).isTrue(ErrorCode.ASS_EXIST);
            // 此次请求为二次发起售后
            bo.setRecvCustomerId(
                this.handlerAfterSale(deliveryRoomId, orderInfo.getGameId(), param.getOrderNo(), param.getAssType()));
            bo.setRoomId(deliveryRoomId);
            assScheduleDomainService.saveAssSchedule(bo);
        } else {
            // 此次请求为第一次发起售后
            // 拉人进群
            bo.setRecvCustomerId(
                this.handlerAfterSale(deliveryRoomId, orderInfo.getGameId(), param.getOrderNo(), param.getAssType()));
            bo.setRoomId(deliveryRoomId);
            assScheduleDomainService.saveAssSchedule(bo);
        }
        // 发卡片
        this.sendAssCard(loginUserId, deliveryRoomId, param.getAssType(), param.getOrderNo(),
            orderInfo.getProductUniqueNo());
        return deliveryRoomId;
    }

    public AssScheduleRespDTO find(String roomId) {
        log.info("find,roomId:{}", roomId);
        AssScheduleRespBO assScheduleRespBO = assScheduleDomainService.find(roomId);
        if (assScheduleRespBO == null) {
            AssScheduleRespDTO dto = new AssScheduleRespDTO();
            dto.setRoomId(roomId);
            dto.setFinish(false);
            dto.setLogs(Collections.emptyList());
            return dto;
        }
        AssScheduleRespDTO assScheduleRespDTO = AssScheduleMapping.INSTANCE.toAssScheduleRespDTO(assScheduleRespBO);
        List<AssScheduleLogRespBO> assScheduleLogs =
            assScheduleLogDomainService.find(assScheduleRespBO.getScheduleId());
        List<AssScheduleLogRespDTO> assScheduleLogRespDTO =
            AssScheduleMapping.INSTANCE.toAssScheduleLogRespDTO(assScheduleLogs);
        // 查询创建用户
        List<String> userIds = assScheduleLogs.stream().map(AssScheduleLogRespBO::getCreateUserId).toList();
        HashSet<String> sysUserIds = new HashSet<>(userIds);
        Map<String, String> userMap = getUserInfo(sysUserIds, userIds);
        assScheduleLogRespDTO.forEach(item -> item.setCreateUser(userMap.get(item.getCreateUserId())));
        assScheduleRespDTO.setLogs(assScheduleLogRespDTO);
        String nodeId = assScheduleLogs.get(assScheduleLogs.size() - 1).getNodeId();
        AssScheduleNode assScheduleNode = AssScheduleNode.getById(nodeId);
        List<AssScheduleNode> lists = AssScheduleNode.nextNode(assScheduleNode, assScheduleRespBO.getAssType());
        List<AssScheduleNodeDTO> nextNodes = new ArrayList<>(lists.size());
        for (AssScheduleNode node : lists) {
            nextNodes.add(new AssScheduleNodeDTO(node));
        }
        assScheduleRespDTO.setNextNode(nextNodes);
        return assScheduleRespDTO;
    }

    public List<AssStatusRespDTO> checkAss(List<AssStatusReqDTO> list) {
        if (CollUtil.isEmpty(list)) {
            return CollUtil.newArrayList();
        }
        List<AssSchedule> schedules = assScheduleRepository.findListByOrderItemIdAndRoomId(list);
        Map<String, AssSchedule> scheduleMap =
            schedules.stream().collect(Collectors.toMap(e -> e.getOrderItemId() + "," + e.getRoomId(),
                Function.identity(), BinaryOperator.maxBy(Comparator.comparingLong(AssSchedule::getId))));
        List<AssStatusRespDTO> statusList = Lists.newArrayList();
        for (AssStatusReqDTO s : list) {
            AssStatusRespDTO entity = new AssStatusRespDTO();
            entity.setOrderItemId(s.getOrderItemId());
            entity.setRoomId(s.getRoomId());
            AssSchedule assSchedule = scheduleMap.get(s.getOrderItemId() + "," + s.getRoomId());
            if (Objects.isNull(assSchedule)) {
                entity.setApply(AssApplyStatusEnum.NOT_APPLY.getCode());
            } else {
                if (BooleanUtils.isTrue(assSchedule.getFinish())) {
                    entity.setApply(AssApplyStatusEnum.FINISHED.getCode());
                } else {
                    entity.setApply(AssApplyStatusEnum.UNCOMPLETED.getCode());
                }
            }
            statusList.add(entity);
        }
        return statusList;
    }

    /**
     * 查询用户信息
     *
     * @param sysUserIds 系统用户id
     * @param userIds 普通用户id
     */
    public Map<String, String> getUserInfo(Set<String> sysUserIds, List<String> userIds) {
        // 查询系统用户
        HashMap<String, String> userMap = new HashMap<>();
        MultiResponse<GetSysUserRespDTO> userInfoByIds =
            sysUserServiceI.getUserInfoByIds(GetUserInfoReqDTO.builder().userIds(sysUserIds).build());
        if (!CollectionUtils.isEmpty(userInfoByIds.getData())) {
            userInfoByIds.getData().forEach(item -> userMap.put(item.getUserId(), item.getUserName()));
        }
        // 查询普通用户
        List<UserShortInfoDTO> userShortInfoList = userRpcGateway.findBaseUserInfoList(userIds);
        if (!CollectionUtils.isEmpty(userShortInfoList)) {
            userShortInfoList.forEach(item -> userMap.put(item.getUserId(), item.getNickname()));
        }
        return userMap;
    }

    public AssSchedule getByRoomId(String roomId) {
        return assScheduleRepository.getByRoomId(roomId);
    }

    public String getAddressUrl(String roomId) {
        if (StringUtils.isBlank(roomId)) {
            return "";
        }
        return Optional.of(roomId).map(this::getByRoomId).map(AssSchedule::getWorkOrderId)
            .map(assRetrieveWoDomainService::findOneById).map(AssRetrieveWo::getWorkOrderNo).orElse("");
    }

    private void sendAssCard(String loginUserId, String roomId, Integer assType, String orderItemId,
        String productUniqueNo) {
        AssWoType assWoType = AssWoType.fromCode(assType);
        CustomercareNoticeContent noticeMsgDTO = new CustomercareNoticeContent(
            String.format(ASS_APPLY_FORMAT, assWoType.getDesc(), productUniqueNo, orderItemId));
        SendRichTextMsgReqDTO msgReqDTO = new SendRichTextMsgReqDTO().setTargetId(roomId).setFromUserId(loginUserId)
            .setTitle("售后申请").setCustomercareNoticeContent(noticeMsgDTO)
            .setContent(List.of(new RichTextContent("售后类型: " + assWoType.getDesc()),
                new RichTextContent("商品编号: " + productUniqueNo), new RichTextContent("订单编号: " + orderItemId)));
        SingleResponse<String> response = DubboResultAssert
            .wrapException(() -> sendCommonMsgServiceI.sendRichTextMsg(msgReqDTO), ErrorCode.RPC_ERROR);
        log.info("sendAssCard request:{} , response:{}", JSONObject.toJSONString(msgReqDTO),
            JSONObject.toJSONString(response));
        if (!response.isSuccess()) {
            log.error("sendAssCard fail request:{} , response:{}", JSONObject.toJSONString(msgReqDTO),
                JSONObject.toJSONString(response));
        }
    }

    public void updateRecvCustomerId(Long id,String recvCustomerId){
        assScheduleDomainService.updateRecvCustomerId(id,recvCustomerId);
    }
}
