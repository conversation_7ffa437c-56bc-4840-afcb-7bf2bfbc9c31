package com.pxb7.mall.trade.ass.app.mapping;

import com.pxb7.mall.trade.ass.app.dto.reqeust.AssRetrieveGameConfigDetailReqDTO;
import com.pxb7.mall.trade.ass.app.dto.response.AssRetrieveGameConfigDetailRespDTO;
import com.pxb7.mall.trade.ass.domain.model.AssRetrieveGameConfigDetailReqBO;
import com.pxb7.mall.trade.ass.domain.model.AssRetrieveGameConfigDetailRespBO;

import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;


@Mapper
public interface AssRetrieveGameConfigDetailAppMapping {

    AssRetrieveGameConfigDetailAppMapping INSTANCE = Mappers.getMapper(AssRetrieveGameConfigDetailAppMapping.class);


    AssRetrieveGameConfigDetailReqBO.AddBO assRetrieveGameConfigDetailDTO2AddBO(AssRetrieveGameConfigDetailReqDTO.AddDTO source);

    AssRetrieveGameConfigDetailReqBO.UpdateBO assRetrieveGameConfigDetailDTO2UpdateBO(AssRetrieveGameConfigDetailReqDTO.UpdateDTO source);

    AssRetrieveGameConfigDetailReqBO.DelBO assRetrieveGameConfigDetailDTO2DelBO(AssRetrieveGameConfigDetailReqDTO.DelDTO source);

    AssRetrieveGameConfigDetailReqBO.SearchBO assRetrieveGameConfigDetailDTO2SearchBO(AssRetrieveGameConfigDetailReqDTO.SearchDTO source);

    AssRetrieveGameConfigDetailReqBO.PageBO assRetrieveGameConfigDetailDTO2PageBO(AssRetrieveGameConfigDetailReqDTO.PageDTO source);

    AssRetrieveGameConfigDetailRespDTO.DetailDTO assRetrieveGameConfigDetailBO2DetailDTO(AssRetrieveGameConfigDetailRespBO.DetailBO source);

    List<AssRetrieveGameConfigDetailRespDTO.DetailDTO> assRetrieveGameConfigDetailBO2ListDTO(List<AssRetrieveGameConfigDetailRespBO.DetailBO> source);

    Page<AssRetrieveGameConfigDetailRespDTO.DetailDTO> assRetrieveGameConfigDetailBO2PageDTO(Page<AssRetrieveGameConfigDetailRespBO.DetailBO> source);

}


