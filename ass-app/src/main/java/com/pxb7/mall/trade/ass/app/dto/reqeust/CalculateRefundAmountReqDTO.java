package com.pxb7.mall.trade.ass.app.dto.reqeust;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class CalculateRefundAmountReqDTO {

    /**
     * 退款类别，1，整单退款（全额退） 2，部分退款
     */
    @NotNull(message = "退款类别不能为空")
    private Integer wholeRefund;

    /**
     * 退款金额, 退商品差价时传入
     */
    private Long refundAmount;

    @NotBlank(message = "订单行id不能为空")
    private String orderItemId;

    /**
     * 违约金详情
     */
    private RefundViolateDetailReqDTO refundViolateDetail;
}
