package com.pxb7.mall.trade.ass.app.dto.response;

import lombok.Data;

@Data
public class RefundReasonPageRespDTO {

    /**
     * 退款原因id
     */
    private String reasonRefundId;
    /**
     * 退款原因
     */
    private String reasonRefundContent;
    /**
     * 拦截退款，挽留
     */
    private Integer reasonIntercept;
    /**
     * 商品自动上架 1:是,0:否
     */
    private Integer autoGrounding;
    /**
     * 适用范围:1用户端申请退款,2客户端申请退款,3全部
     */
    private Integer reasonRange;
    /**
     * 退款类型
     * 0: 全额退款
     * 1: 部分退款
     */
    private Integer refundType;
    /**
     * 退款范围
     * 1: 用户端申请退款
     * 2: 客服端申请退款
     * 3:全选
     */
    private Integer refundRange;
}
