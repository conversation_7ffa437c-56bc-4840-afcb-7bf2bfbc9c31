package com.pxb7.mall.trade.ass.app.dto.response;

import lombok.Data;

@Data
public class CalculateRefundAmountRespDTO {
    /**
     * 商品号价退款金额
     */
    private long productAmount;
    /**
     * 包赔金额
     */
    private long indemnityAmount;
    /**
     * 手续费金额
     */
    private long feeAmount;
    /**
     * 红包
     */
    private long redPacketAmount;
    /**
     * 退款总额
     */
    private long totalAmount;
    /**
     * 违约金额最大值
     */
    private long violateAmountMax;
    /**
     * 守约金
     */
    private long promiseAmount;
}
