package com.pxb7.mall.trade.ass.app.dto.response;

import com.pxb7.mall.trade.ass.client.enums.AssScheduleNode;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/8/13
 */
@Data
public class AssScheduleNodeDTO {

    /**
     * id
     */
    private final String nodeId;
    /**
     * 描述
     */
    private final String nodeDesc;
    /**
     * 消息
     */
    private final String msg;

    public AssScheduleNodeDTO(AssScheduleNode node) {
        this.nodeId = node.name();
        this.nodeDesc = node.getName();
        this.msg = node.getMsg();
    }

}
