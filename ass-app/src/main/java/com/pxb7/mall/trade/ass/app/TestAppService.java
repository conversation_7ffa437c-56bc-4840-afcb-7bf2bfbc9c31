package com.pxb7.mall.trade.ass.app;

import com.alibaba.cola.dto.SingleResponse;
import com.pxb7.mall.trade.order.client.api.OrderInfoDubboServiceI;
import com.pxb7.mall.trade.order.client.dto.response.ass.StartAssInfo;

import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

/**
 * 测试
 *
 * <AUTHOR>
 * @since: 2024-09-21 10:34
 **/
@Service
public class TestAppService {
    @DubboReference
    private OrderInfoDubboServiceI orderInfoDubboServiceI;

    public StartAssInfo test22(String orderNo) {
        // 从订单查询交易群信息
        SingleResponse<StartAssInfo> response = orderInfoDubboServiceI.startAss(orderNo);
        return response.getData();
    }

}
