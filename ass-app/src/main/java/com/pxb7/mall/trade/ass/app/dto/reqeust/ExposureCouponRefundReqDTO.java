package com.pxb7.mall.trade.ass.app.dto.reqeust;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/9/24
 */
@Data
public class ExposureCouponRefundReqDTO {

    /**
     * 订单行id
     */
    private String orderItemId;

    /**
     * 优惠券ids JsonString
     */
    private String userCouponIds;

    /**
     * 退款金额
     */
    private Long sumApportionAmount;

    /**
     * 退款来源 1 用户 2 客服
     */
    private Integer refundSource;

    /**
     * 客服id
     */
    private String systemUserId;

}
