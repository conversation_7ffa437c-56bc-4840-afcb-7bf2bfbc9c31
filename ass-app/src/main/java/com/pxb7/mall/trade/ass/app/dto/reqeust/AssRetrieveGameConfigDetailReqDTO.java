package com.pxb7.mall.trade.ass.app.dto.reqeust;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Min;
import lombok.ToString;


/**
 * 售后找回游戏配置明细表(AssRetrieveGameConfigDetail)实体类
 *
 * <AUTHOR>
 * @since 2025-07-30 13:59:50
 */
public class AssRetrieveGameConfigDetailReqDTO {

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class AddDTO {

        /**
         * 业务主键
         */
        @NotBlank(message = "detailId不能为空")
        private String detailId;

        /**
         * ass_retrieve_game_config.game_config_id
         */
        @NotBlank(message = "gameConfigId不能为空")
        private String gameConfigId;

        /**
         * 找回处理时间单位天
         */
        @NotNull(message = "retrieveDays不能为空")
        private Integer retrieveDays;

        /**
         * 游戏id
         */
        @NotBlank(message = "gameId不能为空")
        private String gameId;

        /**
         * 用户端进群开关 1:已开启 0:未开启
         */
        @NotNull(message = "groupOpen不能为空")
        private Boolean groupOpen;

    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class UpdateDTO {

        /**
         * 自增id
         */
        @NotNull(message = "id不能为空")
        private Long id;


        /**
         * 业务主键
         */
        @NotBlank(message = "detailId不能为空")
        private String detailId;


        /**
         * ass_retrieve_game_config.game_config_id
         */
        @NotBlank(message = "gameConfigId不能为空")
        private String gameConfigId;


        /**
         * 找回处理时间单位天
         */
        @NotNull(message = "retrieveDays不能为空")
        private Integer retrieveDays;


        /**
         * 游戏id
         */
        @NotBlank(message = "gameId不能为空")
        private String gameId;


        /**
         * 用户端进群开关 1:已开启 0:未开启
         */
        @NotNull(message = "groupOpen不能为空")
        private Boolean groupOpen;


    }


    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class DelDTO {
        @NotNull(message = "id不能为空")
        private Long id;
    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class SearchDTO {
        /**
         * 业务主键
         */
        @NotBlank(message = "detailId不能为空")
        private String detailId;

        /**
         * ass_retrieve_game_config.game_config_id
         */
        @NotBlank(message = "gameConfigId不能为空")
        private String gameConfigId;

        /**
         * 找回处理时间单位天
         */
        @NotNull(message = "retrieveDays不能为空")
        private Integer retrieveDays;

        /**
         * 游戏id
         */
        @NotBlank(message = "gameId不能为空")
        private String gameId;

        /**
         * 用户端进群开关 1:已开启 0:未开启
         */
        @NotNull(message = "groupOpen不能为空")
        private Boolean groupOpen;

    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class PageDTO {

        /**
         * 业务主键
         */
        @NotBlank(message = "detailId不能为空")
        private String detailId;

        /**
         * ass_retrieve_game_config.game_config_id
         */
        @NotBlank(message = "gameConfigId不能为空")
        private String gameConfigId;

        /**
         * 找回处理时间单位天
         */
        @NotNull(message = "retrieveDays不能为空")
        private Integer retrieveDays;

        /**
         * 游戏id
         */
        @NotBlank(message = "gameId不能为空")
        private String gameId;

        /**
         * 用户端进群开关 1:已开启 0:未开启
         */
        @NotNull(message = "groupOpen不能为空")
        private Boolean groupOpen;


        /**
         * 页码，从1开始
         */
        @NotNull(message = "pageIndex不能为空")
        @Min(value = 1, message = "pageIndex不能小于1")
        private Integer pageIndex;
        /**
         * 每页数量
         */
        @NotNull(message = "pageSize不能为空")
        private Integer pageSize;

    }

}

