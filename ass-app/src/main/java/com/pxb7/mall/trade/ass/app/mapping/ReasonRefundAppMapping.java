package com.pxb7.mall.trade.ass.app.mapping;

import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import com.pxb7.mall.trade.ass.app.dto.reqeust.RefundReasonReqDTO;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.RefundReason;

@Mapper
public interface ReasonRefundAppMapping {

    ReasonRefundAppMapping INSTANCE = Mappers.getMapper(ReasonRefundAppMapping.class);

    RefundReason toRefundReason(RefundReasonReqDTO.InsertDTO insertDTO);

    List<RefundReason> toRefundReasonList(List<RefundReasonReqDTO.InsertDTO> insertDTOList);
}
