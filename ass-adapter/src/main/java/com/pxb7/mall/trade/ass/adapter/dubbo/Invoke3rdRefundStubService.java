package com.pxb7.mall.trade.ass.adapter.dubbo;

import cn.hutool.core.lang.Assert;
import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.cola.exception.BizException;
import com.alibaba.cola.extension.BizScenario;
import com.alibaba.cola.extension.ExtensionExecutor;
import com.alibaba.fastjson2.JSON;
import com.pxb7.mall.response.TraceHandler;
import com.pxb7.mall.trade.ass.client.api.Invoke3rdRefundServiceI;
import com.pxb7.mall.trade.ass.client.dto.model.refund.PayChannelEnum;
import com.pxb7.mall.trade.ass.client.dto.model.refund.RefundErrorCode;
import com.pxb7.mall.trade.ass.client.dto.request.refund.RefundInvokeReqDTO;
import com.pxb7.mall.trade.ass.client.dto.request.refund.RefundQueryReqDTO;
import com.pxb7.mall.trade.ass.client.dto.response.refund.RefundInvokeRespDTO;
import com.pxb7.mall.trade.ass.client.dto.response.refund.RefundQueryRespDTO;
import com.pxb7.mall.trade.ass.domain.refund.converter.RefundParamConverter;
import com.pxb7.mall.trade.ass.domain.refund.invoke3rd.PayRefundExtPt;
import com.pxb7.mall.trade.ass.domain.refund.model.RefundInvokeReqBO;
import com.pxb7.mall.trade.ass.domain.refund.model.RefundInvokeRespBO;
import com.pxb7.mall.trade.ass.domain.refund.model.RefundQueryReqBO;
import com.pxb7.mall.trade.ass.domain.refund.model.RefundQueryRespBO;
import com.pxb7.mall.trade.ass.infra.constant.FeiShuWarningConstants;
import com.pxb7.mall.trade.ass.infra.repository.gateway.common.FeiShuGatewayRepository;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;

import java.util.function.Function;

@DubboService
@Slf4j
public class Invoke3rdRefundStubService implements Invoke3rdRefundServiceI {
    @Resource
    private ExtensionExecutor extensionExecutor;
    @Resource
    private FeiShuGatewayRepository feiShuGatewayRepository;

    @Override
    public SingleResponse<RefundInvokeRespDTO> invoke3rdRefund(RefundInvokeReqDTO refundInvokeReqDTO) {
        log.info("[DUBBO]调用第三方退款接口，请求参数:{}", refundInvokeReqDTO);
        RefundInvokeReqBO reqBO = RefundParamConverter.convertToRefundBO(refundInvokeReqDTO);
        Function<PayRefundExtPt, RefundInvokeRespBO> function = refundExt -> {
            try {
                return refundExt.refundV2(reqBO);
            } catch (Exception e) {
                //退款渠道 1：支付宝官方直连 2: 连连支付 3:泰山支付
                PayChannelEnum payChannelEnum = PayChannelEnum.getEnum(refundInvokeReqDTO.getPayChannel());
                log.error("[DUBBO]调用退款接口异常,退款渠道：{},请求参数:{}", payChannelEnum.getLabel(),refundInvokeReqDTO, e);
                //发送飞书预警消息
                feiShuGatewayRepository.sendTextMessage(FeiShuWarningConstants.REFUND_ERROR_MESSAGE_TEMPLATE,
                        payChannelEnum.getLabel(),
                        refundInvokeReqDTO.getRefundTradeNo(),
                        TraceHandler.getTraceId(),
                        e.getMessage());
                if (e instanceof BizException) {
                    throw (BizException) e;
                }
                throw new BizException(RefundErrorCode.REFUND_ERROR.getErrCode(),
                        RefundErrorCode.REFUND_ERROR.getErrDesc());
            }
        };
        PayChannelEnum payChannelEnum = PayChannelEnum.getEnum(refundInvokeReqDTO.getPayChannel());
        Assert.notNull(payChannelEnum, RefundErrorCode.PAY_CHANNEL_NOT_EXIST_ERROR.getErrCode(),
            RefundErrorCode.PAY_CHANNEL_NOT_EXIST_ERROR.getErrDesc());
        RefundInvokeRespBO respBO = extensionExecutor.execute(PayRefundExtPt.class, BizScenario.valueOf(payChannelEnum.getRefundBizNo()), function);
        return SingleResponse.of(RefundParamConverter.convertToRefundDTO(respBO));
    }

    @Override
    public SingleResponse<RefundQueryRespDTO> doInvokeRefundQuery(RefundQueryReqDTO refundQueryReqDTO) {
        log.info("[DUBBO]调用第三方退款查询接口，请求参数:{}", refundQueryReqDTO);
        RefundQueryReqBO reqBO = RefundParamConverter.convertToRefundQueryBO(refundQueryReqDTO);
        Function<PayRefundExtPt, RefundQueryRespBO> function = refundExt -> {
            try {
                return refundExt.refundQueryV2(reqBO);
            } catch (Exception e) {
                log.error("[DUBBO]调用退款查询接口异常,请求参数:{}", JSON.toJSONString(refundQueryReqDTO), e);
                throw new BizException(RefundErrorCode.REFUND_THIRD_FAIL.getErrCode(),
                        RefundErrorCode.REFUND_THIRD_FAIL.getErrDesc());
            }
        };
        PayChannelEnum payChannelEnum = PayChannelEnum.getEnum(refundQueryReqDTO.getPayChannel());
        Assert.notNull(payChannelEnum, RefundErrorCode.PAY_CHANNEL_NOT_EXIST_ERROR.getErrCode(),
            RefundErrorCode.PAY_CHANNEL_NOT_EXIST_ERROR.getErrDesc());
        RefundQueryRespBO respBO = extensionExecutor.execute(PayRefundExtPt.class, BizScenario.valueOf(payChannelEnum.getRefundBizNo()), function);
        RefundQueryRespDTO respDTO = RefundParamConverter.convertToRefundQueryDTO(respBO);
        respDTO.setPayTradeNo(refundQueryReqDTO.getPayTradeNo());
        return SingleResponse.of(respDTO);
    }
}
