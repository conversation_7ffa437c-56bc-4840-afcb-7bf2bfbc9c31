package com.pxb7.mall.trade.ass.adapter.job;

import com.pxb7.mall.trade.ass.domain.helper.CommandHelper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import tech.powerjob.worker.core.processor.ProcessResult;
import tech.powerjob.worker.core.processor.TaskContext;
import tech.powerjob.worker.core.processor.sdk.BasicProcessor;

import java.util.List;

@Component
@Slf4j
public class CommandJob implements BasicProcessor {
    // 模拟 16 个数据库实例
    private static final List<String> DATABASE_INSTANCES = List.of(
            "db0", "db1", "db2", "db3", "db4", "db5", "db6", "db7", "db8",
            "db9", "db10", "db11", "db12", "db13", "db14", "db15"
    );

    @Resource
    private CommandHelper commandHelper;

    @Override
    public ProcessResult process(TaskContext taskContext) {
        log.info("[ASS异步任务] CommandJob start to process command");
        // todo 后续 分片执行
        for (int i = 0; i < DATABASE_INSTANCES.size(); i++) {
            //scanDatabase(database); // 执行任务
            commandHelper.process(DATABASE_INSTANCES.get(i));
        }
        log.info("[ASS异步任务] CommandJob end to process command");
        return new ProcessResult(true, "定时任务执行成功");
    }

//    @Override
//    public ProcessResult process(TaskContext taskContext) throws Exception {
//
//        // 获取分片参数
//        int shardingIndex = getShardingIndex(); // 当前分片编号
//        int shardingTotal = getShardingTotal(); // 分片总数
//
//        // 遍历数据库实例，分配当前分片的任务
//        for (int i = 0; i < DATABASE_INSTANCES.size(); i++) {
//            if (i % shardingTotal == shardingIndex) {
//                String database = DATABASE_INSTANCES.get(i);
//                //scanDatabase(database); // 执行任务
//                commandHelper.process(database);
//            }
//        }
//        return new ProcessResult(true, "定时任务执行成功");
//    }

    private int getShardingIndex() {
        //return Integer.parseInt(XxlJobHelper.getShardingParam().split("/")[0]);
        return 3;
    }

    private int getShardingTotal() {
        //return Integer.parseInt(XxlJobHelper.getShardingParam().split("/")[1]);
        return 12;
    }
}
