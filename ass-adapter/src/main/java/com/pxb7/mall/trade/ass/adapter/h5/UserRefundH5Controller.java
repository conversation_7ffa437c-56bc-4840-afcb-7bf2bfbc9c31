package com.pxb7.mall.trade.ass.adapter.h5;

import java.util.List;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.alibaba.fastjson2.JSONObject;
import com.pxb7.mall.response.PxPageResponse;
import com.pxb7.mall.response.PxResponse;
import com.pxb7.mall.trade.ass.adapter.util.UserUtil;
import com.pxb7.mall.trade.ass.app.OrderRefundReasonAppService;
import com.pxb7.mall.trade.ass.app.RefundAppService;
import com.pxb7.mall.trade.ass.app.dto.reqeust.*;
import com.pxb7.mall.trade.ass.app.dto.response.RefundDetailRespDTO;
import com.pxb7.mall.trade.ass.app.dto.response.RefundOrderItemPageRespDTO;
import com.pxb7.mall.trade.ass.app.dto.response.RefundPageRespDTO;
import com.pxb7.mall.trade.ass.app.dto.response.RefundReasonListRespDTO;
import com.pxb7.mall.trade.ass.infra.enums.RefundActionTypeEnum;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

/**
 * 用户端账号退款
 */
@Slf4j
@RestController
@RequestMapping("/h5/user/refund")
public class UserRefundH5Controller {

    @Resource
    private RefundAppService refundAppService;

    @Resource
    private OrderRefundReasonAppService orderRefundReasonAppService;

    /**
     * 用户请求退款
     */
    @PostMapping(value = "/applyRefund")
    public PxResponse<Boolean> applyRefund(@RequestBody @Validated ApplyRefundReqDTO applyRefundReqDTO) {
        log.info("用户请求账号退款:{}", applyRefundReqDTO);
        return refundAppService.applyRefund(applyRefundReqDTO, UserUtil.getUserInfo());
    }

    /**
     * 提交用户收款账户信息（退款）
     */
    @PostMapping(value = "/submitUserRefundInfo")
    public PxResponse<Boolean> submitUserRefundInfo(@RequestBody @Validated RefundUserInfoDTO refundUserInfoDTO) {
        log.info("提交用户收款账户信息:{}", JSONObject.toJSONString(refundUserInfoDTO));
        return refundAppService.submitUserRefundInfo(refundUserInfoDTO, UserUtil.getUserInfo());
    }

    /**
     * 退款列表
     */
    @PostMapping(value = "/refundPage")
    public PxPageResponse<RefundPageRespDTO> refundPage(@RequestBody @Validated RefundPageReqDTO refundPageReqDTO) {
        log.info("退款列表:{}", JSONObject.toJSONString(refundPageReqDTO));
        return refundAppService.refundPage(refundPageReqDTO, UserUtil.getUserInfo().getUserId());
    }

    /**
     * 用户退款原因列表
     *
     */
    @PostMapping("refundReasonList")
    public PxResponse<List<RefundReasonListRespDTO>> userRefundReasonList() {
        return orderRefundReasonAppService.userRefundReasonList();
    }

    /**
     * 买家取消退款
     */
    @PostMapping(value = "/cancelRefund")
    public PxResponse<Boolean> cancelRefund(@RequestBody @Validated CancelRefundReqDTO refundReqDTO) {
        log.info("买家取消退款:{}", JSONObject.toJSONString(refundReqDTO));
        return refundAppService.cancelRefund(refundReqDTO, UserUtil.getUserInfo(), RefundActionTypeEnum.USER);
    }

    /**
     * 退款详情
     */
    @PostMapping(value = "/detail")
    public PxResponse<RefundDetailRespDTO> detail(@RequestParam String refundVoucherId) {
        log.info("退款详情:{}", refundVoucherId);
        return refundAppService.getRefundDetail(refundVoucherId);
    }

    /**
     * 订单退款列表
     */
    @PostMapping(value = "/orderRefund")
    public PxResponse<List<RefundOrderItemPageRespDTO>>
        orderRefund(@RequestBody @Validated OrderRefundReqDTO orderRefundReqDTO) {
        log.info("订单退款列表:{}", JSONObject.toJSONString(orderRefundReqDTO));
        return refundAppService.getOrderRefund(orderRefundReqDTO);
    }
}
