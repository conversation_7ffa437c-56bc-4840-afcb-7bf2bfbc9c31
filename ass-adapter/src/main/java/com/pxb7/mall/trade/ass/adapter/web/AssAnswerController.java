package com.pxb7.mall.trade.ass.adapter.web;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.pxb7.mall.response.PxResponse;
import com.pxb7.mall.trade.ass.adapter.util.UserUtil;
import com.pxb7.mall.trade.ass.app.AssAnswerAppService;
import com.pxb7.mall.trade.ass.app.dto.reqeust.AddAssAnswerReqDTO;

import jakarta.annotation.Resource;

/**
 * 售后问题回答-web
 *
 * <AUTHOR>
 **/
@RestController
@RequestMapping("/web/answer")
public class AssAnswerController {
    @Resource
    private AssAnswerAppService assAnswerAppService;

    /**
     * im端 回答问题
     */
    @PostMapping("/insert")
    public PxResponse<String> addAssAnswer(@RequestBody @Validated AddAssAnswerReqDTO reqDTO) {
        return PxResponse.ok(assAnswerAppService.addAssAnswer(reqDTO, UserUtil.getUserInfo().getUserId()));
    }

}
