package com.pxb7.mall.trade.ass.adapter.dubbo;

import org.apache.dubbo.config.annotation.DubboService;

import com.alibaba.cola.dto.SingleResponse;
import com.pxb7.mall.trade.ass.app.RefundAppService;
import com.pxb7.mall.trade.ass.app.dto.reqeust.ExposureCouponRefundReqDTO;
import com.pxb7.mall.trade.ass.app.mapping.RefundMapping;
import com.pxb7.mall.trade.ass.client.api.SinceritySellOrderServiceI;
import com.pxb7.mall.trade.ass.client.dto.request.UserExposureCouponRefundReqDTO;

import jakarta.annotation.Resource;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/9/22
 */
@DubboService
@AllArgsConstructor
public class SinceritySellOrderService implements SinceritySellOrderServiceI {
    @Resource
    private RefundAppService refundAppService;

    @Override
    public SingleResponse<Boolean> userExposureCouponRefund(UserExposureCouponRefundReqDTO param) {
        ExposureCouponRefundReqDTO exposureCouponRefundReqDTO =
            RefundMapping.INSTANCE.toExposureCouponRefundReqDTO(param);
        return SingleResponse.of(refundAppService.sinceritySellOrderRefund(exposureCouponRefundReqDTO));
    }
}
