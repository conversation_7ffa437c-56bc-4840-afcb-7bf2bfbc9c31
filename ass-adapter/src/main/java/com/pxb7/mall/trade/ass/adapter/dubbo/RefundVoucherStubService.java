package com.pxb7.mall.trade.ass.adapter.dubbo;

import com.alibaba.cola.dto.SingleResponse;
import com.pxb7.mall.response.PxResponse;
import com.pxb7.mall.trade.ass.app.RefundAppService;
import com.pxb7.mall.trade.ass.app.dto.reqeust.CancelRefundReqDTO;
import com.pxb7.mall.trade.ass.client.api.RefundVoucherServiceI;
import com.pxb7.mall.trade.ass.client.dto.request.refund.CancelRefundVoucherReqDTO;
import com.pxb7.mall.trade.ass.client.dto.request.refund.IndemnityChangeServiceRefundReqDTO;
import com.pxb7.mall.trade.ass.client.dto.response.refund.IndemnityChangeServiceRefundRespDTO;
import com.pxb7.mall.trade.ass.infra.enums.ErrorCode;
import com.pxb7.mall.trade.ass.infra.enums.RefundActionTypeEnum;
import com.pxb7.mall.trade.ass.infra.model.UserBaseInfo;
import jakarta.annotation.Resource;
import org.apache.dubbo.config.annotation.DubboService;

/**
 * <AUTHOR>
 * @date 2025/5/13 17:28
 */
@DubboService
public class RefundVoucherStubService implements RefundVoucherServiceI {

    @Resource
    private RefundAppService refundAppService;

    /**
     * 创建包赔变更退款单
     *
     * @param request 请求参数
     * @return 返回结果
     */
    @Override
    public SingleResponse<IndemnityChangeServiceRefundRespDTO> createIndemnityChangeRefund(IndemnityChangeServiceRefundReqDTO request) {
        return SingleResponse.of(refundAppService.serviceSubmitIndemnityChangeRefund(request));

    }

    @Override
    public SingleResponse<Boolean> cancelIndemnityChangeRefund(CancelRefundVoucherReqDTO request) {
        CancelRefundReqDTO refundReqDTO = new CancelRefundReqDTO();
        refundReqDTO.setRefundVoucherId(request.getRefundVoucherId());

        UserBaseInfo userBaseInfo = new UserBaseInfo();
        userBaseInfo.setUserId(request.getUserId());
        userBaseInfo.setUserName(request.getUserName());

        RefundActionTypeEnum cancelRole = RefundActionTypeEnum.SERVICE;

        try {
            PxResponse<Boolean> response = refundAppService.cancelRefund(refundReqDTO, userBaseInfo, cancelRole);
            if (response.isSuccess()) {
                return SingleResponse.of(response.getData());
            } else {
                return SingleResponse.buildFailure(ErrorCode.REFUND_CANCEL_FAIL.getErrCode(), response.getErrMessage());
            }
        } catch (Exception ex) {
            return SingleResponse.buildFailure(ErrorCode.REFUND_CANCEL_FAIL.getErrCode(), ex.getMessage());
        }
    }
}
