package com.pxb7.mall.trade.ass.adapter.web;


import com.pxb7.mall.response.PxPageResponse;
import com.pxb7.mall.response.PxResponse;
import com.pxb7.mall.trade.ass.app.AssWorkOrderAppService;
import com.pxb7.mall.trade.ass.app.common.UserIdUtils;
import com.pxb7.mall.trade.ass.app.dto.reqeust.AssWorkOrderReqDTO;
import com.pxb7.mall.trade.ass.app.dto.response.AssWorkOrderRespDTO;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 *
 *
 * 用户端售后工单模块
 *
 * <p>功能描述:</p>
 * 作者：xuexiyang
 * 创建日期：2025/07/30
 * 公司名称：金华博淳网络科技有限公司
 * 域名： www.pxb7.com
 */
@Validated
@RestController
@RequestMapping("/web/workOrder")
public class AssWorkOrderWebController {


    @Resource
    private AssWorkOrderAppService assWorkOrderAppService;


    /**
     * 分页查询用户售后订单列表
     *
     * @param clientType      客户端类型
     * @param orderPageReqDTO 请求体
     * @return 售后单列表信息
     */
    @PostMapping(value = "/page")
    public PxPageResponse<AssWorkOrderRespDTO.PageDetailDTO> pageQueryOrder(@RequestHeader(value = "client_type", required = false) Integer clientType,
                                                                            @RequestBody @Validated AssWorkOrderReqDTO.PageOrderDTO orderPageReqDTO) {
        return assWorkOrderAppService.page(orderPageReqDTO);
    }


    /**
     * 单个售后单详情查询
     *
     * @param orderNo 交易订单号
     * @return 订单售后详情
     */
    @GetMapping(value = "/detail")
    public PxResponse<AssWorkOrderRespDTO.AssWorkOrderDetailDTO> findOrderDetail(@RequestParam("orderNo") String orderNo) {
        return assWorkOrderAppService.findOrderDetail(orderNo);
    }



    /**
     * 售后单取消
     *
     * @param orderNo 交易订单号
     * @return 取消结果
     */
    @GetMapping(value = "/cancel")
    public PxResponse<Boolean> cancelOrder(@RequestParam("orderNo") String orderNo) {
        return PxResponse.ok(assWorkOrderAppService.cancel(orderNo, UserIdUtils.getUserId()));
    }


    /**
     * 获取未读售后单数量
     * @return 未读售后单数量
     */
    @GetMapping(value = "/unReadCount")
    public PxResponse<Integer> unReadCount() {
        return PxResponse.ok(assWorkOrderAppService.unreadCount(UserIdUtils.getUserId()));
    }

}
