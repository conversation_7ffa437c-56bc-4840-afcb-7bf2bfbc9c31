package com.pxb7.mall.trade.ass.adapter.dev;


import com.alibaba.fastjson2.JSON;
import com.pxb7.mall.response.PxResponse;
import com.pxb7.mall.trade.ass.domain.PayCompanyAccountDomainService;
import com.pxb7.mall.trade.ass.infra.remote.model.outpayparam.request.JdPayOutParamPO;
import com.pxb7.mall.trade.ass.infra.remote.service.http.JdPayHttpApiService;
import com.pxb7.mall.trade.ass.infra.remote.service.http.dto.request.JdPayReqPO;
import com.pxb7.mall.trade.ass.infra.remote.service.http.dto.response.JdPayRespPO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static com.pxb7.mall.trade.order.client.constants.JdPayConstant.JD_BANK_ACCOUNT_ID;


@Slf4j
@RestController
@RequestMapping("/test")
public class TestController {

    @Resource
    private JdPayHttpApiService jdPayHttpApiService;
    @Resource
    private PayCompanyAccountDomainService payCompanyAccountDomainService;

    /**
     * 京东银行卡 退款回调
     */
    @Value("${refund.jdpay.callback.url}")
    public String jdPayRefundCallbackUrl;


    /**
     * 发起退款
     * http://127.0.0.1:8086/api/ass/test/testRefund
     */
    @GetMapping("/testRefund")
    public PxResponse<JdPayRespPO.RefundRespPO> testRefund() {
        //外部支付参数
        String outPaymentParam = payCompanyAccountDomainService.getDecryptOutParamByAccountId(JD_BANK_ACCOUNT_ID);
        JdPayOutParamPO paramPO = JSON.parseObject(outPaymentParam, JdPayOutParamPO.class);
        //组装请求参数
        JdPayReqPO.RefundParam po = new JdPayReqPO.RefundParam();
        po.setOutTradeNo("refund_zxj_007");
        po.setOriginalOutTradeNo("zxj_007");
        po.setTradeAmount(String.valueOf(1));
        po.setNotifyUrl(jdPayRefundCallbackUrl);
        JdPayRespPO.RefundRespPO respPO = jdPayHttpApiService.refund(po,paramPO);
        return PxResponse.ok(respPO);
    }

    /**
     * 退款结果查询
     * http://127.0.0.1:8086/api/ass/test/testRefundQuery
     */
    @GetMapping("/testRefundQuery")
    public PxResponse<JdPayRespPO.RefundRespPO> testRefundQuery() {
        //外部支付参数
        String outPaymentParam = payCompanyAccountDomainService.getDecryptOutParamByAccountId(JD_BANK_ACCOUNT_ID);
        JdPayOutParamPO paramPO = JSON.parseObject(outPaymentParam, JdPayOutParamPO.class);
        //组装请求参数
        JdPayReqPO.RefundQueryParam po = new JdPayReqPO.RefundQueryParam();
        po.setOutTradeNo("refund_zxj_007");
        JdPayRespPO.RefundRespPO respPO = jdPayHttpApiService.refundQuery(po,paramPO);
        return PxResponse.ok(respPO);
    }

}
