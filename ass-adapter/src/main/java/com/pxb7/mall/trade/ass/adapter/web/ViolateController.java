package com.pxb7.mall.trade.ass.adapter.web;

import com.pxb7.mall.response.PxResponse;
import com.pxb7.mall.trade.ass.adapter.util.UserUtil;
import com.pxb7.mall.trade.ass.app.ViolateAppService;
import com.pxb7.mall.trade.ass.app.dto.reqeust.violate.ViolateApplyDTO;
import com.pxb7.mall.trade.ass.app.dto.reqeust.violate.ViolateQueryDTO;
import com.pxb7.mall.trade.ass.app.dto.reqeust.violate.ViolateUpdateDTO;
import com.pxb7.mall.trade.ass.app.dto.response.violate.ViolateOrderRespDTO;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.ViolateOrder;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
/**
 * 客服端违约单
 */
@RestController
@RequestMapping("/web/violate")
@Slf4j
public class ViolateController {

    @Resource
    private ViolateAppService violateAppService;

    /**
     * 修改违约单
     *
     * @param violateUpdateDTO
     * @return
     */
    @PostMapping(value = "/update")
    public PxResponse<Boolean> update(@RequestBody @Validated ViolateUpdateDTO violateUpdateDTO) {
        log.info("[violateUpdate] violateUpdateDTO:{}", violateUpdateDTO);
        violateAppService.update(violateUpdateDTO, UserUtil.getUserInfo());
        return PxResponse.ok(true);
    }

    /**
     * 获取违约单列表
     *
     * @param queryDTO
     * @return
     */
    @PostMapping(value = "/list")
    public PxResponse<List<ViolateOrderRespDTO>> list(@RequestBody @Validated ViolateQueryDTO queryDTO) {
        log.info("[violateQuery] param:{}", queryDTO);
        List<ViolateOrderRespDTO> list = violateAppService.list(queryDTO, UserUtil.getUserInfo());
        return PxResponse.ok(list);
    }

    /**
     * 发起违约单收款
     *
     * @param violateApplyDTO
     * @return
     */
    @PostMapping(value = "/apply")
    public PxResponse<ViolateOrder> apply(@RequestBody @Validated ViolateApplyDTO violateApplyDTO) {
        log.info("[violateApply] violateApplyDTO:{}", violateApplyDTO);
        return PxResponse.ok(violateAppService.apply(violateApplyDTO, UserUtil.getUserInfo()));
    }

    /**
     * 发起违约收款 按钮
     *
     * @return
     */
    @PostMapping(value = "/applyBtn")
    public PxResponse<Boolean> applyBtn(@RequestBody @Validated ViolateQueryDTO violateQueryDTO) {
        log.info("[applyBtn] violateQueryDTO:{}", violateQueryDTO);
        return PxResponse.ok(violateAppService.getViolateByOrder(violateQueryDTO.getOrderItemId()));
    }

}