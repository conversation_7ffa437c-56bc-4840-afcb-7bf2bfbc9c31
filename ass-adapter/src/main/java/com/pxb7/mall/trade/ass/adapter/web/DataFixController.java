package com.pxb7.mall.trade.ass.adapter.web;

import cn.dev33.satoken.annotation.SaIgnore;
import com.pxb7.mall.response.PxResponse;
import com.pxb7.mall.trade.ass.domain.refund.RefundInvokeService;
import com.pxb7.mall.trade.ass.domain.refund.message.provider.RefundMessageService;
import com.pxb7.mall.trade.ass.domain.refund.model.RefundInvokeReqBO;
import com.pxb7.mall.trade.ass.infra.model.mq.RefundQueryMessage;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping(value = "/data/fix")
public class DataFixController {
    @Resource
    private RefundMessageService refundMessageService;
    @Resource
    private RefundInvokeService refundInvokeService;


    @SaIgnore
    @PostMapping(value = "/resendRefundQueryMQ")
    public PxResponse<Void> resendRefundQueryMQ(@RequestBody RefundQueryMessage message) {
        refundMessageService.sendRefundQueryMessage(message);
        return PxResponse.ok();
    }


    @SaIgnore
    @PostMapping(value = "/reTradeAdminRefund")
    public PxResponse<Void> reTradeAdminRefund(@RequestBody RefundInvokeReqBO invokeReqBO) {
        refundInvokeService.doInvokeRefund(invokeReqBO);
        return PxResponse.ok();
    }
}
