package com.pxb7.mall.trade.ass.adapter.consumer;

import com.pxb7.mall.components.rocketmq.RocketMQListenerExt;
import com.pxb7.mall.trade.ass.app.RefundCallbackAppService;
import com.pxb7.mall.trade.ass.infra.config.AssertIdeaCondition;
import com.pxb7.mall.trade.ass.infra.constant.RMQConstant;
import com.pxb7.mall.trade.ass.infra.model.mq.RefundCallbackMessage;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.annotation.RocketMQMessageListener;
import org.apache.rocketmq.client.apis.consumer.ConsumeResult;
import org.apache.rocketmq.client.apis.message.MessageId;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.springframework.context.annotation.Conditional;
import org.springframework.stereotype.Service;

/**
 * 退款回调MQ
 */
@Service
@Slf4j
@Conditional(AssertIdeaCondition.class)
@RocketMQMessageListener(consumerGroup = RMQConstant.REFUND_CALLBACK_GROUP, topic = RMQConstant.ASS_REFUND_CENTER_TOPIC, tag = RMQConstant.REFUND_CALLBACK_TAG)
public class RefundCallbackMessageConsumer implements RocketMQListenerExt<RefundCallbackMessage> {

    @Resource
    private RefundCallbackAppService refundCallbackAppService;

    @Override
    public ConsumeResult consume(MessageView messageView, RefundCallbackMessage message) {
        MessageId messageId = messageView.getMessageId();
        log.info("[退款回调] 消息处理 messageId = {},message = {}", messageId, message);
        try {
            boolean flag = refundCallbackAppService.RefundCallbackHandler(message);
            return flag ? ConsumeResult.SUCCESS : ConsumeResult.FAILURE;
        } catch (Exception e) {
            log.error("error in consumed from ass_refund_center_topic: messageId = {},message = {}", messageId, message, e);
            return ConsumeResult.FAILURE;
        }
    }

}
