package com.pxb7.mall.trade.ass.adapter.consumer.violate;

import com.pxb7.mall.components.rocketmq.RocketMQListenerExt;
import com.pxb7.mall.trade.ass.app.RiskBlackAppService;
import com.pxb7.mall.trade.ass.client.enums.RiskBlackOperateType;
import com.pxb7.mall.trade.ass.domain.violate.mq.message.ViolateRiskBlackMessage;
import com.pxb7.mall.trade.ass.infra.config.AssertIdeaCondition;
import com.pxb7.mall.trade.ass.infra.config.nacos.ViolateRiskBlackConfig;
import com.pxb7.mall.trade.ass.infra.constant.RMQConstant;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.annotation.RocketMQMessageListener;
import org.apache.rocketmq.client.apis.consumer.ConsumeResult;
import org.apache.rocketmq.client.apis.message.MessageId;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.springframework.context.annotation.Conditional;
import org.springframework.stereotype.Service;

/**
 * 违约单-风控拉黑
 */
@Service
@Slf4j
@Conditional(AssertIdeaCondition.class)
@RocketMQMessageListener(consumerGroup = RMQConstant.VIOLATE_RISK_BLACK_GROUP, topic = RMQConstant.VIOLATE_TOPIC,
    tag = RMQConstant.VIOLATE_RISK_BLACK_TAG)
public class ViolateRiskBlackMessageConsumer implements RocketMQListenerExt<ViolateRiskBlackMessage> {

    @Resource
    private RiskBlackAppService riskBlackAppService;

    @Resource
    private ViolateRiskBlackConfig violateRiskBlackConfig;

    @Override
    public ConsumeResult consume(MessageView messageView, ViolateRiskBlackMessage message) {
        MessageId messageId = messageView.getMessageId();
        log.info("[违约单风控处理]，messageId = {},message = {}", messageId, message);
        try {
            String violateId = message.getViolateId();
            if (RiskBlackOperateType.ADD.getCode().equals(message.getOperationType())) {
                if (violateRiskBlackConfig.getAddRiskBlackSwitch()) {
                    riskBlackAppService.addViolateBlackRecord(violateId);
                }

            } else if (RiskBlackOperateType.REMOVE.getCode().equals(message.getOperationType())) {
                if (violateRiskBlackConfig.getRemoveRiskBlackSwitch()) {
                    riskBlackAppService.removeViolateBlackRecord(violateId);
                }
            }
            return ConsumeResult.SUCCESS;
        } catch (Exception e) {
            log.error("[违约单风控处理] error : messageId = {},message = {}", messageId, message, e);
            return ConsumeResult.FAILURE;
        }
    }
}
