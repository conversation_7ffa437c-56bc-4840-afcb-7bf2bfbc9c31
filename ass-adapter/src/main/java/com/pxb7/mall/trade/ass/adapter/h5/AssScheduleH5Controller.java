package com.pxb7.mall.trade.ass.adapter.h5;

import com.pxb7.mall.response.PxResponse;
import com.pxb7.mall.trade.ass.adapter.web.AssScheduleWebController;
import com.pxb7.mall.trade.ass.app.AssScheduleLogAppService;
import com.pxb7.mall.trade.ass.app.dto.response.assSchedule.AssScheduleRespMobileDTO;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 售后流程进度-h5
 *
 * <AUTHOR>
 **/
@RestController
@RequestMapping("/h5/schedule")
public class AssScheduleH5Controller extends AssScheduleWebController {
    @Resource
    private AssScheduleLogAppService assScheduleLogAppService;
    /**
     * c端用户获取进度
     */
    @GetMapping("/record")
    public PxResponse<AssScheduleRespMobileDTO> record(@RequestParam("orderNo") String orderNo) {
        return PxResponse.ok(assScheduleLogAppService.recordLogList(orderNo));
    }
}
