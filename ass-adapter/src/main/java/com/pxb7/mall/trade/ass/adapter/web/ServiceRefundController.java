package com.pxb7.mall.trade.ass.adapter.web;

import com.pxb7.mall.response.PxResponse;
import com.pxb7.mall.trade.ass.adapter.util.UserUtil;
import com.pxb7.mall.trade.ass.app.OrderRefundReasonAppService;
import com.pxb7.mall.trade.ass.app.RefundAppService;
import com.pxb7.mall.trade.ass.app.dto.reqeust.*;
import com.pxb7.mall.trade.ass.app.dto.response.*;
import com.pxb7.mall.trade.ass.infra.enums.RefundActionTypeEnum;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 客服端退款
 */
@RestController
@RequestMapping("/web/service/refund")
@Slf4j
public class ServiceRefundController {

    @Resource
    private RefundAppService refundAppService;

    @Resource
    private OrderRefundReasonAppService orderRefundReasonAppService;


    /**
     * 客服审核或发起退款
     */
    @PostMapping(value = "/serviceSubmitRefund")
    public PxResponse<Boolean>
    serviceSubmitRefund(@RequestBody @Validated ServiceSubmitRefundReqDTO serviceSubmitRefundReqDTO) {
        log.info("[order refund] serviceSubmitRefund:{}", serviceSubmitRefundReqDTO);
        return refundAppService.serviceSubmitRefund(serviceSubmitRefundReqDTO, UserUtil.getUserInfo());
    }


    /**
     * 客服拒绝买家的退款请求
     */
    @PostMapping(value = "/supportRefundReject")
    public PxResponse<Boolean>
    supportRefundReject(@RequestBody @Validated SupportRefundRejectReqDTO supportRefundRejectReqDTO) {
        log.info("[order refund] supportRefundReject:{}, refundVoucherId: {}",
                supportRefundRejectReqDTO.getRefundVoucherId(), supportRefundRejectReqDTO);
        return refundAppService.supportRefundReject(supportRefundRejectReqDTO, UserUtil.getUserInfo());
    }


    /**
     * 客服取消退款
     */
    @PostMapping(value = "/serviceCancelRefund")
    public PxResponse<Boolean> serviceCancelRefund(@RequestBody @Validated CancelRefundReqDTO refundReqDTO) {
        log.info("[order refund] serviceCancelRefund:{}", refundReqDTO);
        return refundAppService.cancelRefund(refundReqDTO, UserUtil.getUserInfo(), RefundActionTypeEnum.SERVICE);
    }

    /**
     * 发送收集用户退款信息卡片
     */
    @PostMapping(value = "/sendCollectRefundInfoCard")
    public PxResponse<Boolean> sendCollectRefundInfoCard(@RequestParam("refundVoucherId") String refundVoucherId) {
        log.info("[order refund] sendCollectRefundInfoCard:{}", refundVoucherId);
        return refundAppService.sendCollectRefundInfoCard(refundVoucherId, UserUtil.getUserInfo());
    }

    /**
     * 客服发起或审核时初始化接口，用于判断支持的退款方式
     */
    @PostMapping(value = "/getRefundInitInfo")
    public PxResponse<RefundInitInfoRespDTO> getRefundInitInfo(@RequestBody @Validated ImRoomIdReqDTO imRoomIdReqDTO) {
        log.info("[order refund] getRefundInitInfo:{}", imRoomIdReqDTO);
        return PxResponse.ok(refundAppService.getRefundInitInfo(imRoomIdReqDTO));
    }

    /**
     * 客服退款原因列表,用于退款页面动态获取退款原因
     */
    @PostMapping("serviceRefundReasonList")
    public PxResponse<List<RefundReasonListRespDTO>>
    serviceRefundReasonList(@RequestBody @Validated UserRefundReasonListReqDTO reqDTO) {
        return orderRefundReasonAppService.serviceRefundReasonList(reqDTO);
    }

    /**
     * 订单退款列表
     */
    @PostMapping(value = "/orderRefund")
    public PxResponse<List<RefundOrderItemPageRespDTO>>
    orderRefund(@RequestBody @Validated OrderRefundReqDTO orderRefundReqDTO) {
        log.info("[order refund] orderRefund:{}", orderRefundReqDTO);
        return refundAppService.getOrderRefund(orderRefundReqDTO);
    }

    /**
     * 退款详情
     */
    @PostMapping(value = "/detail")
    public PxResponse<RefundDetailRespDTO> detail(@RequestParam String refundVoucherId) {
        log.info("[order refund] detail:{}", refundVoucherId);
        return refundAppService.getRefundDetail(refundVoucherId);
    }

    /**
     * 客服发起或审核退款时，输入商品退款金额，返回详细退款金额
     */
    @PostMapping(value = "/calculateRefundAmount")
    public PxResponse<CalculateRefundAmountRespDTO>
    calculateRefundAmount(@RequestBody @Validated CalculateRefundAmountReqDTO calculateRefundAmountDTO) {
        log.info("[order refund] calculateRefundAmount:{}", calculateRefundAmountDTO);
        return PxResponse.ok(refundAppService.calculateRefundAmount(calculateRefundAmountDTO));
    }

    /**
     * 违约金初始化数据
     */
    @PostMapping(value = "/getViolateInitInfo")
    public PxResponse<RefundInitInfoRespDTO> getViolateInitInfo(@RequestBody @Validated ImRoomIdReqDTO imRoomIdReqDTO) {
        log.info("[order refund] getViolateInitInfo:{}", imRoomIdReqDTO);
        return PxResponse.ok(refundAppService.getViolateInitInfo(imRoomIdReqDTO));
    }

}