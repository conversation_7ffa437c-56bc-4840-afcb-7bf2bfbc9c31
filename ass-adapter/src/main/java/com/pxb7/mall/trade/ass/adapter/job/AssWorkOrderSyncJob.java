package com.pxb7.mall.trade.ass.adapter.job;

import cn.hutool.core.util.StrUtil;
import com.pxb7.mall.trade.ass.app.AssWorkOrderAppService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import tech.powerjob.worker.core.processor.ProcessResult;
import tech.powerjob.worker.core.processor.TaskContext;
import tech.powerjob.worker.core.processor.sdk.BasicProcessor;

/**
 * <p>功能描述:存量未完成售后工单同步任务</p>
 * 作者：杨学玺
 * 创建日期：2025/08/12
 * 公司名称：金华博淳网络科技有限公司
 * 域名：www.pxb7.com
 */
@Service
@Slf4j
public class AssWorkOrderSyncJob implements BasicProcessor {


    @Resource
    private AssWorkOrderAppService assWorkOrderAppService;

    @Override
    public ProcessResult process(TaskContext taskContext) throws Exception {

        try {
            String jobParams = taskContext.getJobParams();
            if (StrUtil.isBlank(jobParams)) {
                return new ProcessResult(false, "参数解析失败");
            }
            String[] split = jobParams.split("\\,");
            String minId = split[0].trim();
            String maxId = split[1].trim();
            assWorkOrderAppService.initWorkOrder(Long.parseLong(minId), Long.parseLong(maxId));
            return new ProcessResult(true, "定时任务执行成功");
        } catch (Exception exception) {
            log.error("AssWorkOrderSyncJob", exception);
            taskContext.getOmsLogger().error("定时任务执行失败: " + exception.getMessage());
            return new ProcessResult(false, "定时任务执行失败");
        }
    }


}
