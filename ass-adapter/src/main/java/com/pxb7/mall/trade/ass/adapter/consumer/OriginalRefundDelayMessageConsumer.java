package com.pxb7.mall.trade.ass.adapter.consumer;

import com.pxb7.mall.components.rocketmq.RocketMQListenerExt;
import com.pxb7.mall.trade.ass.domain.refund.PayRefundApiDomainService;
import com.pxb7.mall.trade.ass.infra.config.AssertIdeaCondition;
import com.pxb7.mall.trade.ass.infra.constant.RMQConstant;
import com.pxb7.mall.trade.ass.infra.model.mq.OriginalRefundMessage;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.annotation.RocketMQMessageListener;
import org.apache.rocketmq.client.apis.consumer.ConsumeResult;
import org.apache.rocketmq.client.apis.message.MessageId;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.springframework.context.annotation.Conditional;
import org.springframework.stereotype.Service;

/**
 * 抢单支付/重复支付 延迟原路退（原payLog退）MQ
 */
@Service
@Slf4j
@Conditional(AssertIdeaCondition.class)
@RocketMQMessageListener(consumerGroup = RMQConstant.ASS_ORIGINAL_REFUND_DELAY_GROUP, topic = RMQConstant.ASS_REFUND_CENTER_DELAY_TOPIC, tag = RMQConstant.ASS_ORIGINAL_REFUND_TAG)
public class OriginalRefundDelayMessageConsumer implements RocketMQListenerExt<OriginalRefundMessage> {
    @Resource
    private PayRefundApiDomainService payRefundApiDomainService;

    @Override
    public ConsumeResult consume(MessageView messageView, OriginalRefundMessage message) {
        MessageId messageId = messageView.getMessageId();
        log.info("[线上原路退款] 延迟 消息处理，messageId = {},message = {}", messageId, message);
        try {
            payRefundApiDomainService.originalRefund(message, true);
            return ConsumeResult.SUCCESS;
        } catch (Exception e) {
            log.error("error in consumed from ass_refund_center_delay_topic: messageId = {},message = {}", messageId, message, e);
            return ConsumeResult.FAILURE;
        }
    }
}
