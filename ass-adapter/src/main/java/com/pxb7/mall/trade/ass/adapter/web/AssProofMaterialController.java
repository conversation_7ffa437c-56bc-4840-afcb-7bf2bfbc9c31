package com.pxb7.mall.trade.ass.adapter.web;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.pxb7.mall.response.PxResponse;
import com.pxb7.mall.trade.ass.adapter.util.UserUtil;
import com.pxb7.mall.trade.ass.app.AssScheduleAppService;
import com.pxb7.mall.trade.ass.app.dto.reqeust.AddProofReqDTO;

import jakarta.annotation.Resource;

/**
 * 售后证据材料-web
 *
 * <AUTHOR>
 **/
@RestController
@RequestMapping("/web/proof")
public class AssProofMaterialController {
    @Resource
    private AssScheduleAppService assScheduleAppService;

    /**
     * im端 添加证据材料
     */
    @PostMapping("/add")
    public PxResponse<Boolean> addProof(@RequestBody @Validated AddProofReqDTO reqDTO) {
        return PxResponse.ok(assScheduleAppService.addProof(reqDTO, UserUtil.getUserInfo().getUserId()));
    }
}
