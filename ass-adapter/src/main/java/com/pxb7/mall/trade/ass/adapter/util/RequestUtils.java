package com.pxb7.mall.trade.ass.adapter.util;

import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

@Slf4j
public class RequestUtils {
    /**
     * 将form表单参数转成map类型
     *
     * @param request
     * @return
     */
    public static Map<String, String> formToParamMap(HttpServletRequest request) {
        Map<String, String> params = new HashMap<>();
        Map<String, String[]> requestParams = request.getParameterMap();
        for (String key : requestParams.keySet()) {
            String[] values = requestParams.get(key);
            String valueStr = String.join(",", values);
            params.put(key, valueStr);
        }
        return params;
    }

    /**
     * 将json参数转成map类型
     *
     * @param request
     * @return
     */
    public static String requestToStr(HttpServletRequest request) throws IOException {
        StringBuilder resultStr = new StringBuilder();
        BufferedReader in = new BufferedReader(new InputStreamReader(request.getInputStream(), StandardCharsets.UTF_8));
        String line;
        while ((line = in.readLine()) != null) {
            resultStr.append(line);
        }
        return resultStr.toString();
    }
}
