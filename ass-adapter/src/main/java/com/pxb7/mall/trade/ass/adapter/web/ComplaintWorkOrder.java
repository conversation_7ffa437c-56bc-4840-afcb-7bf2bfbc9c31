package com.pxb7.mall.trade.ass.adapter.web;

import java.util.List;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.pxb7.mall.auth.c.util.ImUserUtil;
import com.pxb7.mall.response.PxResponse;
import com.pxb7.mall.trade.ass.app.ComplaintWorkOrderAppService;
import com.pxb7.mall.trade.ass.app.dto.reqeust.complaint.req.ComplaintWorkOrderFinishReqDTO;
import com.pxb7.mall.trade.ass.app.dto.reqeust.complaint.req.ComplaintWorkOrderReqDTO;
import com.pxb7.mall.trade.ass.app.dto.reqeust.complaint.req.ComplaintWorkOrderTransferReqDTO;
import com.pxb7.mall.trade.ass.app.dto.reqeust.complaint.resp.ComplaintDepartmentConfigRespDTO;
import com.pxb7.mall.trade.ass.app.dto.reqeust.complaint.resp.ComplaintWorkLogRespDTO;
import com.pxb7.mall.trade.ass.app.dto.reqeust.complaint.resp.ComplaintWorkOrderDetailRespDTO;
import com.pxb7.mall.trade.ass.app.dto.reqeust.complaint.resp.ComplaintWorkOrderRespDTO;

import jakarta.annotation.Resource;

/**
 * 客诉工单-web
 *
 */
@RestController
@RequestMapping("/web/complaint/workOrder")
public class ComplaintWorkOrder {

    @Resource
    private ComplaintWorkOrderAppService complaintWorkOrderAppService;

    /**
     * 创建客诉工单
     *
     * @param complaintWorkOrderReqDTO 创建客诉工单请求的DTO
     * @return Boolean
     */
    @PostMapping("/create")
    public PxResponse<Boolean> create(@RequestBody @Validated ComplaintWorkOrderReqDTO complaintWorkOrderReqDTO) {
        String userId = ImUserUtil.getUserId();
        return PxResponse.ok(complaintWorkOrderAppService.create(complaintWorkOrderReqDTO, userId));
    }

    /**
     * 工单列表
     *
     */
    @GetMapping("/list")
    public PxResponse<List<ComplaintWorkOrderRespDTO>> list(@RequestParam("userId") String userId) {
        String customerId = ImUserUtil.getUserId();
        return PxResponse.ok(complaintWorkOrderAppService.list(customerId, userId));
    }

    /**
     * 客诉工单详情
     *
     * @param complaintWorkId 客诉工单id
     */
    @GetMapping("/detail")
    public PxResponse<ComplaintWorkOrderDetailRespDTO> detail(String complaintWorkId) {
        return PxResponse.ok(complaintWorkOrderAppService.detail(complaintWorkId));
    }

    /**
     * 完结工单
     *
     * @param complaintWorkOrderFinishReqDTO 完结工单请求的DTO
     * @return Boolean
     */
    @PostMapping("/finish")
    public PxResponse<Boolean>
        finish(@RequestBody @Validated ComplaintWorkOrderFinishReqDTO complaintWorkOrderFinishReqDTO) {
        String userId = ImUserUtil.getUserId();
        return PxResponse.ok(complaintWorkOrderAppService.finish(complaintWorkOrderFinishReqDTO, userId));
    }

    /**
     * 工单转交
     *
     * @param complaintWorkOrderTransferReqDTO 转交工单请求的DTO
     * @return Boolean
     */
    @PostMapping("/transfer")
    public PxResponse<Boolean>
        transfer(@RequestBody @Validated ComplaintWorkOrderTransferReqDTO complaintWorkOrderTransferReqDTO) {
        String userId = ImUserUtil.getUserId();
        return PxResponse.ok(complaintWorkOrderAppService.transfer(complaintWorkOrderTransferReqDTO, userId));
    }

    /**
     * 客诉部门列表
     *
     * @return ComplaintDepartmentConfigRespDTO
     */
    @GetMapping("/departmentList")
    public PxResponse<List<ComplaintDepartmentConfigRespDTO>> departmentList() {
        return PxResponse.ok(complaintWorkOrderAppService.departmentList());
    }

    /**
     * 查看工单日志
     *
     * @param complaintWorkId 工单id
     * @return
     */
    @GetMapping("/viewLog")
    public PxResponse<List<ComplaintWorkLogRespDTO>> log(String complaintWorkId) {
        return PxResponse.ok(complaintWorkOrderAppService.viewLog(complaintWorkId));
    }

    /**
     * 手机号状态查询
     * @param phone 待验证的手机号
     * @return 是否注销 true 已注销  false 未注册或正常已注册用户
     */
    @GetMapping("/phone/status")
    public PxResponse<Boolean> phoneStatus(@RequestParam("phone") String phone) {
        return PxResponse.ok(complaintWorkOrderAppService.checkPhoneStatus(phone));
    }
}
