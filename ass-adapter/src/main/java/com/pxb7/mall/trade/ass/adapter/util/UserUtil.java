package com.pxb7.mall.trade.ass.adapter.util;

import java.util.Objects;
import java.util.Optional;

import com.pxb7.mall.auth.c.util.AdminUserUtil;
import com.pxb7.mall.auth.c.util.ImUserUtil;
import com.pxb7.mall.auth.c.util.LoginUserUtil;
import com.pxb7.mall.auth.c.util.MerchantUserUtil;
import com.pxb7.mall.auth.dto.AdminUserDTO;
import com.pxb7.mall.auth.dto.ImUserDTO;
import com.pxb7.mall.auth.dto.LoginUserDTO;
import com.pxb7.mall.auth.dto.MerchantUserDTO;
import com.pxb7.mall.trade.ass.infra.model.UserBaseInfo;

/**
 * <AUTHOR>
 * @date 2024/10/24 20:00
 **/
public class UserUtil {

    /**
     * im 中为客服类型
     */
    private static final Integer IM_USER_TYPE_CUSTOMER_CARE = 1;
    /**
     * 获取用户信息
     */
    public static UserBaseInfo getUserInfo() {
        if (ImUserUtil.isLogin()) {
            ImUserDTO imUser = ImUserUtil.getImUser();
            // 1是客服 也是admin角色
            Integer imUserType = Optional.ofNullable(imUser).map(ImUserDTO::getUserType).orElse(null);
            return new UserBaseInfo().setUserId(Objects.isNull(imUser) ? "" : imUser.getUserId())
                .setUserName(Objects.isNull(imUser) ? "" : imUser.getUserName()).setIsMerchant(Boolean.FALSE)
                .setIsAdmin(IM_USER_TYPE_CUSTOMER_CARE.equals(imUserType));
        } else if (LoginUserUtil.isLogin()) {
            LoginUserDTO loginUser = LoginUserUtil.getLoginUser();
            return new UserBaseInfo().setUserId(Objects.isNull(loginUser) ? "" : loginUser.getUserId())
                .setUserName(Objects.isNull(loginUser) ? "" : loginUser.getUserName()).setIsMerchant(Boolean.FALSE)
                .setIsAdmin(Boolean.FALSE);
        } else if (MerchantUserUtil.isLogin()) {
            MerchantUserDTO merchantUser = MerchantUserUtil.getMerchantUser();
            return new UserBaseInfo().setUserId(Objects.isNull(merchantUser) ? "" : merchantUser.getUserId())
                .setUserName(Objects.isNull(merchantUser) ? "" : merchantUser.getUserName())
                .setIsMerchant(Boolean.TRUE).setMerchantId(merchantUser.getMerchantId())
                .setIsAdmin(Boolean.FALSE);
        } else if (AdminUserUtil.isLogin()) {
            AdminUserDTO adminUser = AdminUserUtil.getAdminUser();
            return new UserBaseInfo().setUserId(Objects.isNull(adminUser) ? "" : adminUser.getUserId())
                .setUserName(Objects.isNull(adminUser) ? "" : adminUser.getUserName()).setIsMerchant(Boolean.FALSE)
                .setIsAdmin(Boolean.TRUE);
        }
        return new UserBaseInfo();
    }
}
