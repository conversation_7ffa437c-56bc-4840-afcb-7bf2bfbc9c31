package com.pxb7.mall.trade.ass.adapter.consumer.violate;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.pxb7.mall.trade.ass.domain.violate.manager.ViolateDealManager;
import com.pxb7.mall.trade.ass.infra.constant.RMQConstant;
import com.pxb7.mall.trade.ass.infra.enums.ErrorCode;
import com.pxb7.mall.trade.ass.infra.exception.Ensure;
import com.pxb7.mall.trade.ass.infra.repository.db.RefundVoucherRepository;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.RefundVoucher;
import com.pxb7.mall.trade.ass.infra.util.ObjectMapperUtil;
import com.pxb7.mall.trade.order.client.message.OrderItemStatusChangeMQDTO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.annotation.RocketMQMessageListener;
import org.apache.rocketmq.client.apis.consumer.ConsumeResult;
import org.apache.rocketmq.client.apis.message.MessageId;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.apache.rocketmq.client.core.RocketMQListener;
import org.springframework.stereotype.Service;

import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;

/**
 * 退款完成-处理违约单
 */
@Service
@Slf4j
@RocketMQMessageListener(consumerGroup = RMQConstant.VIOLATE_DEAL_GROUP, topic = "order_status_change_topic", tag = "refundCancel")
public class OrderRefundViolateDealMessageConsumer implements RocketMQListener {
    @Resource
    private ViolateDealManager violateDealManager;
    @Resource
    private RefundVoucherRepository refundVoucherRepository;


    @Override
    public ConsumeResult consume(MessageView message) {
        MessageId messageId = message.getMessageId();
        log.info("[违约金-开始处理退款违约单]，messageId = {},message = {}", messageId, message);
        ByteBuffer body = message.getBody();
        try {
            String bodyString = StandardCharsets.UTF_8.decode(body).toString();
            OrderItemStatusChangeMQDTO mqObject = ObjectMapperUtil.fromJson(bodyString, OrderItemStatusChangeMQDTO.class);

            if (StrUtil.isBlank(mqObject.getOrderItemId())) {
                log.info("[违约金-开始处理退款违约单] 订单缺失 :empty orderItem, messageId = {},message = {}", messageId, message);
                return ConsumeResult.SUCCESS;
            }

            String orderItemId = mqObject.getOrderItemId();
            violateDealManager.deal(orderItemId,getRefundVoucherId(orderItemId));
            return ConsumeResult.SUCCESS;
        } catch (Exception e) {
            log.warn("[违约金-开始处理退款违约单] error : messageId = {},message = {}", messageId, message, e);
            return ConsumeResult.FAILURE;
        }

    }

    private String getRefundVoucherId(String orderItemId) {
        RefundVoucher refundVoucher = refundVoucherRepository.lastWholeByOrderItemId(orderItemId);

        Ensure.that(ObjectUtil.isNotEmpty(refundVoucher)).isTrue(ErrorCode.REFUND_NOT_FOUND_ERROR);

        return refundVoucher.getRefundVoucherId();
    }
}
