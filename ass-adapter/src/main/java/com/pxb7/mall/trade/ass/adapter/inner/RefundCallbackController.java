package com.pxb7.mall.trade.ass.adapter.inner;

import com.pxb7.mall.trade.ass.adapter.util.RequestUtils;
import com.pxb7.mall.trade.ass.client.dto.model.refund.PayChannelEnum;
import com.pxb7.mall.trade.ass.domain.refund.message.provider.RefundMessageService;
import com.pxb7.mall.trade.ass.infra.constant.PayConstant;
import com.pxb7.mall.trade.ass.infra.model.mq.RefundCallbackMessage;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * 退款回调 controller
 */
@Slf4j
@RestController
@RequestMapping("/inner/refund")
public class RefundCallbackController {

    @Resource
    private RefundMessageService refundMessageService;

    /**
     * 京东协议支付退款回调 TODO JD*** 待调试
     */
    @PostMapping(value = "/jdpay/callback")
    public void jdPayRefundCallback(HttpServletRequest request, HttpServletResponse response) {
        log.info("【京东协议支付】退款回调... ");
        try {
            String requestStr = RequestUtils.requestToStr(request);
            // 发送退款回调消息
            RefundCallbackMessage message = new RefundCallbackMessage();
            message.setChannel(PayChannelEnum.JD_PAY);
            message.setCallbackData(requestStr);
            refundMessageService.sendRefundCallbackMessage(message);

            response.getWriter().write(PayConstant.SUCCESS);
        } catch (Exception e) {
            log.error("【京东协议支付】退款回调异常", e);
        }
    }
}
