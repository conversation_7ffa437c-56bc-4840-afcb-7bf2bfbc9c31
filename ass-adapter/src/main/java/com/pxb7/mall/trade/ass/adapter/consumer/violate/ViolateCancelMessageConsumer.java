package com.pxb7.mall.trade.ass.adapter.consumer.violate;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.pxb7.mall.trade.ass.domain.violate.ViolateOrderDomainService;
import com.pxb7.mall.trade.ass.domain.violate.manager.ViolateApplyManager;
import com.pxb7.mall.trade.ass.infra.constant.RMQConstant;
import com.pxb7.mall.trade.ass.infra.enums.RefundWholeEnum;
import com.pxb7.mall.trade.ass.infra.repository.db.RefundVoucherRepository;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.RefundVoucher;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.ViolateOrder;
import com.pxb7.mall.trade.ass.infra.util.ObjectMapperUtil;
import com.pxb7.mall.trade.order.client.enums.order.RefundStatusEnum;
import com.pxb7.mall.trade.order.client.message.OrderItemRefundStatusChangeMQDTO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.annotation.RocketMQMessageListener;
import org.apache.rocketmq.client.apis.consumer.ConsumeResult;
import org.apache.rocketmq.client.apis.message.MessageId;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.apache.rocketmq.client.core.RocketMQListener;
import org.springframework.stereotype.Service;

import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.util.Set;

import static com.pxb7.mall.trade.ass.infra.constant.RMQConstant.ORDER_VOUCHER_STATUS_CHANGE_TOPIC;

/**
 * 违约单取消
 */
@Service
@Slf4j
@RocketMQMessageListener(consumerGroup = RMQConstant.VIOLATE_CANCEL_GROUP, topic = ORDER_VOUCHER_STATUS_CHANGE_TOPIC, tag = "refundFail||refundClose")
public class ViolateCancelMessageConsumer implements RocketMQListener {
    @Resource
    private ViolateApplyManager violateApplyManager;
    @Resource
    private RefundVoucherRepository refundVoucherRepository;
    @Resource
    private ViolateOrderDomainService violateOrderDomainService;

    @Override
    public ConsumeResult consume(MessageView message) {
        MessageId messageId = message.getMessageId();
        log.info("[违约金-监听订单退款状态变更]，messageId = {},message = {}", messageId, message);
        ByteBuffer body = message.getBody();
        try {
            String bodyString = StandardCharsets.UTF_8.decode(body).toString();
            OrderItemRefundStatusChangeMQDTO mqObject = ObjectMapperUtil.fromJson(bodyString, OrderItemRefundStatusChangeMQDTO.class);

            if (StrUtil.isBlank(mqObject.getRefundVoucherId())) {
                log.info("[违约金-监听订单退款状态变更] 退款单id缺失 :empty refundVoucherId, messageId = {},message = {}", messageId, message);
                return ConsumeResult.SUCCESS;
            }

            String violateId = getWaitCancelViolate(mqObject.getRefundVoucherId());
            if (StrUtil.isBlank(violateId)) {
                log.info("[违约金-监听订单退款状态变更] 暂无待取消的违约单 :have no data to be canceled, messageId = {},message = {}", messageId, message);
                return ConsumeResult.SUCCESS;
            }

            violateApplyManager.cancelViolate(violateId);
            return ConsumeResult.SUCCESS;
        } catch (Exception e) {
            log.warn("[违约金-监听订单退款状态变更] error : messageId = {},message = {}", messageId, message, e);
            return ConsumeResult.FAILURE;
        }
    }

    private String getWaitCancelViolate(String refundVoucherId) {
        RefundVoucher refundVoucher = refundVoucherRepository.getByRefundId(refundVoucherId);

        if (ObjectUtil.isEmpty(refundVoucher)
                || !RefundWholeEnum.WHOLE.getValue().equals(refundVoucher.getWholeRefund())
                || !Set.of(RefundStatusEnum.FAIL.getValue(), RefundStatusEnum.CLOSE.getValue()).contains(refundVoucher.getRefundStatus())) {
            log.info("[违约金-监听订单退款状态变更] error: empty refundVoucher or illegal data,refundVoucherId:{}", refundVoucherId);
            return null;
        }

        ViolateOrder violateOrder = violateOrderDomainService.violateWaitingByRefundId(refundVoucher.getRefundVoucherId());
        if (ObjectUtil.isEmpty(violateOrder)) {
            log.info("[违约金-监听订单退款状态变更] error: empty violateOrder,refundVoucherId:{}", refundVoucher.getRefundVoucherId());
            return null;
        }
        return violateOrder.getViolateId();
    }
}
