package com.pxb7.mall.trade.ass.adapter.web;

import java.util.List;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.pxb7.mall.response.PxResponse;
import com.pxb7.mall.trade.ass.adapter.util.UserUtil;
import com.pxb7.mall.trade.ass.app.AssRetrieveWoAppService;
import com.pxb7.mall.trade.ass.app.AssScheduleAppService;
import com.pxb7.mall.trade.ass.app.dto.reqeust.AssRetrieveWoReqDTO;
import com.pxb7.mall.trade.ass.app.dto.response.AssDealUserList;

import jakarta.annotation.Resource;

/**
 * 售后找回工单-web
 *
 * <AUTHOR>
 **/
@RestController
@RequestMapping("/web/retrieve")
public class AssRetrieveWoController {
    @Resource
    private AssScheduleAppService assScheduleAppService;
    @Resource
    private AssRetrieveWoAppService assRetrieveWoAppService;

    /**
     * im端 分配处理人员
     */
    @PostMapping("/setDealUser")
    public PxResponse<Boolean> setDealUser(@RequestBody @Validated AssRetrieveWoReqDTO.SetDealUserReqDTO reqDTO) {
        return PxResponse.ok(assScheduleAppService.setDealUser(reqDTO, UserUtil.getUserInfo().getUserId()));
    }

    /**
     * im端 关闭工单
     */
    @GetMapping("/close")
    public PxResponse<Boolean> closeRetrieveWo(@RequestParam("roomId") String roomId) {
        return PxResponse.ok(assScheduleAppService.closeRetrieveWo(roomId, UserUtil.getUserInfo().getUserId()));
    }

    /**
     * im端 找回处理人员列表
     */
    @GetMapping("/dealUserList")
    public PxResponse<List<AssDealUserList>> dealUserList() {
        return PxResponse.ok(assRetrieveWoAppService.dealUserList());
    }

}
