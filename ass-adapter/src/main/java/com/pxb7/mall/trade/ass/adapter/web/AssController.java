package com.pxb7.mall.trade.ass.adapter.web;

import com.pxb7.mall.trade.ass.app.AssRejectReasonConfigAppService;
import com.pxb7.mall.trade.ass.app.AssRetrieveAssistanceConfigAppService;
import com.pxb7.mall.trade.ass.app.dto.reqeust.AssRetrieveAssistanceConfigReqDTO;
import com.pxb7.mall.trade.ass.app.dto.response.AssRejectReasonConfigRespDTO;
import com.pxb7.mall.trade.ass.app.dto.response.AssRetrieveAssistanceConfigRespDTO;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.pxb7.mall.response.PxResponse;
import com.pxb7.mall.trade.ass.adapter.util.UserUtil;
import com.pxb7.mall.trade.ass.app.AssScheduleAppService;

import jakarta.annotation.Resource;

import java.util.List;

/**
 * 售后工单(im)
 *
 * <AUTHOR>
 **/
@RestController
@RequestMapping("/web/ass/workOrder")
public class AssController {
    @Value("${work_order_url}")
    private String url;
    @Resource
    private AssScheduleAppService assScheduleAppService;
    @Resource
    private AssRetrieveAssistanceConfigAppService retrieveAssistanceConfigAppService;
    @Resource
    private AssRejectReasonConfigAppService rejectReasonConfigAppService;

    /**
     * im端 创建工单
     */
    @GetMapping("/create")
    public PxResponse<String> createWorkOrder(@RequestParam("roomId") String roomId, @RequestParam(value = "assistance") Integer assistance,@RequestParam(value = "assistanceUserDesc",required = false) String assistanceUserDesc) {
        String userId = UserUtil.getUserInfo().getUserId();
        return PxResponse.ok(assScheduleAppService.createWorkOrder(roomId, userId, assistance, assistanceUserDesc));
    }


    /**
     * 协助类型列表查询
     * @return 返回协助类型列表
     */
    @GetMapping("/getRetrieveAssistanceConfigList")
    public PxResponse<List<AssRetrieveAssistanceConfigRespDTO.DetailDTO>> getRetrieveAssistanceConfigList() {
        return PxResponse.ok(retrieveAssistanceConfigAppService.list(new AssRetrieveAssistanceConfigReqDTO.SearchDTO()));
    }





    /**
     * im端 工单详情 获取售后后台地址
     */
    @GetMapping("/address")
    public PxResponse<String> getAddress(@RequestParam(value = "roomId", defaultValue = "") String roomId) {
        return PxResponse.ok(url + "?orderInfo=" + assScheduleAppService.getAddressUrl(roomId));
    }


    /**
     * im端售后拒绝原因配置列表查询
     * @param assType  售后类型1找回2纠纷
     * @return 返回拒绝原因列表信息
     */
    @GetMapping("/rejectList")
    public PxResponse<List<AssRejectReasonConfigRespDTO.DetailDTO>> getListByType(@RequestParam("assType") Integer assType) {
        return PxResponse.ok(rejectReasonConfigAppService.getListByType(assType));
    }
}
