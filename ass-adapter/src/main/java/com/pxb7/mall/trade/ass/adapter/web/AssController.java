package com.pxb7.mall.trade.ass.adapter.web;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.pxb7.mall.response.PxResponse;
import com.pxb7.mall.trade.ass.adapter.util.UserUtil;
import com.pxb7.mall.trade.ass.app.AssScheduleAppService;

import jakarta.annotation.Resource;

/**
 * 售后工单
 *
 * <AUTHOR>
 **/
@RestController
@RequestMapping("/web/ass/workOrder")
public class AssController {
    @Value("${work_order_url}")
    private String url;
    @Resource
    private AssScheduleAppService assScheduleAppService;

    /**
     * im端 创建工单
     */
    @GetMapping("/create")
    public PxResponse<String> createWorkOrder(@RequestParam("roomId") String roomId, @RequestParam(value = "assistance") Integer assistance) {
        String userId = UserUtil.getUserInfo().getUserId();
        return PxResponse.ok(assScheduleAppService.createWorkOrder(roomId, userId, assistance));
    }

    /**
     * im端 工单详情 获取售后后台地址
     */
    @GetMapping("/address")
    public PxResponse<String> getAddress(@RequestParam(value = "roomId", defaultValue = "") String roomId) {
        return PxResponse.ok(url + "?orderInfo=" + assScheduleAppService.getAddressUrl(roomId));
    }
}
