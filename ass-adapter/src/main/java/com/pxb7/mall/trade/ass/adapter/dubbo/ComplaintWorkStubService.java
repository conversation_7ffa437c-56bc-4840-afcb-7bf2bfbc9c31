package com.pxb7.mall.trade.ass.adapter.dubbo;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.pxb7.mall.trade.ass.app.ComplaintWorkOrderAppService;
import com.pxb7.mall.trade.ass.app.dto.reqeust.complaint.req.ComplaintWorkOrderFinishReqDTO;
import com.pxb7.mall.trade.ass.app.dto.reqeust.complaint.req.ComplaintWorkOrderReqDTO;
import com.pxb7.mall.trade.ass.app.dto.reqeust.complaint.req.ComplaintWorkOrderTransferReqDTO;
import com.pxb7.mall.trade.ass.app.dto.reqeust.complaint.resp.ComplaintDepartmentConfigRespDTO;
import com.pxb7.mall.trade.ass.app.dto.reqeust.complaint.resp.ComplaintWorkLogRespDTO;
import com.pxb7.mall.trade.ass.app.dto.reqeust.complaint.resp.ComplaintWorkOrderDetailRespDTO;
import com.pxb7.mall.trade.ass.app.mapping.ComplaintWorkOrderAppMapping;
import com.pxb7.mall.trade.ass.client.api.ComplaintWorkServiceI;
import com.pxb7.mall.trade.ass.client.dto.request.CreateWorkOrderReqDTO;
import com.pxb7.mall.trade.ass.client.dto.request.FinishWorkOrderReqDTO;
import com.pxb7.mall.trade.ass.client.dto.request.TransferWorkOrderReqDTO;
import com.pxb7.mall.trade.ass.client.dto.response.DepartmentConfigRespDTO;
import com.pxb7.mall.trade.ass.client.dto.response.WorkOrderDetailRespDTO;
import com.pxb7.mall.trade.ass.client.dto.response.WorkOrderLogRespDTO;
import jakarta.annotation.Resource;
import org.apache.dubbo.config.annotation.DubboService;

import java.util.List;

/**
 * Copyright (C), 2002-2024, 螃蟹游戏服务网
 *
 * @fileName: ComplaintWorkStubService.java
 * @description: 客诉工单接口
 * @author: guominqiang
 * @email: <EMAIL>
 * @date: 2024/9/23 16:23
 * @history: //修改记录
 * <author> <time> <version> <desc>
 * 修改人姓名 修改时间 版本号 描述
 */
@DubboService
public class ComplaintWorkStubService implements ComplaintWorkServiceI {
    @Resource
    private ComplaintWorkOrderAppService complaintWorkOrderAppService;

    @Override
    public SingleResponse<Boolean> createWork(CreateWorkOrderReqDTO createWorkOrderReqDTO) {
        ComplaintWorkOrderReqDTO complaintWorkOrderReqDTO = ComplaintWorkOrderAppMapping.INSTANCE.toComplaintWorkOrderReqDTO(createWorkOrderReqDTO);
        return SingleResponse.of(complaintWorkOrderAppService.create(complaintWorkOrderReqDTO, createWorkOrderReqDTO.getProcessorId()));
    }

    @Override
    public SingleResponse<Boolean> finishWork(FinishWorkOrderReqDTO finishWorkOrderReqDTO) {
        ComplaintWorkOrderFinishReqDTO complaintWorkOrderFinishReqDTO = ComplaintWorkOrderAppMapping.INSTANCE.toComplaintWorkOrderFinishReqDTO(finishWorkOrderReqDTO);
        return SingleResponse.of(complaintWorkOrderAppService.finish(complaintWorkOrderFinishReqDTO, finishWorkOrderReqDTO.getProcessorId()));
    }

    @Override
    public SingleResponse<Boolean> transferWork(TransferWorkOrderReqDTO transferWorkOrderReqDTO) {
        ComplaintWorkOrderTransferReqDTO complaintWorkOrderTransferReqDTO = ComplaintWorkOrderAppMapping.INSTANCE.toComplaintWorkOrderTransferReqDTO(transferWorkOrderReqDTO);
        return SingleResponse.of(complaintWorkOrderAppService.transfer(complaintWorkOrderTransferReqDTO, transferWorkOrderReqDTO.getProcessorId()));
    }

    @Override
    public SingleResponse<WorkOrderDetailRespDTO> viewDetail(String complaintWorkId) {
        ComplaintWorkOrderDetailRespDTO complaintWorkOrderDetailRespDTO = complaintWorkOrderAppService.detail(complaintWorkId);
        WorkOrderDetailRespDTO workOrderDetailRespDTO = ComplaintWorkOrderAppMapping.INSTANCE.toWorkOrderDetailRespDTO(complaintWorkOrderDetailRespDTO);
        return SingleResponse.of(workOrderDetailRespDTO);
    }

    @Override
    public MultiResponse<WorkOrderLogRespDTO> viewLog(String complaintWorkId) {
        List<ComplaintWorkLogRespDTO> complaintWorkLogRespDTOList = complaintWorkOrderAppService.viewLog(complaintWorkId);
        List<WorkOrderLogRespDTO> workOrderLogRespDTOS = ComplaintWorkOrderAppMapping.INSTANCE.toWorkOrderLogRespDTOList(complaintWorkLogRespDTOList);
        return MultiResponse.of(workOrderLogRespDTOS);
    }

    @Override
    public MultiResponse<DepartmentConfigRespDTO> departmentList() {
        List<ComplaintDepartmentConfigRespDTO> complaintDepartmentConfigRespDTOList = complaintWorkOrderAppService.departmentList();
        List<DepartmentConfigRespDTO> departmentConfigRespDTOS = ComplaintWorkOrderAppMapping.INSTANCE.toDepartmentConfigRespDTOList(complaintDepartmentConfigRespDTOList);
        return MultiResponse.of(departmentConfigRespDTOS);
    }
}
