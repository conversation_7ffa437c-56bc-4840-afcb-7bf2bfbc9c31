package com.pxb7.mall.trade.ass.adapter.consumer.violate;

import com.pxb7.mall.components.rocketmq.RocketMQListenerExt;
import com.pxb7.mall.trade.ass.domain.violate.manager.ViolatePayRecordManager;
import com.pxb7.mall.trade.ass.domain.violate.mq.message.ViolatePayRecordMessage;
import com.pxb7.mall.trade.ass.domain.violate.request.ViolatePayRecordReqBO;
import com.pxb7.mall.trade.ass.infra.constant.RMQConstant;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.annotation.RocketMQMessageListener;
import org.apache.rocketmq.client.apis.consumer.ConsumeResult;
import org.apache.rocketmq.client.apis.message.MessageId;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.springframework.stereotype.Service;

/**
 * 违约扣款
 */
@Service
@Slf4j
@RocketMQMessageListener(consumerGroup = RMQConstant.VIOLATE_DEDUCTION_GROUP, topic = RMQConstant.VIOLATE_PAY_RECORD_TOPIC, tag = RMQConstant.VIOLATE_DEDUCTION_TAG)
public class ViolateDeductionMessageConsumer implements RocketMQListenerExt<ViolatePayRecordMessage> {
    @Resource
    private ViolatePayRecordManager violatePayRecordManager;

    @Override
    public ConsumeResult consume(MessageView messageView, ViolatePayRecordMessage message) {
        MessageId messageId = messageView.getMessageId();
        log.info("[违约金-扣款]，messageId = {},message = {}", messageId, message);
        try {
            ViolatePayRecordReqBO violatePayRecordReqBO = new ViolatePayRecordReqBO()
                    .setViolateId(message.getViolateId())
                    .setPayRecordId(message.getViolateReceiptPayRecordId());
            violatePayRecordManager.violateDeduction(violatePayRecordReqBO);
            return ConsumeResult.SUCCESS;
        } catch (Exception e) {
            log.warn("[违约金-扣款] error : messageId = {},message = {}", messageId, message, e);
            return ConsumeResult.FAILURE;
        }
    }
}
