package com.pxb7.mall.trade.ass.adapter.consumer.violate;

import com.pxb7.mall.components.rocketmq.RocketMQListenerExt;
import com.pxb7.mall.pay.client.constants.PayMessageTopics;
import com.pxb7.mall.pay.client.enums.AccountTradeStatusEnum;
import com.pxb7.mall.pay.client.enums.AccountTradeTypeEnum;
import com.pxb7.mall.trade.ass.domain.violate.manager.ViolatePayRecordManager;
import com.pxb7.mall.trade.ass.domain.violate.request.ViolateTransferCallbackReqBO;
import com.pxb7.mall.trade.ass.infra.config.AssertIdeaCondition;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.annotation.RocketMQMessageListener;
import org.apache.rocketmq.client.apis.consumer.ConsumeResult;
import org.apache.rocketmq.client.apis.message.MessageId;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.springframework.context.annotation.Conditional;
import org.springframework.stereotype.Service;
import com.pxb7.mall.pay.client.dto.response.PayTradeCallbackMessage;

import static com.pxb7.mall.trade.ass.infra.constant.RMQConstant.VIOLATE_ACCOUNT_GROUP;

/**
 * 违约打款
 */
@Service
@Slf4j
@Conditional(AssertIdeaCondition.class)
@RocketMQMessageListener(consumerGroup = VIOLATE_ACCOUNT_GROUP, topic = PayMessageTopics.AccountTradeCallBack.TOPIC, tag = PayMessageTopics.AccountTradeCallBack.TAG)
public class PayAccountTradeMessageConsumer implements RocketMQListenerExt<PayTradeCallbackMessage> {
    @Resource
    private ViolatePayRecordManager violatePayRecordManager;

    @Override
    public ConsumeResult consume(MessageView messageView, PayTradeCallbackMessage message) {
        MessageId messageId = messageView.getMessageId();
        log.info("[平台给守约方账号打款后通知结果]，messageId = {},message = {}", messageId, message);
        try {
            AccountTradeStatusEnum tradeStatus = message.getTradeStatus();
            if (AccountTradeStatusEnum.FAIL.equals(tradeStatus)) {
                // todo 打款失败如何处理？？不处理
                log.error("[平台给守约方账号打款后通知结果]打款失败 : messageId = {},message = {}", messageId, message);
                return ConsumeResult.FAILURE;
            }
            if (!AccountTradeTypeEnum.WYJ_TRANSFER.equals(message.getTradeTypeEnum())) {
                // todo 这里后续需要和钱包端调整,不是我的msg不应该过来
                log.info("[平台给守约方账号打款后通知结果]非违约金 : messageId = {},message = {}", messageId, message);
                return ConsumeResult.SUCCESS;
            }

            // 打款成功
            ViolateTransferCallbackReqBO bo = new ViolateTransferCallbackReqBO()
                    .setViolatePayRecordId(message.getVoucherId())
                    .setOutTradeId(message.getTradeId());
            violatePayRecordManager.transferCallback(bo);
            return ConsumeResult.SUCCESS;
        } catch (Exception e) {
            log.warn("[平台给守约方账号打款后通知结果] error : messageId = {},message = {}", messageId, message, e);
            return ConsumeResult.FAILURE;
        }
    }
}
