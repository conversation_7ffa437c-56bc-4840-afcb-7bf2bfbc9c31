package com.pxb7.mall.trade.ass.adapter.web;

import java.util.List;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.pxb7.mall.response.PxResponse;
import com.pxb7.mall.trade.ass.app.AssQuestionClassifyAppService;
import com.pxb7.mall.trade.ass.app.dto.reqeust.ListAssQuestionClassifyRespDTO;

import jakarta.annotation.Resource;

/**
 * 售后问题归类配置
 *
 * <AUTHOR>
 **/
@RestController
@RequestMapping("/web/question/classify")
public class AssQuestionClassifyController {
    @Resource
    private AssQuestionClassifyAppService assQuestionClassifyAppService;

    /**
     * im端 获取售后问题归类配置列表
     */
    @GetMapping("/list")
    public PxResponse<List<ListAssQuestionClassifyRespDTO>> list(@RequestParam("assType") Integer assType) {
        return PxResponse.ok(assQuestionClassifyAppService.list(assType));
    }
}
