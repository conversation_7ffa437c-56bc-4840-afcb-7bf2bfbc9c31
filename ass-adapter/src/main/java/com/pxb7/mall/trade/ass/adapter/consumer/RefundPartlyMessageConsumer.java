package com.pxb7.mall.trade.ass.adapter.consumer;

import com.pxb7.mall.components.rocketmq.RocketMQListenerExt;
import com.pxb7.mall.trade.ass.domain.refund.PayRefundApiDomainService;
import com.pxb7.mall.trade.ass.infra.config.AssertIdeaCondition;
import com.pxb7.mall.trade.ass.infra.constant.RMQConstant;
import com.pxb7.mall.trade.ass.infra.model.mq.PartRefundMessage;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.annotation.RocketMQMessageListener;
import org.apache.rocketmq.client.apis.consumer.ConsumeResult;
import org.apache.rocketmq.client.apis.message.MessageId;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.springframework.context.annotation.Conditional;
import org.springframework.stereotype.Service;

/**
 * 部分退款 MQ
 */
@Service
@Slf4j
@Conditional(AssertIdeaCondition.class)
@RocketMQMessageListener(consumerGroup = RMQConstant.ASS_REFUND_PARTLY_GROUP, topic = RMQConstant.ASS_REFUND_CENTER_TOPIC, tag = RMQConstant.ASS_REFUND_PARTLY_TAG)
public class RefundPartlyMessageConsumer implements RocketMQListenerExt<PartRefundMessage> {
    @Resource
    private PayRefundApiDomainService payRefundApiDomainService;

    @Override
    public ConsumeResult consume(MessageView messageView, PartRefundMessage message) {
        MessageId messageId = messageView.getMessageId();
        log.info("[线上部分退款] 消息处理 messageId = {},message = {}", messageId, message);
        try {
            payRefundApiDomainService.refundPartly(message);
            return ConsumeResult.SUCCESS;
        } catch (Exception e) {
            log.error("error in consumed from ass_refund_center_topic: messageId = {},message = {}", messageId, message, e);
            return ConsumeResult.FAILURE;
        }
    }
}
