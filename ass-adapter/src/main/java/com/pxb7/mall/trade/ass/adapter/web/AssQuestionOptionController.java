package com.pxb7.mall.trade.ass.adapter.web;

import java.util.List;
import java.util.Objects;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.pxb7.mall.response.PxResponse;
import com.pxb7.mall.trade.ass.adapter.util.UserUtil;
import com.pxb7.mall.trade.ass.app.AssQuestionOptionAppService;
import com.pxb7.mall.trade.ass.app.AssScheduleAppService;
import com.pxb7.mall.trade.ass.client.enums.AssScheduleNode;
import com.pxb7.mall.trade.ass.domain.AssScheduleLogDomainService;
import com.pxb7.mall.trade.ass.infra.enums.ErrorCode;
import com.pxb7.mall.trade.ass.infra.exception.Ensure;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.AssSchedule;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.AssScheduleLog;
import com.pxb7.mall.trade.ass.infra.util.NumberUtil;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

/**
 * 售后问答问题配置-web
 *
 * <AUTHOR>
 **/
@Slf4j
@RestController
@RequestMapping("/web/question/")
public class AssQuestionOptionController {
    @Resource
    private AssScheduleAppService assScheduleAppService;
    @Resource
    private AssScheduleLogDomainService assScheduleLogDomainService;
    @Resource
    private AssQuestionOptionAppService assQuestionOptionAppService;

    /**
     * im端 根据游戏id获取问题
     */
    @GetMapping("getQuestionByGame")
    public PxResponse<Boolean> getQuestionByGame(@RequestParam("roomId") String roomId) {
        String userId = UserUtil.getUserInfo().getUserId();
        AssSchedule assSchedule = assScheduleAppService.getByRoomId(roomId);
        Ensure.that(assSchedule).isNotNull(ErrorCode.ROOM_NOT_EXISTS);
        boolean flag = assQuestionOptionAppService.sendQuestionByGame(assSchedule, userId);
        List<AssScheduleLog> logList = assScheduleLogDomainService.findListByScheduleId(assSchedule.getScheduleId());
        long afterSaleCount =
            logList.stream().filter(o -> Objects.equals(o.getNodeId(), AssScheduleNode.INITIATE_AFTER_SALE.name())
                && Objects.equals(o.getNodeDesc(), "发起售后申请：找回")).count();
        long sendCount =
            logList.stream().filter(o -> Objects.equals(o.getNodeId(), AssScheduleNode.SEND_QUESTION.name())).count();
        long l = afterSaleCount - sendCount;
        if (NumberUtil.between(l, 1, 2)) {
            assScheduleAppService.sendQuestion(roomId, userId);
        }
        return PxResponse.ok(flag);
    }

}
