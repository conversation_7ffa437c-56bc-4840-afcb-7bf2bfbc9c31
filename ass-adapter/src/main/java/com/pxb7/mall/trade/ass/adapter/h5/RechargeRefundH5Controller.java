package com.pxb7.mall.trade.ass.adapter.h5;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson2.JSONObject;
import com.pxb7.mall.response.PxResponse;
import com.pxb7.mall.trade.ass.adapter.util.UserUtil;
import com.pxb7.mall.trade.ass.app.RechargeRefundAppService;
import com.pxb7.mall.trade.ass.app.dto.reqeust.ApplyRechargeRefundReqDTO;
import com.pxb7.mall.trade.ass.app.dto.reqeust.RechargeRefundAuditReqDTO;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

/**
 * 充值退款-h5
 */
@Slf4j
@RestController
@RequestMapping("/h5/user/refund/recharge")
public class RechargeRefundH5Controller {

    @Resource
    private RechargeRefundAppService rechargeRefundAppService;

    /**
     * 用户请求充值退款
     */
    @PostMapping(value = "/apply")
    public PxResponse<Boolean> applyRefund(@RequestBody @Validated ApplyRechargeRefundReqDTO applyRefundReqDTO) {
        log.info("用户请求充值退款:{}", JSONObject.toJSONString(applyRefundReqDTO));
        return rechargeRefundAppService.applyRechargeRefund(applyRefundReqDTO, UserUtil.getUserInfo());
    }

    /**
     * 商家拒绝充值退款
     */
    @PostMapping(value = "/reject")
    public PxResponse<Boolean> rejectRefund(@RequestBody @Validated RechargeRefundAuditReqDTO auditReqDTO) {
        log.info("supportRefundReject:{}", JSONObject.toJSONString(auditReqDTO));
        return rechargeRefundAppService.rejectRefund(auditReqDTO, UserUtil.getUserInfo());
    }

    /**
     * 商家通过充值退款
     */
    @PostMapping(value = "/pass")
    public PxResponse<Boolean> passRefund(@RequestBody @Validated RechargeRefundAuditReqDTO auditReqDTO) {
        log.info("serviceSubmitRefund:{}", JSONObject.toJSONString(auditReqDTO));
        return rechargeRefundAppService.passRefund(auditReqDTO, UserUtil.getUserInfo());
    }

}
