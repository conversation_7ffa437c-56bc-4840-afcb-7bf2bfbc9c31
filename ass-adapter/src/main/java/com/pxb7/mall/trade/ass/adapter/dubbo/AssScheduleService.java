package com.pxb7.mall.trade.ass.adapter.dubbo;

import java.util.List;

import org.apache.dubbo.config.annotation.DubboService;

import com.alibaba.cola.dto.MultiResponse;
import com.pxb7.mall.trade.ass.app.AssScheduleAppService;
import com.pxb7.mall.trade.ass.client.api.AssScheduleServiceI;
import com.pxb7.mall.trade.ass.client.dto.request.AssStatusReqDTO;
import com.pxb7.mall.trade.ass.client.dto.response.AssStatusRespDTO;

import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/8/22
 */
@DubboService
@AllArgsConstructor
public class AssScheduleService implements AssScheduleServiceI {

    private final AssScheduleAppService assScheduleAppService;

    @Override
    public MultiResponse<AssStatusRespDTO> checkAss(List<AssStatusReqDTO> list) {
        return MultiResponse.of(assScheduleAppService.checkAss(list));
    }
}
