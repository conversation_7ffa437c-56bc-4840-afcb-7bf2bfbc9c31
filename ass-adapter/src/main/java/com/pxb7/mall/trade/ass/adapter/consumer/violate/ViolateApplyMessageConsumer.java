package com.pxb7.mall.trade.ass.adapter.consumer.violate;

import com.pxb7.mall.components.rocketmq.RocketMQListenerExt;
import com.pxb7.mall.trade.ass.domain.violate.manager.ViolateApplyManager;
import com.pxb7.mall.trade.ass.domain.violate.mq.message.ViolateApplyMessage;
import com.pxb7.mall.trade.ass.domain.violate.request.ViolateApplyReqBO;
import com.pxb7.mall.trade.ass.infra.constant.RMQConstant;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.annotation.RocketMQMessageListener;
import org.apache.rocketmq.client.apis.consumer.ConsumeResult;
import org.apache.rocketmq.client.apis.message.MessageId;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.springframework.stereotype.Service;

/**
 * 发起退款-创建待处理的违约单
 */
@Service
@Slf4j
@RocketMQMessageListener(consumerGroup = RMQConstant.VIOLATE_CREATE_GROUP, topic = RMQConstant.VIOLATE_TOPIC, tag = RMQConstant.VIOLATE_CREATE_TAG)
public class ViolateApplyMessageConsumer implements RocketMQListenerExt<ViolateApplyMessage> {
    @Resource
    private ViolateApplyManager violateApplyManager;

    @Override
    public ConsumeResult consume(MessageView messageView, ViolateApplyMessage message) {
        MessageId messageId = messageView.getMessageId();
        log.info("[违约金-创建违约单]，messageId = {},message = {}", messageId, message);
        try {
            ViolateApplyReqBO bo = new ViolateApplyReqBO()
                    .setRefundVoucherId(message.getRefundVoucherId())
                    .setOrderItemId(message.getOrderItemId())
                    .setViolateUserType(message.getViolateUserType())
                    .setViolateUserId(message.getViolateUserId())
                    .setViolateAmount(message.getViolateAmount())
                    .setPromiseAmount(message.getPromiseAmount())
                    .setCreateUserId(message.getCreateUserId())
                    .setCreateUserName(message.getCreateUserName());
            violateApplyManager.apply(bo);
            return ConsumeResult.SUCCESS;
        } catch (Exception e) {
            log.warn("[违约金-创建违约单] error : messageId = {},message = {}", messageId, message, e);
            return ConsumeResult.FAILURE;
        }
    }
}
