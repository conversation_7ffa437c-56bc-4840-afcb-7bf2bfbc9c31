package com.pxb7.mall.trade.ass.adapter.web;

import com.alibaba.cola.exception.BizException;
import com.pxb7.mall.response.PxResponse;
import com.pxb7.mall.trade.ass.adapter.util.UserUtil;
import com.pxb7.mall.trade.ass.app.*;
import com.pxb7.mall.trade.ass.app.common.UserIdUtils;
import com.pxb7.mall.trade.ass.app.dto.reqeust.ApplyAfcReqDTO;
import com.pxb7.mall.trade.ass.app.dto.reqeust.AssScheduleReqDTO;
import com.pxb7.mall.trade.ass.app.dto.reqeust.schedule.ReqCreateAssScheduleDTO;
import com.pxb7.mall.trade.ass.app.dto.response.AfcQuestionConfRespDTO;
import com.pxb7.mall.trade.ass.app.dto.response.AssRejectReasonConfigRespDTO;
import com.pxb7.mall.trade.ass.app.dto.response.AssScheduleRespDTO;
import com.pxb7.mall.trade.ass.app.dto.response.assSchedule.AssScheduleRespMobileDTO;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.pxb7.mall.trade.ass.client.enums.AssScheduleNode.*;


/**
 * 售后拒绝原因配置-im
 */
@RestController
@RequestMapping("/web/rejectConfig")
public class AssRejectReasonConfigWebController {
    @Resource
    private AssRejectReasonConfigAppService rejectReasonConfigAppService;


    /**
     * im端售后拒绝原因配置列表查询
     * @param assType  售后类型1找回2纠纷
     * @return 返回拒绝原因列表信息
     */
    @GetMapping("/list")
    public PxResponse<List<AssRejectReasonConfigRespDTO.DetailDTO>> getListByType(@RequestParam("assType") Integer assType) {
        return PxResponse.ok(rejectReasonConfigAppService.getListByType(assType));
    }

}
