package com.pxb7.mall.trade.ass.adapter.consumer.violate;

import cn.hutool.core.util.StrUtil;
import com.pxb7.mall.components.rocketmq.RocketMQListenerExt;
import com.pxb7.mall.trade.ass.domain.violate.manager.ViolateDealManager;
import com.pxb7.mall.trade.ass.domain.violate.mq.message.ViolatePayRecordMessage;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.annotation.RocketMQMessageListener;
import org.apache.rocketmq.client.apis.consumer.ConsumeResult;
import org.apache.rocketmq.client.apis.message.MessageId;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.springframework.stereotype.Service;

import static com.pxb7.mall.trade.ass.infra.constant.RMQConstant.*;

/**
 * 违约金收款超时中断
 */
@Service
@Slf4j
@RocketMQMessageListener(consumerGroup = VIOLATE_RECEIPT_STOP_GROUP, topic = VIOLATE_RECEIPT_DELAY_TOPIC, tag = VIOLATE_RECEIPT_STOP_TAG)
public class ViolateDelayStopMessageConsumer implements RocketMQListenerExt<ViolatePayRecordMessage> {
    @Resource
    private ViolateDealManager violateDealManager;

    @Override
    public ConsumeResult consume(MessageView messageView, ViolatePayRecordMessage message) {
        MessageId messageId = messageView.getMessageId();
        log.info("[违约金收款超时中断]，messageId = {},message = {}", messageId, message);
        try {
            if (StrUtil.isBlank(message.getViolateId())) {
                log.warn("[违约金收款超时中断] 参数缺失，messageId = {},message = {}", messageId, message);
                return ConsumeResult.SUCCESS;
            }

            violateDealManager.receiptStop(message.getViolateId());
            return ConsumeResult.SUCCESS;
        } catch (Exception e) {
            log.warn("[违约金收款超时中断] error : messageId = {},message = {}", messageId, message, e);
            return ConsumeResult.FAILURE;
        }
    }
}
