package com.pxb7.mall.trade.ass.adapter.dubbo;

import com.alibaba.cola.dto.SingleResponse;
import com.pxb7.mall.trade.ass.app.AssDisputeWoAppService;
import com.pxb7.mall.trade.ass.app.AssRetrieveWoAppService;
import com.pxb7.mall.trade.ass.app.ComplaintWorkOrderAppService;
import com.pxb7.mall.trade.ass.client.api.AfcWorkOrderServiceI;
import com.pxb7.mall.trade.ass.client.dto.response.afc.ComplaintWORespDTO;
import com.pxb7.mall.trade.ass.client.dto.response.afc.DisputeWORespDTO;
import com.pxb7.mall.trade.ass.client.dto.response.afc.RetrieveWORespDTO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;

/**
 * Copyright (C), 2002-2025, 螃蟹游戏服务网
 *
 * @fileName: AfcWorkOrderStubService.java
 * @description: 售后工单dubbo接口
 * @author: guominqiang
 * @email: <EMAIL>
 * @date: 2025/4/22 11:26
 * @history: //修改记录
 * <author> <time> <version> <desc>
 * 修改人姓名 修改时间 版本号 描述
 */
@DubboService
@Slf4j
public class AfcWorkOrderStubService implements AfcWorkOrderServiceI {
    @Resource
    private AssRetrieveWoAppService assRetrieveWoAppService;
    @Resource
    private AssDisputeWoAppService assDisputeWoAppService;
    @Resource
    private ComplaintWorkOrderAppService complaintWorkOrderAppService;
    @Override
    public SingleResponse<RetrieveWORespDTO> searchRetrieveWOById(String workOrderId) {
        return SingleResponse.of(assRetrieveWoAppService.searchRetrieveWOById(workOrderId));
    }

    @Override
    public SingleResponse<DisputeWORespDTO> searchDisputeWOById(String workOrderId) {
        return SingleResponse.of(assDisputeWoAppService.searchDisputeWOById(workOrderId));
    }

    @Override
    public SingleResponse<ComplaintWORespDTO> searchComplaintWOById(String workOrderId) {
        return SingleResponse.of(complaintWorkOrderAppService.searchComplaintWOById(workOrderId));
    }
}
