package com.pxb7.mall.trade.ass.adapter.web;

import static com.pxb7.mall.trade.ass.client.enums.AssScheduleNode.*;

import java.util.List;

import com.pxb7.mall.trade.ass.app.*;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.alibaba.cola.exception.BizException;
import com.pxb7.mall.response.PxResponse;
import com.pxb7.mall.trade.ass.adapter.util.UserUtil;
import com.pxb7.mall.trade.ass.app.common.UserIdUtils;
import com.pxb7.mall.trade.ass.app.dto.reqeust.ApplyAfcReqDTO;
import com.pxb7.mall.trade.ass.app.dto.reqeust.AssScheduleReqDTO;
import com.pxb7.mall.trade.ass.app.dto.reqeust.schedule.ReqCreateAssScheduleDTO;
import com.pxb7.mall.trade.ass.app.dto.response.AfcQuestionConfRespDTO;
import com.pxb7.mall.trade.ass.app.dto.response.AssScheduleRespDTO;
import com.pxb7.mall.trade.ass.app.dto.response.assSchedule.AssScheduleRespMobileDTO;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;

/**
 * 售后流程进度-web
 *
 * <AUTHOR>
 **/
@Validated
@RestController
@RequestMapping("/web/schedule")
public class AssScheduleWebController {
    @Resource
    private AfcRecordAppService afcRecordAppService;
    @Resource
    private AssScheduleAppService assScheduleAppService;
    @Resource
    private AssScheduleLogAppService assScheduleLogAppService;
    @Resource
    private AfcQuestionConfigAppService afcQuestionConfigAppService;

    /**
     * c端用户发起售后(散户及号商)
     */
    @PostMapping("/create")
    @Deprecated
    public PxResponse<String> createAssSchedule(@Validated @RequestBody ReqCreateAssScheduleDTO assScheduleReqDTO) {
        String userId = UserUtil.getUserInfo().getUserId();
        return PxResponse.ok(assScheduleAppService.mobileCreateAssSchedule(assScheduleReqDTO, userId));
    }

    /**
     * 获取售后问题配置列表
     *
     * @return {@link List<AfcQuestionConfRespDTO>}
     */
    @GetMapping("/getAfcQuestionConfList")
    public PxResponse<List<AfcQuestionConfRespDTO>> getAfcQuestionConfList() {
        String userId = UserIdUtils.getUserId();
        return PxResponse.ok(afcQuestionConfigAppService.getAfcQuestionConfList(userId));
    }

    /**
     * 发起售后-new(散户及号商)
     *
     * @param applyAfcReqDTO 用户申请售后请求参数
     * @return {@link String 房间id}
     */
    @PostMapping("/applyAfc")
    public PxResponse<String> applyAfc(@RequestBody @Valid ApplyAfcReqDTO applyAfcReqDTO) {
        String userId = UserUtil.getUserInfo().getUserId();
        return PxResponse.ok(afcRecordAppService.applyAfc(applyAfcReqDTO, userId));
    }

    /**
     * c端用户获取进度
     */
    @GetMapping("/getLog")
    public PxResponse<AssScheduleRespMobileDTO> record(@RequestParam("orderNo") String orderNo) {
        return PxResponse.ok(assScheduleLogAppService.recordLogList(orderNo));
    }

    /**
     * im端 记录售后流程进度
     */
    @PostMapping("/record")
    public PxResponse<Boolean> record(@RequestBody @Valid AssScheduleReqDTO assScheduleReqDTO) {
        String userId = UserUtil.getUserInfo().getUserId();
        // 校验入参
        boolean isCheck = assScheduleReqDTO.getNode() == INITIATE_AFTER_SALE || assScheduleReqDTO.getNode() == REJECT
            || assScheduleReqDTO.getNode() == TRANSFER_RETRIEVE || assScheduleReqDTO.getNode() == TRANSFER_DISPUTE;
        if (!isCheck) {
            throw new BizException("参数节点错误");
        }
        return PxResponse.ok(assScheduleAppService.record(assScheduleReqDTO, userId));
    }

    /**
     * im端 查询流程记录
     */
    @PostMapping("/find")
    public PxResponse<AssScheduleRespDTO> find(@NotBlank(message = "房间号不能为空") String roomId) {
        return PxResponse.ok(assScheduleAppService.find(roomId));
    }

}
