package com.pxb7.mall.trade.ass.adapter.consumer;

import com.pxb7.mall.components.rocketmq.RocketMQListenerExt;
import com.pxb7.mall.trade.ass.domain.refund.RefundSyncDomainService;
import com.pxb7.mall.trade.ass.domain.refund.message.provider.RefundMessageService;
import com.pxb7.mall.trade.ass.infra.config.AssertIdeaCondition;
import com.pxb7.mall.trade.ass.infra.constant.RMQConstant;
import com.pxb7.mall.trade.ass.infra.model.mq.RefundQueryMessage;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.annotation.RocketMQMessageListener;
import org.apache.rocketmq.client.apis.consumer.ConsumeResult;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.springframework.context.annotation.Conditional;
import org.springframework.stereotype.Service;


/**
 * 退款结果查询 MQ
 */
@Service
@Slf4j
@Conditional(AssertIdeaCondition.class)
@RocketMQMessageListener(consumerGroup = RMQConstant.REFUND_QUERY_DELAY_GROUP, topic = RMQConstant.REFUND_QUERY_DELAY_TOPIC, tag = RMQConstant.REFUND_QUERY_DELAY_TAG)
public class RefundQueryDelayMessageConsumer implements RocketMQListenerExt<RefundQueryMessage> {

    @Resource
    private RefundSyncDomainService refundSyncDomainService;
    @Resource
    private RefundMessageService refundMessageService;

    @Override
    public ConsumeResult consume(MessageView messageView, RefundQueryMessage message) {
        try {
            log.info("[退款结果查询消息] 开始处理 消息内容:{},topic:{},tag:{}", message,
                    messageView.getTopic(), messageView.getTag());
            refundSyncDomainService.handleRefundResult(message);
        } catch (Throwable e) {
            message.setTimes(message.getTimes() + 1);
            refundMessageService.sendRefundQueryMessage(message);
            log.error("[退款结果查询消息] 异常 重新发送MQ消息，message：{}原因:", message, e);
        }
        return ConsumeResult.SUCCESS;
    }
}

