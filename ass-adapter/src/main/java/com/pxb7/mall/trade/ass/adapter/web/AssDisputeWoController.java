package com.pxb7.mall.trade.ass.adapter.web;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.pxb7.mall.response.PxResponse;
import com.pxb7.mall.trade.ass.adapter.util.UserUtil;
import com.pxb7.mall.trade.ass.app.AssDisputeWoAppService;
import com.pxb7.mall.trade.ass.app.dto.reqeust.DealDisputeResultReqDTO;

import jakarta.annotation.Resource;

/**
 * 售后纠纷工单-web
 *
 * <AUTHOR>
 **/
@RestController
@RequestMapping("/web/dispute")
public class AssDisputeWoController {
    @Resource
    private AssDisputeWoAppService assDisputeWoAppService;

    /**
     * im端 完结纠纷工单
     */
    @PostMapping("/complete")
    public PxResponse<Boolean> completeDispute(@RequestBody @Validated DealDisputeResultReqDTO reqDTO) {
        assDisputeWoAppService.completeDispute(reqDTO, UserUtil.getUserInfo().getUserId());
        return PxResponse.ok(Boolean.TRUE);
    }
}
