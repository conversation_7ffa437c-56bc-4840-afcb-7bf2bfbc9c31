package com.pxb7.mall.trade.ass.adapter.dubbo;

import java.util.List;

import org.apache.dubbo.config.annotation.DubboService;

import com.pxb7.mall.trade.ass.client.api.OrderItemAmountServiceI;
import com.pxb7.mall.trade.ass.client.dto.model.order.OrderItemAmountInfo;
import com.pxb7.mall.trade.ass.client.dto.response.order.OrderItemAmountRespDTO;
import com.pxb7.mall.trade.ass.domain.refund.RefundDomainService;
import com.pxb7.mall.trade.ass.domain.refund.utils.RefundAmountCalculator;
import com.pxb7.mall.trade.ass.infra.remote.model.refund.OrderItemReceiptVoucherPO;
import com.pxb7.mall.trade.ass.infra.repository.db.RefundVoucherRepository;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.RefundVoucher;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

@DubboService
@Slf4j
public class OrderItemAmountStubService implements OrderItemAmountServiceI {
    @Resource
    private RefundVoucherRepository refundVoucherRepository;
    @Resource
    private RefundDomainService refundDomainService;

    @Override
    public OrderItemAmountRespDTO getOrderItemAmount(String orderItemId) {
        // 查询成功的退款单
        List<RefundVoucher> successRefundVoucher = refundVoucherRepository.getSuccessRefund(orderItemId);
        // 查询成功收款单
        List<OrderItemReceiptVoucherPO> successReceiptVoucherDetail =
            refundDomainService.getSuccessReceiptVoucherDetail(orderItemId, successRefundVoucher);
        OrderItemAmountInfo successRefundInfo = refundDomainService.getSuccessRefundAmountInfo(successRefundVoucher);
        OrderItemAmountInfo wholeReceiptInfo =
            RefundAmountCalculator.getWholeReceiptAmount(successReceiptVoucherDetail);
        OrderItemAmountInfo wholeRefundAmountInfo =
            RefundAmountCalculator.getWholeRefundAmount(orderItemId, wholeReceiptInfo, successRefundInfo);
        return new OrderItemAmountRespDTO().setCurrentAmount(wholeRefundAmountInfo);
    }
}
