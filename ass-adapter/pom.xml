<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.pxb7.mall.trade</groupId>
        <artifactId>ass</artifactId>
        <version>${revision}</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>ass-adapter</artifactId>
    <packaging>jar</packaging>
    <name>ass-adapter</name>
    <properties>
        <maven.deploy.skip>true</maven.deploy.skip>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.pxb7.mall.components</groupId>
            <artifactId>auth-satoken-c-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pxb7.mall.trade</groupId>
            <artifactId>ass-app</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.rocketmq</groupId>
            <artifactId>rocketmq-v5-client-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pxb7.mall.components</groupId>
            <artifactId>rocketmqext-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.dubbo</groupId>
            <artifactId>dubbo-registry-nacos</artifactId>
        </dependency>

        <dependency>
            <groupId>com.pxb7.mall.pay</groupId>
            <artifactId>pay-client</artifactId>
        </dependency>


    </dependencies>
</project>
