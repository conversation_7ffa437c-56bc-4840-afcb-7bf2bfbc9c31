#!/bin/bash

# Sentry问题排查脚本
# Issue ID: 6185
# 来源: webhooks_plugin

echo "=== Sentry Issue 6185 排查脚本 ==="
echo "开始时间: $(date)"
echo ""

# 1. 检查应用状态
echo "1. 检查应用状态..."
echo "应用端口: 8086"
echo "上下文路径: /api/ass"

# 检查应用是否运行
if curl -s http://localhost:8086/api/ass/actuator/health > /dev/null; then
    echo "✓ 应用运行正常"
    curl -s http://localhost:8086/api/ass/actuator/health | jq .
else
    echo "✗ 应用无法访问"
fi
echo ""

# 2. 检查最近的错误日志
echo "2. 检查最近的错误日志..."
LOG_DIR="/path/to/logs"  # 需要根据实际路径修改

if [ -d "$LOG_DIR" ]; then
    echo "最近10条ERROR日志:"
    tail -n 100 $LOG_DIR/error/error.log | grep "ERROR" | tail -n 10
    echo ""
    
    echo "查找webhooks相关错误:"
    grep -i "webhook" $LOG_DIR/error/error-*.log | tail -n 5
    echo ""
    
    echo "查找Dubbo相关错误:"
    grep "dubbo error" $LOG_DIR/error/error-*.log | tail -n 5
    echo ""
else
    echo "日志目录不存在: $LOG_DIR"
fi

# 3. 检查JVM状态
echo "3. 检查JVM状态..."
JAVA_PID=$(pgrep -f "ass")
if [ ! -z "$JAVA_PID" ]; then
    echo "Java进程ID: $JAVA_PID"
    echo "内存使用情况:"
    jstat -gc $JAVA_PID
    echo ""
    echo "线程状态:"
    jstack $JAVA_PID | grep "java.lang.Thread.State" | sort | uniq -c
else
    echo "未找到Java进程"
fi
echo ""

# 4. 检查数据库连接
echo "4. 检查数据库连接..."
# 这里需要根据实际数据库配置修改
# mysql -h db_host -u username -p -e "SELECT 1" 2>/dev/null && echo "✓ 数据库连接正常" || echo "✗ 数据库连接失败"

# 5. 检查Nacos连接
echo "5. 检查Nacos连接..."
NACOS_SERVER="nacos-server:8848"  # 需要根据实际配置修改
if curl -s "http://$NACOS_SERVER/nacos/v1/ns/instance/list?serviceName=ass" > /dev/null; then
    echo "✓ Nacos连接正常"
    echo "注册的服务实例:"
    curl -s "http://$NACOS_SERVER/nacos/v1/ns/instance/list?serviceName=ass" | jq '.hosts[] | {ip, port, healthy}'
else
    echo "✗ Nacos连接失败"
fi
echo ""

# 6. 检查第三方服务连接
echo "6. 检查第三方服务连接..."

# 检查支付宝接口
echo "检查支付宝接口连接..."
if curl -s --connect-timeout 5 https://openapi.alipay.com > /dev/null; then
    echo "✓ 支付宝接口可达"
else
    echo "✗ 支付宝接口不可达"
fi

# 检查飞书机器人
echo "检查飞书机器人连接..."
FEISHU_URL="https://open.feishu.cn/open-apis/bot/v2/hook/4ec42670-fe11-41d8-86e9-175eee19b995"
if curl -s --connect-timeout 5 "$FEISHU_URL" > /dev/null; then
    echo "✓ 飞书机器人可达"
else
    echo "✗ 飞书机器人不可达"
fi
echo ""

# 7. 检查系统资源
echo "7. 检查系统资源..."
echo "CPU使用率:"
top -bn1 | grep "Cpu(s)" | awk '{print $2}' | awk -F'%' '{print $1}'

echo "内存使用情况:"
free -h

echo "磁盘使用情况:"
df -h | grep -E "/$|/var|/tmp"
echo ""

# 8. 检查网络连接
echo "8. 检查网络连接..."
echo "当前TCP连接数:"
netstat -an | grep ESTABLISHED | wc -l

echo "监听的端口:"
netstat -tlnp | grep :8086
echo ""

# 9. 生成问题报告
echo "9. 生成问题报告..."
REPORT_FILE="sentry_issue_6185_report_$(date +%Y%m%d_%H%M%S).txt"

cat > $REPORT_FILE << EOF
Sentry Issue 6185 排查报告
生成时间: $(date)
Issue URL: http://sentry.pxb7.internal/organizations/sentry/issues/6185/?referrer=webhooks_plugin

=== 应用信息 ===
应用名称: ass (售后服务系统)
端口: 8086
上下文路径: /api/ass
Java进程ID: $JAVA_PID

=== 可能的问题原因 ===
1. Dubbo服务调用异常
   - 检查Nacos注册中心连接
   - 检查服务提供者状态
   - 查看CustomExceptionFilter日志

2. 第三方支付接口异常
   - 支付宝接口调用失败
   - 连连支付接口异常
   - 泰山支付接口问题

3. 异步任务处理异常
   - 命令执行失败次数超过6次
   - 消息队列处理异常

4. 数据库连接问题
   - HikariCP连接池异常
   - 分库分表配置问题

5. 配置问题
   - Nacos配置更新失败
   - Sentry配置错误

=== 建议的解决步骤 ===
1. 登录Sentry查看详细错误堆栈
2. 检查应用日志中的ERROR级别日志
3. 验证第三方服务连接状态
4. 检查数据库连接池状态
5. 重启应用服务（如果必要）

=== 监控建议 ===
1. 增加对关键接口的监控告警
2. 设置数据库连接池监控
3. 添加第三方服务调用监控
4. 完善异步任务执行监控
EOF

echo "问题报告已生成: $REPORT_FILE"
echo ""

echo "=== 排查完成 ==="
echo "结束时间: $(date)"
echo ""
echo "下一步操作建议:"
echo "1. 登录Sentry查看详细错误信息"
echo "2. 根据错误堆栈定位具体问题代码"
echo "3. 检查相关配置和依赖服务"
echo "4. 如需要，联系相关团队协助排查"
