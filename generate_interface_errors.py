#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
提炼本项目所有接口的异常信息
表格要求：文案：错误信息；使用场景：接口名
"""

import csv
from typing import List, Dict

def get_all_interfaces_and_errors() -> List[Dict[str, str]]:
    """获取所有接口和异常信息"""
    
    results = []
    
    # 1. Web端接口
    web_interfaces = [
        {
            "interface": "GET /web/question/classify/list",
            "name": "获取售后问题归类配置列表",
            "errors": [
                "参数为空",
                "当前游戏未配置", 
                "参数取值错误"
            ]
        },
        {
            "interface": "POST /web/dispute/complete",
            "name": "完结纠纷工单",
            "errors": [
                "工单ID不能为空",
                "处理结果不能为空",
                "工单已完结",
                "当前工单已完结，不允许完结",
                "完结工单失败"
            ]
        },
        {
            "interface": "POST /web/retrieve/setDealUser",
            "name": "分配处理人员",
            "errors": [
                "工单ID不能为空",
                "处理人员ID不能为空"
            ]
        },
        {
            "interface": "GET /web/retrieve/close",
            "name": "关闭工单",
            "errors": [
                "房间ID不能为空",
                "工单不存在"
            ]
        },
        {
            "interface": "GET /web/ass/workOrder/create",
            "name": "创建工单",
            "errors": [
                "房间ID不能为空",
                "获取客服失败",
                "售后处理中请勿重复操作",
                "创建客诉工单失败"
            ]
        },
        {
            "interface": "GET /web/ass/workOrder/address",
            "name": "获取售后后台地址",
            "errors": [
                "房间ID不能为空"
            ]
        },
        {
            "interface": "POST /web/answer/insert",
            "name": "回答问题",
            "errors": [
                "房间号不能为空",
                "答案不能为空",
                "重复提交,请稍后再试",
                "交付房间不存在"
            ]
        },
        {
            "interface": "POST /web/schedule/create",
            "name": "发起售后(散户及号商)",
            "errors": [
                "售后类型不能为空",
                "房间id不能为空",
                "节点不能为空"
            ]
        },
        {
            "interface": "POST /web/schedule/find",
            "name": "查询流程记录",
            "errors": [
                "房间号不能为空"
            ]
        },
        {
            "interface": "POST /web/proof/add",
            "name": "添加证据材料",
            "errors": [
                "售后工单id不能为空",
                "证据列表不能为空"
            ]
        },
        {
            "interface": "POST /web/violate/update",
            "name": "修改违约单",
            "errors": [
                "请选择违约单",
                "违约金必填",
                "违约金必须大于0",
                "守约金必填",
                "守约金必须大于0"
            ]
        },
        {
            "interface": "POST /web/complaint/workOrder/create",
            "name": "创建客诉工单",
            "errors": [
                "工单参数不能为空"
            ]
        },
        {
            "interface": "GET /web/complaint/workOrder/departmentList",
            "name": "客诉部门列表",
            "errors": []
        },
        {
            "interface": "GET /web/complaint/workOrder/viewLog",
            "name": "查看工单日志",
            "errors": [
                "工单ID不能为空"
            ]
        },
        {
            "interface": "GET /web/complaint/workOrder/phone/status",
            "name": "手机号状态查询",
            "errors": [
                "手机号不能为空",
                "请输入正确的手机号"
            ]
        }
    ]
    
    # 2. Mobile端接口
    mobile_interfaces = [
        {
            "interface": "POST /mobile/answer/insert",
            "name": "提交问题回答",
            "errors": [
                "房间号不能为空",
                "答案不能为空",
                "重复提交,请稍后再试",
                "交付房间不存在"
            ]
        },
        {
            "interface": "GET /mobile/schedule/record",
            "name": "获取售后进度记录",
            "errors": [
                "订单号不能为空"
            ]
        },
        {
            "interface": "POST /mobile/user/refund/recharge/apply",
            "name": "用户请求充值退款",
            "errors": [
                "申请参数不能为空"
            ]
        }
    ]
    
    # 3. H5端接口
    h5_interfaces = [
        {
            "interface": "POST /h5/answer/insert",
            "name": "H5端提交问题回答",
            "errors": [
                "房间号不能为空",
                "答案不能为空",
                "重复提交,请稍后再试",
                "交付房间不存在"
            ]
        },
        {
            "interface": "GET /h5/schedule/record",
            "name": "H5端获取售后进度记录",
            "errors": [
                "订单号不能为空"
            ]
        },
        {
            "interface": "POST /h5/user/refund/recharge/apply",
            "name": "H5端充值退款申请",
            "errors": [
                "申请参数不能为空"
            ]
        }
    ]
    
    # 4. 数据修复接口
    data_fix_interfaces = [
        {
            "interface": "POST /data/fix/resendRefundQueryMQ",
            "name": "重发退款查询MQ",
            "errors": [
                "消息参数不能为空"
            ]
        },
        {
            "interface": "POST /data/fix/reTradeAdminRefund",
            "name": "重新调用TradeAdmin退款",
            "errors": [
                "调用参数不能为空"
            ]
        }
    ]
    
    # 合并所有接口
    all_interfaces = web_interfaces + mobile_interfaces + h5_interfaces + data_fix_interfaces
    
    # 转换为结果格式
    for interface_info in all_interfaces:
        interface_name = f"{interface_info['interface']} - {interface_info['name']}"
        
        if interface_info['errors']:
            for error in interface_info['errors']:
                results.append({
                    "使用场景": interface_name,
                    "文案": error
                })
        else:
            # 如果没有特定错误，添加通用错误
            results.append({
                "使用场景": interface_name,
                "文案": "系统繁忙，请稍后再试"
            })
    
    return results

def get_common_errors() -> List[Dict[str, str]]:
    """获取通用错误信息"""
    
    common_errors = [
        # 系统级错误码
        ("通用系统错误", "重复操作"),
        ("通用系统错误", "数据不存在或已被删除"),
        ("通用系统错误", "系统繁忙，请稍后再试"),
        ("通用系统错误", "RPC调用失败,数据为空"),
        ("通用系统错误", "系统内部调用异常"),
        ("通用系统错误", "参数为空"),
        ("通用系统错误", "参数取值错误"),
        ("通用系统错误", "查询订单信息失败"),
        ("通用系统错误", "登录失败，无法访问系统资源"),
        
        # 售后业务错误码
        ("售后业务错误", "获取客服失败"),
        ("售后业务错误", "售后处理中请勿重复操作"),
        ("售后业务错误", "不存在客诉工单"),
        ("售后业务错误", "问题部门未配置"),
        ("售后业务错误", "工单处理人员未配置"),
        ("售后业务错误", "工单已完结"),
        ("售后业务错误", "工单无法转交给自己"),
        ("售后业务错误", "工单无法转交给他人"),
        ("售后业务错误", "转交人不存在"),
        ("售后业务错误", "客诉工单日志记录不存在"),
        ("售后业务错误", "获取用户基础信息失败"),
        ("售后业务错误", "获取系统用户信息失败"),
        ("售后业务错误", "未查询到订单"),
        ("售后业务错误", "排班分流获取客服失败,请去管理后台配置"),
        ("售后业务错误", "交付房间不存在"),
        ("售后业务错误", "未查询到群聊中的已成交订单信息"),
        ("售后业务错误", "处理工单失败"),
        ("售后业务错误", "工单不存在"),
        ("售后业务错误", "当前工单存在未完结的赔付单,不允许完结"),
        ("售后业务错误", "当前工单已完结，不允许完结"),
        ("售后业务错误", "未查询到群聊中的订单信息"),
        ("售后业务错误", "订单未完结--无法申请售后"),
        ("售后业务错误", "重复提交,请稍后再试"),
        ("售后业务错误", "重复申请售后"),
        ("售后业务错误", "请输入正确的手机号"),
        ("售后业务错误", "核验手机号注册状态失败"),
        ("售后业务错误", "获取用户实名认证信息失败"),
        ("售后业务错误", "保存售后申请记录失败"),
        ("售后业务错误", "文本审核不通过"),
        ("售后业务错误", "请输入其他问题"),
        ("售后业务错误", "其他问题不能超过50个字"),
        ("售后业务错误", "链接错误"),
        ("售后业务错误", "创建客诉工单失败"),
        ("售后业务错误", "售后问题不存在"),
        ("售后业务错误", "完结工单失败"),
        ("售后业务错误", "答案不能为空"),
        
        # 退款相关错误码
        ("退款相关错误", "当前订单状态不允许退款"),
        ("退款相关错误", "当前订单有其他退款单正在进行中"),
        ("退款相关错误", "卖家账号信息已经暴露，不允许退款"),
        ("退款相关错误", "发送客服确认卡片失败，请重新尝试"),
        ("退款相关错误", "当前订单有其他放款单正在进行中"),
        ("退款相关错误", "当前订单有其他收款单正在进行中"),
        ("退款相关错误", "您不是交付客服，不能操作退款"),
        ("退款相关错误", "发送买家信息收集卡片失败"),
        ("退款相关错误", "未匹配到退款类型"),
        ("退款相关错误", "未匹配到订单支付模式，请确认订单已完成付款"),
        ("退款相关错误", "退款异常"),
        ("退款相关错误", "退款状态异常"),
        ("退款相关错误", "未匹配到支付中心收款信息"),
        ("退款相关错误", "退款类型选择校验失败"),
        ("退款相关错误", "未匹配到退款原因"),
        ("退款相关错误", "退款金额不合法,如需将剩余号价退完，请选择全额退款"),
        ("退款相关错误", "退款类型不合法"),
        ("退款相关错误", "退款数据保存或更新失败"),
        ("退款相关错误", "退款数据不存在"),
        ("退款相关错误", "退款三方请求失败"),
        ("退款相关错误", "不支持该付款方式"),
        ("退款相关错误", "加签失败"),
        ("退款相关错误", "找不到当前订单成功的收款单"),
        ("退款相关错误", "当前订单退款金额不足"),
        ("退款相关错误", "退款单买家账号信息已提交"),
        ("退款相关错误", "该退款单状态不允许取消退款"),
        ("退款相关错误", "退款单审核状态异常"),
        ("退款相关错误", "im客服群不存在,无法发起退款，请重试"),
        ("退款相关错误", "充值订单非待发货状态，无法申请退款"),
        ("退款相关错误", "退款商品类型非法，客服无法操作退款"),
        ("退款相关错误", "下单6小时后可以申请退款"),
        ("退款相关错误", "订单号与退款单号不匹配，无法审核退款，请刷新后重试"),
        ("退款相关错误", "当前订单已过在线退款有效期，请走线下退款"),
        ("退款相关错误", "存在进行中的收退放单据"),
        ("退款相关错误", "退款单取消失败"),
        ("退款相关错误", "退款渠道系统异常"),
        ("退款相关错误", "退款需要重试"),
        ("退款相关错误", "三方渠道异常"),
        
        # 违约金相关错误码
        ("违约金相关错误", "非整单退,不可申请违约金"),
        ("违约金相关错误", "违约金超出最大金额"),
        ("违约金相关错误", "违约金参数缺失,请补全"),
        ("违约金相关错误", "守约金不可大于总违约金"),
        ("违约金相关错误", "整单退款成功后才可操作违约金"),
        ("违约金相关错误", "创建违约单失败"),
        ("违约金相关错误", "违约金状态非收款失败,不可编辑"),
        ("违约金相关错误", "违约金状态有误"),
        ("违约金相关错误", "收款中断时间暂未到"),
        ("违约金相关错误", "违约金已存在扣款单"),
        ("违约金相关错误", "总违约金必须>=守约金额"),
        ("违约金相关错误", "总违约金必须<剩余可退金额"),
        ("违约金相关错误", "您不是交付客服,不可操作"),
        ("违约金相关错误", "更新违约单状态失败"),
        ("违约金相关错误", "更新违约单打款状态失败"),
        ("违约金相关错误", "创建违约支付单失败"),
        ("违约金相关错误", "已存在重复单据"),
        ("违约金相关错误", "取消违约支付单失败"),
        ("违约金相关错误", "更新违约支付单状态失败"),
        ("违约金相关错误", "打款回调更新失败"),
        ("违约金相关错误", "钱包扣款请求失败,等待重试"),
        ("违约金相关错误", "钱包扣款处理中,等待重试"),
        ("违约金相关错误", "已有违约金,请勿重复申请"),
        ("违约金相关错误", "存在未处理完成的违约金,请勿重复申请"),
        ("违约金相关错误", "创建违约收款失败,请稍后再试"),
        ("违约金相关错误", "处理违约收款失败,请稍后再试"),
        ("违约金相关错误", "违约收款失败,更新订单状态失败"),
        ("违约金相关错误", "编辑失败,请稍后再试"),
        
        # 支付网关错误码
        ("支付网关错误", "dubbo请求钱包查询扣款结果失败"),
        ("支付网关错误", "dubbo请求钱包扣款失败"),
        
        # 包赔相关错误码
        ("包赔相关错误", "包赔变更查询异常"),
        
        # 其他错误
        ("其他错误", "获取违约单锁失败"),
        ("其他错误", "支付渠道不存在"),
        ("其他错误", "请求支付宝异常"),
        ("其他错误", "可以直接置失败的错误码")
    ]
    
    return [{"使用场景": scene, "文案": error} for scene, error in common_errors]

def create_excel_table():
    """创建Excel表格"""
    
    print("正在提炼项目接口异常信息...")
    
    # 获取接口错误信息
    interface_errors = get_all_interfaces_and_errors()
    
    # 获取通用错误信息
    common_errors = get_common_errors()
    
    # 合并所有错误信息
    all_errors = interface_errors + common_errors
    
    # 去重
    unique_errors = []
    seen = set()
    for error in all_errors:
        key = f"{error['使用场景']}|{error['文案']}"
        if key not in seen:
            seen.add(key)
            unique_errors.append(error)
    
    print(f"共提取到 {len(unique_errors)} 条唯一的异常信息")
    
    # 创建CSV文件
    with open('项目接口异常信息表.csv', 'w', newline='', encoding='utf-8-sig') as f:
        writer = csv.DictWriter(f, fieldnames=['使用场景', '文案'])
        writer.writeheader()
        writer.writerows(unique_errors)
    
    print("已生成: 项目接口异常信息表.csv")
    
    # 创建制表符分隔的文件（Excel友好格式）
    with open('项目接口异常信息表.txt', 'w', encoding='utf-8') as f:
        f.write("使用场景\t文案\n")
        for error in unique_errors:
            f.write(f"{error['使用场景']}\t{error['文案']}\n")
    
    print("已生成: 项目接口异常信息表.txt (制表符分隔，Excel友好)")
    
    # 统计信息
    scenes = set(error['使用场景'] for error in unique_errors)
    print(f"\n统计信息：")
    print(f"- 共有 {len(scenes)} 个不同的使用场景")
    print(f"- 共有 {len(unique_errors)} 条唯一的异常文案")
    
    # 显示前10条数据作为预览
    print(f"\n数据预览（前10条）：")
    for i, error in enumerate(unique_errors[:10], 1):
        print(f"{i:2d}. 使用场景: {error['使用场景']}")
        print(f"    文案: {error['文案']}")
        print()

if __name__ == '__main__':
    create_excel_table()
