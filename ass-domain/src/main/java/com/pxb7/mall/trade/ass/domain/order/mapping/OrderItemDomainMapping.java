package com.pxb7.mall.trade.ass.domain.order.mapping;

import com.pxb7.mall.trade.ass.domain.order.model.OrderItemBO;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.OrderItem;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.OrderItemExtend;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "Spring", unmappedTargetPolicy = ReportingPolicy.IGNORE,imports = {
    OrderItemExtendDomainMapping.class
})
public interface OrderItemDomainMapping {

    OrderItemBO entityToBO(OrderItem orderItem);



}
