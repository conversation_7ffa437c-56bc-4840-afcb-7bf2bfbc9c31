package com.pxb7.mall.trade.ass.domain.order;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.cola.exception.BizException;
import com.pxb7.mall.trade.ass.domain.order.mapping.OrderItemDomainMapping;
import com.pxb7.mall.trade.ass.domain.order.mapping.OrderItemExtendDomainMapping;
import com.pxb7.mall.trade.ass.domain.order.model.OrderItemBO;
import com.pxb7.mall.trade.ass.domain.order.model.OrderItemExtendBO;
import com.pxb7.mall.trade.ass.infra.enums.ErrorCode;
import com.pxb7.mall.trade.ass.infra.model.UserBaseInfo;
import com.pxb7.mall.trade.ass.infra.repository.db.OrderItemExtendRepository;
import com.pxb7.mall.trade.ass.infra.repository.db.OrderItemRepository;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.OrderItem;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.OrderItemExtend;
import com.pxb7.mall.trade.order.client.enums.extPayment.ExtVoucherStatusEnum;
import com.pxb7.mall.trade.order.client.enums.order.OrderItemStatusEnum;
import com.pxb7.mall.trade.order.client.enums.order.ReceiptVoucherStatusEnum;
import com.pxb7.mall.trade.order.client.enums.order.RefundStatusEnum;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Slf4j
@Service
public class OrderItemDomainService {

    @Resource
    private OrderItemExtendRepository orderItemExtendRepository;

    @Resource
    private OrderItemRepository orderItemRepository;

    @Resource
    private OrderItemExtendDomainMapping orderItemExtendDomainMapping;

    @Resource
    private OrderItemDomainMapping orderItemDomainMapping;

    public OrderItemBO getOrderItemAndExtend(String orderItemId) {
        OrderItem orderItem = orderItemRepository.getOrderItem(orderItemId);
        OrderItemExtend orderItemExtend = orderItemExtendRepository.getOneByItemId(orderItemId);
        OrderItemBO orderItemBO = orderItemDomainMapping.entityToBO(orderItem);
        if (null != orderItemBO) {
            orderItemBO.setOrderItemExtend(orderItemExtendDomainMapping.entityToBO(orderItemExtend));
        }
        return orderItemBO;
    }

    public OrderItemExtendBO getOrderItemExtend(String orderItemId) {
        OrderItemExtend orderItemExtend = orderItemExtendRepository.getOneByItemId(orderItemId);
        return orderItemExtendDomainMapping.entityToBO(orderItemExtend);
    }


    public void  checkPermission(String orderItemId, UserBaseInfo userInfo) {
        if (userInfo.getIsAdmin()){
            return;
        }
        OrderItemBO orderItemBO = getOrderItemAndExtend(orderItemId);
        if (null == orderItemBO) {
            throw new RuntimeException("订单不存在");
        }
        if (userInfo.getIsMerchant()) {
            OrderItemExtendBO orderItemExtendBO = orderItemBO.getOrderItemExtend();
            if (null==orderItemExtendBO){
                throw new RuntimeException("订单拓展数据异常");
            }
            // 商家 校验部门
            String merchantId = userInfo.getMerchantId();
            if (!Objects.equals(orderItemExtendBO.getBuyerMerchantId(), merchantId) && !Objects.equals(
                orderItemExtendBO.getSellerMerchantId(), merchantId)) {
                log.warn("checkPermission no permission, buyerMerchantId:{}, sellerMerchantId:{},  merchantId:{}",
                    orderItemExtendBO.getBuyerMerchantId(), orderItemExtendBO.getSellerMerchantId(), merchantId);
                throw new RuntimeException("无查看权限");
            }
        } else {
            String userId = userInfo.getUserId();
            // 散户 校验本人
            if (!Objects.equals(orderItemBO.getBuyerId(), userId) && !Objects.equals(orderItemBO.getSellerId(), userId)) {
                log.warn("checkPermission no permission, buyerId:{}, sellerId:{},  nowUserId:{}",
                    orderItemBO.getBuyerId(), orderItemBO.getSellerId(), userId);
                throw new RuntimeException("无查看权限");
            }
        }
    }


    // 订单流程中的单据
    public void checkOrderProcessingVoucher(OrderItem orderItem) {
        if (orderItem == null) {
            throw new BizException(ErrorCode.ORDER_NOT_EXIST.getErrCode(), ErrorCode.ORDER_NOT_EXIST.getErrDesc());
        }
        if (ObjectUtil.equal(RefundStatusEnum.WAIT_APPLY.getValue(), orderItem.getRefundStatus())
                || ObjectUtil.equal(RefundStatusEnum.APPLYING.getValue(), orderItem.getRefundStatus())) {
            throw new BizException(ErrorCode.ORDER_HAVE_OTHER_REFUND_RECEIPT_FAIL.getErrCode(),
                    ErrorCode.ORDER_HAVE_OTHER_REFUND_RECEIPT_FAIL.getErrDesc());
        }

        if (ObjectUtil.equal(ExtVoucherStatusEnum.CREATED.getValue(), orderItem.getPayoutStatus())
                || ObjectUtil.equal(ExtVoucherStatusEnum.PROCESSING.getValue(), orderItem.getPayoutStatus())) {
            throw new BizException(ErrorCode.ORDER_HAVE_OTHER_EXT_PAYMENT_FAIL.getErrCode(),
                    ErrorCode.ORDER_HAVE_OTHER_EXT_PAYMENT_FAIL.getErrDesc());
        }

        if (ObjectUtil.equal(ReceiptVoucherStatusEnum.PENDING.getValue(), orderItem.getReceiptStatus())
                || ObjectUtil.equal(ReceiptVoucherStatusEnum.IN_PROGRESS.getValue(), orderItem.getReceiptStatus())) {
            throw new BizException(ErrorCode.ORDER_HAVE_OTHER_COLLECTION_RECEIPT_FAIL.getErrCode(),
                    ErrorCode.ORDER_HAVE_OTHER_COLLECTION_RECEIPT_FAIL.getErrDesc());
        }

    }

}
