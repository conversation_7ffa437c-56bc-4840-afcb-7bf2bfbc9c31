package com.pxb7.mall.trade.ass.domain.violate.model;

import com.pxb7.mall.trade.ass.client.enums.ViolateAggregationStatusEnum;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class ViolateOrderBO {


    /**
     * 违约单id
     */
    private String  violateId;
    /**
     * 订单id
     */
    private String  orderItemId;
    /**
     * 退款单id
     */
    private String  refundVoucherId;
    /**
     * 违约方userID
     */
    private String  violateUserId;
    /**
     * 守约方userID
     */
    private String  promiseUserId;
    /**
     * 违约方:1买家、2卖家
     */
    private Integer  violateUserType;
    /**
     * 违约金,分
     */
    private Long  violateAmount;
    /**
     * 守约金,分
     */
    private Long  promiseAmount;
    /**
     * 平台金额,分
     */
    private Long  platformAmount;
    /**
     * 违约单状态:1待执行、2执行中、3执行成功、4执行失败、5取消
     */
    private Integer  violateStatus;
    /**
     * 收款状态:1待缴款、2缴款成功、3缴款失败、4缴款中断、5缴款取消
     */
    private Integer  receiptStatus;
    /**
     * 打款状态:1打款中、2打款成功、3打款失败
     */
    private Integer  transferStatus;
    /**
     * 制单客服userid
     */
    private String  createUserId;
    /**
     * 制单客服
     */
    private String  createUsername;
    /**
     * 编辑客服userid
     */
    private String  updateUserId;
    /**
     * 编辑客服
     */
    private String  updateUsername;


    private LocalDateTime  createTime;

    private LocalDateTime  updateTime;
    /**
     * 完结时间
     */

    private LocalDateTime completedTime;
    /**
     * 取消时间
     */

    private LocalDateTime  cancelTime;
    /**
     * 0正常、1已删除
     */
    private Integer  isDeleted;

    public Integer getViolateAggregationStatus() {
       return ViolateAggregationStatusEnum.getViolateAggregationStatus(this.violateStatus, this.receiptStatus, this.transferStatus);
    }

}
