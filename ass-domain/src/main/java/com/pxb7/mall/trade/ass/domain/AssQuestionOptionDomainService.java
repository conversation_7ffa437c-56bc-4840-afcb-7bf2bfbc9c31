package com.pxb7.mall.trade.ass.domain;

import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.fastjson2.JSON;
import com.pxb7.mall.im.client.api.SendCommonMsgServiceI;
import com.pxb7.mall.im.client.api.SendMsgServiceI;
import com.pxb7.mall.im.client.dto.request.GroupNotifyMsgReqDTO;
import com.pxb7.mall.im.client.dto.request.card.SendRichTextMsgReqDTO;
import com.pxb7.mall.im.client.dto.request.card.button.ButtonCon;
import com.pxb7.mall.im.client.dto.request.card.button.ButtonConUrl;
import com.pxb7.mall.im.client.dto.request.card.button.clienttype.ClientAndroid;
import com.pxb7.mall.im.client.dto.request.card.button.clienttype.ClientPC;
import com.pxb7.mall.im.client.dto.request.card.button.clienttype.ClientWap;
import com.pxb7.mall.im.client.dto.request.card.button.targettype.TargetTypeWindow;
import com.pxb7.mall.im.client.dto.request.card.richtext.RichTextContent;
import com.pxb7.mall.trade.ass.client.enums.AssScheduleNode;
import com.pxb7.mall.trade.ass.domain.model.assQuestion.TransferQuestionDataBO;
import com.pxb7.mall.trade.ass.infra.enums.ErrorCode;
import com.pxb7.mall.trade.ass.infra.exception.Ensure;
import com.pxb7.mall.trade.ass.infra.repository.db.AssQuestionOptionRelationRepository;
import com.pxb7.mall.trade.ass.infra.repository.db.AssQuestionOptionRepository;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.AssQuestionOption;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.AssSchedule;
import com.pxb7.mall.trade.ass.infra.util.DubboResultAssert;
import com.pxb7.mall.trade.order.client.api.OrderInfoDubboServiceI;
import com.pxb7.mall.trade.order.client.dto.response.order.dubbo.OrderInfoDubboRespDTO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * Copyright (C), 2002-2025, 螃蟹游戏服务网 售后问答问题配置
 *
 * <AUTHOR> 2025/5/26 下午3:40
 */
@Service
@Slf4j
public class AssQuestionOptionDomainService {

    @DubboReference
    private SendMsgServiceI sendMsgServiceI;
    @DubboReference
    private SendCommonMsgServiceI sendCommonMsgServiceI;
    @DubboReference
    private OrderInfoDubboServiceI orderInfoDubboServiceI;
    @Resource
    private AssQuestionOptionRepository assQuestionOptionRepository;
    @Resource
    private AssQuestionOptionRelationRepository assQuestionOptionRelationRepository;

    public boolean sendQuestionByGame(AssSchedule assSchedule, String userId) {
        String roomId = assSchedule.getRoomId();
        String orderItemId = assSchedule.getOrderItemId();
        Ensure.that(orderItemId).isNotBlank(ErrorCode.PARAM_EMPTY);
        SingleResponse<OrderInfoDubboRespDTO> orderInfo = orderInfoDubboServiceI.getOrderInfo(orderItemId);
        AssQuestionOption questionOption = Optional.ofNullable(orderInfo).map(SingleResponse::getData)
            .map(OrderInfoDubboRespDTO::getGameId).map(assQuestionOptionRelationRepository::getOptionId)
            .map(assQuestionOptionRepository::findOneByOptionId).orElse(null);

        if (Objects.isNull(questionOption)) {
            // 发提醒
            sendTipsMsg(roomId, userId);
            return false;
        } else {
            sendQuestionCard(roomId, userId, orderInfo.getData().getBuyerId(), questionOption.getQuestionList());
            return true;
        }
    }

    // 发送售后问题
    public void sendQuestionCard(String roomId, String customerId, String buyerId, String questionList) {
        // 封装按钮信息
        List<TransferQuestionDataBO> questionDataList = JSON.parseArray(questionList, TransferQuestionDataBO.class);
        ButtonConUrl h5Url = new ButtonConUrl().setClientType(new ClientWap()).setUrl("BasicAfterSalesIssuesPopupRef")
            .setTargetType(new TargetTypeWindow("BasicAfterSalesIssuesPopupRef"));
        ButtonConUrl pcUrl = new ButtonConUrl().setClientType(new ClientPC()).setUrl("BasicAfterSalesIssuesPopupRef")
            .setTargetType(new TargetTypeWindow("BasicAfterSalesIssuesPopupRef"));
        ButtonConUrl mobileUrl =
            new ButtonConUrl().setClientType(new ClientAndroid()).setUrl("BasicAfterSalesIssuesPopupRef")
                .setTargetType(new TargetTypeWindow("BasicAfterSalesIssuesPopupRef"));
        ButtonCon buttonContent = new ButtonCon().setParam(questionDataList).setLabel("去回答")
            .setButtonUrl(Arrays.asList(h5Url, pcUrl, mobileUrl));
        SendRichTextMsgReqDTO cardReqDTO = new SendRichTextMsgReqDTO().setTitle("售后基础信息").setFromUserId(customerId)
            .setTargetId(roomId).setOperation("ass_question").setButtons(Collections.singletonList(buttonContent))
            .setOperatorUserIds(Collections.singletonList(buyerId)).setMentionedIds(Collections.singletonList(buyerId))
            .setContent(Collections.singletonList(new RichTextContent(AssScheduleNode.SEND_QUESTION.getMsg())));
        DubboResultAssert.wrapException(() -> sendCommonMsgServiceI.sendRichTextMsg(cardReqDTO), ErrorCode.RPC_ERROR);
        // 发融云消息
        sendGroupNotifyMsg(roomId, customerId, AssScheduleNode.SEND_QUESTION);
    }

    // 匹配不到问题 给客服发提醒消息
    public void sendTipsMsg(String roomId, String userId) {
        SendRichTextMsgReqDTO msgReqDTO =
            new SendRichTextMsgReqDTO().setTargetId(roomId).setFromUserId(userId).setTitle("售后基础信息")
                .setMentionedIds(Collections.singletonList(userId)).setTargetUserIds(Collections.singletonList(userId))
                .setContent(List.of(new RichTextContent(AssScheduleNode.SEND_QUESTION_FAIL.getMsg())));
        DubboResultAssert.wrapException(() -> sendCommonMsgServiceI.sendRichTextMsg(msgReqDTO), ErrorCode.RPC_ERROR);
        // 发融云消息
        sendGroupNotifyMsg(roomId, userId, AssScheduleNode.SEND_QUESTION_FAIL);
    }

    // 发融云消息-更新页面
    public void sendGroupNotifyMsg(String roomId, String customerId, AssScheduleNode assScheduleNode) {
        GroupNotifyMsgReqDTO msgDto = new GroupNotifyMsgReqDTO();
        msgDto.setGroupId(roomId);
        msgDto.setFromUserId(customerId);
        msgDto.setContent(assScheduleNode.getId());
        msgDto.setOperation("after_sale_node_message");
        sendMsgServiceI.sendGroupNotify(msgDto);
    }
}
