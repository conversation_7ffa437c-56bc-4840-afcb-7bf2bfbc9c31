package com.pxb7.mall.trade.ass.domain.mapping;

import com.pxb7.mall.trade.ass.domain.model.AssFollowupConfigReqBO;
import com.pxb7.mall.trade.ass.domain.model.AssFollowupConfigRespBO;
import com.pxb7.mall.trade.ass.infra.model.AssFollowupConfigReqPO;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.AssFollowupConfig;

import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;


@Mapper
public interface AssFollowupConfigDomainMapping {

    AssFollowupConfigDomainMapping INSTANCE = Mappers.getMapper(AssFollowupConfigDomainMapping.class);


    AssFollowupConfigReqPO.AddPO assFollowupConfigBO2AddPO(AssFollowupConfigReqBO.AddBO source);

    AssFollowupConfigReqPO.UpdatePO assFollowupConfigBO2UpdatePO(AssFollowupConfigReqBO.UpdateBO source);

    AssFollowupConfigReqPO.DelPO assFollowupConfigBO2DelPO(AssFollowupConfigReqBO.DelBO source);

    AssFollowupConfigReqPO.SearchPO assFollowupConfigBO2SearchPO(AssFollowupConfigReqBO.SearchBO source);

    AssFollowupConfigReqPO.PagePO assFollowupConfigBO2PagePO(AssFollowupConfigReqBO.PageBO source);

    AssFollowupConfigRespBO.DetailBO assFollowupConfigPO2DetailBO(AssFollowupConfig source);

    List<AssFollowupConfigRespBO.DetailBO> assFollowupConfigPO2ListBO(List<AssFollowupConfig> source);

    Page<AssFollowupConfigRespBO.DetailBO> assFollowupConfigPO2PageBO(Page<AssFollowupConfig> source);

}


