package com.pxb7.mall.trade.ass.domain;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.pxb7.mall.im.client.api.SendCommonMsgServiceI;
import com.pxb7.mall.im.client.dto.request.card.SendRichTextMsgReqDTO;
import com.pxb7.mall.im.client.dto.request.card.richtext.CustomercareNoticeContent;
import com.pxb7.mall.im.client.dto.request.card.richtext.RichTextContent;
import com.pxb7.mall.trade.ass.client.enums.AssScheduleNode;
import com.pxb7.mall.trade.ass.client.enums.AssWoType;
import com.pxb7.mall.trade.ass.domain.mapping.AssWorkOrderDomainMapping;
import com.pxb7.mall.trade.ass.domain.mapping.AssWorkOrderLogDomainMapping;
import com.pxb7.mall.trade.ass.domain.mapping.assSchedule.AssScheduleDomainMapping;
import com.pxb7.mall.trade.ass.domain.model.AssWorkOrderLogReqBO;
import com.pxb7.mall.trade.ass.domain.model.AssWorkOrderReqBO;
import com.pxb7.mall.trade.ass.domain.model.assSchedule.ReqCreateAssScheduleBO;
import com.pxb7.mall.trade.ass.domain.model.reqeust.ApplyAfcRecordReqBO;
import com.pxb7.mall.trade.ass.domain.processor.AfcApplyDataConvertProcessor;
import com.pxb7.mall.trade.ass.infra.constant.AfcConstant;
import com.pxb7.mall.trade.ass.infra.enums.ErrorCode;
import com.pxb7.mall.trade.ass.infra.exception.BusinessException;
import com.pxb7.mall.trade.ass.infra.exception.Ensure;
import com.pxb7.mall.trade.ass.infra.remote.dubbo.ImRpcGateway;
import com.pxb7.mall.trade.ass.infra.remote.dubbo.OrderItemRpcGateway;
import com.pxb7.mall.trade.ass.infra.repository.db.AfcQuestionConfRepository;
import com.pxb7.mall.trade.ass.infra.repository.db.AfcSubmitRecordRepository;
import com.pxb7.mall.trade.ass.infra.repository.db.AssScheduleRepository;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.AfcQuestionConf;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.AssSchedule;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.AssScheduleLog;
import com.pxb7.mall.trade.ass.infra.util.DubboResultAssert;
import com.pxb7.mall.trade.ass.infra.util.NumberUtil;
import com.pxb7.mall.trade.order.client.dto.response.ass.StartAssInfo;
import com.pxb7.mall.trade.order.client.enums.order.OrderItemStatusEnum;
import io.vavr.Tuple;
import io.vavr.Tuple3;
import io.vavr.Tuple6;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

import static com.pxb7.mall.trade.ass.client.enums.AssWoType.RETRIEVE;

/**
 * Copyright (C), 2002-2025, 螃蟹游戏服务网
 *
 * @fileName: AfcRecordDomainService.java
 * @description: 售后申请记录domain service
 * @author: guominqiang
 * @email: <EMAIL>
 * @date: 2025/4/11 15:02
 * @history: //修改记录
 * <author> <time> <version> <desc>
 * 修改人姓名 修改时间 版本号 描述
 */
@Service
@Slf4j
public class AfcRecordDomainService {
    @Resource
    private DataSourceTransactionManager transactionManager;
    @Resource
    private AssScheduleDomainService assScheduleDomainService;
    @Resource
    private AfcApplyDataConvertProcessor afcApplyDataConvertProcessor;
    @Resource
    private AfcQuestionConfRepository afcQuestionConfRepository;
    @Resource
    private AfcSubmitRecordRepository afcSubmitRecordRepository;
    @Resource
    private AssScheduleRepository assScheduleRepository;
    @Resource
    private OrderItemRpcGateway orderItemRpcGateway;
    @Resource
    private ImRpcGateway imRpcGateway;
    @DubboReference
    private SendCommonMsgServiceI sendCommonMsgServiceI;
    @Resource
    private AssScheduleLogDomainService assScheduleLogDomainService;
    @Resource
    private AssQuestionOptionDomainService assQuestionOptionDomainService;
    @Resource
    private AssWorkOrderDomainService assWorkOrderDomainService;
    @Resource
    private AssWorkOrderLogDomainService assWorkOrderLogDomainService;

    public String applyAfc(ApplyAfcRecordReqBO applyAfcRecordReqBO,
                           ReqCreateAssScheduleBO applyAfcScheduleBO,
                           String userId) {
        Tuple3<String, String, String> tuple = Tuple.of(userId, applyAfcRecordReqBO.getOrderItemId(), applyAfcRecordReqBO.getRoomId());
        StartAssInfo startAssInfo = orderItemRpcGateway.startAss(tuple._2);

        AfcQuestionConf afcQuestionConf = afcQuestionConfRepository.getById(applyAfcRecordReqBO.getQuestionId());
        preCheck(applyAfcRecordReqBO, startAssInfo, afcQuestionConf);

        String deliveryRoomId = StrUtil.isNotBlank(tuple._3) ? tuple._3 : startAssInfo.getDeliveryRoomId();
        imRpcGateway.orderJumpGroup(deliveryRoomId, tuple._1);

        applyAfcRecordReqBO = afcApplyDataConvertProcessor.buildApplyAfcRecordBO(applyAfcRecordReqBO, afcQuestionConf, deliveryRoomId, tuple._1);
        applyAfcScheduleBO = afcApplyDataConvertProcessor.buildApplyAfcScheduleBO(applyAfcScheduleBO, afcQuestionConf, deliveryRoomId, tuple._1);
        // 查询日志信息
        AssSchedule schedule = assScheduleRepository.findOneByOrderItemIdAndRoomId(tuple._2, deliveryRoomId);
        if (ObjectUtil.isNotEmpty(schedule)) {
            if (!schedule.getFinish()) {
                throw new BusinessException(ErrorCode.ASS_EXIST);
            }
        }

        DefaultTransactionDefinition defaultTransactionDefinition = new DefaultTransactionDefinition();
        defaultTransactionDefinition.setTimeout(6);
        TransactionStatus status = transactionManager.getTransaction(defaultTransactionDefinition);
        try {
            String scheduleId = assScheduleDomainService.saveAssSchedule(applyAfcScheduleBO);
            afcSubmitRecordRepository.insert(AssScheduleDomainMapping.INSTANCE.toAfcSubmitRecordPO(applyAfcRecordReqBO));
            saveWorkOrder(applyAfcRecordReqBO, applyAfcScheduleBO, scheduleId);
            transactionManager.commit(status);
        } catch (Exception e) {
            log.info("applyAfc failed: applyAfcRecordReqBO {}", applyAfcRecordReqBO, e);
            transactionManager.rollback(status);
            throw new BusinessException(ErrorCode.SAVE_AFC_APPLY_RECORD_ERROR);
        } finally {
            if (!status.isCompleted()) {
                transactionManager.rollback(status);
            }
        }
        postHandle(deliveryRoomId, tuple._1, applyAfcRecordReqBO, afcQuestionConf);

        return deliveryRoomId;
    }


    private void saveWorkOrder(ApplyAfcRecordReqBO applyAfcRecordReqBO, ReqCreateAssScheduleBO applyAfcScheduleBO, String scheduleId) {
        if (!NumberUtil.between(applyAfcRecordReqBO.getAfcType(), AssWoType.RETRIEVE.getCode(), AssWoType.DISPUTE.getCode())) {
            return;
        }

        AssWorkOrderReqBO.AddBO assWorkOrderBO = AssWorkOrderDomainMapping.INSTANCE.toAssWorkOrderBO(applyAfcRecordReqBO, applyAfcScheduleBO, scheduleId);

        assWorkOrderDomainService.insert(assWorkOrderBO);

        AssWorkOrderLogReqBO.AddBO assWorkOrderLogBO = AssWorkOrderLogDomainMapping.INSTANCE.toAssWorkOrderLogBO(applyAfcRecordReqBO, applyAfcScheduleBO);
        assWorkOrderLogBO.setWorkOrderId(assWorkOrderBO.getWorkOrderId());

        assWorkOrderLogDomainService.insert(assWorkOrderLogBO);

        log.info("saveWorkOrder: {} assWorkOrderLogBO:{}", JSON.toJSONString(assWorkOrderBO),JSON.toJSONString(assWorkOrderLogBO));
    }




    /**
     * 售后申请前置校验
     *
     * @param applyAfcRecordReqBO 售后申请记录
     * @param startAssInfo        订单售后信息
     * @param afcQuestionConf     售后问题配置
     */
    private void preCheck(ApplyAfcRecordReqBO applyAfcRecordReqBO,
                          StartAssInfo startAssInfo,
                          AfcQuestionConf afcQuestionConf) {
        if (ObjectUtil.isEmpty(startAssInfo)) {
            throw new BusinessException(ErrorCode.ORDER_NOT_EXIST);
        }
        if (ObjectUtil.notEqual(OrderItemStatusEnum.DEAL_SUCCESS.getValue(), startAssInfo.getOrderItemStatus())) {
            throw new BusinessException(ErrorCode.ORDER_NO_DEAL);
        }
        if (StrUtil.isBlank(startAssInfo.getDeliveryRoomId())) {
            throw new BusinessException(ErrorCode.ROOM_NOT_EXISTS);
        }
        if (ObjectUtil.isEmpty(afcQuestionConf)) {
            throw new BusinessException(ErrorCode.AFC_QUESTION_NOT_EXISTS);
        }
        // 判断是否为其他问题
        if (StrUtil.contains(afcQuestionConf.getQuestionName(), AfcConstant.OTHER_QUESTION_MARK)) {
            if (StrUtil.isBlank(applyAfcRecordReqBO.getRemark())) {
                throw new BusinessException(ErrorCode.PLEASE_ENTER_OTHER_SPECIFIC_QUESTION);
            }
            if (applyAfcRecordReqBO.getRemark().length() > 50) {
                throw new BusinessException(ErrorCode.OTHER_QUESTION_TOO_LONG);
            }
        }
    }

    /**
     * 售后申请后置处理
     *
     * @param deliveryRoomId      房间id
     * @param userId              用户id
     * @param applyAfcRecordReqBO 售后申请记录请求参数
     * @param afcQuestionConf     问题配置
     */
    private void postHandle(String deliveryRoomId,
                            String userId,
                            ApplyAfcRecordReqBO applyAfcRecordReqBO,
                            AfcQuestionConf afcQuestionConf) {
        Tuple6<String, String, String, String, String, AssWoType> tuple = Tuple.of(userId, applyAfcRecordReqBO.getOrderItemId(), deliveryRoomId, applyAfcRecordReqBO.getProductUniqueNo(),
            StrUtil.contains(afcQuestionConf.getQuestionName(), AfcConstant.OTHER_QUESTION_MARK)
                ? "【" + afcQuestionConf.getQuestionName() + "】" + applyAfcRecordReqBO.getRemark()
                : afcQuestionConf.getQuestionName(), AssWoType.fromCode(applyAfcRecordReqBO.getAfcType()));

        CustomercareNoticeContent noticeMsgDTO = new CustomercareNoticeContent(String.format(AfcConstant.AFC_APPLY_CARD, tuple._6.getDesc(), tuple._4, tuple._2, tuple._5));
        SendRichTextMsgReqDTO msgReqDTO = new SendRichTextMsgReqDTO().setTargetId(deliveryRoomId)
            .setFromUserId(userId)
            .setTitle("售后申请")
            .setCustomercareNoticeContent(noticeMsgDTO)
            .setContent(List.of(new RichTextContent("售后类型: " + tuple._6.getDesc()), new RichTextContent("商品编号: " + tuple._4), new RichTextContent("订单编号: " + tuple._2), new RichTextContent("售后问题: " + tuple._5)));
        String mentionedId = imRpcGateway.getOneAfterSaleCustomerCare(deliveryRoomId, tuple._6.getCode());
        if (StrUtil.isNotBlank(mentionedId)) {
            msgReqDTO.setMentionedIds(Collections.singletonList(mentionedId));
        }
        SingleResponse<String> response = DubboResultAssert.wrapException(() -> sendCommonMsgServiceI.sendRichTextMsg(msgReqDTO), ErrorCode.RPC_ERROR);
        // 发找回问题
        if (Objects.equals(RETRIEVE.getCode(),applyAfcRecordReqBO.getAfcType())) {
            sendQuestion(deliveryRoomId, mentionedId);
        }
        log.info("sendApplyAfcCard request:{} , response:{}", JSONObject.toJSONString(msgReqDTO), JSONObject.toJSONString(response));
        if (!response.isSuccess()) {
            log.error("sendApplyAfcCard fail request:{} , response:{}", JSONObject.toJSONString(msgReqDTO), JSONObject.toJSONString(response));
        }
    }


    /**
     * 发送问题
     * 添加流程信息
     *
     * @param roomId  : 房间id
     * @param userId  :用户id
     */
    public void sendQuestion(String roomId, String userId) {
        log.info("sendQuestionAuto,userId:{},roomId:{}", userId, roomId);
        // 1、参数校验
        AssSchedule schedule = assScheduleRepository.getByRoomId(roomId);
        Ensure.that(schedule).isNotNull(ErrorCode.ROOM_NOT_EXISTS);

        boolean sendRes = assQuestionOptionDomainService.sendQuestionByGame(schedule, userId);

        List<AssScheduleLog> logList = assScheduleLogDomainService.findListByScheduleId(schedule.getScheduleId());
        long afterSaleCount =
            logList.stream().filter(o -> Objects.equals(o.getNodeId(), AssScheduleNode.INITIATE_AFTER_SALE.name())
                && Objects.equals(o.getNodeDesc(), "发起售后申请：找回")).count();
        long sendCount =
            logList.stream().filter(o -> Objects.equals(o.getNodeId(), AssScheduleNode.SEND_QUESTION.name())).count();
        long l = afterSaleCount - sendCount;
        if (NumberUtil.between(l, 1, 2)) {
            // 2、添加流程信息
            if (sendRes) {
                assScheduleDomainService.updateFinishById(schedule.getId(), AssScheduleNode.SEND_QUESTION.isFinish(),
                    userId);
                assScheduleLogDomainService.add(schedule.getScheduleId(), AssScheduleNode.SEND_QUESTION, "",
                    AssWoType.RETRIEVE, userId, "");
            } else {
                assScheduleDomainService.updateFinishById(schedule.getId(),
                    AssScheduleNode.SEND_QUESTION_FAIL.isFinish(), userId);
                assScheduleLogDomainService.add(schedule.getScheduleId(), AssScheduleNode.SEND_QUESTION_FAIL, "",
                    AssWoType.RETRIEVE, userId, "");
            }
        }
    }
}
