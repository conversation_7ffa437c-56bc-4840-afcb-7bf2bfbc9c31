package com.pxb7.mall.trade.ass.domain.refund.model;

import com.alibaba.cola.dto.DTO;
import com.pxb7.mall.trade.ass.infra.model.Money;
import lombok.*;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

@Setter
@Getter
@Accessors(chain = true)
@ToString
public class RefundQueryRespBO extends DTO {
    /**
     * 退款的 innerTradeNo(连连、支付宝需要)
     */
    private String refundTradeNo;
    /**
     * 退款第三方单号
     */
    private String refundOutTradeNo;
    /**
     * 2执行中 3执行成功 4执行失败 999 已执行
     */
    private Integer refundTradeStatus;
    /**
     * 结果字符串
     */
    private String result;
    /**
     * 退款成功金额
     */
    private Money refundAmount;
    /**
     * 退款成功时间
     */
    private LocalDateTime successTime;

    /**
     * 已退款金额
     */
    private Long alreadyRefundAmount;

}
