package com.pxb7.mall.trade.ass.domain.violate;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.pxb7.mall.trade.ass.client.enums.violate.ViolateChannelTypeEnum;
import com.pxb7.mall.trade.ass.client.enums.violate.ViolatePayRecordStatusEnum;
import com.pxb7.mall.trade.ass.client.enums.violate.ViolatePayTypeEnum;
import com.pxb7.mall.trade.ass.infra.enums.ErrorCode;
import com.pxb7.mall.trade.ass.infra.model.ViolatePayRecordReqPO;
import com.pxb7.mall.trade.ass.infra.repository.db.ViolatePayRecordRepository;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.ViolateOrder;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.ViolatePayRecord;
import com.pxb7.mall.trade.ass.infra.util.IdGenUtil;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

import static com.pxb7.mall.trade.ass.client.enums.violate.ViolatePayTypeEnum.DEDUCTION;
import static com.pxb7.mall.trade.ass.client.enums.violate.ViolatePayTypeEnum.TRANSFER;
import static com.pxb7.mall.trade.ass.infra.enums.ErrorCode.VIOLATE_REPEAT_PAY_RECORD_ERROR;
import static com.pxb7.mall.trade.ass.infra.enums.ErrorCode.VIOLATE_UPDATE_PAY_RECORD_ERROR;

@Service
public class ViolatePayRecordDomainService {
    @Resource
    private ViolatePayRecordRepository violatePayRecordRepository;


    public ViolatePayRecord findEffectiveByViolateId(String violateId, Integer type) {
        ViolatePayRecordReqPO.SearchPO searchPO = new ViolatePayRecordReqPO.SearchPO()
                .setViolateId(violateId)
                .setType(type)
                .setStatusList(ViolatePayRecordStatusEnum.getEffectiveStatus());
        List<ViolatePayRecord> list = violatePayRecordRepository.list(searchPO);
        if (CollUtil.isEmpty(list)) {
            return null;
        }
        return CollUtil.getFirst(list);
    }

    // 违约-扣款
    public ViolatePayRecord deduction(ViolateOrder violateOrder, Integer channel) {
        Assert.isNull(findEffectiveByViolateId(violateOrder.getViolateId(), DEDUCTION.getValue()), VIOLATE_REPEAT_PAY_RECORD_ERROR.getErrDesc());
        return create(violateOrder, DEDUCTION.getValue(), channel);
    }

    // 违约-打款
    public ViolatePayRecord transfer(ViolateOrder violateOrder) {
        return create(violateOrder, TRANSFER.getValue(), ViolateChannelTypeEnum.WALLET.getValue());
    }

    // 创建违约支付单:扣款 or 打款
    private ViolatePayRecord create(ViolateOrder violateOrder, Integer type, Integer channel) {
        String PREX = ViolatePayTypeEnum.getPrex(type);
        Long amount = DEDUCTION.eq(type) ? violateOrder.getViolateAmount() : violateOrder.getPromiseAmount();
        String userId = DEDUCTION.eq(type) ? violateOrder.getViolateUserId() : violateOrder.getPromiseUserId();

        ViolatePayRecord record = new ViolatePayRecord()
                .setRecordId(PREX + IdGenUtil.getShardingColumnId(violateOrder.getViolateId()))
                .setViolateId(violateOrder.getViolateId())
                .setOrderItemId(violateOrder.getOrderItemId())
                .setUserId(userId)
                .setType(type)
                .setChannel(channel)
                .setAmount(amount)
                .setCreateUserId(violateOrder.getCreateUserId())
                .setCreateUsername(violateOrder.getCreateUsername());
        Assert.isTrue(violatePayRecordRepository.save(record), ErrorCode.VIOLATE_CREATE_PAY_RECORD_ERROR.getErrDesc());
        return record;
    }

    public ViolatePayRecord getDetailById(String payRecordId) {
        ViolatePayRecordReqPO.SearchPO searchPO = new ViolatePayRecordReqPO.SearchPO()
                .setRecordId(payRecordId);
        return violatePayRecordRepository.find(searchPO);
    }

    // 取消违约支付单-非完结的状态
    public void cancelPayRecord(String payRecordId, String remark) {
        LambdaUpdateWrapper<ViolatePayRecord> update = new LambdaUpdateWrapper<ViolatePayRecord>()
                .eq(ViolatePayRecord::getRecordId, payRecordId)
                .notIn(ViolatePayRecord::getStatus, ViolatePayRecordStatusEnum.getCompleted())
                .set(ViolatePayRecord::getRemark, remark)
                .set(ViolatePayRecord::getCancelTime, LocalDateTime.now())
                .set(ViolatePayRecord::getStatus, ViolatePayRecordStatusEnum.CANCEL.getValue());
        Assert.isTrue(violatePayRecordRepository.update(update), ErrorCode.VIOLATE_CANCEL_PAY_RECORD_ERROR.getErrDesc());
    }

    // 违约单支付处理成功
    public void paid(String recordId, String outTradeNo) {
        LambdaUpdateWrapper<ViolatePayRecord> update = new LambdaUpdateWrapper<ViolatePayRecord>()
                .eq(ViolatePayRecord::getRecordId, recordId)
                .notIn(ViolatePayRecord::getStatus, ViolatePayRecordStatusEnum.getCompleted())
                .set(ViolatePayRecord::getOutTradeNo, outTradeNo)
                .set(ViolatePayRecord::getStatus, ViolatePayRecordStatusEnum.SUCCESS.getValue())
                .set(ViolatePayRecord::getCompletedTime, LocalDateTime.now());
        Assert.isTrue(violatePayRecordRepository.update(update), VIOLATE_UPDATE_PAY_RECORD_ERROR.getErrDesc());
    }

    public ViolatePayRecord findDealingByOrderId(String orderItemId, Integer type) {
        ViolatePayRecordReqPO.SearchPO searchPO = new ViolatePayRecordReqPO.SearchPO()
                .setOrderItemId(orderItemId)
                .setType(type)
                .setStatusList(ViolatePayRecordStatusEnum.getDealing());
        List<ViolatePayRecord> list = violatePayRecordRepository.list(searchPO);
        if (CollUtil.isEmpty(list)) {
            return null;
        }
        return CollUtil.getFirst(list);
    }


}
