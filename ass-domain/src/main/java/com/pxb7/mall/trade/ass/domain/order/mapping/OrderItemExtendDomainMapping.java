package com.pxb7.mall.trade.ass.domain.order.mapping;

import com.pxb7.mall.trade.ass.domain.order.model.OrderItemExtendBO;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.OrderItemExtend;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "Spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface OrderItemExtendDomainMapping {

    OrderItemExtendBO entityToBO(OrderItemExtend orderItemExtend);
}
