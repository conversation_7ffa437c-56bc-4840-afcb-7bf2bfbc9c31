package com.pxb7.mall.trade.ass.domain.violate.constant;

public class OrderViolateConstant {
    // im 消息标题
    public static final String ORDER_VIOLATE_BC_TITLE = "平台补偿发放";
    public static final String ORDER_VIOLATE_KK_TITLE = "平台收取违约金";

    // im消息模板
    public static final String ORDER_BC_BUYER_MSG = "因卖家在本次交易中违约，平台补偿的%s元已发送到你的钱包账户。";
    public static final String ORDER_BC_SELLER_MSG = "因买家在本次交易中违约，平台补偿的%s元已发送到你的钱包账户。";
    public static final String ORDER_KK_SELLER_MSG = "因卖家在本次交易中违约，你的钱包账户支出%s元抵扣违约金缴款。";
    public static final String ORDER_KK_BUYER_MSG = "因买家在本次交易中违约，你的钱包账户支出%s元抵扣违约金缴款。";


    // 超时中断 默认24h
    public static final Long VIOLATE_RECEIPT_STOP = 86400L;



}
