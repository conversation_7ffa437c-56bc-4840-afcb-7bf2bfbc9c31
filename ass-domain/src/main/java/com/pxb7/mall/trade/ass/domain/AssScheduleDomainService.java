package com.pxb7.mall.trade.ass.domain;

import java.util.Objects;

import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.alibaba.cola.exception.BizException;
import com.pxb7.mall.components.idgen.IdGen;
import com.pxb7.mall.trade.ass.client.enums.AssScheduleNode;
import com.pxb7.mall.trade.ass.client.enums.AssWoType;
import com.pxb7.mall.trade.ass.domain.mapping.assSchedule.AssScheduleDomainMapping;
import com.pxb7.mall.trade.ass.domain.model.assSchedule.ReqCreateAssScheduleBO;
import com.pxb7.mall.trade.ass.domain.model.reqeust.AssScheduleReqBO;
import com.pxb7.mall.trade.ass.domain.model.response.AssScheduleRespBO;
import com.pxb7.mall.trade.ass.infra.enums.ErrorCode;
import com.pxb7.mall.trade.ass.infra.repository.db.AssScheduleRepository;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.AssSchedule;
import com.pxb7.mall.trade.ass.infra.util.IdGenUtil;

import cn.hutool.core.util.ObjUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class AssScheduleDomainService {
    @Resource
    private IdGen idGen;
    @Resource
    private AssScheduleRepository assScheduleRepository;
    @Resource
    private AssScheduleLogDomainService assScheduleLogDomainService;

    public String record(AssScheduleReqBO bo, String userId) {
        AssSchedule as = assScheduleRepository.findOneByOrderItemIdAndRoomId(bo.getOrderNo(), bo.getRoomId());
        if (as == null) {
            as = new AssSchedule().setScheduleId(idGen.nextId() + "").setOrderItemId(bo.getOrderNo())
                .setWorkOrderId(bo.getWorkOrderId()).setAssType(bo.getAssType().getCode())
                .setRecvCustomerId(bo.getRecvCustomerId()).setAuditCustomerId(bo.getAuditCustomerId())
                .setRoomId(bo.getRoomId()).setFinish(bo.getNode().isFinish()).setSourceType(bo.getSourceType())
                .setCreateUserId(userId).setUpdateUserId(userId);
            assScheduleRepository.save(as);
        } else {
            assert bo.getAssType() != null;
            assScheduleRepository.lambdaUpdate().eq(AssSchedule::getId, as.getId())
                .set(StringUtils.isNotBlank(bo.getWorkOrderId()), AssSchedule::getWorkOrderId, bo.getWorkOrderId())
                .set(bo.getAssType() != null, AssSchedule::getAssType, bo.getAssType().getCode())
                .set(StringUtils.isNotBlank(bo.getRecvCustomerId()), AssSchedule::getRecvCustomerId,
                    bo.getRecvCustomerId())
                .set(StringUtils.isNotBlank(bo.getAuditCustomerId()), AssSchedule::getAuditCustomerId,
                    bo.getAuditCustomerId())
                .set(AssSchedule::getFinish, bo.getNode().isFinish()).set(AssSchedule::getUpdateUserId, userId)
                .update();
        }
        return as.getScheduleId();
    }

    public String saveAssSchedule(ReqCreateAssScheduleBO bo) {
        AssSchedule assSchedule = AssScheduleDomainMapping.INSTANCE.toAssSchedule(bo);
        assSchedule.setScheduleId(IdGenUtil.generateId());
        assSchedule.setRecvCustomerId(bo.getRecvCustomerId());
        assSchedule.setOrderItemId(bo.getOrderNo());
        assSchedule.setRoomId(bo.getRoomId());
        assSchedule.setCreateUserId(bo.getLoginUserId());
        assSchedule.setUpdateUserId(bo.getLoginUserId());
        assSchedule.setFinish(Boolean.FALSE);
        assSchedule.setSourceType(bo.getSourceType());
        assScheduleRepository.save(assSchedule);
        assScheduleLogDomainService.record(assSchedule.getScheduleId(), bo.getAssType(), bo.getLoginUserId(),
                bo.getSourceType());
        return assSchedule.getScheduleId();
    }

    public AssScheduleRespBO find(String roomId) {
        AssSchedule as = assScheduleRepository.getByRoomId(roomId);
        if (as == null) {
            return null;
        }
        AssScheduleRespBO bo = new AssScheduleRespBO();
        bo.setOrderNo(as.getOrderItemId());
        bo.setScheduleId(as.getScheduleId());
        bo.setWorkOrderId(as.getWorkOrderId());
        bo.setAssType(AssWoType.fromCode(as.getAssType()));
        bo.setRecvCustomerId(as.getRecvCustomerId());
        bo.setAuditCustomerId(as.getAuditCustomerId());
        bo.setRoomId(as.getRoomId());
        bo.setFinish(as.getFinish());
        return bo;
    }

    public AssSchedule getAssSchedule(String orderItemId) {
        AssSchedule assSchedule = assScheduleRepository.lambdaQuery().eq(AssSchedule::getOrderItemId, orderItemId)
            .orderByDesc(AssSchedule::getId).last("limit 1").one();
        if (ObjUtil.isEmpty(assSchedule)) {
            String outMessage = orderItemId + ErrorCode.NO_DATA_MSG.getErrDesc();
            log.error(outMessage);
            throw new BizException(ErrorCode.NO_DATA_MSG.getErrCode(), outMessage);
        }
        return assSchedule;
    }

    /**
     * 完成节点更新
     */
    public void updateFinishByScheduleId(String scheduleId, AssScheduleNode node) {
        if (StringUtils.isNotBlank(scheduleId) && Objects.nonNull(node) && BooleanUtils.isTrue(node.isFinish())) {
            assScheduleRepository.lambdaUpdate().eq(AssSchedule::getScheduleId, scheduleId)
                .set(AssSchedule::getFinish, node.isFinish()).update();
        }
    }

    public boolean updateWorkOrderIdAndAssTypeAndFinishById(Long id, String workOrderId, Integer assType,
        Boolean finish, String updateUserId) {
        if (Objects.isNull(id) || StringUtils.isBlank(workOrderId) || Objects.isNull(assType) || Objects.isNull(finish)
            || StringUtils.isBlank(updateUserId)) {
            return false;
        }
        return assScheduleRepository.updateWorkOrderIdAndAssTypeAndFinishById(id, workOrderId, assType, finish,
            updateUserId);
    }

    public boolean updateFinishById(Long id, Boolean finish, String updateUserId) {
        if (Objects.isNull(id) || Objects.isNull(finish) || StringUtils.isBlank(updateUserId)) {
            return false;
        }
        return assScheduleRepository.updateFinishById(id, finish, updateUserId);
    }

    public boolean updateById(Long id, AssScheduleReqBO bo, String userId) {
        assert bo.getAssType() != null;
        return assScheduleRepository.lambdaUpdate().eq(AssSchedule::getId, id)
            .set(AssSchedule::getAssType, bo.getAssType().getCode())
            .set(AssSchedule::getRecvCustomerId, bo.getRecvCustomerId())
            .set(StringUtils.isNotBlank(bo.getAuditCustomerId()), AssSchedule::getAuditCustomerId,
                bo.getAuditCustomerId())
            .set(AssSchedule::getFinish, bo.getNode().isFinish()).set(AssSchedule::getUpdateUserId, userId).update();
    }

    public void updateRecvCustomerId(Long id, String recvCustomerId) {
        if (StringUtils.isBlank(recvCustomerId)) {
            return;
        }
        assScheduleRepository.lambdaUpdate().eq(AssSchedule::getId, id)
            .set(AssSchedule::getRecvCustomerId, recvCustomerId).update();
    }
}
