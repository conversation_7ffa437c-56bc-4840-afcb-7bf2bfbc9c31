package com.pxb7.mall.trade.ass.domain.refund.model;

import java.io.Serializable;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 客服Im发起退款请求
 */
@Data
@Accessors(chain = true)
public class ServiceSubmitRefundBO implements Serializable {
    /**
     * 订单行id
     */
    private String orderItemId;

    /**
     * 退款单id，审核时传入，发起时为空
     */
    private String refundVoucherId;

    /**
     * 退款原因
     */
    private String refundReasonId;

    /**
     * 退款金额,整单退款时可以为空
     */
    private Long refundAmount;

    /**
     * 退款类别，1，整单退款 2，退商品差价
     */
    private Integer wholeRefund;

    /**
     * 退款类型，1，在线退款，2，线下退款，3，挂账
     */
    private Integer refundType;
    /**
     * 费用承担方
     */
    private Integer responseUser;

}
