package com.pxb7.mall.trade.ass.domain.mapping;

import com.pxb7.mall.trade.ass.domain.model.reqeust.RecordBO;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.AssFollowupLog;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 售后跟进记录
 *
 * <AUTHOR>
 * @since: 2024-10-14 13:49
 **/
@Mapper
public interface AssFollowupLogDomainMapping {
    AssFollowupLogDomainMapping INSTANCE = Mappers.getMapper(AssFollowupLogDomainMapping.class);

    AssFollowupLog toEntity(RecordBO recordBO);

}
