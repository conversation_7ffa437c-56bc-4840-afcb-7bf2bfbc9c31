package com.pxb7.mall.trade.ass.domain.mapping;

import com.pxb7.mall.trade.ass.domain.model.AssRejectReasonConfigReqBO;
import com.pxb7.mall.trade.ass.domain.model.AssRejectReasonConfigRespBO;
import com.pxb7.mall.trade.ass.infra.model.AssRejectReasonConfigReqPO;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.AssRejectReasonConfig;

import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;


@Mapper
public interface AssRejectReasonConfigDomainMapping {

    AssRejectReasonConfigDomainMapping INSTANCE = Mappers.getMapper(AssRejectReasonConfigDomainMapping.class);


    AssRejectReasonConfigReqPO.AddPO assRejectReasonConfigBO2AddPO(AssRejectReasonConfigReqBO.AddBO source);

    AssRejectReasonConfigReqPO.UpdatePO assRejectReasonConfigBO2UpdatePO(AssRejectReasonConfigReqBO.UpdateBO source);

    AssRejectReasonConfigReqPO.DelPO assRejectReasonConfigBO2DelPO(AssRejectReasonConfigReqBO.DelBO source);

    AssRejectReasonConfigReqPO.SearchPO assRejectReasonConfigBO2SearchPO(AssRejectReasonConfigReqBO.SearchBO source);

    AssRejectReasonConfigReqPO.PagePO assRejectReasonConfigBO2PagePO(AssRejectReasonConfigReqBO.PageBO source);

    AssRejectReasonConfigRespBO.DetailBO assRejectReasonConfigPO2DetailBO(AssRejectReasonConfig source);

    List<AssRejectReasonConfigRespBO.DetailBO> assRejectReasonConfigPO2ListBO(List<AssRejectReasonConfig> source);

    Page<AssRejectReasonConfigRespBO.DetailBO> assRejectReasonConfigPO2PageBO(Page<AssRejectReasonConfig> source);

}


