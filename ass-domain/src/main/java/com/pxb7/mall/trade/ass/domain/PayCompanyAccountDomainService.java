package com.pxb7.mall.trade.ass.domain;

import com.pxb7.mall.trade.ass.infra.config.AESSecretConfig;
import com.pxb7.mall.trade.ass.infra.repository.db.PayCompanyAccountRepository;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.PayCompanyAccount;
import com.pxb7.mall.trade.order.client.utils.AESSecretUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Slf4j
@Service
public class PayCompanyAccountDomainService {

    @Resource
    private PayCompanyAccountRepository payCompanyAccountRepository;
    @Resource
    private AESSecretConfig aesSecretConfig;


    /**
     * 根据账号ID 获取解密后的 三方支付参数
     */
    public String getDecryptOutParamByAccountId(String accountId) {
        PayCompanyAccount account = payCompanyAccountRepository.getCompanyAccountById(accountId);
        return getDecryptOutParamByAccount(account);
    }

    /**
     * 获取解密后的 三方支付参数
     */
    private String getDecryptOutParamByAccount(PayCompanyAccount account) {
        if (Objects.isNull(account)) {
            return null;
        }
        return AESSecretUtil.decrypt(account.getOutPaymentParam(), aesSecretConfig.secretKey);
    }

    public String getDecryptOutParamByAccount(String companyAccount) {
        PayCompanyAccount account = payCompanyAccountRepository.findCompanyAccountByAccount(companyAccount);
        return getDecryptOutParamByAccount(account);
    }

}
