package com.pxb7.mall.trade.ass.domain.refund.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class RefundVoucherBO {

    /**
     * 主键
     */
    private Long id;
    /**
     * 订单行id
     */
    private String orderItemId;
    /**
     * 退款单id
     */
    private String refundVoucherId;
    /**
     * 商品类型: 1账号 2充值 3金币 4装备 5初始号 20诚心卖曝光劵 21诚心卖服务
     */
    private Integer productType;
    /**
     * 退款状态 1待审核, 2退款中, 3退款成功, 4退款失败, 5退款关闭
     */
    private Integer refundStatus;
    /**
     * 退款原因id
     */
    private String refundReasonId;
    /**
     * 退款原因
     */
    private String refundReason;
    /**
     * 退款类型 1 整单退款 2.退商品差价
     */
    private Integer wholeRefund;
    /**
     * 退款类型 1线上原路返回 2线下打款 3 挂账
     */
    private Integer refundType;
    /**
     * 使用的对应收款单id,线上退款时绑定
     */
    private String receiptVoucherId;
    /**
     * 对应支付单id，审核通过后绑定
     */
    private String paymentId;
    /**
     * 退款金额
     */
    private Long refundAmount;
    /**
     * 实际退款金额，退款完成后写入
     */
    private Long actualRefundAmount;
    /**
     * 提交人
     */
    private String submitter;
    /**
     * 1-用户,2-客服
     */
    private Integer submitterType;
    /**
     * 1-用户,2-客服,3-财务
     */
    private Integer closeType;
    /**
     * 客服拒绝原因
     */
    private String rejectReason;
    /**
     * 申请时间
     */
    private LocalDateTime applyTime;
    /**
     * 退款完成时间
     */
    private LocalDateTime finishTime;
    /**
     * 1支付宝 2微信 3银行卡
     */
    private Integer refundChannel;
    /**
     * 买家收款账户
     */
    private String refundAccount;
    /**
     * 买家姓名
     */
    private String buyerName;
    /**
     * 审核状态 1待客服审核,2待提交打款信息(线下打款才有),3待财务审核,4审核成功,5审核失败
     */
    private Integer auditStatus;
    /**
     * 财务审核人用户id
     */
    private String auditUser;
    /**
     * 财务审核人审核备注
     */
    private String auditRemark;
    /**
     * 财务审核截图
     */
    private String auditImg;
    /**
     * 打款人用户id
     */
    private String executeUser;
    /**
     * 打款账号名称
     */
    private String accountName;
    /**
     * 打款账号
     */
    private String companyAccount;
    /**
     * 扩展信息
     */
    private String extInfo;
    /**
     * 创建人id
     */
    private String createUserId;
    /**
     * 更新人id
     */
    private String updateUserId;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    /**
     * 是否删除
     */
    private Boolean deleted;
}
