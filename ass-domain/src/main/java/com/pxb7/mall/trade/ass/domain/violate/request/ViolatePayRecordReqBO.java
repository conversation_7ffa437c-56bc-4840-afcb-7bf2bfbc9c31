package com.pxb7.mall.trade.ass.domain.violate.request;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class ViolatePayRecordReqBO {
    /**
     * 订单ID
     */
    private String orderItemId;
    /**
     * 违约单ID
     */
    private String violateId;
    /**
     * 支付单号
     */
    private String payRecordId;
    /**
     * 扣款方式:1在线支付 2钱包
     */
    private Integer channel;

}