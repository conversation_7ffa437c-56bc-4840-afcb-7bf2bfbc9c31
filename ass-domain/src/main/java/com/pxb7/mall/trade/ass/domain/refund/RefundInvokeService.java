package com.pxb7.mall.trade.ass.domain.refund;

import com.alibaba.cola.exception.BizException;
import com.alibaba.cola.exception.SysException;
import com.alibaba.cola.extension.BizScenario;
import com.alibaba.cola.extension.ExtensionExecutor;
import com.alibaba.fastjson2.JSON;
import com.pxb7.mall.response.TraceHandler;
import com.pxb7.mall.trade.ass.client.dto.model.refund.PayChannelEnum;
import com.pxb7.mall.trade.ass.client.dto.model.refund.RefundTradeStatusEnum;
import com.pxb7.mall.trade.ass.domain.refund.invoke3rd.PayRefundExtPt;
import com.pxb7.mall.trade.ass.domain.refund.model.RefundInvokeReqBO;
import com.pxb7.mall.trade.ass.domain.refund.model.RefundInvokeRespBO;
import com.pxb7.mall.trade.ass.domain.refund.model.RefundQueryReqBO;
import com.pxb7.mall.trade.ass.domain.refund.model.RefundQueryRespBO;
import com.pxb7.mall.trade.ass.infra.constant.FeiShuWarningConstants;
import com.pxb7.mall.trade.ass.infra.enums.ErrorCode;
import com.pxb7.mall.trade.ass.infra.enums.RefundCallBackStatusEnum;
import com.pxb7.mall.trade.ass.infra.exception.RefundSysException;
import com.pxb7.mall.trade.ass.infra.repository.gateway.common.FeiShuGatewayRepository;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;
import java.util.function.Function;

@Slf4j
@Service
public class RefundInvokeService {
    @Resource
    private ExtensionExecutor extensionExecutor;
    @Resource
    private FeiShuGatewayRepository feiShuGatewayRepository;

    private static List<String> LIANLIAN_RETRY_MESSAGE_LIST = List.of("账户余额不足[112001]");

    public RefundInvokeRespBO doInvokeRefund(RefundInvokeReqBO reqBO) {
        log.info("[线上原路退款] 开始分发 V2，请求参数:{}", reqBO);
        Function<PayRefundExtPt, RefundInvokeRespBO> consumer = refundExt -> {
            try {
                return refundExt.refundV2(reqBO);
            } catch (RefundSysException e) {
                throw e;
            } catch (Exception e) {
                if(reqBO.isRetry()){
                    this.notifyMessage(reqBO, e);
                }
                this.wrapperException(reqBO, e);
                return RefundInvokeRespBO.builder()
                        .statusEnum(RefundCallBackStatusEnum.PROCESSING)
                        .build();
            }
        };

        PayChannelEnum payChannelEnum = PayChannelEnum.getEnum(reqBO.getPayChannel());
        return extensionExecutor.execute(PayRefundExtPt.class,
                BizScenario.valueOf(payChannelEnum.getRefundBizNo()), consumer);
    }

    public RefundQueryRespBO refundQuery(RefundQueryReqBO reqBO) {
        log.info("[退款结果查询] 开始分发:{}", reqBO);
        Function<PayRefundExtPt, RefundQueryRespBO> function = refundExt -> {
            try {
                // 调用退款查询接口，接口内部成功/失败/异常
                return refundExt.refundQueryV2(reqBO);
            } catch (Exception e) {
                log.error("调用退款查询接口异常,请求参数:{}", reqBO, e);
                RefundQueryRespBO queryRespBO = new RefundQueryRespBO();
                queryRespBO.setRefundTradeStatus(RefundTradeStatusEnum.EXECUTING.getValue());
                return queryRespBO;
            }
        };
        PayChannelEnum payChannelEnum = PayChannelEnum.getEnum(reqBO.getPayChannel());
        return extensionExecutor.execute(PayRefundExtPt.class,
                BizScenario.valueOf(payChannelEnum.getRefundBizNo()), function);
    }

    private void wrapperException(RefundInvokeReqBO reqBO, Exception e) {
        if (null == reqBO) {
            return;
        }
        PayChannelEnum payChannelEnum = PayChannelEnum.getEnum(reqBO.getPayChannel());
        if (PayChannelEnum.LIANLIANPAY.equals(payChannelEnum)) {
            //连连判定指定异常信息重试
            String expMessage = Optional.ofNullable(e).map(Exception::getMessage).orElse(null);
            if(StringUtils.isEmpty(expMessage)){
                return;
            }
            if(LIANLIAN_RETRY_MESSAGE_LIST.contains(expMessage)){
                log.info("[退款异常转换] 退款渠道：{},请求参数:{}",
                    payChannelEnum.getLabel(), JSON.toJSONString(reqBO), e);
                throw new RefundSysException(ErrorCode.REFUND_NEED_RETRY);
            }
        }else if(PayChannelEnum.TSPAY.equals(payChannelEnum)){
            //泰山判定异常类型进行重试
            boolean timeout = e instanceof SysException && e.getMessage().equals("timeout");
            boolean badGateway = e instanceof BizException && e.getMessage().equals("Bad Gateway");
            if(timeout || badGateway){
                log.info("[退款异常转换] 退款渠道：{},请求参数:{}",
                    payChannelEnum.getLabel(), JSON.toJSONString(reqBO), e);
                throw new RefundSysException(ErrorCode.TS_REFUND_CHANNEL_SYS_ERROR);
            }
        }
    }

    /**
     * 告警通知
     */
    private void notifyMessage(RefundInvokeReqBO reqBO, Throwable e){
        //退款渠道 1：支付宝官方直连 2: 连连支付 3:泰山支付
        PayChannelEnum payChannelEnum = PayChannelEnum.getEnum(reqBO.getPayChannel());

        log.error("调用退款接口异常,退款渠道：{},请求参数:{}", payChannelEnum.getLabel(), reqBO, e);
        //发送飞书预警消息
        feiShuGatewayRepository.sendTextMessage(FeiShuWarningConstants.REFUND_ERROR_MESSAGE_TEMPLATE_V2,
            payChannelEnum.getLabel(),
            reqBO.getRefundTradeNo(),
            TraceHandler.getTraceId(),
            e.getMessage());
    }
}
