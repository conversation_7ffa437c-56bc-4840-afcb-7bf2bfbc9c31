package com.pxb7.mall.trade.ass.domain;


import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.fastjson.JSON;
import com.pxb7.mall.response.PxPageResponse;
import com.pxb7.mall.trade.ass.client.enums.*;
import com.pxb7.mall.trade.ass.domain.model.AssWorkOrderLogReqBO;
import com.pxb7.mall.trade.ass.domain.model.reqeust.RecordBO;
import com.pxb7.mall.trade.ass.infra.model.AssInfoReqPO;
import com.pxb7.mall.trade.ass.infra.model.AssWorkOrderLogReqPO;
import com.pxb7.mall.trade.ass.infra.repository.db.AssWorkOrderLogRepository;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.AssSchedule;
import com.pxb7.mall.trade.ass.infra.repository.es.entity.AssInfoAggregation;
import com.pxb7.mall.trade.ass.infra.repository.es.impl.AssInfoEsRepository;
import com.pxb7.mall.trade.ass.infra.util.IdGenUtil;
import com.pxb7.mall.trade.ass.infra.util.OptionalUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Service;

import com.pxb7.mall.trade.ass.domain.model.AssWorkOrderReqBO;
import com.pxb7.mall.trade.ass.domain.model.AssWorkOrderRespBO;
import com.pxb7.mall.trade.ass.domain.mapping.AssWorkOrderDomainMapping;
import com.pxb7.mall.trade.ass.infra.model.AssWorkOrderReqPO;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.AssWorkOrder;
import com.pxb7.mall.trade.ass.infra.repository.db.AssWorkOrderRepository;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.util.CollectionUtils;

import static com.pxb7.mall.trade.ass.client.enums.AssWoType.RETRIEVE;


/**
 * 售后工单domain服务
 *
 * <AUTHOR>
 * @since 2025-07-30 13:47:52
 */
@Service
@Slf4j
public class AssWorkOrderDomainService {

    @Resource
    private AssWorkOrderRepository assWorkOrderRepository;


    @Resource
    private AssWorkOrderLogRepository assWorkOrderLogRepository;

    @Resource
    private AssInfoEsRepository assInfoEsRepository;


    /**
     * 转接操作
     * @param assUserOperatorLog 操作类型
     * @param operatorId 操作人
     * @param scheduleId 进度id
     */
    public void transfer(AssUserOperatorLog assUserOperatorLog, String operatorId, String scheduleId) {
        AssWorkOrder assWorkOrder = assWorkOrderRepository.findByScheduleId(scheduleId);
        if (Objects.isNull(assWorkOrder)) {
            log.warn("售后工单不存在 transfer scheduleId:{}", scheduleId);
            return;
        }

        AssWorkOrderLogReqPO.AddPO addPO = new AssWorkOrderLogReqPO.AddPO()
                .setWorkOrderId(assWorkOrder.getWorkOrderId())
                .setWorkOrderLogId(IdGenUtil.generateId())
                .setOrderItemId(assWorkOrder.getOrderItemId())
                .setTitle(assUserOperatorLog.getUserDesc())
                .setContent(AssUserOperatorLog.TRANSFER_DISPUTE.getUserDesc())
                .setShowType(AssShowType.ALL.getCode())
                .setCreateUserId(operatorId)
                .setUpdateUserId(operatorId)
                .setAdminContent(AssUserOperatorLog.TRANSFER_DISPUTE.getAdminDesc());

        if (Objects.equals(assUserOperatorLog, AssUserOperatorLog.TRANSFER_DISPUTE)) {
            //更新售后单状态为转接纠纷,已读状态
            assWorkOrderRepository.update(new AssWorkOrderReqPO.UpdatePO()
                    .setId(assWorkOrder.getId())
                    .setReadFlag(false)
                    .setAssType(AssWoType.DISPUTE.getCode()));
            addPO.setAssType(AssWoType.DISPUTE.getCode());

        }


        if (Objects.equals(assUserOperatorLog, AssUserOperatorLog.TRANSFER_RETRIEVE)) {
            //转接找回
            assWorkOrderRepository.update(new AssWorkOrderReqPO.UpdatePO()
                    .setId(assWorkOrder.getId())
                    .setReadFlag(false)
                    .setAssType(AssWoType.RETRIEVE.getCode()));
            addPO.setAssType(AssWoType.RETRIEVE.getCode());
        }
        log.info("transfer addPO :{}", JSON.toJSONString(addPO));
        assWorkOrderLogRepository.insert(addPO);
    }



    /**
     * 创建工单(im客服端)
     * @param recordBO
     * @param schedule
     */
    public void createWorkOrder(RecordBO recordBO, AssSchedule schedule) {
        AssWorkOrder assWorkOrder = getAssWorkOrder(schedule);
        if (assWorkOrder == null) return;
        String desc = AssWoType.fromCode(assWorkOrder.getAssType()).getDesc();
        String content = StrUtil.format(AssUserOperatorLog.ADD_WORK_ORDER.getUserDesc(), desc);
        AssWorkOrderLogReqPO.AddPO addBO = new AssWorkOrderLogReqPO.AddPO()
                .setWorkOrderLogId(IdGenUtil.generateId())
                .setWorkOrderId(assWorkOrder.getWorkOrderId())
                .setOrderItemId(schedule.getOrderItemId())
                .setContent(content)
                .setAdminContent(recordBO.getContent())
                .setAddWay(recordBO.getAddWay())
                .setNodeId(AssScheduleNode.CREATE_WORK_ORDER.name())
                .setNodeDesc(AssScheduleNode.CREATE_WORK_ORDER.getDesc() + desc)
                .setShowType(AssShowType.ALL.getCode())
                .setAssType(assWorkOrder.getAssType())
                .setCreateUserId(recordBO.getOperatorId())
                .setUpdateUserId(recordBO.getOperatorId())
                .setTitle(recordBO.getTitle());
        assWorkOrderLogRepository.insert(addBO);
        log.info("im createWorkOrder ：{}", com.alibaba.fastjson.JSON.toJSONString(addBO));

    }



    /**
     * (用户-发起售后申请)
     * @param schedule
     */
    public void applyAfc(AssSchedule schedule) {
        AssWorkOrder assWorkOrder = getAssWorkOrder(schedule);
        if (assWorkOrder == null) {
            assWorkOrder = new AssWorkOrder()
                    .setWorkOrderId(IdGenUtil.generateId())
                    .setOrderItemId(schedule.getOrderItemId())
                    .setScheduleId(schedule.getScheduleId())
                    .setAssType(schedule.getAssType())
                    .setAssStatus(AssWorkOrderStatusEnum.PROCESS.getCode())
                    .setApplyTime(LocalDateTime.now());
        }
        String desc = AssWoType.fromCode(assWorkOrder.getAssType()).getDesc();
        String content = StrUtil.format(AssUserOperatorLog.USER_ADD_ASS.getUserDesc(), desc);
        AssWorkOrderLogReqPO.AddPO addBO = new AssWorkOrderLogReqPO.AddPO()
                .setWorkOrderLogId(IdGenUtil.generateId())
                .setWorkOrderId(assWorkOrder.getWorkOrderId())
                .setOrderItemId(schedule.getOrderItemId())
                .setContent(content)
                .setAdminContent(AssUserOperatorLog.USER_ADD_ASS.getAdminDesc())
                .setAddWay(1)
                .setNodeId(AssScheduleNode.INITIATE_AFTER_SALE.name())
                .setNodeDesc(AssScheduleNode.INITIATE_AFTER_SALE.getDesc() + desc)
                .setShowType(AssShowType.ALL.getCode())
                .setAssType(assWorkOrder.getAssType())
                .setCreateUserId(schedule.getCreateUserId())
                .setUpdateUserId(schedule.getCreateUserId());
        assWorkOrderLogRepository.insert(addBO);
        log.info("user applyAfc ：{}", com.alibaba.fastjson.JSON.toJSONString(addBO));
    }

    public void setDealUser(RecordBO recordBO, AssSchedule schedule) {
        AssWorkOrder assWorkOrder = getAssWorkOrder(schedule);
        if (assWorkOrder == null) return;
        AssWorkOrderLogReqPO.AddPO addBO = new AssWorkOrderLogReqPO.AddPO()
                .setWorkOrderLogId(IdGenUtil.generateId())
                .setWorkOrderId(assWorkOrder.getWorkOrderId())
                .setOrderItemId(schedule.getOrderItemId())
                .setContent(AssUserOperatorLog.SET_DEAL_USER.getUserDesc())
                .setAdminContent(recordBO.getContent())
                .setAddWay(recordBO.getAddWay())
                .setShowType(AssShowType.ALL.getCode())
                .setAssType(assWorkOrder.getAssType())
                .setCreateUserId(recordBO.getOperatorId())
                .setUpdateUserId(recordBO.getOperatorId())
                .setTitle(recordBO.getTitle());
        assWorkOrderLogRepository.insert(addBO);
        log.info("平台分配售后专员跟进：{}", com.alibaba.fastjson2.JSON.toJSONString(addBO));

    }


    /**
     * 关闭工单(im客服端)
     * @param recordBO
     * @param schedule
     */
    public void closeWorkOrder(RecordBO recordBO, AssSchedule schedule) {
        AssWorkOrder assWorkOrder = getAssWorkOrder(schedule);
        if (assWorkOrder == null) return;
        AssWorkOrderReqPO.UpdatePO updatePO = new AssWorkOrderReqPO.UpdatePO()
                .setId(assWorkOrder.getId())
                .setReadFlag(false)
                .setCompleteTime(LocalDateTime.now())
                .setAssStatusMemo(AssUserOperatorLog.IM_CLOSE_ORDER.getUserDesc())
                .setAssStatus(AssWorkOrderStatusEnum.FINISH.getCode());
        assWorkOrderRepository.update(updatePO);
        log.info("im closeWorkOrder ：{}", com.alibaba.fastjson.JSON.toJSONString(updatePO));
    }


    public void addProof(RecordBO recordBO, AssSchedule schedule) {
        AssWorkOrder assWorkOrder = getAssWorkOrder(schedule);
        if (assWorkOrder == null) return;

        AssWorkOrderLogReqPO.AddPO addBO = new AssWorkOrderLogReqPO.AddPO()
                .setWorkOrderLogId(IdGenUtil.generateId())
                .setWorkOrderId(assWorkOrder.getWorkOrderId())
                .setOrderItemId(schedule.getOrderItemId())
                .setContent(AssUserOperatorLog.ADD_PROOF.getUserDesc())
                .setAdminContent(recordBO.getContent())
                .setAddWay(recordBO.getAddWay())
                .setNodeId(AssScheduleNode.UPLOAD_EVIDENCE.getId())
                .setNodeDesc(AssScheduleNode.UPLOAD_EVIDENCE.getDesc())
                .setShowType(AssShowType.ALL.getCode())
                .setAssType(RETRIEVE.getCode())
                .setCreateUserId(recordBO.getOperatorId())
                .setUpdateUserId(recordBO.getOperatorId())
                .setTitle(recordBO.getTitle());
        assWorkOrderLogRepository.insert(addBO);
        log.info("addProof：{}", com.alibaba.fastjson2.JSON.toJSONString(addBO));

    }


    @Nullable
    public AssWorkOrder getAssWorkOrder(AssSchedule schedule) {
        if (StrUtil.isBlank(schedule.getScheduleId())) {
            log.warn("scheduleId is null", JSON.toJSONString(schedule));
            return null;
        }
        AssWorkOrder assWorkOrder = assWorkOrderRepository.findByScheduleId(schedule.getScheduleId());
        if (Objects.isNull(assWorkOrder)) {
            log.warn("售后工单不存在 scheduleId:{}", schedule.getScheduleId());
            return null;
        }
        return assWorkOrder;
    }

    public boolean insert(AssWorkOrderReqBO.AddBO param) {
        AssWorkOrderReqPO.AddPO addPO = AssWorkOrderDomainMapping.INSTANCE.assWorkOrderBO2AddPO(param);
        return assWorkOrderRepository.insert(addPO);
    }


    public boolean updateWithOpt(Long id, Integer originStatus, Integer targetStatus, String assStatusMemo) {
        return assWorkOrderRepository.updateWithOpt(id, originStatus, targetStatus, assStatusMemo);
    }

    public boolean update(AssWorkOrderReqBO.UpdateBO param) {
        AssWorkOrderReqPO.UpdatePO updatePO = AssWorkOrderDomainMapping.INSTANCE.assWorkOrderBO2UpdatePO(param);
        return assWorkOrderRepository.update(updatePO);
    }

    public boolean deleteById(AssWorkOrderReqBO.DelBO param) {
        AssWorkOrderReqPO.DelPO delPO = AssWorkOrderDomainMapping.INSTANCE.assWorkOrderBO2DelPO(param);
        return assWorkOrderRepository.deleteById(delPO);
    }

    public AssWorkOrderRespBO.DetailBO findById(Long id) {
        AssWorkOrder entity = assWorkOrderRepository.findById(id);
        return AssWorkOrderDomainMapping.INSTANCE.assWorkOrderPO2DetailBO(entity);

    }

    public AssWorkOrderRespBO.DetailBO findByOrderItemId(String orderItemId) {
        AssWorkOrder entity = assWorkOrderRepository.findByOrderItemId(orderItemId);
        return AssWorkOrderDomainMapping.INSTANCE.assWorkOrderPO2DetailBO(entity);

    }

    public Optional<AssWorkOrderRespBO.DetailBO> findByScheduleId(String scheduleId) {
        AssWorkOrder entity = assWorkOrderRepository.findByOrderItemId(scheduleId);
        return Optional.ofNullable(AssWorkOrderDomainMapping.INSTANCE.assWorkOrderPO2DetailBO(entity));
    }


    public List<AssWorkOrderRespBO.DetailBO> list(AssWorkOrderReqBO.SearchBO param) {
        AssWorkOrderReqPO.SearchPO searchPO = AssWorkOrderDomainMapping.INSTANCE.assWorkOrderBO2SearchPO(param);
        List<AssWorkOrder> list = assWorkOrderRepository.list(searchPO);
        return AssWorkOrderDomainMapping.INSTANCE.assWorkOrderPO2ListBO(list);
    }

    public Page<AssWorkOrderRespBO.DetailBO> page(AssWorkOrderReqBO.PageBO param) {
        AssWorkOrderReqPO.PagePO pagePO = AssWorkOrderDomainMapping.INSTANCE.assWorkOrderBO2PagePO(param);
        Page<AssWorkOrder> page = assWorkOrderRepository.page(pagePO);
        return AssWorkOrderDomainMapping.INSTANCE.assWorkOrderPO2PageBO(page);
    }

    public PxPageResponse<AssWorkOrderRespBO.AssInfoPageDetailBO> pageAssInfoFromEs(AssWorkOrderReqBO.PageAssOrderBO param) {
        AssInfoReqPO.PagePO pagePO= AssWorkOrderDomainMapping.INSTANCE.assWorkOrderPageBO2PagePO(param);

        Page<AssInfoAggregation> page = assInfoEsRepository.page(pagePO);

        if (page == null || CollectionUtils.isEmpty(page.getRecords())) {
            return PxPageResponse.of(param.getPageSize(), param.getPageIndex());
        }

        List<AssInfoAggregation> records = page.getRecords();

        List<AssWorkOrderRespBO.AssInfoPageDetailBO> list = AssWorkOrderDomainMapping.INSTANCE.convertPageRecords(records);

        Set<String> gameIdSet = OptionalUtil.of(list)
                .stream()
                .filter(item -> Objects.equals(item.getAssType(), RETRIEVE.getCode()))
                .map(AssWorkOrderRespBO.AssInfoPageDetailBO::getGameId).collect(Collectors.toSet());
        if(CollUtil.isNotEmpty(gameIdSet)){

        }
        return PxPageResponse.of(list, (int) page.getTotal(), pagePO.getPageSize(), pagePO.getPageIndex());
    }

    public Long count(AssWorkOrderReqBO.PageAssOrderBO param){
        AssInfoReqPO.PagePO pagePO= AssWorkOrderDomainMapping.INSTANCE.assWorkOrderPageBO2PagePO(param);
        return assInfoEsRepository.count(pagePO);
    }




    public AssWorkOrderRespBO.AssInfoPageDetailBO getAssInfoByOrderItemId(String orderItemId) {
        AssInfoAggregation assInfo = assInfoEsRepository.findByOrderItemId(orderItemId);
        return AssWorkOrderDomainMapping.INSTANCE.convertPageRecord(assInfo);
    }

}

