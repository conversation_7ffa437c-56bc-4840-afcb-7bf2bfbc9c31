package com.pxb7.mall.trade.ass.domain.command;

import com.google.common.collect.Maps;

import java.util.Map;

/**
 * 指令工厂
 *
 * <AUTHOR>
 */
public final class CommandFactory {

    private static final Map<String, AbstractCommand<?>> COMMAND_HANDLER_MAP = Maps.newHashMap();

    private CommandFactory() {
    }

    /**
     * 获取
     */
    public static AbstractCommand<?> getInstance(String commandType) {
        return COMMAND_HANDLER_MAP.getOrDefault(commandType, null);
    }

    /**
     * 注册
     */
    public static void register(String commandType, AbstractCommand<?> handler) {
        //注册
        COMMAND_HANDLER_MAP.putIfAbsent(commandType, handler);
    }

}
