package com.pxb7.mall.trade.ass.domain.refund.utils;

import java.util.List;

import org.apache.curator.shaded.com.google.common.collect.Lists;
import org.jetbrains.annotations.NotNull;

import com.alibaba.fastjson2.JSONObject;
import com.pxb7.mall.im.client.dto.request.card.SendFormMsgReqDTO;
import com.pxb7.mall.im.client.dto.request.card.button.ButtonCon;
import com.pxb7.mall.im.client.dto.request.card.button.ButtonConUrl;
import com.pxb7.mall.im.client.dto.request.card.button.clienttype.ClientAndroid;
import com.pxb7.mall.im.client.dto.request.card.button.clienttype.ClientPC;
import com.pxb7.mall.im.client.dto.request.card.button.clienttype.ClientWap;
import com.pxb7.mall.im.client.dto.request.card.button.styletype.Primary;
import com.pxb7.mall.im.client.dto.request.card.button.targettype.TargetTypeAPI;
import com.pxb7.mall.im.client.dto.request.card.button.targettype.TargetTypeWindow;
import com.pxb7.mall.im.client.dto.request.card.form.BaseFormMsgContent;
import com.pxb7.mall.im.client.dto.request.card.form.FormBoxEnum;
import com.pxb7.mall.im.client.dto.request.card.form.SingleFormMsgContent;
import com.pxb7.mall.im.client.dto.request.card.form.TextFormMsgContent;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.OrderItem;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.OrderItemExtend;

public class RefundImCardUtil {

    public static final String USER_ACCOUNT_TITLE = "请买家提交退款账户";
    public static final String USER_ACCOUNT_OPERATION = "REFUND_COLLECT_USER_ACCOUNT";

    public static final String USER_ACCOUNT_SUBMIT_BUTTON_LABEL = "确认提交";
    public static final String USER_ACCOUNT_SUBMIT_URL_H5 = "/ass/h5/user/refund/submitUserRefundInfo";
    public static final String USER_ACCOUNT_SUBMIT_URL_PC = "/ass/web/user/refund/submitUserRefundInfo";
    public static final String USER_ACCOUNT_SUBMIT_URL_MOBILE = "/ass/mobile/user/refund/submitUserRefundInfo";
    public static final String USER_ACCOUNT_CANCEL_BUTTON_LABEL = "取消退款";
    public static final String USER_ACCOUNT_CANCEL_URL_H5 = "/ass/h5/user/refund/cancelRefund";
    public static final String USER_ACCOUNT_CANCEL_URL_PC = "/ass/web/user/refund/cancelRefund";
    public static final String USER_ACCOUNT_CANCEL_URL_MOBILE = "/ass/mobile/user/refund/cancelRefund";

    @NotNull
    public static SendFormMsgReqDTO buildFormDataMsg(String refundVoucherId, OrderItemExtend orderItemExtend,
        OrderItem orderItem) {
        JSONObject param = new JSONObject();
        param.put("refundVoucherId", refundVoucherId);
        // 提交按钮
        ButtonConUrl submitPC = new ButtonConUrl();
        submitPC.setUrl(USER_ACCOUNT_SUBMIT_URL_PC);
        submitPC.setClientType(new ClientPC());
        submitPC.setTargetType(new TargetTypeAPI());

        ButtonConUrl submitH5 = new ButtonConUrl();
        submitH5.setUrl(USER_ACCOUNT_SUBMIT_URL_H5);
        submitH5.setClientType(new ClientWap());
        submitH5.setTargetType(new TargetTypeAPI());

        ButtonConUrl submitMobile = new ButtonConUrl();
        submitMobile.setUrl(USER_ACCOUNT_SUBMIT_URL_MOBILE);
        submitMobile.setClientType(new ClientAndroid());
        submitMobile.setTargetType(new TargetTypeAPI());

        ButtonCon submitButton = new ButtonCon();
        submitButton.setLabel(USER_ACCOUNT_SUBMIT_BUTTON_LABEL);
        submitButton.setType(new Primary());
        submitButton.setParam(param);
        submitButton.setButtonUrl(List.of(submitPC, submitH5, submitMobile));
        submitButton.setDisabled(false);

        // 取消按钮
        ButtonConUrl cancelPC = new ButtonConUrl();
        cancelPC.setUrl(USER_ACCOUNT_CANCEL_URL_PC);
        cancelPC.setClientType(new ClientPC());
        cancelPC.setTargetType(new TargetTypeWindow(USER_ACCOUNT_CANCEL_URL_PC));

        ButtonConUrl cancelH5 = new ButtonConUrl();
        cancelH5.setUrl(USER_ACCOUNT_CANCEL_URL_H5);
        cancelH5.setClientType(new ClientWap());
        cancelH5.setTargetType(new TargetTypeAPI());

        ButtonConUrl cancelMobile = new ButtonConUrl();
        cancelMobile.setUrl(USER_ACCOUNT_CANCEL_URL_MOBILE);
        cancelMobile.setClientType(new ClientAndroid());
        cancelMobile.setTargetType(new TargetTypeAPI());

        ButtonCon cancelButton = new ButtonCon();
        cancelButton.setLabel(USER_ACCOUNT_CANCEL_BUTTON_LABEL);
        cancelButton.setParam(param);
        cancelButton.setButtonUrl(List.of(cancelPC, cancelH5, cancelMobile));
        cancelButton.setDisabled(false);

        FormBoxEnum alipay = new FormBoxEnum();
        alipay.setLabel("支付宝");
        alipay.setValue(1);

        FormBoxEnum wx = new FormBoxEnum();
        wx.setLabel("微信");
        wx.setValue(2);

        FormBoxEnum bk = new FormBoxEnum();
        bk.setLabel("银行卡");
        bk.setValue(3);

        BaseFormMsgContent channel =
            new SingleFormMsgContent("收款方式", "refundChannel", Lists.newArrayList(alipay, wx, bk));

        BaseFormMsgContent name = new TextFormMsgContent("收款人姓名", "buyerName");
        BaseFormMsgContent account = new TextFormMsgContent("收款账号", "refundAccount");

        // 发送收集买家退款账号信息卡片
        SendFormMsgReqDTO msgReqDTO = new SendFormMsgReqDTO();
        msgReqDTO.setTitle(USER_ACCOUNT_TITLE);
        msgReqDTO.setFromUserId(orderItemExtend.getDeliveryCustomerId());
        msgReqDTO.setMentionedIds(List.of(orderItem.getBuyerId()));
        msgReqDTO.setTargetId(orderItemExtend.getDeliveryRoomId());
        msgReqDTO.setTargetUserIds(List.of(orderItem.getBuyerId()));
        msgReqDTO.setOperation(USER_ACCOUNT_OPERATION);
        msgReqDTO.setOperatorUserIds(List.of(orderItem.getBuyerId()));
        msgReqDTO.setButtons(List.of(cancelButton, submitButton));
        msgReqDTO.setContent(List.of(channel, name, account));
        return msgReqDTO;
    }
}
