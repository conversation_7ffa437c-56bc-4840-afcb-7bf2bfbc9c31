package com.pxb7.mall.trade.ass.domain.config;

import com.pxb7.mall.trade.ass.domain.refund.model.YeePayOutParamPO;
import com.yeepay.yop.sdk.auth.credentials.provider.YopCredentialsProviderRegistry;
import com.yeepay.yop.sdk.model.YopRequestConfig;
import com.yeepay.yop.sdk.service.common.YopClient;
import com.yeepay.yop.sdk.service.common.YopClientBuilder;
import org.springframework.context.annotation.Configuration;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Configuration
public class YeePayConfig {
    public final static String REFUND_QUERY_URL = "/rest/v1.0/trade/refund/query";
    public final static String REFUND_URL = "/rest/v1.0/trade/refund";

    // AppKey -> YopClient
    private static final Map<String, YopClient> clientMap = new ConcurrentHashMap<>();


    public static YopClient getClient(YeePayOutParamPO yeePayOutParamPO) {
        return clientMap.computeIfAbsent(yeePayOutParamPO.getAppKey(), key -> {
            synchronized (YeePayConfig.class) {
                // 这里需要再次检查，避免并发情况下重复创建
                if (!clientMap.containsKey(key)) {
                    YopCredentialsProviderRegistry.registerProvider(
                            new CustomFixedCredentialsProvider(yeePayOutParamPO.getRsaPrivateKey())
                    );
                    return YopClientBuilder.builder().build();
                }
                return clientMap.get(key);
            }
        });
    }

    public static YopRequestConfig getYopRequestConfig(String appKey) {
        YopRequestConfig requestConfig = new YopRequestConfig();
        // 请求appkey设置(可选)，否则取默认appKey
        requestConfig.setAppKey(appKey);
        // 指定单次请求获取数据的超时时间, 单位：ms(可选，默认采用配置文件中的设置)
        requestConfig.setReadTimeout(3000);
        // 指定单次请求建立连接的超时, 单位：ms(可选，默认采用配置文件中的设置)
        requestConfig.setConnectTimeout(3000);
        // 设置所有参数加密：用此方式，所有参数自动加密，不用单独设置某个参数
        requestConfig.setTotalEncrypt(true);
        return requestConfig;
    }
}
