package com.pxb7.mall.trade.ass.domain.refund.model;

import lombok.Data;

/**
 * // <a href="https://open.yeepay.com/docs-v2/apis/jsapi-payment/get__rest__v1.0__trade__order__query#anchor4-2">...</a>
 */
@Data
public class YeeRefundQueryResultDTO {
    private String orderId;

    private String parentMerchantNo;

    private String merchantNo;

    private String code;

    private String message;

    /**
     * PROCESSING：退款处理中
     * SUCCESS：退款成功
     * FAILED：退款失败
     * CANCEL:退款关闭,商户通知易宝结束该笔退款后返回该状态
     * SUSPEND:退款中断,如需继续退款,请调用上送卡信息退款进行打款退款;如想结束退款,请调用结束退款来关闭退款订单
     * 说明:调用申请极速退款、上送卡信息退款、结束退款前,请联系易宝提前开通相应的退款服务。
     */
    private String status;

    // 易宝收款订单号
    private String uniqueOrderNo;

    // 订单金额
    private Long orderAmount;

    // 支付方式
    private String payWay;

    // 商户退款请求号
    private String refundRequestId;

    // 易宝退款订单号
    private String uniqueRefundNo;

    // 实际退款金额,单位:元
    private String refundAmount;

    // 退款成功时间 2021-01-01 00:00:00
    private String refundSuccessDate;
}
