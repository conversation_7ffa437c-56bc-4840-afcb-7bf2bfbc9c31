package com.pxb7.mall.trade.ass.domain.refund.invoke3rd.impl;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.alibaba.cola.exception.BizException;
import com.alibaba.cola.extension.Extension;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alipay.api.AlipayApiException;
import com.alipay.api.domain.AlipayTradeFastpayRefundQueryModel;
import com.alipay.api.domain.AlipayTradeRefundModel;
import com.alipay.api.request.AlipayTradeFastpayRefundQueryRequest;
import com.alipay.api.request.AlipayTradeRefundRequest;
import com.alipay.api.response.AlipayTradeFastpayRefundQueryResponse;
import com.alipay.api.response.AlipayTradeRefundResponse;
import com.google.common.collect.ImmutableList;
import com.pxb7.mall.trade.ass.client.dto.model.refund.RefundErrorCode;
import com.pxb7.mall.trade.ass.client.dto.model.refund.RefundTradeStatusEnum;
import com.pxb7.mall.trade.ass.client.dto.request.refund.RefundInvokeReqDTO;
import com.pxb7.mall.trade.ass.client.dto.request.refund.RefundQueryReqDTO;
import com.pxb7.mall.trade.ass.client.dto.response.refund.RefundInvokeRespDTO;
import com.pxb7.mall.trade.ass.client.dto.response.refund.RefundQueryRespDTO;
import com.pxb7.mall.trade.ass.domain.refund.invoke3rd.PayRefundExtPt;
import com.pxb7.mall.trade.ass.domain.refund.invoke3rd.integration.alipay.AlipayOrderClient;
import com.pxb7.mall.trade.ass.domain.refund.model.*;
import com.pxb7.mall.trade.ass.infra.enums.RefundCallBackStatusEnum;
import com.pxb7.mall.trade.ass.infra.model.Money;
import com.pxb7.mall.trade.ass.infra.remote.model.outpayparam.request.AlipayCreationReqPO;
import com.pxb7.mall.trade.ass.infra.util.AmountUtil;
import com.pxb7.mall.trade.ass.infra.util.DateUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

@Slf4j
@Extension(bizId = "alipayRefund")
public class AlipayRefundExtPt implements PayRefundExtPt {

    private static final List<String> queryOptions = ImmutableList.<String>builder()
            .add("gmt_refund_pay")
            .build();
    private final static String REFUND_SUCCESS = "REFUND_SUCCESS";
    /**
     * 错误码（subCode） - 查询的交易不存在
     */
    private final static String ACQ_TRADE_NOT_EXIST = "ACQ.TRADE_NOT_EXIST";
    @Resource
    private AlipayOrderClient alipayOrderClient;

    @Override
    public RefundInvokeRespDTO refund(RefundInvokeReqDTO refundInvokeReqDTO) throws AlipayApiException {
        AlipayCreationReqPO bo = JSON.parseObject(refundInvokeReqDTO.getOutPayParam(), AlipayCreationReqPO.class);
        // 构造请求参数以调用接口
        AlipayTradeRefundRequest request = new AlipayTradeRefundRequest();
        AlipayTradeRefundModel model = new AlipayTradeRefundModel();
        // 设置商户订单号
        model.setOutTradeNo(refundInvokeReqDTO.getInnerTradeNo());
        // 设置退款金额
        model.setRefundAmount(AmountUtil.convertYuan(refundInvokeReqDTO.getRefundAmount()));
        // 设置退款请求号
        model.setOutRequestNo(refundInvokeReqDTO.getRefundTradeNo());

        request.setBizModel(model);

        AlipayTradeRefundResponse response = alipayOrderClient.refund(request, bo);
        /**
         * body { "alipay_trade_refund_response":{ "code":"10000", "msg":"Success", "buyer_logon_id":"175******45",
         * "fund_change":"Y", "gmt_refund_pay":"2024-09-26 10:51:10", "out_trade_no":"108294337216601",
         * "refund_fee":"0.01", "send_back_fee":"0.00", "trade_no":"2024092022001459101437194356",
         * "buyer_open_id":"010Yu9jN0I1Qkkto3WMLqPsvFn8kICURWV_ifaH2_Y0Jik1" },
         * "alipay_cert_sn":"f57639868b076e3ce0e46c915dbac6a2",
         * "sign":"JdChGWnE7WWsxXNU2TBS6U7ic9bSNk2TlU7ZXSmtHPHLr6nPRDm4CBhRc7qwFUjCVJuKNtL66iPgSxH5J2qwVLrxkOWV5iARijRhUcNDm7KYcIygnAb2PWFpxf3zks2aeqwmOOJvn178x7olK911RDUXRAJcdnu8LYh+ugYQhvr6DceuaxuVmnChhoTYQcJW1i17bVrsvxzCx8ODIgbcZThb0qdCstFYV3dXc/9kjA7clEfVZwxJtVmC209w6a562ZxZUcusQ6S5/QADCDFrq+WNJSpdSJuTvZz+RsAY/zbZiwspdCMU3uCaiKfw7fc7r9AtH9q2YyUnfL/0GUgN8A=="
         * }
         *
         */
        RefundInvokeRespDTO refundExtRespBO = new RefundInvokeRespDTO();
        if (!response.isSuccess()) {
            log.error("调用支付宝退款失败，response:{}", JSONObject.toJSONString(response));
            refundExtRespBO.setSuccess(Boolean.FALSE);
            refundExtRespBO.setResult(response.getBody());
            return refundExtRespBO;
        }
        // TODO 支付宝同步执行成功,直接走成功流程
        refundExtRespBO.setResult(response.getBody());
        refundExtRespBO.setSuccess(Boolean.TRUE);
        return refundExtRespBO;
    }

    @Override
    public RefundQueryRespDTO refundQuery(RefundQueryReqDTO refundQueryReqDTO) throws AlipayApiException {
        /**
         * {
         *     "alipay_trade_fastpay_refund_query_response": {
         *         "code": "10000",
         *         "msg": "Success",
         *         "gmt_refund_pay": "2024-12-24 16:51:17",
         *         "out_request_no": "12551345147090025543",
         *         "out_trade_no": "12347213113756225543",
         *         "refund_amount": "183.60",
         *         "refund_status": "REFUND_SUCCESS",
         *         "total_amount": "183.60",
         *         "trade_no": "2024121322001492211440961031"
         *     },
         *     "alipay_cert_sn": "4bd246e39dadbb3ae05ca3386978c6a3",
         *     "sign": "Pu357jLxV3AiZf59T6hYMNV8BAmplCRUf398PmY4qG13g4dhTnEIIeraN5B/WcHcFdP92xqBPLfH9vm/KV1ryeT/s14egV+siNS71Pu0u9NxDjViPMeeu2FrtPgZZbOsNv9o/AyCDaxn69jzQe2sT4IkUUyfPCFomtAji2TnJ5C4FljHBo9p5oN2Rn0JeZWLzB2y619C/skpQgWdSS32cDzBMQNc+YBqlnyinoYvczzvCjKgfwYMRM4snsrgzXvA5AfwnutBW/zEcPthlJVdt0airMg7tA/winhPB34H8CnRvVVyqg7dQeaorl/1NKz8PMKTYFkmHI0sLAnepsZ17w=="
         * }
         */
        AlipayCreationReqPO alipayCreationReqPO =
                JSON.parseObject(refundQueryReqDTO.getOutPayParam(), AlipayCreationReqPO.class);
        // 构造请求参数以调用接口
        AlipayTradeFastpayRefundQueryRequest request = new AlipayTradeFastpayRefundQueryRequest();
        AlipayTradeFastpayRefundQueryModel model = new AlipayTradeFastpayRefundQueryModel();
        // 设置商户订单号
        model.setOutTradeNo(refundQueryReqDTO.getPayTradeNo());
        model.setQueryOptions(queryOptions);
        // 设置退款请求号
        model.setOutRequestNo(refundQueryReqDTO.getRefundTradeNo());
        request.setBizModel(model);
        AlipayTradeFastpayRefundQueryResponse response = alipayOrderClient.refundQuery(request, alipayCreationReqPO);
        log.info("AlipayRefundExtPt.refundQuery 查询退款结果 response = {}", response.getBody());
        JSONObject respBody = JSON.parseObject(response.getBody());
        if (!response.isSuccess() || respBody == null) {
            log.error("调用支付宝退款失败或结果为空，{},respBody{}", response.getErrorCode(), response.getBody());
            throw new BizException(RefundErrorCode.REFUND_ERROR.getErrCode(),
                    RefundErrorCode.REFUND_ERROR.getErrDesc());
        }

        JSONObject refundQueryResponse = respBody.getJSONObject("alipay_trade_fastpay_refund_query_response");
        if (refundQueryResponse == null) {
            log.error("调用支付宝退款refundQueryResponse结果为空，{},respBody{}", response.getErrorCode(), response.getBody());
            throw new BizException(RefundErrorCode.REFUND_ERROR.getErrCode(),
                    RefundErrorCode.REFUND_ERROR.getErrDesc());
        }

        String refundStatus = refundQueryResponse.getString("refund_status");
        String refundAmount = refundQueryResponse.getString("refund_amount");
        String refundTime = refundQueryResponse.getString("gmt_refund_pay");
        String outTradeNo = refundQueryResponse.getString("trade_no");

        Integer tradeStatus = RefundTradeStatusEnum.EXECUTING.getValue();
        if ("REFUND_SUCCESS".equals(refundStatus)) {
            tradeStatus = RefundTradeStatusEnum.SUCCESS.getValue();
        }

        RefundQueryRespDTO refundQueryRespDTO = new RefundQueryRespDTO();
        refundQueryRespDTO.setRefundTradeStatus(tradeStatus);
        refundQueryRespDTO.setResult(response.getBody());
        refundQueryRespDTO.setRefundAmount(AmountUtil.convertFen(refundAmount));
        refundQueryRespDTO.setPayTime(LocalDateTimeUtil.parse(refundTime, "yyyy-MM-dd HH:mm:ss"));
        refundQueryRespDTO.setRefundTradeNo(refundQueryReqDTO.getRefundTradeNo());
        refundQueryRespDTO.setPayTradeNo(refundQueryReqDTO.getPayTradeNo());
        refundQueryRespDTO.setOutTradeNo(outTradeNo);
        return refundQueryRespDTO;
    }

    @Override
    public RefundInvokeRespBO refundV2(RefundInvokeReqBO reqBO) throws Exception {
        AlipayCreationReqPO bo = JSON.parseObject(reqBO.getOutPayParam(), AlipayCreationReqPO.class);
        // 构造请求参数以调用接口
        AlipayTradeRefundRequest request = new AlipayTradeRefundRequest();
        AlipayTradeRefundModel model = new AlipayTradeRefundModel();
        // 设置商户订单号
        model.setOutTradeNo(reqBO.getPayLogId());
        // 设置退款金额
        model.setRefundAmount(new Money(reqBO.getRefundAmount()).getYuan());
        // 设置退款请求号
        model.setOutRequestNo(reqBO.getRefundTradeNo());
        request.setBizModel(model);
        alipayOrderClient.refund(request, bo);
        return RefundInvokeRespBO.builder().statusEnum(RefundCallBackStatusEnum.PROCESSING).build();
    }

    @Override
    public RefundQueryRespBO refundQueryV2(RefundQueryReqBO reqBO) {
        RefundQueryRespBO bo = new RefundQueryRespBO();
        AlipayCreationReqPO reqPO =
                JSON.parseObject(reqBO.getOutPayParam(), AlipayCreationReqPO.class);
        // 构造请求参数以调用接口
        AlipayTradeFastpayRefundQueryRequest request = new AlipayTradeFastpayRefundQueryRequest();
        AlipayTradeFastpayRefundQueryModel model = new AlipayTradeFastpayRefundQueryModel();
        // 设置商户订单号
        model.setOutTradeNo(reqBO.getPayLogId());
        model.setQueryOptions(queryOptions);
        // 设置退款请求号
        model.setOutRequestNo(reqBO.getRefundTradeNo());
        request.setBizModel(model);
        try {
            AlipayTradeFastpayRefundQueryResponse response = alipayOrderClient.refundQuery(request, reqPO);
            bo.setResult(response.getBody());
            bo.setRefundTradeNo(reqBO.getRefundTradeNo());
            // 仅仅代表受理成功 todo 受理失败可能是系统异常
            if (response.isSuccess()) {
                String refundStatus = response.getRefundStatus();
                if (StringUtils.equals(refundStatus, REFUND_SUCCESS)) {
                    bo.setRefundAmount(new Money(response.getRefundAmount()));
                    bo.setRefundOutTradeNo(response.getTradeNo());
                    bo.setRefundTradeStatus(RefundTradeStatusEnum.SUCCESS.getValue());
                    bo.setSuccessTime(DateUtil.from(response.getGmtRefundPay()));
                } else {
                    bo.setRefundTradeStatus(RefundTradeStatusEnum.FAIL.getValue());
                }
            } else if (response.getSubCode().equals(ACQ_TRADE_NOT_EXIST)) {
                bo.setRefundTradeStatus(RefundTradeStatusEnum.FAIL.getValue());
            } else {
                bo.setRefundTradeStatus(RefundTradeStatusEnum.EXECUTING.getValue());
            }
            return bo;
        } catch (AlipayApiException e) {
            log.error("支付宝退款查询请求异常: {}", JSON.toJSONString(request), e);
            throw new BizException(RefundErrorCode.ALIPAY_REQUEST_EXCEPTION.getErrCode(),
                    RefundErrorCode.ALIPAY_REQUEST_EXCEPTION.getErrDesc());
        }
    }

    @Override
    public RefundCallBackBO refundCallbackSign(RefundCallBackReqBO reqBO) throws Exception {
        return null;
    }
}
