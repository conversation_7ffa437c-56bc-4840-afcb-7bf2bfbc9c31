package com.pxb7.mall.trade.ass.domain;

import com.pxb7.mall.trade.ass.domain.mapping.ComplaintWorkOrderDomainMapping;
import com.pxb7.mall.trade.ass.domain.model.response.ComplaintWorkOrderDetailRespBO;
import com.pxb7.mall.trade.ass.domain.model.response.ComplaintWorkOrderLogRespBO;
import com.pxb7.mall.trade.ass.domain.model.response.ComplaintWorkOrderRespBO;
import com.pxb7.mall.trade.ass.infra.enums.complaint.WorkOrderStatus;
import com.pxb7.mall.trade.ass.infra.repository.db.ComplaintWorkLogRepository;
import com.pxb7.mall.trade.ass.infra.repository.db.ComplaintWorkRepository;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.ComplaintWork;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.ComplaintWorkLog;
import com.pxb7.mall.trade.ass.infra.util.IdGenUtil;

import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

import jakarta.annotation.Resource;

/**
 * Copyright (C), 2002-2024, 螃蟹游戏服务网
 *
 * @fileName: ComplaintWorkOrderDomainService.java
 * @description: 客诉工单domain service
 * @author: guominqiang
 * @email: <EMAIL>
 * @date: 2024/9/20 20:03
 * @history: //修改记录
 * <author> <time> <version> <desc>
 * 修改人姓名 修改时间 版本号 描述
 */
@Service
public class ComplaintWorkOrderDomainService {
    @Resource
    private ComplaintWorkRepository complaintWorkRepository;
    @Resource
    private ComplaintWorkLogRepository complaintWorkLogRepository;

    public ComplaintWorkOrderDetailRespBO searchComplaintWorkOrderDetail(String complaintWorkId) {
        ComplaintWork complaintWork = complaintWorkRepository.queryById(complaintWorkId);
        return ComplaintWorkOrderDomainMapping.INSTANCE.toComplaintWorkOrderDetail(complaintWork);
    }

    /**
     * 完结工单
     *
     * @param finisherId                     完结人id
     * @param complaintWorkOrderDetailRespBO
     * @return
     */
    public Boolean finishComplaintWorkOrder(String finisherId,
                                            ComplaintWorkOrderDetailRespBO complaintWorkOrderDetailRespBO) {

        var finisherTime = LocalDateTime.now();
        ComplaintWork complaintWork = ComplaintWorkOrderDomainMapping.INSTANCE.transformComplaintWork(complaintWorkOrderDetailRespBO);
        complaintWork.setCurrentProcessorId(finisherId);
        complaintWork.setFinisherId(finisherId);
        complaintWork.setFinishTime(finisherTime);
        complaintWork.setUpdateUserId(finisherId);
        complaintWork.setWorkOrderStatus(WorkOrderStatus.WORK_STATUS_FINISH.getCode());
        return complaintWorkRepository.finishWorkOrder(complaintWork);
    }

    /**
     * 转交工单
     *
     * @param complaintWorkOrderDetailRespBO
     * @return
     */
    public boolean transferComplaintWorkOrder(ComplaintWorkOrderDetailRespBO complaintWorkOrderDetailRespBO) {
        ComplaintWork complaintWork = ComplaintWorkOrderDomainMapping.INSTANCE.transformComplaintWork(complaintWorkOrderDetailRespBO);
        return complaintWorkRepository.transferWorkOrder(complaintWork);
    }

    /**
     * 保存客诉工单日志
     *
     * @param complaintWorkId 客诉工单id
     * @param roomId          房间id
     * @param logType         日志类型 1:创建；2:转交；3:完结
     * @param processorId     处理人id
     * @param transfereeId    被转交人id
     * @param transferNote    转交备注
     * @return
     */
    public Boolean saveComplaintWorkOrderLog(String complaintWorkId,
                                             String roomId,
                                             Integer logType,
                                             String processorId,
                                             String transfereeId,
                                             String transferNote) {
        var complaintWorkLogId = IdGenUtil.generateId();
        var currentTime = LocalDateTime.now();
        ComplaintWorkLog complaintWorkLog = new ComplaintWorkLog();
        if (logType == 2) {
            complaintWorkLog.setTransfereeId(transfereeId);
            complaintWorkLog.setTransferNote(transferNote);
        }
        complaintWorkLog.setComplaintWorkId(complaintWorkId);
        complaintWorkLog.setComplaintWorkLogId(complaintWorkLogId);
        complaintWorkLog.setLogType(logType);
        complaintWorkLog.setRoomId(roomId);
        complaintWorkLog.setCurrentProcessorId(processorId);
        complaintWorkLog.setCreateTime(currentTime);
        complaintWorkLog.setCreateUserId(processorId);
        complaintWorkLog.setUpdateTime(currentTime);
        complaintWorkLog.setUpdateUserId(processorId);
        return complaintWorkLogRepository.save(complaintWorkLog);
    }

    public List<ComplaintWorkOrderLogRespBO> queryByWorkId(String complaintWorkId) {
        List<ComplaintWorkLog> complaintWorkLogList = complaintWorkLogRepository.queryByWorkId(complaintWorkId);
        return ComplaintWorkOrderDomainMapping.INSTANCE.toComplaintWorkOrderLogist(complaintWorkLogList);
    }

    public List<ComplaintWorkOrderRespBO> list(String customerId, String userId) {
        List<ComplaintWork> complaintWorkList = complaintWorkRepository.listByUserId(customerId, userId);
        return ComplaintWorkOrderDomainMapping.INSTANCE.toComplaintWorkOrderRespBOList(complaintWorkList);
    }
}
