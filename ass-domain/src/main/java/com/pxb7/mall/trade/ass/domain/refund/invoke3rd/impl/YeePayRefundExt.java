package com.pxb7.mall.trade.ass.domain.refund.invoke3rd.impl;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.alibaba.cola.exception.BizException;
import com.alibaba.cola.exception.SysException;
import com.alibaba.cola.extension.Extension;
import com.alibaba.fastjson2.JSON;
import com.pxb7.mall.trade.ass.client.dto.model.refund.RefundTradeStatusEnum;
import com.pxb7.mall.trade.ass.client.dto.request.refund.RefundInvokeReqDTO;
import com.pxb7.mall.trade.ass.client.dto.request.refund.RefundQueryReqDTO;
import com.pxb7.mall.trade.ass.client.dto.response.refund.RefundInvokeRespDTO;
import com.pxb7.mall.trade.ass.client.dto.response.refund.RefundQueryRespDTO;
import com.pxb7.mall.trade.ass.domain.config.YeePayConfig;
import com.pxb7.mall.trade.ass.domain.refund.invoke3rd.PayRefundExtPt;
import com.pxb7.mall.trade.ass.domain.refund.model.*;
import com.pxb7.mall.trade.ass.infra.enums.RefundCallBackStatusEnum;
import com.pxb7.mall.trade.ass.infra.model.Money;
import com.yeepay.yop.sdk.service.common.request.YopRequest;
import com.yeepay.yop.sdk.service.common.response.YopResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@Extension(bizId = "yeePayRefund")
public class YeePayRefundExt implements PayRefundExtPt {


    private void refundCompleteRequest(YopRequest request, RefundInvokeReqBO reqBO, YeePayOutParamPO payParam) {
        // 原收款商户编号
        // 收款商编
        request.addParameter("parentMerchantNo", payParam.getParentMerchantNo());
        // 平台商编
        request.addParameter("merchantNo", payParam.getMerchantNo());
        request.addParameter("orderId", reqBO.getPayLogId());
        request.addParameter("refundRequestId", reqBO.getRefundTradeNo());

        // 单位：元，两位小数，最低0.01，退款金额不能大于原订单金额，多次退款时，累计退款金额不能超过原订单金额
        request.addParameter("refundAmount", new Money(reqBO.getRefundAmount()).getYuan());
    }

    private void refundQueryCompleteRequest(YopRequest request, RefundQueryReqBO reqBO, YeePayOutParamPO payParam) {
        // 收款商编
        request.addParameter("parentMerchantNo", payParam.getParentMerchantNo());
        // 原收款商户编号
        request.addParameter("merchantNo", payParam.getMerchantNo());
        //
        request.addParameter("orderId", reqBO.getPayLogId());
        // 退款请求号
        request.addParameter("refundRequestId", reqBO.getRefundTradeNo());
    }


    @Override
    public RefundInvokeRespDTO refund(RefundInvokeReqDTO refundInvokeReqDTO) throws Exception {
        throw new SysException("not support");
    }

    @Override
    public RefundQueryRespDTO refundQuery(RefundQueryReqDTO refundQueryReqDTO) throws Exception {
        throw new SysException("not support");
    }

    /**
     * 一笔退款失败后重新提交，请不要更换退款单号，请使用相同的商户退款请求号请求退款。
     * <p>
     * 受理失败时，抛异常，可能是系统异常，上游原单号重试即可
     */
    @Override
    public RefundInvokeRespBO refundV2(RefundInvokeReqBO reqBO) {
        log.info("[易宝支付] 去退款 入参:{}", reqBO);
        // 获取支付渠道参数
        YeePayOutParamPO payParam = JSON.parseObject(reqBO.getOutPayParam(), YeePayOutParamPO.class);

        // 指定要请求的API地址和请求方式
        YopRequest request = new YopRequest(YeePayConfig.REFUND_URL, "POST", YeePayConfig.getYopRequestConfig(payParam.getAppKey()));

        // 补充请求参数
        refundCompleteRequest(request, reqBO, payParam);

        try {
            log.info("[易宝支付] 去退款 invoke req:{}", JSON.toJSONString(request.getParameters()));
            YopResponse response = YeePayConfig.getClient(payParam).request(request);
            log.info("[易宝支付] 去退款 invoke resp:{}", response.getStringResult());
            checkRefundRes(response);
        } catch (Exception e) {
            log.error("[易宝支付] 去退款 异常:{}", reqBO, e);
            throw new SysException(e.getMessage());
        }
        return RefundInvokeRespBO.builder().statusEnum(RefundCallBackStatusEnum.PROCESSING).build();
    }

    /**
     * （1）当响应参数code=OPR00000时,说明易宝已受理该笔退款,此时需要根据status来判断退款状态；
     * （2）当响应参数code≠OPR00000时,说明易宝没有受理该笔退款，可根据返回的message进行相应处理。
     */
    private void checkRefundRes(YopResponse response) {
        YeePayToRefundResponse yeeRefundQueryResultDTO = JSON.parseObject(response.getStringResult(), YeePayToRefundResponse.class);
        // 当响应参数code=OPR00000时,说明易宝已受理该笔退款
        if (!"OPR00000".equals(yeeRefundQueryResultDTO.getCode())) {
            throw new BizException(yeeRefundQueryResultDTO.getMessage());
        }
    }

    /**
     * 成功，失败，异常
     */
    @Override
    public RefundQueryRespBO refundQueryV2(RefundQueryReqBO reqBO) {
        // 获取支付渠道参数
        YeePayOutParamPO payParam = JSON.parseObject(reqBO.getOutPayParam(), YeePayOutParamPO.class);

        // 指定要请求的API地址和请求方式
        YopRequest request = new YopRequest(YeePayConfig.REFUND_QUERY_URL, "GET", YeePayConfig.getYopRequestConfig(payParam.getAppKey()));

        // 补充请求参数
        refundQueryCompleteRequest(request, reqBO, payParam);

        try {
            log.info("[易宝支付] 退款查询 req:{}", JSON.toJSONString(request.getParameters()));
            YopResponse response = YeePayConfig.getClient(payParam).request(request);
            log.info("[易宝支付] 退款查询  resp:{}", response.getStringResult());
            // 解析
            return getRefundQueryRespBO(response);
        } catch (Exception e) {
            log.error("[易宝支付] 退款查询 异常:{}", reqBO, e);
            throw new SysException(e.getMessage());
        }
    }

    @Override
    public RefundCallBackBO refundCallbackSign(RefundCallBackReqBO reqBO) throws Exception {
        return null;
    }

    /**
     * PROCESSING：退款处理中
     * SUCCESS：退款成功
     * FAILED：退款失败
     * CANCEL:退款关闭,商户通知易宝结束该笔退款后返回该状态
     * SUSPEND:退款中断,如需继续退款,请调用上送卡信息退款进行打款退款;如
     * 想结束退款,请调用结束退款来关闭退款订单
     * 说明:调用申请极速退款、上送卡信息退款、结束退款前,请联系易宝提前开通相应的退款服务。
     */
    private RefundQueryRespBO getRefundQueryRespBO(YopResponse response) {
        YeeRefundQueryResultDTO resultDTO = JSON.parseObject(response.getStringResult(), YeeRefundQueryResultDTO.class);
        // 当响应参数code=OPR00000时,说明易宝已受理该笔退款
        if (!"OPR00000".equals(resultDTO.getCode())) {
            log.error("yeePay refund query error:{}", resultDTO.getMessage());
            throw new BizException(resultDTO.getMessage());
        }

        // 状态处理
        RefundQueryRespBO bo = new RefundQueryRespBO();
        bo.setResult(response.getStringResult());
        bo.setRefundTradeNo(resultDTO.getRefundRequestId());

        if ("SUCCESS".equalsIgnoreCase(resultDTO.getStatus())) {
            bo.setRefundAmount(new Money(resultDTO.getRefundAmount()));
            bo.setRefundOutTradeNo(resultDTO.getUniqueRefundNo());
            bo.setRefundTradeStatus(RefundTradeStatusEnum.SUCCESS.getValue());
            bo.setSuccessTime(LocalDateTimeUtil.parse(resultDTO.getRefundSuccessDate(), "yyyy-MM-dd HH:mm:ss"));
        } else if ("FAILED".equalsIgnoreCase(resultDTO.getStatus())) {
            bo.setRefundTradeStatus(RefundTradeStatusEnum.FAIL.getValue());
        } else if ("PROCESSING".equalsIgnoreCase(resultDTO.getStatus())) {
            bo.setRefundTradeStatus(RefundTradeStatusEnum.EXECUTING.getValue());
        } else {
            log.error("yeePay refund query un support status : {}", resultDTO.getMessage());
            throw new BizException("un support status");
        }

        return bo;
    }
}
