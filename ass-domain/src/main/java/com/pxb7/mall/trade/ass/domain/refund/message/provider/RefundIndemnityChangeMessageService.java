package com.pxb7.mall.trade.ass.domain.refund.message.provider;

import com.pxb7.mall.trade.ass.domain.helper.RocketMqMessageHelper;
import com.pxb7.mall.trade.ass.domain.model.RocketMqMessageDTO;
import com.pxb7.mall.trade.ass.infra.model.mq.IndemnityChangeTradeFinishedMessage;
import com.pxb7.mall.trade.ass.infra.util.ObjectMapperUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import static com.pxb7.mall.trade.order.client.constants.mq.RMQConstant.*;

@Slf4j
@Service
public class RefundIndemnityChangeMessageService {
    @Resource
    private RocketMqMessageHelper rocketMqMessageHelper;

    // 通知包赔变更
    public void refundSendIndemnityChange(IndemnityChangeTradeFinishedMessage message) {
        log.info("[refundSendIndemnityChange] 发送 退款完成通知包赔变更消息，message = {}", message);
        RocketMqMessageDTO messageDTO = RocketMqMessageDTO.builder()
                .topic(INDEMNITY_CHANGE_TRADE_FINISHED_TOPIC)
                .tag(INDEMNITY_CHANGE_TRADE_FINISHED_TAG)
                .isDelay(false)
                .content(ObjectMapperUtil.toJsonStr(message))
                .bizId(message.getVoucherId())
                .isSharding(false)
                .build();
        // 本地消息
        rocketMqMessageHelper.saveAndSend(messageDTO);
    }
}
