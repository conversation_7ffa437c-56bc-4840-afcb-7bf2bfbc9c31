package com.pxb7.mall.trade.ass.domain.violate.mapping;

import com.pxb7.mall.trade.ass.domain.violate.model.ViolateOrderBO;
import com.pxb7.mall.trade.ass.domain.violate.request.ViolateCreateReqBO;
import com.pxb7.mall.trade.ass.domain.violate.request.ViolateApplyReqBO;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.ViolateOrder;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

import java.util.List;

@Mapper(componentModel = "Spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ViolateOrderDomainMapping {
    @Mapping(source = "violateCreateReqBO.createUserName", target = "createUsername")
    @Mapping(source = "violateCreateReqBO.updateUserName", target = "updateUsername")
    ViolateOrder toViolateOrder(ViolateCreateReqBO violateCreateReqBO);

    ViolateOrderBO toViolateOrderBO(ViolateOrder violateOrder);

    List<ViolateOrderBO> toViolateOrderBOList(List<ViolateOrder> violateOrder);

    @Mapping(target = "updateUserId", source = "createUserId")
    @Mapping(target = "updateUserName", source = "createUserName")
    ViolateCreateReqBO toViolateCreateReqBO(ViolateApplyReqBO violateApplyReqBO);
}
