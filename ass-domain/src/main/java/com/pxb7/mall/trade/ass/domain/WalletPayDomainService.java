package com.pxb7.mall.trade.ass.domain;

import cn.hutool.core.util.StrUtil;
import com.pxb7.mall.pay.client.constants.PayMessageTopics;
import com.pxb7.mall.pay.client.dto.model.AccountTradeExtDTO;
import com.pxb7.mall.pay.client.dto.model.PayAccountTradeMessage;
import com.pxb7.mall.pay.client.dto.response.PayAccountTradeResp;
import com.pxb7.mall.trade.ass.domain.helper.RocketMqMessageHelper;
import com.pxb7.mall.trade.ass.domain.model.RocketMqMessageDTO;
import com.pxb7.mall.trade.ass.domain.model.WalletPayInboundBO;
import com.pxb7.mall.trade.ass.domain.model.WalletToPayBO;
import com.pxb7.mall.trade.ass.infra.remote.dubbo.PayServiceGateway;
import com.pxb7.mall.trade.ass.infra.repository.db.OrderItemExtendRepository;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.OrderItemExtend;
import com.pxb7.mall.trade.ass.infra.util.ObjectMapperUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Slf4j
@Service
public class WalletPayDomainService {

    @Resource
    private RocketMqMessageHelper rocketMqMessageHelper;
    @Resource
    private OrderItemExtendRepository orderItemExtendRepository;
    @Resource
    private PayServiceGateway payServiceGateway;

    /**
     * 钱包入账
     * <p>
     * 场景1: 违约金打款
     */
    public void walletPayInBound(WalletPayInboundBO walletPayInboundBO) {
        AccountTradeExtDTO extMessage = null;

        if (StrUtil.isNotBlank(walletPayInboundBO.getOrderId())) {
            OrderItemExtend orderItemExtend = orderItemExtendRepository.getOneByItemId(walletPayInboundBO.getOrderId());
            if (Objects.nonNull(orderItemExtend)) {
                extMessage = new AccountTradeExtDTO();
                extMessage.setOrderItemId(orderItemExtend.getOrderItemId());
                extMessage.setGameName(orderItemExtend.getGameName());
                extMessage.setProductUniqueNo(orderItemExtend.getProductUniqueNo());
            }
        }

        PayAccountTradeMessage message = PayAccountTradeMessage.builder()
                .voucherId(walletPayInboundBO.getVoucherId())
                .callback(walletPayInboundBO.getCallback())
                .amount(walletPayInboundBO.getAmount())
                .userId(walletPayInboundBO.getUserId())
                .orderId(walletPayInboundBO.getOrderId())
                .tradeType(walletPayInboundBO.getAccountTradeTypeEnum().code)
                .build();

        if (Objects.nonNull(message)) {
            message.setTradeExt(extMessage);
        }

        RocketMqMessageDTO mqMessageDTO = RocketMqMessageDTO.builder()
                .bizId(walletPayInboundBO.getVoucherId())
                .topic(PayMessageTopics.AccountTrade.TOPIC)
                .tag(PayMessageTopics.AccountTrade.TAG)
                .content(ObjectMapperUtil.toJsonStr(message))
                .build();

        rocketMqMessageHelper.saveAndSend(mqMessageDTO);
    }

    /**
     * 钱包支付分发
     */
    public PayAccountTradeResp toPayWallet(WalletToPayBO walletToPayBO) {
        log.info("[去钱包扣款] 开始处理 订单号:{}, 单据id:{} 入参: {}", walletToPayBO.getOrderId(), walletToPayBO.getVoucherId(), walletToPayBO);

        AccountTradeExtDTO extMessage = null;

        if (StrUtil.isNotBlank(walletToPayBO.getOrderId())) {
            OrderItemExtend orderItemExtend = orderItemExtendRepository.getOneByItemId(walletToPayBO.getOrderId());
            if (Objects.nonNull(orderItemExtend)) {
                extMessage = new AccountTradeExtDTO();
                extMessage.setOrderItemId(orderItemExtend.getOrderItemId());
                extMessage.setGameName(orderItemExtend.getGameName());
                extMessage.setProductUniqueNo(orderItemExtend.getProductUniqueNo());
            }
        }

        PayAccountTradeResp payAccountTradeResp = payServiceGateway.walletDeduction(
                walletToPayBO.getOrderId()
                , walletToPayBO.getVoucherId()
                , walletToPayBO.getUserId()
                , walletToPayBO.getAmount()
                , walletToPayBO.getAccountTradeTypeEnum().getCode(), extMessage);


//        Consumer<WalletPayExtPt> callBackFunction = extPt -> {
//            extPt.walletToPay(walletToPayBO);
//        };

        // 执行扩展点
//        extensionExecutor.executeVoid(WalletPayExtPt.class,
//                BizScenario.valueOf(walletToPayBO.getExtension().getBizId()),
//                callBackFunction);
        log.info("[去钱包扣款] 处理完成 订单号:{}, 单据id:{} 入参: {}", walletToPayBO.getOrderId(), walletToPayBO.getVoucherId(), walletToPayBO);

        return payAccountTradeResp;
    }
}
