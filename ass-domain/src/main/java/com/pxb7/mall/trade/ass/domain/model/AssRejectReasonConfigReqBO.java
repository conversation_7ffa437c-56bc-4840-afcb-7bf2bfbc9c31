package com.pxb7.mall.trade.ass.domain.model;

import java.util.Date;
import java.time.*;
import java.math.BigDecimal;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.ToString;


/**
 * 驳回原因配置表(AssRejectReasonConfig)实体类
 *
 * <AUTHOR>
 * @since 2025-07-30 13:49:17
 */
public class AssRejectReasonConfigReqBO {

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class AddBO {

        /**
         * 业务主键
         */
        private String reasonConfigId;

        /**
         * 售后类型1找回2纠纷
         */
        private Integer assType;

        /**
         * 驳回原因文案
         */
        private String reason;

        /**
         * 用户测文案
         */
        private String userDesc;

        /**
         * 排序
         */
        private Integer sort;

        /**
         * 创建人id
         */
        private String createUserId;

        /**
         * 更新人id
         */
        private String updateUserId;

    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class UpdateBO {

        /**
         * 自增id
         */
        private Long id;


        /**
         * 业务主键
         */
        private String reasonConfigId;


        /**
         * 售后类型1找回2纠纷
         */
        private Integer assType;


        /**
         * 驳回原因文案
         */
        private String reason;


        /**
         * 用户测文案
         */
        private String userDesc;


        /**
         * 排序
         */
        private Integer sort;


        /**
         * 创建人id
         */
        private String createUserId;


        /**
         * 更新人id
         */
        private String updateUserId;


    }


    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class DelBO {
        private Long id;
    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class SearchBO {
        /**
         * 业务主键
         */
        private String reasonConfigId;

        /**
         * 售后类型1找回2纠纷
         */
        private Integer assType;

        /**
         * 驳回原因文案
         */
        private String reason;

        /**
         * 用户测文案
         */
        private String userDesc;

        /**
         * 排序
         */
        private Integer sort;

        /**
         * 创建人id
         */
        private String createUserId;

        /**
         * 更新人id
         */
        private String updateUserId;

    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class PageBO {

        /**
         * 业务主键
         */
        private String reasonConfigId;

        /**
         * 售后类型1找回2纠纷
         */
        private Integer assType;

        /**
         * 驳回原因文案
         */
        private String reason;

        /**
         * 用户测文案
         */
        private String userDesc;

        /**
         * 排序
         */
        private Integer sort;

        /**
         * 创建人id
         */
        private String createUserId;

        /**
         * 更新人id
         */
        private String updateUserId;


        /**
         * 页码，从1开始
         */
        private Integer pageIndex;
        /**
         * 每页数量
         */
        private Integer pageSize;

    }

}

