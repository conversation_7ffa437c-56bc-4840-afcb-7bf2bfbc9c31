package com.pxb7.mall.trade.ass.domain;


import java.util.List;

import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import com.pxb7.mall.trade.ass.domain.model.AssRejectReasonConfigReqBO;
import com.pxb7.mall.trade.ass.domain.model.AssRejectReasonConfigRespBO;
import com.pxb7.mall.trade.ass.domain.mapping.AssRejectReasonConfigDomainMapping;
import com.pxb7.mall.trade.ass.infra.model.AssRejectReasonConfigReqPO;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.AssRejectReasonConfig;
import com.pxb7.mall.trade.ass.infra.repository.db.AssRejectReasonConfigRepository;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;


/**
 * 驳回原因配置表domain服务
 *
 * <AUTHOR>
 * @since 2025-07-30 13:49:17
 */
@Service
public class AssRejectReasonConfigDomainService {

    @Resource
    private AssRejectReasonConfigRepository assRejectReasonConfigRepository;

    public boolean insert(AssRejectReasonConfigReqBO.AddBO param) {
        AssRejectReasonConfigReqPO.AddPO addPO = AssRejectReasonConfigDomainMapping.INSTANCE.assRejectReasonConfigBO2AddPO(param);
        return assRejectReasonConfigRepository.insert(addPO);
    }

    public boolean update(AssRejectReasonConfigReqBO.UpdateBO param) {
        AssRejectReasonConfigReqPO.UpdatePO updatePO = AssRejectReasonConfigDomainMapping.INSTANCE.assRejectReasonConfigBO2UpdatePO(param);
        return assRejectReasonConfigRepository.update(updatePO);
    }

    public boolean deleteById(AssRejectReasonConfigReqBO.DelBO param) {
        AssRejectReasonConfigReqPO.DelPO delPO = AssRejectReasonConfigDomainMapping.INSTANCE.assRejectReasonConfigBO2DelPO(param);
        return assRejectReasonConfigRepository.deleteById(delPO);
    }

    public AssRejectReasonConfigRespBO.DetailBO findById(Long id) {
        AssRejectReasonConfig entity = assRejectReasonConfigRepository.findById(id);
        return AssRejectReasonConfigDomainMapping.INSTANCE.assRejectReasonConfigPO2DetailBO(entity);

    }

    public List<AssRejectReasonConfigRespBO.DetailBO> list(AssRejectReasonConfigReqBO.SearchBO param) {
        AssRejectReasonConfigReqPO.SearchPO searchPO = AssRejectReasonConfigDomainMapping.INSTANCE.assRejectReasonConfigBO2SearchPO(param);
        List<AssRejectReasonConfig> list = assRejectReasonConfigRepository.list(searchPO);
        return AssRejectReasonConfigDomainMapping.INSTANCE.assRejectReasonConfigPO2ListBO(list);
    }

    public List<AssRejectReasonConfigRespBO.DetailBO> getListByType(Integer assType){
        List<AssRejectReasonConfig> listByType = assRejectReasonConfigRepository.getListByType(assType);
        return AssRejectReasonConfigDomainMapping.INSTANCE.assRejectReasonConfigPO2ListBO(listByType);
    }

    public Page<AssRejectReasonConfigRespBO.DetailBO> page(AssRejectReasonConfigReqBO.PageBO param) {
        AssRejectReasonConfigReqPO.PagePO pagePO = AssRejectReasonConfigDomainMapping.INSTANCE.assRejectReasonConfigBO2PagePO(param);
        Page<AssRejectReasonConfig> page = assRejectReasonConfigRepository.page(pagePO);
        return AssRejectReasonConfigDomainMapping.INSTANCE.assRejectReasonConfigPO2PageBO(page);
    }

}

