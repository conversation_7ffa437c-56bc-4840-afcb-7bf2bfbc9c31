package com.pxb7.mall.trade.ass.domain.mapping;

import com.pxb7.mall.trade.ass.domain.model.AssRetrieveGameConfigDetailReqBO;
import com.pxb7.mall.trade.ass.domain.model.AssRetrieveGameConfigDetailRespBO;
import com.pxb7.mall.trade.ass.infra.model.AssRetrieveGameConfigDetailReqPO;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.AssRetrieveGameConfigDetail;

import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;


@Mapper
public interface AssRetrieveGameConfigDetailDomainMapping {

    AssRetrieveGameConfigDetailDomainMapping INSTANCE = Mappers.getMapper(AssRetrieveGameConfigDetailDomainMapping.class);


    AssRetrieveGameConfigDetailReqPO.AddPO assRetrieveGameConfigDetailBO2AddPO(AssRetrieveGameConfigDetailReqBO.AddBO source);

    AssRetrieveGameConfigDetailReqPO.UpdatePO assRetrieveGameConfigDetailBO2UpdatePO(AssRetrieveGameConfigDetailReqBO.UpdateBO source);

    AssRetrieveGameConfigDetailReqPO.DelPO assRetrieveGameConfigDetailBO2DelPO(AssRetrieveGameConfigDetailReqBO.DelBO source);

    AssRetrieveGameConfigDetailReqPO.SearchPO assRetrieveGameConfigDetailBO2SearchPO(AssRetrieveGameConfigDetailReqBO.SearchBO source);

    AssRetrieveGameConfigDetailReqPO.PagePO assRetrieveGameConfigDetailBO2PagePO(AssRetrieveGameConfigDetailReqBO.PageBO source);

    AssRetrieveGameConfigDetailRespBO.DetailBO assRetrieveGameConfigDetailPO2DetailBO(AssRetrieveGameConfigDetail source);

    List<AssRetrieveGameConfigDetailRespBO.DetailBO> assRetrieveGameConfigDetailPO2ListBO(List<AssRetrieveGameConfigDetail> source);

    Page<AssRetrieveGameConfigDetailRespBO.DetailBO> assRetrieveGameConfigDetailPO2PageBO(Page<AssRetrieveGameConfigDetail> source);

}


