package com.pxb7.mall.trade.ass.domain.refund.invoke3rd.integration.alipay;

import com.alibaba.fastjson.JSON;
import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.AlipayConfig;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.internal.util.AlipayLogger;
import com.alipay.api.request.AlipayTradeFastpayRefundQueryRequest;
import com.alipay.api.request.AlipayTradeRefundRequest;
import com.alipay.api.response.AlipayTradeFastpayRefundQueryResponse;
import com.alipay.api.response.AlipayTradeRefundResponse;
import com.google.common.base.Stopwatch;
import com.pxb7.mall.trade.ass.infra.constant.PayConstant;
import com.pxb7.mall.trade.ass.infra.remote.model.outpayparam.request.AlipayCreationReqPO;
import lombok.extern.slf4j.Slf4j;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.springframework.stereotype.Service;

import java.security.Security;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class AlipayOrderClient {
    private static final BouncyCastleProvider provider = new BouncyCastleProvider();

    static {
        Security.addProvider(provider);
        AlipayLogger.setNeedEnableLogger(false);
    }

    private static final Map<String, AlipayClient> requestMap = new ConcurrentHashMap<>();

    public AlipayTradeRefundResponse refund(AlipayTradeRefundRequest request, AlipayCreationReqPO req) throws AlipayApiException {
        log.info("支付宝退款请求参数,request={}", JSON.toJSONString(request));
        AlipayClient alipayClient = getClient(req);
        Stopwatch elapsedTime = Stopwatch.createStarted();
        AlipayTradeRefundResponse response = alipayClient.certificateExecute(request);
        elapsedTime.stop();
        log.info("支付宝退款请求耗时{}ms,response={}", elapsedTime.elapsed(TimeUnit.MILLISECONDS), JSON.toJSONString(response));
        return response;
    }

    public AlipayTradeFastpayRefundQueryResponse refundQuery(AlipayTradeFastpayRefundQueryRequest request, AlipayCreationReqPO req) throws AlipayApiException {
        log.info("支付宝退款查询请求参数,request={}", JSON.toJSONString(request));
        AlipayClient client = getClient(req);
        Stopwatch elapsedTime = Stopwatch.createStarted();
        AlipayTradeFastpayRefundQueryResponse response = client.certificateExecute(request);
        elapsedTime.stop();
        log.info("支付宝退款查询请求耗时{}ms,response={}", elapsedTime.elapsed(TimeUnit.MILLISECONDS), JSON.toJSONString(response));
        return response;
    }

    /**
     * 动态初始化证书配置
     */
    private AlipayClient getClient(AlipayCreationReqPO req) throws AlipayApiException {
        String merchantId = req.getAppId();
        AlipayClient alipayClient = requestMap.get(merchantId);
        if (Objects.nonNull(alipayClient)) {
            return alipayClient;
        }
        log.info("开始支付宝AlipayClient初始化,商户号:{}", merchantId);
        AlipayClient client = new DefaultAlipayClient(initOutPaymentParam(req));
        requestMap.put(merchantId, client);
        log.info("支付宝AlipayClient初始化成功,商户号:{}", merchantId);
        return client;
    }

    /**
     * 初始化支付宝配置
     *
     * @param
     * @param
     * @return
     */
    private AlipayConfig initOutPaymentParam(AlipayCreationReqPO req) {
        AlipayConfig config = new AlipayConfig();
        // 支付宝地址
        config.setServerUrl(PayConstant.SERVER_URL);
        config.setSignType(PayConstant.SIGN_TYPE_RSA2);
        config.setCharset(PayConstant.UTF8);
        config.setFormat(PayConstant.ALIPAY_FORMAT);
        config.setAppId(req.getAppId());
        // 支付宝公钥证书内容 alipayCertPublicKey_RSA2.crt
        config.setAlipayPublicCertContent(req.getPublicCertKey());
        // 应用公钥证书内容 appCertPublicKey_2021004171671410.crt
        config.setAppCertContent(req.getPublicKey());
        // 支付宝根证书内容 alipayRootCert.crt
        config.setRootCertContent(req.getRootCertKey());
        // 应用私钥证书内容
        config.setPrivateKey(req.getPrivateKey());
        return config;
    }
}
