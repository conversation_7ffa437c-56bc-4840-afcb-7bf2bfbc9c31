package com.pxb7.mall.trade.ass.domain.refund.converter;

import com.pxb7.mall.trade.ass.client.dto.request.refund.RefundInvokeReqDTO;
import com.pxb7.mall.trade.ass.client.dto.request.refund.RefundQueryReqDTO;
import com.pxb7.mall.trade.ass.client.dto.response.refund.RefundInvokeRespDTO;
import com.pxb7.mall.trade.ass.client.dto.response.refund.RefundQueryRespDTO;
import com.pxb7.mall.trade.ass.domain.refund.model.RefundInvokeReqBO;
import com.pxb7.mall.trade.ass.domain.refund.model.RefundInvokeRespBO;
import com.pxb7.mall.trade.ass.domain.refund.model.RefundQueryReqBO;
import com.pxb7.mall.trade.ass.domain.refund.model.RefundQueryRespBO;
import com.pxb7.mall.trade.ass.infra.enums.RefundCallBackStatusEnum;

public class RefundParamConverter {

    public static RefundInvokeReqBO convertToRefundBO(RefundInvokeReqDTO reqDTO) {
        RefundInvokeReqBO reqBO = new RefundInvokeReqBO();
        reqBO.setPayLogId(reqDTO.getInnerTradeNo());
        reqBO.setPayType(reqDTO.getPayType());
        reqBO.setTotalAmount(reqDTO.getTotalAmount());
        reqBO.setUserId(reqDTO.getUserId());
        reqBO.setPayChannel(reqDTO.getPayChannel());
        reqBO.setRefundAmount(reqDTO.getRefundAmount());
        reqBO.setAlreadyRefundAmount(reqDTO.getAlreadyRefundAmount());
        reqBO.setOutPayParam(reqDTO.getOutPayParam());
        reqBO.setRefundTradeNo(reqDTO.getRefundTradeNo());
        return reqBO;
    }

    public static RefundInvokeRespDTO convertToRefundDTO(RefundInvokeRespBO reqBO) {
        RefundInvokeRespDTO reqDTO = new RefundInvokeRespDTO();
        reqDTO.setSuccess(RefundCallBackStatusEnum.SUCCESS.equals(reqBO.getStatusEnum()));
        reqDTO.setResult(reqBO.getCallBackStr());
        return reqDTO;
    }

    public static RefundQueryReqBO convertToRefundQueryBO(RefundQueryReqDTO reqDTO) {
        RefundQueryReqBO reqBO = new RefundQueryReqBO();
        reqBO.setPayChannel(reqDTO.getPayChannel());
        reqBO.setRefundTradeNo(reqDTO.getRefundTradeNo());
        reqBO.setPayLogId(reqDTO.getPayTradeNo());
        reqBO.setAlreadyRefundAmount(reqDTO.getAlreadyRefundAmount());
        reqBO.setRefundAmount(reqDTO.getRefundAmount());
        reqBO.setOutPayParam(reqDTO.getOutPayParam());
        return reqBO;
    }

    public static RefundQueryRespDTO convertToRefundQueryDTO(RefundQueryRespBO reqBO) {
        RefundQueryRespDTO reqDTO = new RefundQueryRespDTO();
        reqDTO.setRefundTradeNo(reqBO.getRefundTradeNo());
        reqDTO.setOutTradeNo(reqBO.getRefundOutTradeNo());
        reqDTO.setRefundTradeStatus(reqBO.getRefundTradeStatus());
        reqDTO.setResult(reqBO.getResult());
        reqDTO.setRefundAmount(reqBO.getRefundAmount().getFen());
        reqDTO.setPayTime(reqBO.getSuccessTime());
        reqDTO.setAlreadyRefundAmount(reqBO.getAlreadyRefundAmount());

        return reqDTO;
    }

}
