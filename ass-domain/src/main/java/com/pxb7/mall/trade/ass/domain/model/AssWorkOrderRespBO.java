package com.pxb7.mall.trade.ass.domain.model;

import java.util.Date;
import java.time.*;
import java.math.BigDecimal;

import com.pxb7.mall.trade.ass.client.enums.AssScheduleSourceTypeEnums;
import com.pxb7.mall.trade.ass.infra.repository.es.entity.AssInfoAggregation;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.ToString;
import lombok.experimental.FieldNameConstants;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.DateFormat;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;


/**
 * 售后工单(AssWorkOrder)实体类
 *
 * <AUTHOR>
 * @since 2025-07-30 13:47:52
 */
public class AssWorkOrderRespBO {


    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class DetailBO {
        /**
         * 自增id
         */
        private Long id;
        /**
         * 售后工单号
         */
        private String workOrderId;
        /**
         * 订单ID
         */
        private String orderItemId;
        /**
         * 订单ID
         */
        private String roomId;
        /**
         * 售后类型1找回2纠纷
         */
        private Integer assType;
        /**
         * 关联找回/纠纷工单号
         */
        private String relOrderId;
        /**
         * 0:处理中 1:已完成 2:已取消
         */
        private Integer assStatus;
        /**
         * 售后处理结果描述
         */
        private String assStatusMemo;

        /**
         * 发起售后申请时间
         */
        private LocalDateTime applyTime;
        /**
         * 预计完成时间
         */
        private LocalDateTime expectedTime;
        /**
         * 完成时间
         */
        private LocalDateTime completeTime;
        /**
         * 是否已读 1:已读 0:未读
         */
        private Boolean readFlag;
        /**
         * 创建人id
         */
        private String createUserId;
        /**
         * 创建时间
         */
        private LocalDateTime createTime;
        /**
         * 更新人id
         */
        private String updateUserId;
        /**
         * 更新时间
         */
        private LocalDateTime updateTime;
        /**
         * 是否删除 1:已删除 0:未删除
         */
        private Boolean deleted;

        private String scheduleId;

        /**
         * 来源1:c端用户 2:客服 3:admin用户
         *
         * @see AssScheduleSourceTypeEnums
         */
        private Integer sourceType;
        /**
         * 申请人类型 1:买家 2:卖家'
         */
        private Integer proposerType;

        /**
         * 是否显示加入群聊按钮
         */
        private Boolean showJoinGroup;

    }


    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class AssInfoPageDetailBO {
        /*************************************************售后单信息****************************************************/
        /**
         * 售后单ID
         */
        private String assWorkOrderId;

        /*************************************************订单信息****************************************************/
        /**
         * 订单ID
         */
        @Id
        private String orderItemId;
        /**
         * 商品id
         */
        private String productId;
        /**
         * 游戏ID
         */
        private String gameId;
        /**
         * 商品类型: 1账号 2充值 3金币 4装备 5初始号 20诚心卖
         */
        private Integer productType;
        /**
         * 1待付款 2交易中 3待结算 4已成交 5已取消 (待支付是买家, 其他都是卖家状态) 6退款已取消
         */
        private Integer orderItemStatus;

        private String buyerPhone;
        /**
         * 订单行金额
         */
        private Long orderItemAmount;
        /**
         * 订单行应付款金额
         */
        private Long orderItemPayAmount;
        /**
         * 订单行实付金额
         */
        private Long orderItemActualPayAmount;

        /**
         * 商品销售价格
         */
        private Long productSalePrice;

        /**
         * 购买的商品数量
         */
        private Integer productQuantity;
        /**
         * 订单完结时间
         */
        private LocalDateTime completeTime;
        /**
         * 订单取消时间
         */
        private LocalDateTime cancelTime;

        // 订单创建时间
        private LocalDateTime createTime;

        // 索引更新时间
        private LocalDateTime indexUpdateTime;
        /**
         * 商品名称
         */
        private String productName;
        /**
         * 商品短标
         */
        private String productShortName;
        /**
         * 商品图片
         */
        private String productPic;
        /**
         * 商品属性
         */
        private String productAttr;
        /**
         * 账号信息
         */
        private String gameAccount;

        private String productUniqueNo;
        /**
         * 游戏名称
         */
        private String gameName;
        /**
         * 订单支付时间
         */
        private String payTime;
        /**
         * 游戏名称
         */
        private String gameAttr;
        /**
         * 交付群聊房间id, 智能交付是新群交付, 中介订单是原群交付
         */
        private String deliveryRoomId;
        /**
         * 买家id
         */
        private String buyerId;
        /**
         * 买家身份 1散户 2号商
         */
        private Integer buyerUserType;

        private String buyerMerchantId;
        /**
         * 卖家id,诚心卖的卖家是系统
         */
        private String sellerId;
        /**
         * 卖家身份 0系统 1散户 2号商
         */
        private Integer sellerUserType;

        private String sellerMerchantId;

        /**
         * 包赔服务
         */
        private String indemnityName;


        //售后工单模块

        /**
         * 最新售后工单日志
         */
        private AssWorkOrderLogBO assLatestWorkOrderLog;

        /**
         * 售后类型: 1:找回 2:纠纷
         */
        private Integer assType;

        /**
         * 售后状态: 0:处理中 1:已完成 2:已取消
         */
        private Integer assStatus;

        /**
         * 售后工单状态备注
         */
        private String assStatusMemo;

        /**
         * 售后子订单id
         */
        private String assSubOrderId;
        /**
         * 售后完成时间
         */
        private LocalDateTime assCompleteTime;
        /**
         * 售后期望完成时间
         */
        private LocalDateTime assExpectedTime;

        /**
         * 售后工单是否已读 0:未读 1:已读
         */
        private Integer readFlag;

        /**
         * 售后申请时间
         */
        private LocalDateTime assApplyTime;


        /**
         * 售后单更新时间
         */
        private LocalDateTime assUpdateTime;

        /**
         * 售后创建人id
         */
        private String assCreateUserId;
    }

    @Setter
    @Getter
    @Accessors(chain = true)
    public static class AssWorkOrderLogBO {
        /**
         * 日志id
         */
        private String workOrderLogId;

        /**
         * 日志标题
         */
        private String title;

        /**
         * 售后类型1找回2纠纷
         */
        private Integer assType;
        /**
         * 0:全展示 1:用户端展示 2:后台展示
         */
        private Integer showType;
        /**
         * 用户端展示操作内容
         */
        private String content;
        /**
         * admin展示操作内容
         */
        private String adminContent;
        /**
         * 日志添加方式1系统自动产生的日志 2手动添加的日志
         */
        private Integer addWay;
        /**
         * 节点id
         */
        private String nodeId;
        /**
         * 当前节点状态描述
         */
        private String nodeDesc;

        /**
         * 通知类型 0:不通知 1:追回账号,待卖家换绑2:追回号款,待卖家提供收款账号3:售后到期,待买家接受赔付
         */
        private Integer noticeType;
        /**
         * 进群提醒文案
         */
        private String joinGroupMsg;
        /**
         * 创建人id
         */
        private String createUserId;
        /**
         * 记录创建时间
         */
        private LocalDateTime createTime;
        /**
         * 更新人id
         */
        private String updateUserId;

    }

}

