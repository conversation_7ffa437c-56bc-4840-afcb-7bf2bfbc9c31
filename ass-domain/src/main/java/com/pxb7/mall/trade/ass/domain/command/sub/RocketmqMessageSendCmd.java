package com.pxb7.mall.trade.ass.domain.command.sub;

import cn.hutool.core.util.StrUtil;
import com.alibaba.cola.exception.SysException;
import com.pxb7.mall.trade.ass.domain.command.AbstractCommand;
import com.pxb7.mall.trade.ass.domain.command.CommandEnum;
import com.pxb7.mall.trade.ass.domain.command.sub.bo.RocketMqMessageBO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.apis.producer.SendReceipt;
import org.apache.rocketmq.client.core.RocketMQClientTemplate;
import org.apache.rocketmq.client.support.RocketMQHeaders;
import org.apache.rocketmq.client.support.RocketMQUtil;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.Objects;


/**
 * RocketmqMessageSendCmd
 */
@Component
@Slf4j
public class RocketmqMessageSendCmd extends AbstractCommand<RocketMqMessageBO> {
    @Resource
    private RocketMQClientTemplate rocketMQClientTemplate;

    @Override
    protected Class<?> getCommandClass() {
        return RocketMqMessageBO.class;
    }

    @Override
    protected CommandEnum getCommandType() {
        return CommandEnum.ROCKET_MQ_MESSAGE_SEND;
    }

    @Override
    public void execute(RocketMqMessageBO reqDTO) {
        log.info("[异步任务] [消息发送] 开始执行 topic:{} tag:{} data:{}", reqDTO.getTopic(), reqDTO.getTag(), reqDTO);

        SendReceipt sendReceipt;
        Message<String> message = MessageBuilder.withPayload(reqDTO.getContent())
                .setHeader(RocketMQUtil.toRocketHeaderKey(RocketMQHeaders.KEYS), reqDTO.getKey())
                .build();
        if (reqDTO.isDelay() || RocketMqMessageBO.MessageTypeEnum.DELAY.equals(reqDTO.getMessageType())) {
            /**
             * 延迟消息：兼容老逻辑 isDelay，发布后线上观察一段时间 才可以删除isDelay字段！！！
             */
            sendReceipt = rocketMQClientTemplate.syncSendDelayMessage(buildDestination(reqDTO.getTopic(), reqDTO.getTag()), message, Duration.ofSeconds(reqDTO.getSeconds()));
        } else if (null == reqDTO.getMessageType() || RocketMqMessageBO.MessageTypeEnum.NORMAL.equals(reqDTO.getMessageType())) {
            /**
             *  普通消息：messageType字段为新增字段，在发布过程中会出现项目代码新老共存的情况，故在此兼容 ！！！！
             */
            sendReceipt = rocketMQClientTemplate.syncSendNormalMessage(buildDestination(reqDTO.getTopic(), reqDTO.getTag()), message);
        } else if (RocketMqMessageBO.MessageTypeEnum.FIFO.equals(reqDTO.getMessageType())) {
            // 顺序消息
            sendReceipt = rocketMQClientTemplate.syncSendFifoMessage(buildDestination(reqDTO.getTopic(), reqDTO.getTag()), message, reqDTO.getMessageGroup());
        } else {
            throw new SysException("不支持的消息类型");
        }
        if (Objects.isNull(sendReceipt)) {
            throw new SysException("消息发送失败");
        }
        log.info("[异步任务] [消息发送] 发送成功 topic:{} messageId:{}", reqDTO.getTopic(), sendReceipt.getMessageId());
    }

    public String buildDestination(String topic, String tag) {
        return StrUtil.isNotBlank(tag) ? topic + ":" + tag : topic;
    }
}
