package com.pxb7.mall.trade.ass.domain.model.reqeust;

import java.io.Serializable;

import com.pxb7.mall.trade.ass.client.enums.AssScheduleNode;
import com.pxb7.mall.trade.ass.client.enums.AssScheduleSourceTypeEnums;
import com.pxb7.mall.trade.ass.client.enums.AssWoType;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/8/12
 */
@Data
public class AssScheduleReqBO implements Serializable {

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 售后流程进度id
     */
    private String scheduleId;

    /**
     * 工单id
     */
    private String workOrderId;

    /**
     * 售后类型1找回2纠纷
     */
    private AssWoType assType;

    /**
     * 接待客服id
     */
    private String recvCustomerId;

    /**
     * 审核客服id
     */
    private String auditCustomerId;

    /**
     * 房间id
     */
    private String roomId;

    /**
     * 节点
     */
    private AssScheduleNode node;

    /**
     * 纠纷处理结果
     */
    private String disputeResult;

    /**
     * 其他数据
     */
    private String data;
    /**
     * 来源1:c端用户 2:客服 3:admin用户
     *
     * @see AssScheduleSourceTypeEnums
     */
    private Integer sourceType;

}
