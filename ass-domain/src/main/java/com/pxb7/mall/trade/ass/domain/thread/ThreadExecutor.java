package com.pxb7.mall.trade.ass.domain.thread;

import java.util.concurrent.Callable;
import java.util.concurrent.Future;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 线程池接口
 */
public interface ThreadExecutor {

    /**
     * 异步任务执行入口
     *
     * @param executor Executor
     */
    void execute(Executor executor);

    /**
     * 带拒绝策略回调接口的
     *
     * @param executor        Executor
     * @param executionReject ExecutionReject
     */
    void execute(Executor executor, ExecutionReject executionReject);

    /**
     * 带返回结果的任务执行器
     *
     * @param task 任务
     * @param <V>  返回结果类型
     * @return <V>
     */
    <V> Future<V> submit(Callable<V> task);

    /**
     * 设置核心线程数
     *
     * @param size size
     */
    void setCorePoolSize(Integer size);

    /**
     * 任务执行器
     */
    interface Executor {

        /**
         * 任务执行接口
         */
        void execute();
    }

    /**
     * 当队列满、线程不足或是其他原因，可能导致的执行任务未被正常执行时的回调接口
     */
    interface ExecutionReject {

        /**
         * 线程池拒绝策略
         *
         * @param er              ExecutorRunnable
         * @param executorService ThreadPoolExecutor
         */
        void rejected(ExecutorRunnable er, ThreadPoolExecutor executorService);
    }

    /**
     * 获取线程池
     */
    ThreadPoolExecutor getThreadPoolExecutor();

}
