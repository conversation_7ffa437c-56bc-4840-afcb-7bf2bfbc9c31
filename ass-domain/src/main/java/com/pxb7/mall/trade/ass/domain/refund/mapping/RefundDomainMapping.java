package com.pxb7.mall.trade.ass.domain.refund.mapping;

import com.pxb7.mall.trade.ass.domain.refund.model.RefundOrderItemPageRespBO;
import com.pxb7.mall.trade.ass.domain.refund.model.RefundVoucherBO;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.RefundVoucher;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.RefundVoucherDetail;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface RefundDomainMapping {

    RefundDomainMapping INSTANCE = Mappers.getMapper(RefundDomainMapping.class);


    @Mapping(source = "refundVoucher.refundVoucherId", target = "refundVoucherId")
    @Mapping(source = "refundVoucher.orderItemId", target = "orderItemId")
    RefundOrderItemPageRespBO toRefundOrderItemPageRespBO(RefundVoucher refundVoucher, RefundVoucherDetail refundVoucherDetail);

    List<RefundVoucherBO> toRefundVoucherBOList(List<RefundVoucher> refundVouchers);

}
