package com.pxb7.mall.trade.ass.domain.refund.invoke3rd.impl;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.alibaba.cola.exception.BizException;
import com.alibaba.cola.exception.SysException;
import com.alibaba.cola.extension.Extension;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson2.JSON;
import com.pxb7.mall.trade.ass.client.dto.model.refund.RefundTradeStatusEnum;
import com.pxb7.mall.trade.ass.client.dto.request.refund.RefundInvokeReqDTO;
import com.pxb7.mall.trade.ass.client.dto.request.refund.RefundQueryReqDTO;
import com.pxb7.mall.trade.ass.client.dto.response.refund.RefundInvokeRespDTO;
import com.pxb7.mall.trade.ass.client.dto.response.refund.RefundQueryRespDTO;
import com.pxb7.mall.trade.ass.domain.PayCompanyAccountDomainService;
import com.pxb7.mall.trade.ass.domain.refund.invoke3rd.PayRefundExtPt;
import com.pxb7.mall.trade.ass.domain.refund.model.*;
import com.pxb7.mall.trade.ass.infra.config.nacos.RefundErrorCodeConfig;
import com.pxb7.mall.trade.ass.infra.enums.ErrorCode;
import com.pxb7.mall.trade.ass.infra.enums.RefundCallBackStatusEnum;
import com.pxb7.mall.trade.ass.infra.model.Money;
import com.pxb7.mall.trade.ass.infra.remote.model.outpayparam.request.JdPayOutParamPO;
import com.pxb7.mall.trade.ass.infra.remote.service.http.JdPayHttpApiService;
import com.pxb7.mall.trade.ass.infra.remote.service.http.dto.request.JdPayReqPO;
import com.pxb7.mall.trade.ass.infra.remote.service.http.dto.response.JdPayRespPO;
import com.pxb7.mall.trade.ass.infra.util.StringUtil;
import com.pxb7.mall.trade.ass.infra.util.jdPayUtil.JdPayUtil;
import com.pxb7.mall.trade.order.client.enums.pay.JdPayStatusEnum;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * 京东协议支付退款
 */
@Slf4j
@Service
@Extension(bizId = "jdPayRefund")
public class JdPayRefundExt implements PayRefundExtPt {

    @Resource
    private JdPayHttpApiService jdPayHttpApiService;
    @Resource
    private PayCompanyAccountDomainService payCompanyAccountDomainService;
    @Resource
    private RefundErrorCodeConfig refundErrorCodeConfig;

    /**
     * 京东银行卡 退款回调
     */
    @Value("${refund.jdpay.callback.url}")
    public String jdPayRefundCallbackUrl;


    @Override
    public RefundInvokeRespDTO refund(RefundInvokeReqDTO refundInvokeReqDTO) throws Exception {
        throw new SysException("not support");
    }

    @Override
    public RefundQueryRespDTO refundQuery(RefundQueryReqDTO refundQueryReqDTO) throws Exception {
        throw new SysException("not support");
    }

    /**
     * 发起退款 TODO JD***
     *
     *  返回结果真实示例：
     * {
     *     "outTradeNo": "15738468811574523424",
     *     "originalOutTradeNo": "15735307794023323424",
     *     "tradeNo": "202506181421082010620505883364",
     *     "finishDate": null,
     *     "tradeAmount": 1,
     *     "currency": "CNY",
     *     "tradeStatus": "ACSU",
     *     "returnParams": null
     * }
     */
    @Override
    public RefundInvokeRespBO refundV2(RefundInvokeReqBO reqBO) {
        log.info("[京东支付] 去退款 入参:{}", reqBO);
        //外部支付参数
        JdPayOutParamPO paramPO = JSON.parseObject(reqBO.getOutPayParam(), JdPayOutParamPO.class);
        //组装请求参数
        JdPayReqPO.RefundParam param = new JdPayReqPO.RefundParam();
        param.setOutTradeNo(reqBO.getRefundTradeNo());
        param.setOriginalOutTradeNo(reqBO.getPayLogId());
        param.setTradeAmount(String.valueOf(reqBO.getRefundAmount()));
        param.setNotifyUrl(jdPayRefundCallbackUrl);
        try {
            log.info("[京东支付] 去退款 invoke param:{}", JSON.toJSONString(param));
            JdPayRespPO.RefundRespPO respPO = jdPayHttpApiService.refund(param, paramPO);
            log.info("[京东支付] 去退款 invoke resp:{}", respPO);
            checkRefundRes(respPO);
        } catch (BizException e) {
            log.error("[京东支付] 去退款 错误:{}", param, e);
            throw e;
        } catch (Exception e) {
            log.error("[京东支付] 去退款 异常:{}", param, e);
            throw new SysException(e.getMessage());
        }
        return RefundInvokeRespBO.builder().statusEnum(RefundCallBackStatusEnum.PROCESSING).build();
    }

    /**
     * {
     * "resultCode": "0000",
     * "resultDesc": "成功",
     * "outTradeNo": "refund_zxj_006",
     * "originalOutTradeNo": "zxj_006",
     * "tradeNo": "202506112117212011820771111363",
     * "finishDate": null,
     * "tradeAmount": "1",
     * "currency": "CNY",
     * "tradeStatus": "ACSU",
     * "returnParams": null
     * }
     */
    private void checkRefundRes(JdPayRespPO.BaseRespPO respPO) {
        if (!"0000".equals(respPO.getResultCode())) {
            log.warn("【京东支付】发起退款失败，respPO = {}", respPO);
        }
    }

    /**
     * 退款查询   TODO JD***
     */
    @Override
    public RefundQueryRespBO refundQueryV2(RefundQueryReqBO reqBO) {
        log.info("[京东支付] 退款查询 入参:{}", reqBO);
        //外部支付参数
        JdPayOutParamPO paramPO = JSON.parseObject(reqBO.getOutPayParam(), JdPayOutParamPO.class);
        //组装请求参数
        JdPayReqPO.RefundQueryParam param = new JdPayReqPO.RefundQueryParam();
        param.setOutTradeNo(reqBO.getRefundTradeNo());
        try {
            log.info("[京东支付] 退款查询 param:{}", param);
            JdPayRespPO.RefundRespPO respPO = jdPayHttpApiService.refundQuery(param, paramPO);
            log.info("[京东支付] 退款查询  resp:{}", respPO);
            // 解析
            return getRefundQueryRespBO(respPO);
        } catch (Exception e) {
            log.error("[京东支付] 退款查询 异常:{}", reqBO, e);
            throw new SysException(e.getMessage());
        }
    }

    /**
     * {
     * "resultCode": "0000",
     * "resultDesc": "成功",
     * "outTradeNo": "refund_zxj_006",
     * "originalOutTradeNo": "zxj_006",
     * "tradeNo": "202506112117212011820771111363",
     * "finishDate": "20250611211722",
     * "tradeAmount": "1",
     * "currency": "CNY",
     * "tradeStatus": "FINI",
     * "returnParams": null
     * }
     */
    private RefundQueryRespBO getRefundQueryRespBO(JdPayRespPO.RefundRespPO respPO) {
        log.info("[京东支付]退款请求响应结果，respPO = {}", respPO);
        //初始化出参
        RefundQueryRespBO respBO = new RefundQueryRespBO()
                .setResult(JSON.toJSONString(respPO))
                .setRefundTradeNo(respPO.getOutTradeNo());
        // 退款请求受理异常 TODO JD*** 京东接口很奇怪，响应结果会返回错误码，但是错误码是正常结果
        if (!"0000".equals(respPO.getResultCode())) {
            log.warn("[京东支付]退款响应失败，respPO = {}", respPO);
            if (refundErrorCodeConfig.getFailErrorCodes().contains("JD_"+respPO.getResultCode())) {
                log.warn("[京东支付]退款响应，发现特定错误码，执行退款失败操作。respPO = {}", respPO);
                respBO.setRefundTradeStatus(RefundTradeStatusEnum.FAIL.getValue());
                return respBO;
            }
            throw new BizException(respPO.getResultCode(), respPO.getResultDesc());
        }

        //退款成功
        if (JdPayStatusEnum.FINI.eq(respPO.getTradeStatus())) {
            respBO.setRefundAmount(new Money(Long.valueOf(respPO.getTradeAmount())))
                    .setRefundOutTradeNo(respPO.getTradeNo())
                    .setRefundTradeStatus(RefundTradeStatusEnum.SUCCESS.getValue())
                    .setSuccessTime(LocalDateTimeUtil.parse(respPO.getFinishDate(), "yyyyMMddHHmmss"));
        }
        //TODO 这里状态再检查下： REFU = 退款中 ：BUID = 交易建立（交易处理中）;WPAR = 等待支付结果（交易处理中）;ACSU = 受理成功（交易处理中）
        else if (JdPayStatusEnum.REFU.eq(respPO.getTradeStatus())
                || JdPayStatusEnum.BUID.eq(respPO.getTradeStatus())
                || JdPayStatusEnum.WPAR.eq(respPO.getTradeStatus())
                || JdPayStatusEnum.ACSU.eq(respPO.getTradeStatus())) {
            respBO.setRefundTradeStatus(RefundTradeStatusEnum.EXECUTING.getValue());
        }
        //退款失败
        else if (JdPayStatusEnum.CLOS.eq(respPO.getTradeStatus())) {
            respBO.setRefundTradeStatus(RefundTradeStatusEnum.FAIL.getValue());
        } else {
            log.warn("【京东支付】退款响应解析失败，状态非法 = {}", respPO);
            throw new BizException(ErrorCode.REFUND_CHECK_ERROR.getErrCode(), ErrorCode.REFUND_CHECK_ERROR.getErrDesc());
        }
        log.info("【京东支付】退款响应解析，respBO = {}", respBO);
        return respBO;
    }


    /**
     * 退款回调验签&解密
     * {
     * "signData": "8B6F430F73B9E264F4CAF74C0FC0398665EC0C71F2046466C915EC79CA09BFE5",
     * "charset": "UTF-8",
     * "encType": "AP7",
     * "code": "00000",
     * "signType": "SHA-256",
     * "respData": "eyJ0cmFkZUFtb3VudCI6IjEiLCJ0cmFkZU5vIjoiMjAyNTA2MTEyMTE3MjEyMDExODIwNzcxMTExMzYzIiwib3V0VHJhZGVObyI6InJlZnVuZF96eGpfMDA2IiwidHJhZGVTdGF0dXMiOiJGSU5JIiwicmVzdWx0Q29kZSI6IjAwMDAiLCJmaW5pc2hEYXRlIjoiMjAyNTA2MTEyMTE3MjEiLCJjdXJyZW5jeSI6IkNOWSIsIm9yaWdpbmFsT3V0VHJhZGVObyI6Inp4al8wMDYiLCJyZXN1bHREZXNjIjoi5oiQ5YqfIn0=",
     * "formatType": "JSON",
     * "desc": "成功",
     * "merchantNo": "153915483003"
     * }
     */
    @Override
    public RefundCallBackBO refundCallbackSign(RefundCallBackReqBO reqBO) throws Exception {
        log.info("【京东支付】退款回调验签 reqBO:{}", reqBO);
        String callbackData = reqBO.getBodyStr();
        //外部支付参数
        com.alibaba.fastjson2.JSONObject jsonData = JSON.parseObject(callbackData);
        String merchantNo = (String) jsonData.get("merchantNo");
        if (StringUtil.isBlank(merchantNo)) {
            log.error("[京东支付] 退款回调  商户号数据缺失 : {}", jsonData.toJSONString());
            throw new BizException("[京东支付]退款回调 处理失败，数据缺失");
        }
        //查询支付外部参数
        String outPaymentParam = payCompanyAccountDomainService.getDecryptOutParamByAccount(merchantNo);
        JdPayOutParamPO paramPO = JSON.parseObject(outPaymentParam, JdPayOutParamPO.class);
        //验签&解密&返回结果
        JSONObject jsonObject = JdPayUtil.verifySignAndReturn(callbackData, paramPO.getSignKey());
        log.info("【京东支付】退款回调 result = {}", jsonObject.toJSONString());
        JdPayRespPO.RefundRespPO respPO = jsonObject.toJavaObject(JdPayRespPO.RefundRespPO.class);
        //校验 & 转换
        RefundQueryRespBO respBO = getRefundQueryRespBO(respPO);
        //京东支付回调场景下，只有退款成功 或 失败 才执行更新退款单据操作
        if (RefundTradeStatusEnum.SUCCESS.eq(respBO.getRefundTradeStatus()) || RefundTradeStatusEnum.FAIL.eq(respBO.getRefundTradeStatus())) {
            return RefundCallBackBO.builder()
                    .refundLogId(respPO.getOutTradeNo())
                    .payLogId(respPO.getOriginalOutTradeNo())
                    .refundQueryRespBO(respBO)
                    .build();
        }
        return null;
    }
}
