package com.pxb7.mall.trade.ass.domain;

import com.pxb7.mall.trade.ass.infra.model.Money;
import com.pxb7.mall.trade.ass.infra.repository.db.OrderItemRepository;
import com.pxb7.mall.trade.ass.infra.repository.db.OrderOperateRepository;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.OrderItem;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.OrderOperate;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.ViolateOrder;
import com.pxb7.mall.trade.order.client.enums.order.OrderOperateTypeEnum;
import com.pxb7.mall.trade.order.client.enums.order.OrderOptUserTypeEnum;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 订单操作日志
 */
@Service
@Slf4j
public class OrderOperateDomainService {
    @Resource
    private OrderItemRepository orderItemRepository;
    @Resource
    private OrderOperateRepository orderOperateRepository;

    public void addViolateOperate(String orderItemId, ViolateOrder violateOrder, OrderOperateTypeEnum type,
                                  OrderOptUserTypeEnum role, String userId, String userName) {
        try {
            OrderItem orderItem = orderItemRepository.getOrderItem(orderItemId);
            String optContentTmp = "%s%s %s,违约单号:%s,违约金:%s元 守约金:%s元";
            OrderOperate orderOperate = new OrderOperate();
            orderOperate.setOrderId(orderItem.getOrderId());
            orderOperate.setOrderItemId(orderItemId);
            orderOperate.setOptType(type.getValue());
            orderOperate.setOptContent(
                    String.format(optContentTmp, role.getLabel(), userName, type.getLabel(), violateOrder.getViolateId()
                            , new Money(violateOrder.getViolateAmount()).getYuan()
                            , new Money(violateOrder.getPromiseAmount()).getYuan()));
            orderOperate.setOptUserType(role.getValue());
            orderOperate.setOptUserId(userId);

            orderOperateRepository.save(orderOperate);
        } catch (Exception e) {
            log.warn("addViolateOperate error, orderItemId: {},voucherId:{}", orderItemId, violateOrder.getViolateId(), e);
        }
    }
}