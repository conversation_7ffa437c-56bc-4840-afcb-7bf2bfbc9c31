package com.pxb7.mall.trade.ass.domain.command;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.pxb7.mall.components.redis.redisson.RLockUtils;
import com.pxb7.mall.trade.ass.domain.command.sub.CommandDTO;
import com.pxb7.mall.trade.ass.infra.constant.CommandLock;
import com.pxb7.mall.trade.ass.infra.enums.ErrorCode;
import com.pxb7.mall.trade.ass.infra.exception.BusinessException;
import com.pxb7.mall.trade.ass.infra.repository.db.CommandRepository;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.CommandDO;
import com.pxb7.mall.trade.ass.infra.util.CommandStatusEnum;
import com.pxb7.mall.trade.ass.infra.util.ObjectMapperUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;

import javax.annotation.Resource;

/**
 * 指令处理
 *
 * @param <T> 数据对象
 * <AUTHOR>
 */
@Slf4j
public abstract class AbstractCommand<T> implements Command<T>, InitializingBean {

    @Value("${command.deleted:true}")
    public boolean isDelete;

    @Resource
    protected CommandRepository commandRepository;

    public void handle(CommandDTO commandDTO) {
        // 任务加锁，防止任务并发执行
        log.info("[ASS异步任务] 开始执行:commandId:{},bizId:{}, 任务类型: {}", commandDTO.getCommandId(),
                commandDTO.getBizId(), commandDTO.getCommandType());
        String lockKey = String.format(CommandLock.PREFIX, commandDTO.getCommandId());
        RLockUtils.of(lockKey, () -> {
            try {
                T data = parseData(commandDTO);

                // 可以根据data是否为空，判断是否需要执行
                if (data == null) {
                    log.warn("[ASS异步任务] [ASS] 执行失败  数据为空:{}", commandDTO.getCommandId());
                    return null;
                }
                execute(data);
                delete(commandDTO);
                log.info("[ASS异步任务] 执行成功:{},{}", commandDTO.getCommandId(), commandDTO.getBizId());
            } catch (Throwable e) {
                log.info("[ASS异步任务] 执行异常:{}", commandDTO.getCommandId(), e);
                updateFailCount(commandDTO);
            }
            return null;
        }).orElseThrow(() -> new BusinessException(ErrorCode.SYSTEM_LIMIT_ERROR));
    }

    @Override
    public T parseData(CommandDTO commandDTO) throws JsonProcessingException {
        Object o = ObjectMapperUtil.fromJson(commandDTO.getCommandContent(), getCommandClass());
        return (T) o;
    }

    // 测试环境不删除，为了方便测试重放攻击和业务回溯
    private void delete(CommandDTO commandDTO) {
        if (isDelete) {
            Long i = commandRepository.deleteByCommandId(commandDTO.getCommandId());
            if (1 != i) {
                log.error("[ASS异步任务] commandId={}, type = {} 删除异常", commandDTO.getCommandId(), commandDTO.getCommandType());
            }
            return;
        }

        boolean update = commandRepository.lambdaUpdate()
                .set(CommandDO::getCommandStatus, CommandStatusEnum.DONE.getValue())
                .eq(CommandDO::getCommandId, commandDTO.getCommandId())
                .eq(CommandDO::getCommandStatus, CommandStatusEnum.WAIT.getValue())
                .update();
        log.info("[ASS异步任务] commandId={} 更新状态为已完成, update={}", commandDTO.getCommandId(), update);
    }

    private void updateFailCount(CommandDTO commandDTO) {
        commandRepository.lambdaUpdate()
                .setSql("fail_count = fail_count + 1")
                .eq(CommandDO::getCommandId, commandDTO.getCommandId())
                .eq(CommandDO::getCommandStatus, CommandStatusEnum.WAIT.getValue())
                .update();

        if (commandDTO.getFailCount() >= 3) {
            log.error("[ASS异步任务]  严重告警!!! commandId = {}, type = {} fail count > 3", commandDTO.getCommandId(), commandDTO.getCommandType());
        }

        // 失败次数达到一定次数，置为异常，异常不会被重试
        if (commandDTO.getFailCount() >= 6) {
            log.error("[ASS异步任务] 任务异常 commandId = {}, type = {} fail count > 6", commandDTO.getCommandId(), commandDTO.getCommandType());
            commandRepository.update()
                    .eq("command_id", commandDTO.getCommandId())
                    .set("command_status", CommandStatusEnum.EXCEPTION.getValue())
                    .update();
        }
    }

    /**
     * 获取指令Class
     *
     * @return Class
     */
    protected abstract Class<?> getCommandClass();

    /**
     * 获取指令类型
     *
     * @return CommandEnum
     */
    protected abstract CommandEnum getCommandType();

    @Override
    public void afterPropertiesSet() {
        CommandFactory.register(this.getCommandType().getCode(), this);
    }
}
