package com.pxb7.mall.trade.ass.domain.mapping;

import com.pxb7.mall.trade.ass.client.dto.model.order.OrderItemAmountInfo;
import com.pxb7.mall.trade.ass.domain.model.OrderItemAmountInfoBO;
import com.pxb7.mall.trade.ass.domain.model.RefundIndemnityDetailBO;
import com.pxb7.mall.trade.ass.infra.model.RefundIndemnityDetailPO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import com.pxb7.mall.trade.ass.infra.remote.model.refund.OrderItemReceiptVoucherPO;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.ReceiptVoucher;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.ReceiptVoucherDetail;

import java.util.List;

@Mapper
public interface OrderRefundDomainMapping {

    OrderRefundDomainMapping INSTANCE = Mappers.getMapper(OrderRefundDomainMapping.class);

    @Mapping(source = "receiptVoucherDetail.orderItemId", target = "orderItemId")
    @Mapping(source = "receiptVoucherDetail.receiptVoucherId", target = "receiptVoucherId")
    OrderItemReceiptVoucherPO toReceiptVoucherPO(ReceiptVoucher receiptVoucher,
        ReceiptVoucherDetail receiptVoucherDetail);

    OrderItemAmountInfoBO toOrderItemAmountInfoBO(OrderItemAmountInfo orderItemAmountInfo);

    List<RefundIndemnityDetailBO> toRefundIndemnityDetailBOList(List<RefundIndemnityDetailPO> refundIndemnityDetails);
}
