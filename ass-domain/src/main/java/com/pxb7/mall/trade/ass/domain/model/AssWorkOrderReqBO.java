package com.pxb7.mall.trade.ass.domain.model;

import java.util.Date;
import java.time.*;
import java.math.BigDecimal;
import java.util.List;

import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.ToString;


/**
 * 售后工单(AssWorkOrder)实体类
 *
 * <AUTHOR>
 * @since 2025-07-30 13:47:52
 */
public class AssWorkOrderReqBO {

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class AddBO {

        /**
         * 售后工单号
         */
        private String workOrderId;

        /**
         * 订单ID
         */
        private String orderItemId;

        /**
         * 订单ID
         */
        private String roomId;

        /**
         * 售后类型1找回2纠纷
         */
        private Integer assType;

        /**
         * 关联找回/纠纷工单号
         */
        private String relOrderId;

        /**
         * 0:处理中 1:已完成 2:已取消
         */
        private Integer assStatus;

        /**
         * 发起售后申请时间
         */
        private LocalDateTime applyTime;

        /**
         * 预计完成时间
         */
        private LocalDateTime expectedTime;

        /**
         * 完成时间
         */
        private LocalDateTime completeTime;

        /**
         * 是否已读 1:已读 0:未读
         */
        private Boolean readFlag;

        /**
         * 创建人id
         */
        private String createUserId;

        /**
         * 更新人id
         */
        private String updateUserId;

        /**
         * 调度id
         */
        private String scheduleId;

    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class UpdateBO {

        /**
         * 自增id
         */
        private Long id;


        /**
         * 售后工单号
         */
        private String workOrderId;


        /**
         * 订单ID
         */
        private String orderItemId;


        /**
         * 订单ID
         */
        private String roomId;


        /**
         * 售后类型1找回2纠纷
         */
        private Integer assType;


        /**
         * 关联找回/纠纷工单号
         */
        private String relOrderId;


        /**
         * 0:处理中 1:已完成 2:已取消
         */
        private Integer assStatus;


        /**
         * 发起售后申请时间
         */
        private LocalDateTime applyTime;


        /**
         * 预计完成时间
         */
        private LocalDateTime expectedTime;


        /**
         * 完成时间
         */
        private LocalDateTime completeTime;


        /**
         * 是否已读 1:已读 0:未读
         */
        private Boolean readFlag;


        /**
         * 创建人id
         */
        private String createUserId;


        /**
         * 更新人id
         */
        private String updateUserId;

        /**
         * 调度id
         */
        private String scheduleId;


    }


    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class DelBO {
        private Long id;
    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class SearchBO {
        /**
         * 售后工单号
         */
        private String workOrderId;

        /**
         * 订单ID
         */
        private String orderItemId;

        /**
         * 订单ID
         */
        private String roomId;

        /**
         * 售后类型1找回2纠纷
         */
        private Integer assType;

        /**
         * 关联找回/纠纷工单号
         */
        private String relOrderId;

        /**
         * 0:处理中 1:已完成 2:已取消
         */
        private Integer assStatus;

        /**
         * 发起售后申请时间
         */
        private LocalDateTime applyTime;

        /**
         * 预计完成时间
         */
        private LocalDateTime expectedTime;

        /**
         * 完成时间
         */
        private LocalDateTime completeTime;

        /**
         * 是否已读 1:已读 0:未读
         */
        private Boolean readFlag;

        /**
         * 创建人id
         */
        private String createUserId;

        /**
         * 更新人id
         */
        private String updateUserId;

        /**
         * 调度id
         */
        private String scheduleId;

    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class PageBO {

        /**
         * 售后工单号
         */
        private String workOrderId;

        /**
         * 订单ID
         */
        private String orderItemId;

        /**
         * 订单ID
         */
        private String roomId;

        /**
         * 售后类型1找回2纠纷
         */
        private Integer assType;

        /**
         * 关联找回/纠纷工单号
         */
        private String relOrderId;

        /**
         * 0:处理中 1:已完成 2:已取消
         */
        private Integer assStatus;

        /**
         * 发起售后申请时间
         */
        private LocalDateTime applyTime;

        /**
         * 预计完成时间
         */
        private LocalDateTime expectedTime;

        /**
         * 完成时间
         */
        private LocalDateTime completeTime;

        /**
         * 是否已读 1:已读 0:未读
         */
        private Boolean readFlag;

        /**
         * 创建人id
         */
        private String createUserId;

        /**
         * 更新人id
         */
        private String updateUserId;


        /**
         * 页码，从1开始
         */
        private Integer pageIndex;
        /**
         * 每页数量
         */
        private Integer pageSize;

        /**
         * 调度id
         */
        private String scheduleId;

    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class PageAssOrderBO extends BasePageBO{
        /**
         * 下单时间
         * 周期 1本周内 2 当月 3 三个月内 4半年内 5 一年内
         */
        private Integer cycle;

        /**
         * 售后单申请时间
         * 周期 1本周内 2 当月 3 三个月内 4半年内 5 一年内
         */
        private Integer assCycle;

        /**
         * 商品类型
         */
        private Integer productType;

        /**
         * 游戏id
         */
        private String gameId;

        /**
         * 关键词（模糊查询商品标题/订单编号/商品编号）
         */
        private String keyWords;



        /***********号商**************/

        /**
         * 是否是号商(为true表示号商身份查询)
         */
        private Boolean isMerchant;


        /**
         * 订单编号
         */

        private String orderItemId;

        /**
         * 手机号（子账号）
         */
        private String telephone;

        /**
         * 商品名称
         */
        private String productName;

        /**
         * 商品ID
         */
        private String productId;

        /**
         * 商品编号
         */
        private String productUniqueNo;

        /**
         * 用户ID列表
         */
        private List<String> userIdList;

        /**
         * 商家用户ID
         */
        private String merchantUserId;

    }

}

