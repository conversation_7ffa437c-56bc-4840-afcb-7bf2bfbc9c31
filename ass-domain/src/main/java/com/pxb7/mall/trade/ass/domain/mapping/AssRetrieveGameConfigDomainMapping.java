package com.pxb7.mall.trade.ass.domain.mapping;

import com.pxb7.mall.trade.ass.domain.model.AssRetrieveGameConfigReqBO;
import com.pxb7.mall.trade.ass.domain.model.AssRetrieveGameConfigRespBO;
import com.pxb7.mall.trade.ass.infra.model.AssRetrieveGameConfigReqPO;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.AssRetrieveGameConfig;

import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;


@Mapper
public interface AssRetrieveGameConfigDomainMapping {

    AssRetrieveGameConfigDomainMapping INSTANCE = Mappers.getMapper(AssRetrieveGameConfigDomainMapping.class);


    AssRetrieveGameConfigReqPO.AddPO assRetrieveGameConfigBO2AddPO(AssRetrieveGameConfigReqBO.AddBO source);

    AssRetrieveGameConfigReqPO.UpdatePO assRetrieveGameConfigBO2UpdatePO(AssRetrieveGameConfigReqBO.UpdateBO source);

    AssRetrieveGameConfigReqPO.DelPO assRetrieveGameConfigBO2DelPO(AssRetrieveGameConfigReqBO.DelBO source);

    AssRetrieveGameConfigReqPO.SearchPO assRetrieveGameConfigBO2SearchPO(AssRetrieveGameConfigReqBO.SearchBO source);

    AssRetrieveGameConfigReqPO.PagePO assRetrieveGameConfigBO2PagePO(AssRetrieveGameConfigReqBO.PageBO source);

    AssRetrieveGameConfigRespBO.DetailBO assRetrieveGameConfigPO2DetailBO(AssRetrieveGameConfig source);

    List<AssRetrieveGameConfigRespBO.DetailBO> assRetrieveGameConfigPO2ListBO(List<AssRetrieveGameConfig> source);

    Page<AssRetrieveGameConfigRespBO.DetailBO> assRetrieveGameConfigPO2PageBO(Page<AssRetrieveGameConfig> source);

}


