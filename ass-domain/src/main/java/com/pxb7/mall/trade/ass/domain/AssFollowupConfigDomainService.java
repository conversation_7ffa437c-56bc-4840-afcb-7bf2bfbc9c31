package com.pxb7.mall.trade.ass.domain;


import java.util.List;

import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import com.pxb7.mall.trade.ass.domain.model.AssFollowupConfigReqBO;
import com.pxb7.mall.trade.ass.domain.model.AssFollowupConfigRespBO;
import com.pxb7.mall.trade.ass.domain.mapping.AssFollowupConfigDomainMapping;
import com.pxb7.mall.trade.ass.infra.model.AssFollowupConfigReqPO;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.AssFollowupConfig;
import com.pxb7.mall.trade.ass.infra.repository.db.AssFollowupConfigRepository;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;


/**
 * 跟进结果类型配置表domain服务
 *
 * <AUTHOR>
 * @since 2025-07-30 13:40:34
 */
@Service
public class AssFollowupConfigDomainService {

    @Resource
    private AssFollowupConfigRepository assFollowupConfigRepository;

    public boolean insert(AssFollowupConfigReqBO.AddBO param) {
        AssFollowupConfigReqPO.AddPO addPO = AssFollowupConfigDomainMapping.INSTANCE.assFollowupConfigBO2AddPO(param);
        return assFollowupConfigRepository.insert(addPO);
    }

    public boolean update(AssFollowupConfigReqBO.UpdateBO param) {
        AssFollowupConfigReqPO.UpdatePO updatePO = AssFollowupConfigDomainMapping.INSTANCE.assFollowupConfigBO2UpdatePO(param);
        return assFollowupConfigRepository.update(updatePO);
    }

    public boolean deleteById(AssFollowupConfigReqBO.DelBO param) {
        AssFollowupConfigReqPO.DelPO delPO = AssFollowupConfigDomainMapping.INSTANCE.assFollowupConfigBO2DelPO(param);
        return assFollowupConfigRepository.deleteById(delPO);
    }

    public AssFollowupConfigRespBO.DetailBO findById(Long id) {
        AssFollowupConfig entity = assFollowupConfigRepository.findById(id);
        return AssFollowupConfigDomainMapping.INSTANCE.assFollowupConfigPO2DetailBO(entity);

    }

    public List<AssFollowupConfigRespBO.DetailBO> list(AssFollowupConfigReqBO.SearchBO param) {
        AssFollowupConfigReqPO.SearchPO searchPO = AssFollowupConfigDomainMapping.INSTANCE.assFollowupConfigBO2SearchPO(param);
        List<AssFollowupConfig> list = assFollowupConfigRepository.list(searchPO);
        return AssFollowupConfigDomainMapping.INSTANCE.assFollowupConfigPO2ListBO(list);
    }

    public Page<AssFollowupConfigRespBO.DetailBO> page(AssFollowupConfigReqBO.PageBO param) {
        AssFollowupConfigReqPO.PagePO pagePO = AssFollowupConfigDomainMapping.INSTANCE.assFollowupConfigBO2PagePO(param);
        Page<AssFollowupConfig> page = assFollowupConfigRepository.page(pagePO);
        return AssFollowupConfigDomainMapping.INSTANCE.assFollowupConfigPO2PageBO(page);
    }

}

