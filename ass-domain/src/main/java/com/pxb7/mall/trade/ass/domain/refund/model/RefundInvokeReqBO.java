package com.pxb7.mall.trade.ass.domain.refund.model;

import com.alibaba.cola.dto.DTO;
import com.pxb7.mall.trade.order.client.enums.pay.PaymentTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@EqualsAndHashCode(callSuper = false)
@Data
public class RefundInvokeReqBO extends DTO {
    /**
     * 退款渠道 1：支付宝官方直连 2: 连连支付 3:泰山支付
     * @see com.pxb7.mall.trade.order.client.enums.pay.PayChannelEnum
     */
    private Integer payChannel;
    /**
     * 资金账户支付参数
     */
    @ToString.Exclude
    private String outPayParam;
    /**
     * 退款金额，单位分
     */
    private Long refundAmount;
    /**
     * 退款交易单号
     */
    private String refundTradeNo;
    /**
     * 支付类别, 1支付宝 2微信 3银行卡
     *
     * @see PaymentTypeEnum
     */
    private Integer payType;
    /**
     * 原支付金额，单位分(连连支付需要)
     */
    private Long totalAmount;
    /**
     * 原交易付款方user_id(连连支付需要)
     */
    private String userId;
    /**
     * 支付单号
     */
    private String payLogId;
    /**
     * 历史退款金额，单位分，部分退款时对账使用，(泰山支付需要)
     */
    private Long alreadyRefundAmount;

    /**
     * 是否重试的请求
     */
    private boolean retry = false;

    /**
     * 支付客户端类型  0:pc, 1:android, 2:wap, 3:ios, 4:wechat小程序,
     * 5:alipay小程序, 6:闲鱼, 7：鸿蒙, 8：客服端，9：游价值，10：京东小程序
     */
    private Integer payClientType;

    /**
     * 跳转方式 1-拉起支付宝/微信原生app,2-跳转H5页面之后拉起原生app,3-iframe渲染出二维码-扫码,4-链接渲染成二维码-扫码,5-链接直接打开跳转-泰山h5
     * @see com.pxb7.mall.trade.order.client.enums.pay.PayMethodEnum
     */
    private Integer payMethod;
}
