package com.pxb7.mall.trade.ass.domain.thread;

import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.ThreadPoolExecutor;

/**
 * 线程池
 *
 * <AUTHOR>
 */
@Slf4j
public class ExecutorRunnable implements Runnable {

    private final ThreadExecutor.Executor executor;
    private final ThreadExecutor.ExecutionReject executionReject;

    public ExecutorRunnable(ThreadExecutor.Executor executor, ThreadExecutor.ExecutionReject executionReject) {
        this.executor = executor;
        this.executionReject = executionReject;
    }

    @Override
    public void run() {
        if (executor != null) {
            long start = System.currentTimeMillis();
            try {
                executor.execute();
                log.info("异步线程处理成功,耗时：{}毫秒", System.currentTimeMillis() - start);
            } catch (Exception e) {
                log.error("异步线程处理发生异常:", e);
            }
        }
    }

    void rejectedExecution(ThreadPoolExecutor executorService) {
        if (this.executionReject != null) {
            this.executionReject.rejected(this, executorService);
        }
    }
}
