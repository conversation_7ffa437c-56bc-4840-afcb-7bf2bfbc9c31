package com.pxb7.mall.trade.ass.domain;

import com.google.common.collect.Maps;
import com.pxb7.mall.common.client.request.message.SpecialRedirectUrlDTO;
import com.pxb7.mall.trade.ass.domain.mapping.AssSendMessageDomainMapping;
import com.pxb7.mall.trade.ass.infra.enums.notis.AssRedirectUrlEnum;
import com.pxb7.mall.trade.ass.infra.enums.notis.InternalEnum;
import com.pxb7.mall.trade.ass.infra.enums.notis.SmsEnum;
import com.pxb7.mall.trade.ass.infra.enums.notis.WechatEnum;
import com.pxb7.mall.trade.ass.infra.model.InternalMsgRedirectReqPO;
import com.pxb7.mall.trade.ass.infra.remote.dubbo.InternalMessageGateway;
import com.pxb7.mall.trade.ass.infra.remote.dubbo.SmsGateway;
import com.pxb7.mall.trade.ass.infra.remote.dubbo.WechatMessageGateway;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 功能描述:售后发送消息
 * 作者：白春韬
 * 创建日期：2025/08/05
 * 公司名称：金华博淳网络科技有限公司
 * 域名： www.pxb7.com
 */
@Service
public class AssSendMessageDomainService {
    @Resource
    private SmsGateway smsGateway;
    @Resource
    private WechatMessageGateway wechatMessageGateway;
    @Resource
    private InternalMessageGateway internalMessageGateway;
    @Resource
    private AssSendMessageDomainMapping assSendMessageDomainMapping;

    /**
     * 售后找回工单【跟进记录】发送消息
     * @param noticeType 0、售后工单创建成功 1、追回账号，待买家换绑 2、追回账号，待买家提供收款账号 3、售后到期
     */
    public void assFollowUpRecordNotice(String assRetrieveId, Integer noticeType, String orderItemId, String userId, String phone) {
        // 站内信URL参数
        Map<String, String> urlParams = buildUrlParam(orderItemId);
        // 前端区分散户、号商，获取跳转地址
        List<SpecialRedirectUrlDTO> buildRedirectUrl = buildRedirectUrl();
        // 发送微信消息
        wechatMessageGateway.sendWechatMessage(assRetrieveId, userId, WechatEnum.getWechatEnum(noticeType));
        // 发送短信
        smsGateway.sendSms(assRetrieveId, phone, SmsEnum.getSmsEnum(noticeType), buildMsgParam(orderItemId));
        // 发送站内信
        internalMessageGateway.sendNotice(userId, InternalEnum.getInternalEnumTemplateCode(noticeType), buildMsgParam(orderItemId), urlParams, 2, buildRedirectUrl);
    }

    /**
     * 消息内参数
     * @param orderItemId 订单号
     */
    private Map<String, String> buildMsgParam(String orderItemId) {
        Map<String, String> msgParams = Maps.newHashMap();
        msgParams.put("code", orderItemId);
        return msgParams;
    }

    /**
     * 站内信跳转URL参数
     * @param orderItemId 订单号
     */
    private Map<String, String> buildUrlParam(String orderItemId) {
        Map<String, String> params = Maps.newHashMap();
        params.put("orderItemId", orderItemId);
        params.put("orderNo", orderItemId);

        return params;
    }

    /**
     * 站内信跳转地址
     */
    private List<SpecialRedirectUrlDTO> buildRedirectUrl() {
        List<InternalMsgRedirectReqPO> urlPO = AssRedirectUrlEnum.getRedirectUrls();
        return assSendMessageDomainMapping.internalMsgRedirectReqPO2SpecialRedirectUrlDTO(urlPO);
    }
}
