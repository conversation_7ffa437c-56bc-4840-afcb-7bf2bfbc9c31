package com.pxb7.mall.trade.ass.domain;

import com.google.common.collect.Maps;
import com.pxb7.mall.trade.ass.infra.enums.notis.InternalEnum;
import com.pxb7.mall.trade.ass.infra.enums.notis.SmsEnum;
import com.pxb7.mall.trade.ass.infra.enums.notis.WechatEnum;
import com.pxb7.mall.trade.ass.infra.remote.dubbo.InternalMessageGateway;
import com.pxb7.mall.trade.ass.infra.remote.dubbo.SmsGateway;
import com.pxb7.mall.trade.ass.infra.remote.dubbo.WechatMessageGateway;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * 功能描述:售后发送消息
 * 作者：白春韬
 * 创建日期：2025/08/05
 * 公司名称：金华博淳网络科技有限公司
 * 域名： www.pxb7.com
 */
@Service
public class AssSendMessageDomainService {
    @Resource
    private SmsGateway smsGateway;
    @Resource
    private WechatMessageGateway wechatMessageGateway;
    @Resource
    private InternalMessageGateway internalMessageGateway;

    /**
     * 售后找回工单【创建成功】发送消息
     */
    public void assRetrieveCreate(String assRetrieveId, String orderItemId, String userId, String phone) {
        Map<String, String> msgParams = Maps.newHashMap();
        msgParams.put("code", orderItemId);
        // todo 站内信URL参数未定
        // 发送微信消息
        wechatMessageGateway.sendWechatMessage(orderItemId, userId, WechatEnum.WECHAT_AFTER_SALE_WORK_ORDER_CREATE);
        // 发送短信
        smsGateway.sendSms(assRetrieveId, phone, SmsEnum.SMS_AFTER_SALE_WORK_ORDER_CREATE, msgParams);
        // 发送站内信
        internalMessageGateway.sendNotice(userId, InternalEnum.INTERNAL_AFTER_SALE_WORK_ORDER_CREATE.getTemplateCode(), msgParams, new HashMap<>());
    }

    /**
     * 售后找回工单【跟进记录】发送消息
     * @param noticeType 1、追回账号，待买家换绑 2、追回账号，待买家提供收款账号 3、售后到期
     */
    public void assFollowUpRecordNotice(String assRetrieveId, Integer noticeType, String orderItemId, String userId, String phone) {
        Map<String, String> msgParams = Maps.newHashMap();
        msgParams.put("code", orderItemId);
        // todo 站内信URL参数未定
        // 发送微信消息
        wechatMessageGateway.sendWechatMessage(assRetrieveId, userId, WechatEnum.getWechatEnum(noticeType));
        // 发送短信
        smsGateway.sendSms(assRetrieveId, phone, SmsEnum.getSmsEnum(noticeType), msgParams);
        // 发送站内信
        internalMessageGateway.sendNotice(userId, InternalEnum.getInternalEnumTemplateCode(noticeType), msgParams, new HashMap<>());
    }

}
