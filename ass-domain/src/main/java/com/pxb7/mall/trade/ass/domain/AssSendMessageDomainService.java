package com.pxb7.mall.trade.ass.domain;

import com.google.common.collect.Maps;
import com.pxb7.mall.common.client.enums.BusinessTypeEnum;
import com.pxb7.mall.common.client.request.message.SpecialRedirectUrlDTO;
import com.pxb7.mall.trade.ass.domain.mapping.AssSendMessageDomainMapping;
import com.pxb7.mall.trade.ass.infra.enums.notis.AssRedirectUrlEnum;
import com.pxb7.mall.trade.ass.infra.enums.notis.InternalEnum;
import com.pxb7.mall.trade.ass.infra.enums.notis.SmsEnum;
import com.pxb7.mall.trade.ass.infra.enums.notis.WechatEnum;
import com.pxb7.mall.trade.ass.infra.model.InternalMsgRedirectReqPO;
import com.pxb7.mall.trade.ass.infra.model.InternalMsgReqPO;
import com.pxb7.mall.trade.ass.infra.remote.dubbo.InternalMessageGateway;
import com.pxb7.mall.trade.ass.infra.remote.dubbo.SmsGateway;
import com.pxb7.mall.trade.ass.infra.remote.dubbo.UserRpcGateway;
import com.pxb7.mall.trade.ass.infra.remote.dubbo.WechatMessageGateway;
import com.pxb7.mall.user.dto.response.user.UserShortInfoDTO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 功能描述:售后发送消息
 * 作者：白春韬
 * 创建日期：2025/08/05
 * 公司名称：金华博淳网络科技有限公司
 * 域名： www.pxb7.com
 */
@Service
@Slf4j
public class AssSendMessageDomainService {
    @Resource
    private SmsGateway smsGateway;
    @Resource
    private UserRpcGateway userRpcGateway;
    @Resource
    private WechatMessageGateway wechatMessageGateway;
    @Resource
    private InternalMessageGateway internalMessageGateway;
    @Resource(name = "assMessageExecutor")
    private ThreadPoolTaskExecutor msgExecutor;


    /**
     * 售后找回工单【跟进记录】发送消息
     * @param noticeType 0、售后工单创建成功 1、追回账号，待买家换绑 2、追回账号，待买家提供收款账号 3、售后到期
     */
    public void assFollowUpRecordNotice(String assRetrieveId, Integer noticeType, String orderItemId, String userId) {
        CompletableFuture.runAsync(() -> {
            try {
                UserShortInfoDTO userInfo = userRpcGateway.getBaseUserInfo(userId);
                // 站内信URL参数
                Map<String, String> urlParams = buildUrlParam(orderItemId);
                // 前端区分散户、号商，获取跳转地址
                List<SpecialRedirectUrlDTO> buildRedirectUrl = buildRedirectUrl(urlParams);
                // 站内信参数组装
                InternalMsgReqPO internalMsgReqPO =
                    buildInternalMsg(userId, noticeType, orderItemId, urlParams, buildRedirectUrl);
                // 发送微信消息
                wechatMessageGateway.sendWechatMessage(assRetrieveId, userId, WechatEnum.getWechatEnum(noticeType));
                // 发送短信
                smsGateway.sendSms(assRetrieveId, userInfo.getPhone(), SmsEnum.getSmsEnum(noticeType),
                    buildMsgParam(orderItemId));
                // 发送站内信
                internalMessageGateway.sendNotice(internalMsgReqPO);
                log.info("assFollowUpRecordNotice success, assRetrieveId: {},noticeType:{},orderItemId:{}",
                    assRetrieveId, noticeType, orderItemId);
            } catch (Exception e) {
                log.error("assFollowUpRecordNotice error, assRetrieveId: {},noticeType:{},orderItemId:{}",
                    assRetrieveId, noticeType, orderItemId, e);
            }
        }, msgExecutor);
    }

    /**
     * 消息内参数
     * @param orderItemId 订单号
     */
    private Map<String, String> buildMsgParam(String orderItemId) {
        Map<String, String> msgParams = Maps.newHashMap();
        msgParams.put("code", orderItemId);
        return msgParams;
    }

    /**
     * 站内信跳转URL参数
     * @param orderItemId 订单号
     */
    private Map<String, String> buildUrlParam(String orderItemId) {
        Map<String, String> params = Maps.newHashMap();
        params.put("orderItemId", orderItemId);
        params.put("orderNo", orderItemId);

        return params;
    }

    /**
     * 站内信跳转地址
     */
    private List<SpecialRedirectUrlDTO> buildRedirectUrl(Map<String, String> params) {
        List<InternalMsgRedirectReqPO> urlPO = AssRedirectUrlEnum.getRedirectUrls(params);
        return AssSendMessageDomainMapping.INSTANCE.internalMsgRedirectReqPO2SpecialRedirectUrlDTO(urlPO);
    }

    private InternalMsgReqPO buildInternalMsg(String userId, Integer noticeType, String orderItemId, Map<String, String> urlParams, List<SpecialRedirectUrlDTO> buildRedirectUrl) {
        InternalMsgReqPO internalMsgReqPO = new InternalMsgReqPO();
        internalMsgReqPO.setUserId(userId);
        internalMsgReqPO.setTemplateCode(InternalEnum.getInternalEnumTemplateCode(noticeType));
        internalMsgReqPO.setParams(buildMsgParam(orderItemId));
        internalMsgReqPO.setUrlParams(urlParams);
        internalMsgReqPO.setRedirectType(2);
        internalMsgReqPO.setRedirectUrlList(buildRedirectUrl);
        internalMsgReqPO.setBusinessId(orderItemId);
        internalMsgReqPO.setBusinessType(BusinessTypeEnum.AFTER_SALE_DETAIL.getCode());

        return internalMsgReqPO;
    }
}
