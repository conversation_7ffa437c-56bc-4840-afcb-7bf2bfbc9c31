package com.pxb7.mall.trade.ass.domain.refund.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RefundCallBackBO {

    private RefundQueryRespBO refundQueryRespBO;

    /**
     * 系统退款时 = 支付payLog
     */
    private String refundLogId;

    private String payLogId;
}
