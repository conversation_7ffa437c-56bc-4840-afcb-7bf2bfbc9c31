package com.pxb7.mall.trade.ass.domain.model;

import com.pxb7.mall.trade.ass.domain.command.sub.bo.RocketMqMessageBO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RocketMqMessageDTO {
    private String topic;

    private String tag;

    // 是否延迟消息
    private boolean isDelay;

    // 消息体
    private String content;

    // 延迟时间
    private long seconds;
    /**
     * 业务id
     */
    private String bizId;
    /**
     * 是否分表，如果分表会更新bizId分
     */
    @Builder.Default
    private boolean isSharding =true;


    /**
     * 消息分组
     */
    private String messageGroup;

    /**
     * 消息发送的执行时间
     */
    private LocalDateTime executeTime;

    /**
     * 默认普通消息
     */
    @Builder.Default
    private RocketMqMessageBO.MessageTypeEnum messageType = RocketMqMessageBO.MessageTypeEnum.NORMAL;

    public enum messageTypeEnum {
        DELAY,
        NORMAL,
        FIFO
    }
}
