package com.pxb7.mall.trade.ass.domain.refund.invoke3rd.impl;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.alibaba.cola.exception.BizException;
import com.alibaba.cola.extension.Extension;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.Lists;
import com.pxb7.mall.trade.ass.client.dto.model.refund.RefundErrorCode;
import com.pxb7.mall.trade.ass.client.dto.model.refund.RefundTradeStatusEnum;
import com.pxb7.mall.trade.ass.client.dto.request.refund.RefundInvokeReqDTO;
import com.pxb7.mall.trade.ass.client.dto.request.refund.RefundQueryReqDTO;
import com.pxb7.mall.trade.ass.client.dto.response.refund.RefundInvokeRespDTO;
import com.pxb7.mall.trade.ass.client.dto.response.refund.RefundQueryRespDTO;
import com.pxb7.mall.trade.ass.domain.refund.invoke3rd.PayRefundExtPt;
import com.pxb7.mall.trade.ass.domain.refund.model.*;
import com.pxb7.mall.trade.ass.infra.constant.PayConstant;
import com.pxb7.mall.trade.ass.infra.enums.RefundCallBackStatusEnum;
import com.pxb7.mall.trade.ass.infra.model.Money;
import com.pxb7.mall.trade.ass.infra.remote.model.outpayparam.request.LianLianPayCreationReqPO;
import com.pxb7.mall.trade.ass.infra.remote.model.outpayparam.request.LianLianRefundPO;
import com.pxb7.mall.trade.ass.infra.remote.model.request.LianLianRefundQueryReqPO;
import com.pxb7.mall.trade.ass.infra.remote.service.http.LLianPayHttpApiService;
import com.pxb7.mall.trade.ass.infra.util.AmountUtil;
import com.pxb7.mall.trade.order.client.enums.ClientTypeEnum;
import com.pxb7.mall.trade.order.client.enums.pay.PayMethodEnum;
import com.pxb7.mall.trade.order.client.enums.pay.PaymentTypeEnum;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.util.List;

@Slf4j
@Extension(bizId = "lianLianPayRefund")
public class LianLianPayRefundExtPt implements PayRefundExtPt {

    @Resource
    private LLianPayHttpApiService lLianPayHttpApiService;

    @Override
    public RefundInvokeRespDTO refund(RefundInvokeReqDTO refundInvokeReqDTO) throws Exception {

        LianLianPayCreationReqPO creationReqPO =
                JSON.parseObject(refundInvokeReqDTO.getOutPayParam(), LianLianPayCreationReqPO.class);

        LianLianRefundPO refundPO = new LianLianRefundPO();
        refundPO.setTimestamp(LocalDateTimeUtil.format(LocalDateTime.now(), PayConstant.LL_TIMESTAMP_FORMAT));
        refundPO.setOid_partner(creationReqPO.getOid_partner());
        refundPO.setUser_id(refundInvokeReqDTO.getUserId());
        // 原单信息
        LianLianRefundPO.RefundPaymentInfoPO refundPaymentInfoPO = new LianLianRefundPO.RefundPaymentInfoPO();
        refundPaymentInfoPO.setTxn_seqno(refundInvokeReqDTO.getInnerTradeNo());
        refundPaymentInfoPO.setTotal_amount(AmountUtil.convertYuan(refundInvokeReqDTO.getTotalAmount()));
        refundPO.setOriginalOrderInfo(refundPaymentInfoPO);
        // 退款信息
        LianLianRefundPO.RefundInfoPO refundInfoPO = new LianLianRefundPO.RefundInfoPO();
        refundInfoPO.setRefund_seqno(refundInvokeReqDTO.getRefundTradeNo());
        refundInfoPO.setRefund_time(LocalDateTimeUtil.format(LocalDateTime.now(), PayConstant.LL_TIMESTAMP_FORMAT));
        refundInfoPO.setRefund_amount(AmountUtil.convertYuan(refundInvokeReqDTO.getRefundAmount()));
        refundPO.setRefundOrderInfo(refundInfoPO);

        LianLianRefundPO.PayeeRefundInfosPO payeeRefundInfosPO = new LianLianRefundPO.PayeeRefundInfosPO();
        payeeRefundInfosPO.setPayee_id(creationReqPO.getOid_partner());
        payeeRefundInfosPO.setPayee_type("MERCHANT");
        payeeRefundInfosPO.setPayee_accttype("MCHOWN");
        payeeRefundInfosPO.setPayee_refund_amount(AmountUtil.convertYuan(refundInvokeReqDTO.getRefundAmount()));
        refundPO.setPyeeRefundInfos(payeeRefundInfosPO);

        List<LianLianRefundPO.WxAlipayPayMethodPO> refundMethods = Lists.newArrayList();
        LianLianRefundPO.WxAlipayPayMethodPO methodPO = new LianLianRefundPO.WxAlipayPayMethodPO();

        if (PaymentTypeEnum.WECHAT.getValue().equals(refundInvokeReqDTO.getPayType())) {
            methodPO.setMethod(PaymentTypeEnum.WECHAT.getLianlianCode());
        } else if (PaymentTypeEnum.ALIPAY.getValue().equals(refundInvokeReqDTO.getPayType())) {
            methodPO.setMethod(PaymentTypeEnum.ALIPAY.getLianlianCode());
        } else {
            throw new BizException(RefundErrorCode.PAY_CHANNEL_NOT_EXIST_ERROR.getErrCode(),
                    RefundErrorCode.PAY_CHANNEL_NOT_EXIST_ERROR.getErrDesc());
        }
        methodPO.setAmount(AmountUtil.convertYuan(refundInvokeReqDTO.getRefundAmount()));
        refundMethods.add(methodPO);
        refundPO.setRefundMethods(refundMethods);

        JSONObject refund = lLianPayHttpApiService.refund(refundPO, creationReqPO, true);

        /*
         * { "oid_partner":"****************", "total_amount":"0.01", "user_id":"1835595561063985187",
         * "txn_seqno":"tk108333390422034", "ret_msg":"交易成功", "ret_code":"0000", "accp_txno":"****************" }
         */
        return new RefundInvokeRespDTO().setSuccess(Boolean.TRUE).setResult(JSON.toJSONString(refund));
    }

    /**
     * 支付结果查询
     */
    @Override
    public RefundQueryRespDTO refundQuery(RefundQueryReqDTO refundQueryReqDTO) {
        /**
         * {
         *     "accounting_date": "********",
         *     "accp_txno": "********05640443",
         *     "chnl_txno": "****************",
         *     "finish_time": "**************",
         *     "oid_partner": "****************",
         *     "refundMethods": [
         *         {
         *             "amount": "52.80",
         *             "method": "WECHAT_NATIVE"
         *         }
         *     ],
         *     "refund_amount": "52.80",
         *     "ret_code": "0000",
         *     "ret_msg": "交易成功",
         *     "txn_status": "TRADE_SUCCESS"
         * }
         */
        LianLianPayCreationReqPO creationReqPO =
                JSON.parseObject(refundQueryReqDTO.getOutPayParam(), LianLianPayCreationReqPO.class);
        LianLianRefundQueryReqPO reqPO = new LianLianRefundQueryReqPO();
        reqPO.setTimestamp(LocalDateTimeUtil.format(LocalDateTime.now(), PayConstant.LL_TIMESTAMP_FORMAT));
        reqPO.setOid_partner(creationReqPO.getOid_partner());
        reqPO.setRefund_seqno(refundQueryReqDTO.getRefundTradeNo());

        JSONObject map = lLianPayHttpApiService.refundQuery(reqPO, creationReqPO);
        log.info("LianLianPayRefundExtPt.refundQuery 查询退款结果 map = {}", map.toJSONString());
        RefundQueryRespDTO refundQueryRespDTO = new RefundQueryRespDTO();
        String txnStatus = map.getString("txn_status");
        Integer tradeStatus = RefundTradeStatusEnum.EXECUTING.getValue();
        if ("TRADE_SUCCESS".equals(txnStatus)) {
            tradeStatus = RefundTradeStatusEnum.SUCCESS.getValue();
        }
        if ("TRADE_FAILURE".equals(txnStatus)) {
            tradeStatus = RefundTradeStatusEnum.FAIL.getValue();
        }

        String amount = map.getString("refund_amount");
        refundQueryRespDTO.setRefundTradeStatus(tradeStatus);
        refundQueryRespDTO.setResult(JSON.toJSONString(map));
        refundQueryRespDTO.setRefundAmount(AmountUtil.convertFen(amount));
        refundQueryRespDTO.setRefundTradeNo(refundQueryReqDTO.getRefundTradeNo());
        refundQueryRespDTO.setPayTradeNo(refundQueryReqDTO.getPayTradeNo());
        String accpTxno = map.getString("accp_txno");
        if (StringUtils.isNotBlank(accpTxno)) {
            refundQueryRespDTO.setOutTradeNo(accpTxno);
        }
        return refundQueryRespDTO;
    }

    @Override
    public RefundInvokeRespBO refundV2(RefundInvokeReqBO reqBO) throws Exception {
        LianLianPayCreationReqPO creationReqPO = JSON.parseObject(reqBO.getOutPayParam(), LianLianPayCreationReqPO.class);

        LianLianRefundPO refundPO = new LianLianRefundPO();
        refundPO.setTimestamp(LocalDateTimeUtil.format(LocalDateTime.now(), PayConstant.LL_TIMESTAMP_FORMAT));
        refundPO.setOid_partner(creationReqPO.getOid_partner());
        refundPO.setUser_id(reqBO.getUserId());
        // 原单信息
        LianLianRefundPO.RefundPaymentInfoPO refundPaymentInfoPO = new LianLianRefundPO.RefundPaymentInfoPO();
        refundPaymentInfoPO.setTxn_seqno(reqBO.getPayLogId());
        refundPaymentInfoPO.setTotal_amount(new Money(reqBO.getTotalAmount()).getYuan());
        refundPO.setOriginalOrderInfo(refundPaymentInfoPO);
        // 退款信息
        LianLianRefundPO.RefundInfoPO refundInfoPO = new LianLianRefundPO.RefundInfoPO();
        refundInfoPO.setRefund_seqno(reqBO.getRefundTradeNo());
        refundInfoPO.setRefund_time(LocalDateTimeUtil.format(LocalDateTime.now(), PayConstant.LL_TIMESTAMP_FORMAT));
        refundInfoPO.setRefund_amount(new Money(reqBO.getRefundAmount()).getYuan());
        refundPO.setRefundOrderInfo(refundInfoPO);

        LianLianRefundPO.PayeeRefundInfosPO payeeRefundInfosPO = new LianLianRefundPO.PayeeRefundInfosPO();
        payeeRefundInfosPO.setPayee_id(creationReqPO.getOid_partner());
        payeeRefundInfosPO.setPayee_type("MERCHANT");
        payeeRefundInfosPO.setPayee_accttype("MCHOWN");
        payeeRefundInfosPO.setPayee_refund_amount(new Money(reqBO.getRefundAmount()).getYuan());
        refundPO.setPyeeRefundInfos(payeeRefundInfosPO);

        List<LianLianRefundPO.WxAlipayPayMethodPO> refundMethods = Lists.newArrayList();
        LianLianRefundPO.WxAlipayPayMethodPO methodPO = new LianLianRefundPO.WxAlipayPayMethodPO();

        if (PaymentTypeEnum.WECHAT.getValue().equals(reqBO.getPayType())) {
            methodPO.setMethod(PaymentTypeEnum.WECHAT.getLianlianCode());
            if (ClientTypeEnum.isMobileClientType(reqBO.getPayClientType()) && PayMethodEnum.LLIAN_WX_MINI_PAY.getValue().equals(reqBO.getPayMethod())){
                //app 唤起小程序
                methodPO.setMethod("WECHAT_APP_WXA");
            }else if (ClientTypeEnum.WAP.eq(reqBO.getPayClientType()) && PayMethodEnum.LLIAN_WX_MINI_PAY.getValue().equals(reqBO.getPayMethod())){
                //h5 唤起 小程序
                methodPO.setMethod("WECHAT_H5");
            }
        } else if (PaymentTypeEnum.ALIPAY.getValue().equals(reqBO.getPayType()) ) {
            methodPO.setMethod(PaymentTypeEnum.ALIPAY.getLianlianCode());
        } else {
            throw new BizException(RefundErrorCode.PAY_CHANNEL_NOT_EXIST_ERROR.getErrCode(),
                    RefundErrorCode.PAY_CHANNEL_NOT_EXIST_ERROR.getErrDesc());
        }
        methodPO.setAmount(new Money(reqBO.getRefundAmount()).getYuan());
        refundMethods.add(methodPO);
        refundPO.setRefundMethods(refundMethods);
        lLianPayHttpApiService.refund(refundPO, creationReqPO, false);
        return RefundInvokeRespBO.builder().statusEnum(RefundCallBackStatusEnum.PROCESSING).build();
    }

    @Override
    public RefundQueryRespBO refundQueryV2(RefundQueryReqBO reqBO) throws Exception {
        LianLianPayCreationReqPO creationReqPO = JSON.parseObject(reqBO.getOutPayParam(), LianLianPayCreationReqPO.class);
        LianLianRefundQueryReqPO reqPO = new LianLianRefundQueryReqPO();
        reqPO.setTimestamp(LocalDateTimeUtil.format(LocalDateTime.now(), PayConstant.LL_TIMESTAMP_FORMAT));
        reqPO.setOid_partner(creationReqPO.getOid_partner());
        reqPO.setRefund_seqno(reqBO.getRefundTradeNo());
        JSONObject map = lLianPayHttpApiService.refundQuery(reqPO, creationReqPO);
        log.info("LianLianPayRefundExtPt.refundQuery 查询退款结果 map = {}", map.toJSONString());
        RefundQueryRespBO refundQueryRespDTO = new RefundQueryRespBO();
        String txnStatus = map.getString("txn_status");
        Integer tradeStatus = RefundTradeStatusEnum.EXECUTING.getValue();
        if ("TRADE_SUCCESS".equals(txnStatus)) {
            tradeStatus = RefundTradeStatusEnum.SUCCESS.getValue();
        } else if ("TRADE_FAILURE".equals(txnStatus)) {
            tradeStatus = RefundTradeStatusEnum.FAIL.getValue();
        }

        String amount = map.getString("refund_amount");
        refundQueryRespDTO.setRefundTradeStatus(tradeStatus);
        refundQueryRespDTO.setResult(JSON.toJSONString(map));
        if (StringUtils.isNotBlank(amount)) {
            refundQueryRespDTO.setRefundAmount(new Money(amount));
        }
        refundQueryRespDTO.setRefundTradeNo(reqBO.getRefundTradeNo());
        refundQueryRespDTO.setRefundOutTradeNo(map.getString("accp_txno"));
        return refundQueryRespDTO;
    }

    @Override
    public RefundCallBackBO refundCallbackSign(RefundCallBackReqBO reqBO) throws Exception {
        return null;
    }
}
