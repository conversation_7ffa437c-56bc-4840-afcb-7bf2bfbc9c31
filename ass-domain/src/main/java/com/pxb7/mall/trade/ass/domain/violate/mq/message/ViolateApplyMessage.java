package com.pxb7.mall.trade.ass.domain.violate.mq.message;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class ViolateApplyMessage {
    private String refundVoucherId;
    private String orderItemId;
    private String violateUserId;
    private Integer violateUserType;
    private Long violateAmount;
    private Long promiseAmount;
    private String createUserId;
    private String createUserName;
}
