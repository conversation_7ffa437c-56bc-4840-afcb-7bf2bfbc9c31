package com.pxb7.mall.trade.ass.domain.violate.manager;

import com.pxb7.mall.common.client.enums.BusinessTypeEnum;
import com.pxb7.mall.trade.ass.infra.remote.dubbo.IMRoomMessageGateway;
import com.pxb7.mall.trade.ass.infra.remote.dubbo.InternalMessageGateway;
import com.pxb7.mall.trade.ass.infra.repository.db.OrderItemExtendRepository;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.OrderItemExtend;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import java.util.*;

import static com.pxb7.mall.trade.ass.domain.violate.constant.OrderViolateConstant.*;
import static com.pxb7.mall.trade.order.client.enums.order.OrderNoticeTemplateEnum.*;

@Service
@Slf4j
public class ViolateNotifyManager {

    @Resource
    private InternalMessageGateway internalMessageGateway;
    @Resource
    private IMRoomMessageGateway iMRoomMessageGateway;
    @Resource
    private OrderItemExtendRepository orderItemExtendRepository;

    // 守约金打款
    public void transferSendMsg(String orderItemId, Boolean isSeller, String receiptUserId, String paidAmount) {
        try {
            // 站内信
            compensateOrderSendNotice(orderItemId, isSeller, paidAmount, receiptUserId);

            // im
            String conTemplate = isSeller ? ORDER_BC_BUYER_MSG : ORDER_BC_SELLER_MSG;
            paidSendIM(conTemplate, ORDER_VIOLATE_BC_TITLE, orderItemId, receiptUserId, paidAmount);

            log.warn("[transferSendMsg] 守约金打款消息通知,订单id:{}", orderItemId);

        } catch (Throwable e) {
            log.warn("[transferSendMsg] 守约金打款消息通知,失败:{},订单id:{}", e.getMessage(), orderItemId);
        }
    }

    // 违约金扣款
    public void deductionSendMsg(String orderItemId, Boolean isSeller, String receiptUserId, String paidAmount) {
        try {
            // 站内信
            walletDeductionOrderSendNotice(orderItemId, paidAmount, receiptUserId);

            // im
            String conTemplate = isSeller ? ORDER_KK_SELLER_MSG : ORDER_KK_BUYER_MSG;
            paidSendIM(conTemplate, ORDER_VIOLATE_KK_TITLE, orderItemId, receiptUserId, paidAmount);

            log.info("[deductionSendMsg] 违约金钱包扣款消息通知,订单id:{}", orderItemId);

        } catch (Throwable e) {
            log.warn("[deductionSendMsg] 违约金钱包扣款消息通知,失败:{},订单id:{}", e.getMessage(), orderItemId);
        }

    }

    // IM消息 违约金扣款/守约金打款
    public void paidSendIM(String conTemplate, String title, String orderItemId, String receiptUserId, String paidAmount) {
        OrderItemExtend orderItemExtend = orderItemExtendRepository.getOneByItemId(orderItemId);
        List<String> targetUserIds = List.of(receiptUserId);
        String content = String.format(conTemplate, paidAmount);
        List<String> mentionedIdList = new ArrayList<>();
        mentionedIdList.add(orderItemExtend.getDeliveryCustomerId());
        mentionedIdList.add(receiptUserId);

        iMRoomMessageGateway.sendIMMessageWithoutNotice(orderItemExtend, title, content, mentionedIdList, targetUserIds);
    }

    /**
     * 扣款-订单发送站内信消息
     */
    public void walletDeductionOrderSendNotice(String orderItemId, String paidPrice, String receiptUserId) {
        String noticeTemplate = VIOLATE_SELLER_QBKK.getCode();
        Map<String, String> params = new HashMap<>();
        params.put("order_id", orderItemId);
        params.put("money", paidPrice);
        internalMessageGateway.sendNotice(receiptUserId, noticeTemplate, params, orderItemId, BusinessTypeEnum.ORDER.getCode());
    }

    /**
     * 补偿-订单发送站内信消息
     */
    public void compensateOrderSendNotice(String orderItemId, Boolean isSeller, String paidPrice, String receiptUserId) {
        String noticeTemplate = isSeller ? VIOLATE_BUYER_BC.getCode() : VIOLATE_SELLER_BC.getCode();
        Map<String, String> params = new HashMap<>();
        params.put("order_id", orderItemId);
        params.put("money", paidPrice);
        internalMessageGateway.sendNotice(receiptUserId, noticeTemplate, params, orderItemId, BusinessTypeEnum.ORDER.getCode());
    }
}