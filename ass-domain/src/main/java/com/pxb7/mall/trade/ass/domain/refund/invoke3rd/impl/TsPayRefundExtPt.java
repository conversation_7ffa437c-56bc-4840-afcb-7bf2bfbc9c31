package com.pxb7.mall.trade.ass.domain.refund.invoke3rd.impl;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.cola.exception.BizException;
import com.alibaba.cola.exception.SysException;
import com.alibaba.cola.extension.Extension;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.pxb7.mall.trade.ass.client.dto.model.refund.RefundErrorCode;
import com.pxb7.mall.trade.ass.client.dto.model.refund.RefundTradeStatusEnum;
import com.pxb7.mall.trade.ass.client.dto.request.refund.RefundInvokeReqDTO;
import com.pxb7.mall.trade.ass.client.dto.request.refund.RefundQueryReqDTO;
import com.pxb7.mall.trade.ass.client.dto.response.refund.RefundInvokeRespDTO;
import com.pxb7.mall.trade.ass.client.dto.response.refund.RefundQueryRespDTO;
import com.pxb7.mall.trade.ass.domain.refund.invoke3rd.PayRefundExtPt;
import com.pxb7.mall.trade.ass.domain.refund.message.provider.MockRefundAfterListener;
import com.pxb7.mall.trade.ass.domain.refund.message.provider.MockRefundPreListener;
import com.pxb7.mall.trade.ass.domain.refund.model.*;
import com.pxb7.mall.trade.ass.infra.enums.ErrorCode;
import com.pxb7.mall.trade.ass.infra.enums.RefundCallBackStatusEnum;
import com.pxb7.mall.trade.ass.infra.exception.RefundSysException;
import com.pxb7.mall.trade.ass.infra.model.Money;
import com.pxb7.mall.trade.ass.infra.remote.model.outpayparam.request.TspayCreationReqPO;
import com.pxb7.mall.trade.ass.infra.remote.model.request.TaiShanPartRefundV3ReqPO;
import com.pxb7.mall.trade.ass.infra.remote.model.request.TaiShanPayQueryReqPO;
import com.pxb7.mall.trade.ass.infra.remote.model.request.TaiShanRefundReqPO;
import com.pxb7.mall.trade.ass.infra.remote.service.http.TaishanPayHttpApiService;
import com.pxb7.mall.trade.ass.infra.util.AmountUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Objects;

@Slf4j
@Extension(bizId = "tsPayRefund")
public class TsPayRefundExtPt implements PayRefundExtPt {

    @Resource
    private TaishanPayHttpApiService taishanPayHttpApiService;
    @Resource
    private MockRefundPreListener mockRefundPreListener;
    @Resource
    private MockRefundAfterListener mockRefundAfterListener;

    @Override
    public RefundInvokeRespDTO refund(RefundInvokeReqDTO refundInvokeReqDTO) throws Exception {
        log.info("泰山 refund 开始 :{}", refundInvokeReqDTO);
        RefundQueryReqDTO reqBO = new RefundQueryReqDTO();
        reqBO.setOutPayParam(refundInvokeReqDTO.getOutPayParam());
        reqBO.setRefundTradeNo(refundInvokeReqDTO.getRefundTradeNo());
        reqBO.setPayTradeNo(refundInvokeReqDTO.getInnerTradeNo());
        reqBO.setRefundAmount(refundInvokeReqDTO.getRefundAmount());
        reqBO.setAlreadyRefundAmount(refundInvokeReqDTO.getAlreadyRefundAmount());
        reqBO.setRefundStart(false);
        // 退款前查询，判断本次是否已执行
        RefundQueryRespDTO respBO = this.refundQuery(reqBO);

        if (Objects.equals(RefundTradeStatusEnum.REPEATED.getValue(), respBO.getRefundTradeStatus())) {
            log.info("历史退款金额+本次退款金额=三方退款金额,重复退款,无需重新发起, refundInvokeReqDTO:{}",
                    JSONObject.toJSONString(refundInvokeReqDTO));
            return new RefundInvokeRespDTO().setSuccess(Boolean.TRUE);
        }
        if (!RefundTradeStatusEnum.SUCCESS.getValue().equals(respBO.getRefundTradeStatus())) {
            log.error("三方退款对账校验失败,对账交易状态:{}, refundInvokeReqDTO:{}",
                    RefundTradeStatusEnum.getLabel(respBO.getRefundTradeStatus()),
                    JSONObject.toJSONString(refundInvokeReqDTO));
            throw new BizException(RefundErrorCode.REFUND_ERROR.getErrCode(),
                    RefundErrorCode.REFUND_ERROR.getErrDesc());
        }

        // 执行退款
        TspayCreationReqPO po = JSON.parseObject(refundInvokeReqDTO.getOutPayParam(), TspayCreationReqPO.class);
        JSONObject map = null;
        // 如果未发生过退款，且本次是全额退款，则调用泰山付全额退款接口
        if (respBO.getAlreadyRefundAmount() == 0
                && refundInvokeReqDTO.getAlreadyRefundAmount() == 0
                && Objects.equals(refundInvokeReqDTO.getRefundAmount(), refundInvokeReqDTO.getTotalAmount())) {
            // 全额退款
            TaiShanRefundReqPO reqPO = new TaiShanRefundReqPO();
            reqPO.setMerchantTradeNo(refundInvokeReqDTO.getInnerTradeNo());
            map = taishanPayHttpApiService.refund(reqPO, po);
        } else {
            // 部分退款
            TaiShanPartRefundV3ReqPO reqPO = new TaiShanPartRefundV3ReqPO();
            reqPO.setRequestNo(refundInvokeReqDTO.getRefundTradeNo());
            reqPO.setMerchantTradeNo(refundInvokeReqDTO.getInnerTradeNo());
            reqPO.setRefundAmount(AmountUtil.convertYuan(refundInvokeReqDTO.getRefundAmount()));
            map = taishanPayHttpApiService.partRefundV3(reqPO, po);
        }
        if(Objects.isNull(map)) {
            throw new BizException(ErrorCode.CHANNEL_ERROR.getErrCode(), ErrorCode.CHANNEL_ERROR.getErrDesc());
        }

        // check is 200
        checkRespStatus(map);

        RefundInvokeRespDTO refundExtRespBO = new RefundInvokeRespDTO();
        Boolean success = Boolean.FALSE;
        if ("success".equals(map.getString("type"))) {
            success = Boolean.TRUE;
        }
        refundExtRespBO.setResult(JSON.toJSONString(map));
        refundExtRespBO.setSuccess(success);
        return refundExtRespBO;
    }

    @Override
    public RefundQueryRespDTO refundQuery(RefundQueryReqDTO refundQueryReqDTO) {
        log.info("泰山 refundQuery 开始 :{}", refundQueryReqDTO);
        TspayCreationReqPO po = JSON.parseObject(refundQueryReqDTO.getOutPayParam(), TspayCreationReqPO.class);

        TaiShanPayQueryReqPO taishanPayQueryReqPO = new TaiShanPayQueryReqPO();
        taishanPayQueryReqPO.setMerchantTradeNo(refundQueryReqDTO.getPayTradeNo());
        JSONObject map = taishanPayHttpApiService.refundPayQuery(taishanPayQueryReqPO, po);
        // check is 200
        checkRespStatus(map);

        JSONObject jsonObject = map.getJSONObject("result");
        BigDecimal amount = jsonObject.getBigDecimal("refundAmount");
        long tsRefundAmount = amount.multiply(new BigDecimal(100)).longValue();

        String refundTime = jsonObject.getString("refundTime");
        LocalDateTime payTime = LocalDateTime.now();
        if (StringUtils.isNotBlank(refundTime)) {
            payTime = LocalDateTimeUtil.parse(refundTime, "yyyy-MM-dd HH:mm:ss");
        }

        RefundQueryRespDTO payQueryRespBO = new RefundQueryRespDTO();
        payQueryRespBO.setResult(jsonObject.toString());
        payQueryRespBO.setPayTime(payTime);
        payQueryRespBO.setRefundAmount(refundQueryReqDTO.getRefundAmount());
        payQueryRespBO.setRefundTradeNo(refundQueryReqDTO.getRefundTradeNo());
        payQueryRespBO.setPayTradeNo(refundQueryReqDTO.getPayTradeNo());
        String platformOutTradeNo = jsonObject.getString("platformOutTradeNo");
        if (StringUtils.isNotBlank(platformOutTradeNo)) {
            payQueryRespBO.setOutTradeNo(platformOutTradeNo);
        }

        // 查询完成后对账
        Integer tradeStatus = positionRefundQuery(refundQueryReqDTO, tsRefundAmount);
        payQueryRespBO.setRefundTradeStatus(tradeStatus);
        payQueryRespBO.setAlreadyRefundAmount(tsRefundAmount);
        return payQueryRespBO;
    }

    /**
     * {"code":400,"type":"error","message":"买家不存在","time":"2025-03-20 18:42:44"}
     */
    @Override
    public RefundInvokeRespBO refundV2(RefundInvokeReqBO reqBO) {
        log.info("泰山 refundV2 开始 :{}", reqBO);
        RefundQueryReqBO queryReqBO = new RefundQueryReqBO();
        queryReqBO.setOutPayParam(reqBO.getOutPayParam());
        queryReqBO.setRefundTradeNo(reqBO.getRefundTradeNo());
        queryReqBO.setPayLogId(reqBO.getPayLogId());
        queryReqBO.setRefundAmount(reqBO.getRefundAmount());
        queryReqBO.setAlreadyRefundAmount(reqBO.getAlreadyRefundAmount());
        queryReqBO.setRefundStart(false);

        // mock before
//        MockRefundPreListener.MockConfig mockConfig = mockRefundPreListener.getPreInvoke();
//        if(mockConfig != null && mockConfig.isTsMockQuerySysException()){
//            throw new SysException("timeout");
//        }
        // 查询退款结果，判断本次是否已执行
        RefundQueryRespBO queryRespBO = this.refundQueryV2(queryReqBO);
        if (Objects.equals(RefundTradeStatusEnum.REPEATED.getValue(), queryRespBO.getRefundTradeStatus())) {
            // 历史退款金额+本次退款金额=三方退款金额
            log.info("重复退款,无需重新发起, refundInvokeReqDTO:{}", JSONObject.toJSONString(reqBO));
            return RefundInvokeRespBO.builder().build();
        }
        // 历史退款要等于三方退款金额,如果不等于，有异常
        if (!RefundTradeStatusEnum.SUCCESS.getValue().equals(queryRespBO.getRefundTradeStatus())) {
            log.error("三方退款对账校验失败,对账交易状态:{}", JSONObject.toJSONString(reqBO));
            return RefundInvokeRespBO.builder().build();
        }
//
//        // 执行退款
        TspayCreationReqPO po = JSON.parseObject(reqBO.getOutPayParam(), TspayCreationReqPO.class);

        JSONObject jsonObject = null;
        // 如果未发生过退款，且本次是全额退款，则调用泰山付全额退款接口
        if (queryRespBO.getAlreadyRefundAmount() == 0
            && reqBO.getAlreadyRefundAmount() == 0
            && Objects.equals(reqBO.getRefundAmount(), reqBO.getTotalAmount())) {
            // 全额退款
            TaiShanRefundReqPO reqPO = new TaiShanRefundReqPO();
            reqPO.setMerchantTradeNo(reqBO.getPayLogId());
            jsonObject = taishanPayHttpApiService.refund(reqPO, po);
        } else {
            // 部分退款
            TaiShanPartRefundV3ReqPO reqPO = new TaiShanPartRefundV3ReqPO();
            reqPO.setRequestNo(reqBO.getRefundTradeNo());
            reqPO.setMerchantTradeNo(reqBO.getPayLogId());
            reqPO.setRefundAmount(new Money(reqBO.getRefundAmount()).getYuan());
            jsonObject = taishanPayHttpApiService.partRefundV3(reqPO, po);
        }

        // JSONObject jsonObject = null;
//        if(mockConfig != null){
//            if(mockConfig.isTsMockSysException()){
//                throw new SysException("timeout");
//            }
//            if(mockConfig.isTsMockBadGateway()){
//                throw new BizException("Bad Gateway");
//            }
//            if(mockConfig.getResonse() != null){
//                jsonObject = mockConfig.getResonse();
//            }
//        }

//        // mock after
//        if (StrUtil.isNotBlank(mockRefundAfterListener.getAfterInvoke())) {
//            jsonObject = JSON.parseObject(mockRefundAfterListener.getAfterInvoke());
//        }

        if(Objects.isNull(jsonObject)) {
            throw new BizException(ErrorCode.CHANNEL_ERROR.getErrCode(), ErrorCode.CHANNEL_ERROR.getErrDesc());
        }
        Integer code = jsonObject.getInteger("code");
        // 受理成功直接返回，主动查询结果
        if (Objects.equals(code, 200)) {
            return RefundInvokeRespBO.builder().statusEnum(RefundCallBackStatusEnum.PROCESSING).build();
        }

        // 受理失败差异化处理
        if (Objects.equals(code, 400) && jsonObject.getString("message").contains("买家不存在")) {
            log.warn("泰山 refundV2 买家不存在, refundInvokeReqDTO:{}", reqBO);
            // 失败，不需要重试
            return RefundInvokeRespBO.builder()
                    .statusEnum(RefundCallBackStatusEnum.FAIL)
                    .callBackStr(jsonObject.toJSONString()).build();
        } else if (Objects.equals(code, 400) && jsonObject.getString("message").contains("系统异常")) {
            log.warn("泰山 refundV2 系统异常, refundInvokeReqDTO:{}", reqBO);
            // 异常，退款需要重试
            throw new RefundSysException(ErrorCode.TS_REFUND_CHANNEL_SYS_ERROR);
        } else {
            throw new BizException(ErrorCode.CHANNEL_ERROR.getErrCode(), jsonObject.getString("message"));
        }
    }

    @Override
    public RefundQueryRespBO refundQueryV2(RefundQueryReqBO reqBO) {
        log.info("泰山 refundQueryV2 开始 :{}", reqBO);
        TspayCreationReqPO po = JSON.parseObject(reqBO.getOutPayParam(), TspayCreationReqPO.class);
        TaiShanPayQueryReqPO taishanPayQueryReqPO = new TaiShanPayQueryReqPO();
        taishanPayQueryReqPO.setMerchantTradeNo(reqBO.getPayLogId());

        JSONObject map = taishanPayHttpApiService.refundPayQuery(taishanPayQueryReqPO, po);
        log.info("TS 退款查询结果 {}", map.toJSONString());
        // check is 200
        checkRespStatus(map);

        JSONObject jsonObject = map.getJSONObject("result");
        BigDecimal amount = jsonObject.getBigDecimal("refundAmount");
        long tsRefundAmount = amount.multiply(new BigDecimal(100)).longValue();

        String refundTime = jsonObject.getString("refundTime");
        LocalDateTime refundSuccessTime = LocalDateTime.now();
        if (StringUtils.isNotBlank(refundTime)) {
            refundSuccessTime = LocalDateTimeUtil.parse(refundTime, "yyyy-MM-dd HH:mm:ss");
        }

        RefundQueryRespBO payQueryRespBO = new RefundQueryRespBO();
        payQueryRespBO.setResult(jsonObject.toString());
        payQueryRespBO.setSuccessTime(refundSuccessTime);
        payQueryRespBO.setRefundTradeNo(reqBO.getRefundTradeNo());
        String platformOutTradeNo = jsonObject.getString("platformOutTradeNo");
        if (StringUtils.isNotBlank(platformOutTradeNo)) {
            payQueryRespBO.setRefundOutTradeNo(platformOutTradeNo);
        }

        // 查询完成后对账
        Integer tradeStatus = moneyCheck(reqBO, tsRefundAmount);
        payQueryRespBO.setRefundTradeStatus(tradeStatus);
        payQueryRespBO.setRefundAmount(new Money(reqBO.getRefundAmount()));
        payQueryRespBO.setAlreadyRefundAmount(tsRefundAmount);
        return payQueryRespBO;
    }

    @Override
    public RefundCallBackBO refundCallbackSign(RefundCallBackReqBO reqBO) throws Exception {
        return null;
    }

    /**
     * 退款金额比对
     */
    private Integer moneyCheck(RefundQueryReqBO reqBO, long tsRefundAmount) {
        long successRefundAmount = ObjectUtils.defaultIfNull(reqBO.getAlreadyRefundAmount(), 0L);
        long refundAmount = ObjectUtils.defaultIfNull(reqBO.getRefundAmount(), 0L);
        long historyRefundAmount = successRefundAmount;
        if (reqBO.isRefundStart()) {
            historyRefundAmount += refundAmount;
        }

        log.info("泰山对账----request:{},泰山退款金额:{},内部历史退款金额:{},本次退款金额:{}", reqBO,
                tsRefundAmount, historyRefundAmount, refundAmount);
        if (historyRefundAmount > tsRefundAmount) {
            // 内部退款金额大于泰山退款金额 ，退款还没有成功
            return RefundTradeStatusEnum.EXECUTING.getValue();
        } else if (historyRefundAmount == tsRefundAmount) {
            // 内部支付金额等于泰山支付金额 ，退款成功
            return RefundTradeStatusEnum.SUCCESS.getValue();
        } else {
            // 如果发起之前发现历史退款金额+本次退款金额=三方退款金额，认为重复退款，直接返回已成功
            if (!reqBO.isRefundStart() && successRefundAmount + refundAmount == tsRefundAmount) {
                return RefundTradeStatusEnum.REPEATED.getValue();
            }
            // 内部支付金额小于泰山支付金额 ，退款有误，需要人工处理
            log.error("外部退款金额大于系统内部退款金额，泰山退款金额:{},内部历史退款金额:{},本次退款金额:{}", tsRefundAmount, historyRefundAmount,
                    refundAmount);
            throw new BizException(ErrorCode.REFUND_ERROR.getErrCode(), ErrorCode.REFUND_ERROR.getErrDesc());
        }
    }

    /**
     * @param tsRefundAmount : 泰山总退款金额
     * @return
     */
    private Integer positionRefundQuery(RefundQueryReqDTO refundQueryReqDTO, long tsRefundAmount) {
        long successRefundAmount =
                refundQueryReqDTO.getAlreadyRefundAmount() == null ? 0L : refundQueryReqDTO.getAlreadyRefundAmount();
        // 本地退款金额
        long refundAmount = refundQueryReqDTO.getRefundAmount() == null ? 0L : refundQueryReqDTO.getRefundAmount();
        long historyRefundAmount = successRefundAmount;
        if (refundQueryReqDTO.isRefundStart()) {
            historyRefundAmount += refundAmount;
        }

        log.info("泰山对账----泰山总退款金额:{}, 平台总申请退款金额:{},本次退款金额:{}", tsRefundAmount, historyRefundAmount, refundAmount);

        if (historyRefundAmount > tsRefundAmount) {
            // 平台退款金额大于泰山退款金额  表示 本次退款还没有成功
            return RefundTradeStatusEnum.EXECUTING.getValue();
        } else if (historyRefundAmount == tsRefundAmount) {
            // 内部支付金额等于泰山支付金额 ，退款成功
            return RefundTradeStatusEnum.SUCCESS.getValue();
        } else {
            // 如果发起之前发现历史退款金额+本次退款金额=三方退款金额，认为重复退款，直接返回已成功
            if (!refundQueryReqDTO.isRefundStart() && successRefundAmount + refundAmount == tsRefundAmount) {
                return RefundTradeStatusEnum.REPEATED.getValue();
            }
            // 内部支付金额小于泰山支付金额 ，退款有误，需要人工处理
            log.error("外部退款金额大于系统内部退款金额，泰山退款金额:{},内部历史退款金额:{},本次退款金额:{}", tsRefundAmount, historyRefundAmount,
                    refundAmount);
            throw new BizException(ErrorCode.REFUND_ERROR.getErrCode(), ErrorCode.REFUND_ERROR.getErrDesc());
        }
    }

    private void checkRespStatus(JSONObject jsonObject) {
        if (!Objects.equals(jsonObject.getInteger("code"), 200)) {
            throw new BizException(ErrorCode.CHANNEL_ERROR.getErrCode(), jsonObject.getString("message"));
        }
    }
}
