package com.pxb7.mall.trade.ass.domain.thread.impl;

import java.util.concurrent.*;

/**
 * <AUTHOR>
 * @date 2025/6/25 19:49
 * @desc 激进线程池
 */
public class ExtremeThreadPoolExecutor extends ThreadPoolExecutor {

    /**
     * 构造函数
     */
    public ExtremeThreadPoolExecutor(int corePoolSize, int maxPoolSize, long keepAliveTime, TimeUnit unit, int queueSize, ThreadFactory threadFactory) {
        super(corePoolSize, maxPoolSize, keepAliveTime, unit, new ExtremeBlockQueue<>(queueSize), threadFactory, new ExtremeRejectPolicy());

    }

    /**
     * 自定义阻塞队列
     * @param <Runnable>
     */
    static class ExtremeBlockQueue<Runnable> extends LinkedBlockingQueue<Runnable> {
        public ExtremeBlockQueue(int capacity) {
            super(capacity);
        }

        /**
         * 覆盖默认的offer方法，触发拒绝策略执行
         */
        @Override
        public boolean offer(Runnable runnable) {
            return false;
        }

        /**
         * 拒绝策略触发后，真正的保存进阻塞队列
         */
        public boolean extremeOffer(Runnable runnable) {
            return super.offer(runnable);
        }
    }

    /**
     * 自定义拒绝策略
     */
    static class ExtremeRejectPolicy implements RejectedExecutionHandler {
        @Override
        public void rejectedExecution(Runnable r, ThreadPoolExecutor e) {
            //线程池关闭
            if (e.isShutdown()) {
                throw new RejectedExecutionException("Task " + r.toString() +
                        " rejected from " + e);
            }

            if (!((ExtremeBlockQueue)e.getQueue()).extremeOffer(r)) {
                r.run();
            }
        }
    }

}
