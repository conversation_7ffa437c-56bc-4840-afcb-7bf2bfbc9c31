package com.pxb7.mall.trade.ass.domain.violate.manager;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.cola.exception.Assert;
import com.alibaba.cola.exception.BizException;
import com.pxb7.mall.components.redis.redisson.RLockUtils;
import com.pxb7.mall.trade.ass.client.enums.violate.*;
import com.pxb7.mall.trade.ass.client.lock.ViolateLockConstant;
import com.pxb7.mall.trade.ass.domain.order.OrderItemDomainService;
import com.pxb7.mall.trade.ass.domain.violate.ViolateDomainService;
import com.pxb7.mall.trade.ass.domain.violate.ViolateOrderDomainService;
import com.pxb7.mall.trade.ass.domain.violate.ViolatePayRecordDomainService;
import com.pxb7.mall.trade.ass.domain.violate.mapping.ViolateOrderDomainMapping;
import com.pxb7.mall.trade.ass.domain.violate.request.ViolateApplyReqBO;
import com.pxb7.mall.trade.ass.domain.violate.request.ViolateCreateReqBO;
import com.pxb7.mall.trade.ass.domain.violate.request.ViolateUpdateReqBO;
import com.pxb7.mall.trade.ass.infra.enums.ErrorCode;
import com.pxb7.mall.trade.ass.infra.enums.RefundWholeEnum;
import com.pxb7.mall.trade.ass.infra.exception.BusinessException;
import com.pxb7.mall.trade.ass.infra.model.UserBaseInfo;
import com.pxb7.mall.trade.ass.infra.repository.db.*;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.*;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Objects;
import java.util.concurrent.TimeUnit;

import static com.pxb7.mall.trade.ass.client.enums.violate.ViolatePayTypeEnum.DEDUCTION;
import static com.pxb7.mall.trade.ass.infra.enums.ErrorCode.*;

@Service
@Slf4j
public class ViolateApplyManager {
    @Resource
    private OrderItemRepository orderItemRepository;
    @Resource
    private OrderItemExtendRepository orderItemExtendRepository;
    @Resource
    private ViolateOrderDomainService violateOrderDomainService;
    @Resource
    private ViolatePayRecordDomainService violatePayRecordDomainService;
    @Resource
    private ViolateDomainService violateDomainService;
    @Resource
    private RefundVoucherRepository refundVoucherRepository;
    @Resource
    private RefundVoucherDetailRepository refundVoucherDetailRepository;
    @Resource
    private ViolateOrderDomainMapping violateOrderDomainMapping;
    @Resource
    private OrderItemDomainService orderItemDomainService;

    public void apply(ViolateApplyReqBO bo) {
        String lockKey = ViolateLockConstant.getOrderLockKey(bo.getOrderItemId());
        RLockUtils.of(lockKey, () -> createViolateHandler(bo))
                .withWaitTime(2).withTimeUnit(TimeUnit.SECONDS)
                .orElseThrow(() -> new BizException(ErrorCode.REPEAT_OPERATION.getErrCode(), ErrorCode.REPEAT_OPERATION.getErrDesc()));
    }

    public void cancelViolate(String violateId) {
        String lockKey = ViolateLockConstant.getViolateLockKey(violateId);
        RLockUtils.of(lockKey, () -> cancelViolateHandler(violateId))
                .withWaitTime(2).withTimeUnit(TimeUnit.SECONDS)
                .orElseThrow(() -> new BizException(ErrorCode.REPEAT_OPERATION.getErrCode(), ErrorCode.REPEAT_OPERATION.getErrDesc()));
    }

    // 取消违约单
    private boolean cancelViolateHandler(String violateId) {
        ViolateOrder violateOrder = violateOrderDomainService.getDetailById(violateId);
        if (ObjectUtil.isEmpty(violateOrder) || !ViolateStatusEnum.INIT.eq(violateOrder.getViolateStatus())) {
            log.info("[取消违约单] error: empty violateOrder or illegal status,violateId:{}", violateId);
            return false;
        }
        violateOrderDomainService.cancelViolate(violateOrder.getViolateId());
        log.info("[取消违约单] success,violateId:{},退款单id:{}", violateId, violateOrder.getRefundVoucherId());
        return true;
    }

    public ViolateOrder createViolateHandler(ViolateApplyReqBO bo) {
        // 校验:是否已有有效违约单
        if (violateOrderDomainService.hasViolateByOrderIdAndRefundId(bo.getOrderItemId(), bo.getRefundVoucherId())) {
            log.warn("[创建违约单] 失败: violateOrder exist,订单id:{}", bo.getOrderItemId());
            return null;
        }

        OrderItem orderItem = orderItemRepository.getOrderItem(bo.getOrderItemId());

        try {
            // 退款单校验
            RefundVoucher refundVoucher = checkRefundAndGet(bo.getRefundVoucherId());
            // 金额校验
            checkViolateAmount(refundVoucher, bo.getViolateUserType(), bo.getViolateAmount(), bo.getPromiseAmount(), orderItem);
        } catch (Throwable e) {
            log.error("[创建违约单] 失败, 校验失败, 退款单id:{}", bo.getRefundVoucherId(), e);
            return null;
        }

        // 组装数据
        String violateUserId = ViolateUserTypeEnum.BUYER.eq(bo.getViolateUserType())
                ? orderItem.getBuyerId() : orderItem.getSellerId();
        String promiseUserId = ViolateUserTypeEnum.BUYER.eq(bo.getViolateUserType())
                ? orderItem.getSellerId() : orderItem.getBuyerId();
        ViolateCreateReqBO violateCreateReqBO = new ViolateCreateReqBO()
                .setOrderItemId(orderItem.getOrderItemId())
                .setRefundVoucherId(bo.getRefundVoucherId())
                .setViolateUserType(bo.getViolateUserType())
                .setViolateUserId(violateUserId)
                .setPromiseUserId(promiseUserId)
                .setViolateAmount(bo.getViolateAmount())
                .setPromiseAmount(bo.getPromiseAmount())
                .setCreateUserId(bo.getCreateUserId())
                .setCreateUserName(bo.getCreateUserName())
                .setUpdateUserId(bo.getCreateUserId())
                .setUpdateUserName(bo.getCreateUserName());
        return violateOrderDomainService.create(violateCreateReqBO);
    }

    /**
     * 重新编辑&&重发收款卡片
     */
    public boolean violateEdit(ViolateUpdateReqBO bo, UserBaseInfo userBaseInfo) {

        Assert.isTrue(StringUtils.isNotBlank(userBaseInfo.getUserId()), GET_USER_BASE_INFO_ERROR.getErrCode(),
                GET_USER_BASE_INFO_ERROR.getErrDesc());

        ViolateOrder oldViolateOrder = checkViolateAndGet(bo);
        OrderItem orderItem = orderItemRepository.getOrderItem(oldViolateOrder.getOrderItemId());

        // 校验无流程中的单据
        orderItemDomainService.checkOrderProcessingVoucher(orderItem);

        // 若有退款单,则校验
        RefundVoucher refundVoucher = null;
        if (StrUtil.isNotBlank(oldViolateOrder.getRefundVoucherId())) {
            refundVoucher = checkRefundAndGet(oldViolateOrder.getRefundVoucherId());
        }

        checkUpdateAuth(oldViolateOrder.getOrderItemId(), userBaseInfo.getUserId());

        checkViolateAmount(refundVoucher, oldViolateOrder.getViolateUserType(), bo.getViolateAmount(), bo.getPromiseAmount(), orderItem);

        ViolateOrder violateOrder = new ViolateOrder()
                .setOrderItemId(oldViolateOrder.getOrderItemId())
                .setViolateId(oldViolateOrder.getViolateId())
                .setViolateAmount(bo.getViolateAmount())
                .setPromiseAmount(bo.getPromiseAmount())
                .setPlatformAmount(bo.getViolateAmount() - bo.getPromiseAmount())
                .setUpdateUserId(userBaseInfo.getUserId())
                .setUpdateUsername(userBaseInfo.getUserName());
        return violateDomainService.violateUpdate(oldViolateOrder, violateOrder);
    }

    public ViolateOrder checkViolateAndGet(ViolateUpdateReqBO bo) {
        // 违约单数据合法校验
        ViolateOrder oldViolateOrder = violateOrderDomainService.getEffectiveDetailById(bo.getViolateId());
        Assert.isTrue(
                ObjectUtil.isNotEmpty(oldViolateOrder)
                        && ViolateReceiptStatusEnum.getFailed().contains(oldViolateOrder.getReceiptStatus())
                        && ViolateStatusEnum.DEALING.eq(oldViolateOrder.getViolateStatus())
                , ErrorCode.VIOLATE_UPDATE_STATUS_ILLEGAL_ERROR.getErrCode()
                , ErrorCode.VIOLATE_UPDATE_STATUS_ILLEGAL_ERROR.getErrDesc()
        );

        Assert.isTrue(
                ObjectUtil.isEmpty(violatePayRecordDomainService.findEffectiveByViolateId(oldViolateOrder.getViolateId(), DEDUCTION.getValue()))
                , ErrorCode.VIOLATE_UPDATE_REPEAT_DEDUCTION_ERROR.getErrCode()
                , ErrorCode.VIOLATE_UPDATE_REPEAT_DEDUCTION_ERROR.getErrDesc()
        );

        return oldViolateOrder;
    }

    /**
     * 金额校验
     * 总违约金默认 = 订单中商品售价5% or 剩余可退金额
     * 总违约金 >= 守约方获得违约金
     * 总违约金 < 剩余可退金额
     */
    public void checkViolateAmount(RefundVoucher refundVoucher, Integer violateUserType, Long violateAmount, Long promiseAmount, OrderItem orderItem) {
        Assert.isTrue(violateAmount >= promiseAmount
                , VIOLATE_AMOUNT_ONE_ERROR.getErrCode()
                , VIOLATE_AMOUNT_ONE_ERROR.getErrDesc()
        );

        if (ViolateUserTypeEnum.BUYER.eq(violateUserType) && ObjectUtil.isNotEmpty(refundVoucher)) {
            RefundVoucherDetail refundVoucherDetail = refundVoucherDetailRepository.getOneByRefundId(refundVoucher.getRefundVoucherId());
            long totalRefundAmount = refundVoucherDetail.getProductAmount()
                    + refundVoucherDetail.getIndemnityAmount()
                    + refundVoucherDetail.getFeeAmount()
                    + refundVoucherDetail.getRedPacketAmount();
            Assert.isTrue(violateAmount < totalRefundAmount
                    , VIOLATE_AMOUNT_TWO_ERROR.getErrCode(), VIOLATE_AMOUNT_TWO_ERROR.getErrDesc());
        }

        if (ViolateUserTypeEnum.SELLER.eq(violateUserType) && ObjectUtil.isNotEmpty(orderItem)) {
            Assert.isTrue(violateAmount < orderItem.getProductSalePrice()
                    , VIOLATE_AMOUNT_TWO_ERROR.getErrCode(), VIOLATE_AMOUNT_TWO_ERROR.getErrDesc());
        }

    }

    public RefundVoucher checkRefundAndGet(String refundVoucherId) {
        RefundVoucher refundVoucher = refundVoucherRepository.getByRefundId(refundVoucherId);
        Assert.isTrue(
                ObjectUtil.isNotEmpty(refundVoucher)
                        && RefundWholeEnum.WHOLE.getValue().equals(refundVoucher.getWholeRefund())
                , VIOLATE_REFUND_STATUS_ERROR.getErrCode(), VIOLATE_REFUND_STATUS_ERROR.getErrDesc());
        return refundVoucher;
    }

    /**
     * 操作人是否为交付客服
     */
    public void checkUpdateAuth(String orderItemId, String userId) {
        OrderItemExtend orderItemExtend = orderItemExtendRepository.getOneByItemId(orderItemId);

        Assert.isTrue(Objects.equals(orderItemExtend.getDeliveryCustomerId(), userId)
                , VIOLATE_UPDATE_AUTH_ERROR.getErrCode(), VIOLATE_UPDATE_AUTH_ERROR.getErrDesc());
    }

    /**
     * im发起违约收款
     */
    public ViolateOrder createViolateAndDeal(ViolateApplyReqBO bo) {
        // orderItem
        OrderItem orderItem = orderItemRepository.getOrderItem(bo.getOrderItemId());

        // check
        applyHandCheck(bo, orderItem);

        // supplement
        bo.setViolateUserId(ViolateUserTypeEnum.BUYER.eq(bo.getViolateUserType()) ? orderItem.getBuyerId() : orderItem.getSellerId());
        bo.setPromiseUserId(ViolateUserTypeEnum.BUYER.eq(bo.getViolateUserType()) ? orderItem.getSellerId() : orderItem.getBuyerId());

        // has success wholeRefundVoucher
        RefundVoucher refundVoucher = refundVoucherRepository.lastWholeByOrderItemId(bo.getOrderItemId());
        if (ObjectUtil.isNotEmpty(refundVoucher)) {
            bo.setRefundVoucherId(refundVoucher.getRefundVoucherId());
        }

        // create && deal
        ViolateCreateReqBO violateCreateReqBO = violateOrderDomainMapping.toViolateCreateReqBO(bo);
        return violateDomainService.violateCreateAndDeal(violateCreateReqBO);
    }

    private void applyHandCheck(ViolateApplyReqBO bo, OrderItem orderItem) {
        // auth check
        checkUpdateAuth(orderItem.getOrderItemId(), bo.getCreateUserId());
        // Check if there are any vouchers in process
        orderItemDomainService.checkOrderProcessingVoucher(orderItem);
        // Check if there are any violation orders in process
        if (ObjectUtil.isNotEmpty(violateOrderDomainService.violateDealingByOrder(orderItem.getOrderItemId()))) {
            throw new BusinessException(VIOLATE_IN_PROCESS_ERROR);
        }
        // amount check
        Assert.isTrue(bo.getViolateAmount() >= bo.getPromiseAmount()
                , VIOLATE_AMOUNT_ONE_ERROR.getErrCode()
                , VIOLATE_AMOUNT_ONE_ERROR.getErrDesc()
        );
    }

}
