package com.pxb7.mall.trade.ass.domain.violate.manager;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.cola.exception.BizException;
import com.pxb7.mall.components.redis.redisson.RLockUtils;
import com.pxb7.mall.trade.ass.client.enums.violate.*;
import com.pxb7.mall.trade.ass.client.lock.ViolateLockConstant;
import com.pxb7.mall.trade.ass.domain.violate.ViolateDomainService;
import com.pxb7.mall.trade.ass.domain.violate.ViolateOrderDomainService;
import com.pxb7.mall.trade.ass.domain.violate.ViolatePayRecordDomainService;
import com.pxb7.mall.trade.ass.domain.violate.mq.ViolateMessageService;
import com.pxb7.mall.trade.ass.domain.violate.request.ViolatePayRecordReqBO;
import com.pxb7.mall.trade.ass.domain.violate.request.ViolateTransferCallbackReqBO;
import com.pxb7.mall.trade.ass.domain.violate.response.ViolateWalletDeductionRespBO;
import com.pxb7.mall.trade.ass.infra.model.Money;
import com.pxb7.mall.trade.ass.infra.repository.db.ViolateOrderRepository;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.ViolateOrder;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.ViolatePayRecord;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

import static com.pxb7.mall.trade.ass.client.enums.violate.ViolatePayTypeEnum.TRANSFER;
import static com.pxb7.mall.trade.ass.infra.enums.ErrorCode.*;

@Service
@Slf4j
public class ViolatePayRecordManager {
    @Resource
    private ViolateDomainService violateDomainService;
    @Resource
    private ViolateOrderDomainService violateOrderDomainService;
    @Resource
    private ViolatePayRecordDomainService violatePayRecordDomainService;
    @Resource
    private ViolateMessageService violateMessageService;
    @Resource
    private ViolateOrderRepository violateOrderRepository;
    @Resource
    protected DataSourceTransactionManager transactionManager;
    @Resource
    protected ViolateNotifyManager violateNotifyManager;

    // 违约打款
    public void violateTransfer(ViolatePayRecordReqBO bo) {
        String lockKey = ViolateLockConstant.getViolateLockKey(bo.getViolateId());
        RLockUtils.of(lockKey, () -> transferHandler(bo.getViolateId()))
                .withWaitTime(2).withTimeUnit(TimeUnit.SECONDS)
                .orElseThrow(() -> new BizException(REPEAT_OPERATION.getErrCode(), REPEAT_OPERATION.getErrDesc()));
    }

    // 卖家-违约扣款
    public void violateDeduction(ViolatePayRecordReqBO bo) {
        String lockKey = ViolateLockConstant.getViolateLockKey(bo.getViolateId());
        RLockUtils.of(lockKey, () -> deductionHandler(bo))
                .withWaitTime(2).withTimeUnit(TimeUnit.SECONDS)
                .orElseThrow(() -> new BizException(REPEAT_OPERATION.getErrCode(), REPEAT_OPERATION.getErrDesc()));
    }

    // 打款结果回调
    public void transferCallback(ViolateTransferCallbackReqBO bo) {
        ViolatePayRecord payRecord = violatePayRecordDomainService.getDetailById(bo.getViolatePayRecordId());
        String lockKey = ViolateLockConstant.getViolateLockKey(payRecord.getViolateId());
        RLockUtils.of(lockKey, () -> transferCallbackHandler(bo))
                .withWaitTime(2).withTimeUnit(TimeUnit.SECONDS)
                .orElseThrow(() -> new BizException(REPEAT_OPERATION.getErrCode(), REPEAT_OPERATION.getErrDesc()));
    }

    /**
     * 违约方钱包扣款
     */
    public boolean deductionHandler(ViolatePayRecordReqBO bo) {
        // 获取违约单
        ViolateOrder violateOrder = violateOrderDomainService.getEffectiveDetailById(bo.getViolateId());
        if (ObjectUtil.isEmpty(violateOrder)
                || !ViolateStatusEnum.DEALING.eq(violateOrder.getViolateStatus())
                || !ViolateReceiptStatusEnum.INIT.eq(violateOrder.getReceiptStatus())
        ) {
            log.warn("[违约金钱包扣款] 校验 error:violate illegal,违约单id:{}", bo.getViolateId());
            return false;
        }

        // 获取扣款单
        ViolatePayRecord payRecord = violatePayRecordDomainService.getDetailById(bo.getPayRecordId());
        if (ObjectUtil.isEmpty(payRecord)
                || !ViolatePayTypeEnum.DEDUCTION.eq(payRecord.getType())
                || !ViolateChannelTypeEnum.WALLET.eq(payRecord.getChannel())
        ) {
            log.warn("[违约金钱包扣款] 校验 error:violatePayRecord illegal,违约单id:{},收款单id:{}", bo.getViolateId(), bo.getPayRecordId());
            return false;
        }

        // 钱包扣款 里面有异常直接抛出,false扣款失败 true扣款成功
        ViolateWalletDeductionRespBO deductionWalletRes = violateDomainService.deductionDubboToWallet(payRecord);
        if (Boolean.TRUE.equals(deductionWalletRes.isStatus())) {
            // 钱包收款成功
            boolean receiptRes = violateDomainService.receiptSuccess(payRecord, violateOrder, deductionWalletRes.getOutTradeId());

            // 发消息
            if (receiptRes) {
                violateNotifyManager.deductionSendMsg(payRecord.getOrderItemId()
                        , ViolateUserTypeEnum.SELLER.eq(violateOrder.getViolateUserType())
                        , payRecord.getUserId(), new Money(payRecord.getAmount()).getYuan());
            }
            log.info("[违约金-钱包扣款] 成功,违约单ID:{},收款单ID:{}", bo.getViolateId(), payRecord.getRecordId());

        } else {
            // 钱包扣款失败,转在线收款
            violateDomainService.walletConvertOnline(violateOrder, payRecord);
        }

        return true;
    }

    // 打款
    public boolean transferHandler(String violateId) {
        // 校验违约单
        ViolateOrder violateOrder = violateOrderDomainService.getEffectiveDetailById(violateId);
        if (ObjectUtil.isEmpty(violateOrder) || !ViolateStatusEnum.DEALING.eq(violateOrder.getViolateStatus())) {
            log.warn("[违约金打款] 无违约单 or 违约单状态非处理中, violateId:{}", violateId);
            return false;
        }

        if (!ViolateTransferStatusEnum.WAIT.eq(violateOrder.getTransferStatus())) {
            log.warn("[违约金打款] 违约单打款状态非待打款, violateId:{}", violateId);
            return false;
        }

        if (!ViolateReceiptStatusEnum.SUCCESS.eq(violateOrder.getReceiptStatus())) {
            log.warn("[违约金打款] 违约单收款状态非收款成功, violateId:{}", violateId);
            return false;
        }

        if (violateOrder.getPromiseAmount() <= 0) {
            log.warn("[违约金打款] 守约金 <= 0,无须发起打款, violateId:{}", violateId);
            return false;
        }

        // 是否已有打款单
        ViolatePayRecord hasPayRecord = violatePayRecordDomainService.findEffectiveByViolateId(violateId, TRANSFER.getValue());
        if (ObjectUtil.isNotEmpty(hasPayRecord)) {
            log.warn("[违约金打款] 已有违约打款单, violateId:{},payRecordId:{}", violateId, hasPayRecord.getRecordId());
            return false;
        }

        violateDomainService.transferDeal(violateOrder);
        return true;

    }

    // 打款成功
    public boolean transferCallbackHandler(ViolateTransferCallbackReqBO bo) {
        String payRecordId = bo.getViolatePayRecordId();
        ViolatePayRecord payRecord = violatePayRecordDomainService.getDetailById(payRecordId);
        if (ObjectUtil.isEmpty(payRecord)
                || !TRANSFER.eq(payRecord.getType())
                || !ViolatePayRecordStatusEnum.getDealing().contains(payRecord.getStatus())) {
            log.warn("[违约金-打款成功回调] error : empty payRecord or illegal status,payRecordId:{}", payRecordId);
            return false;
        }

        // 校验违约单
        ViolateOrder violateOrder = violateOrderDomainService.getEffectiveDetailById(payRecord.getViolateId());
        if (ObjectUtil.isEmpty(violateOrder) || !ViolateStatusEnum.DEALING.eq(violateOrder.getViolateStatus())) {
            log.warn("[违约金-打款成功回调] error : empty violateOrder or illegal status,payRecordId:{}", payRecordId);
            return false;
        }

        // 更新
        violateDomainService.transferSuccessUpdate(violateOrder, payRecordId, bo.getOutTradeId());

        // 发消息
        violateNotifyManager.transferSendMsg(payRecord.getOrderItemId()
                , ViolateUserTypeEnum.SELLER.eq(violateOrder.getViolateUserType())
                , payRecord.getUserId(), new Money(payRecord.getAmount()).getYuan());

        return true;
    }


}
