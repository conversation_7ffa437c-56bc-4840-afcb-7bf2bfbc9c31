package com.pxb7.mall.trade.ass.domain.provider;

import com.pxb7.mall.trade.ass.client.enums.AfcWOStatus;
import com.pxb7.mall.trade.ass.domain.model.reqeust.mq.AfcWoStatusChange;
import com.pxb7.mall.trade.ass.infra.constant.RMQConstant;
import com.pxb7.mall.trade.ass.infra.messaging.MQProducer;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * Copyright (C), 2002-2025, 螃蟹游戏服务网
 *
 * @fileName: WorkOrderStatusChangeMessageProvider.java
 * @description: 工单状态变更
 * @author: guominqiang
 * @email: <EMAIL>
 * @date: 2025/4/21 19:58
 * @history: //修改记录
 * <author> <time> <version> <desc>
 * 修改人姓名 修改时间 版本号 描述
 */
@Component
@Slf4j
public class WorkOrderStatusChangeMessageProvider {
    @Resource
    private MQProducer mqProducer;

    /**
     * 异步发送创建工单消息
     *
     * @param workOrderId      工单id
     * @param roomId           房间id
     * @param orderItemId      订单id
     * @param afcWorkOrderType 工单类型 1:找回 2:纠纷 3:客诉
     * @param tag              消息tag
     */
    public void asyncSendCreateWOMessage(String workOrderId,
                                         String roomId,
                                         String orderItemId,
                                         Integer afcWorkOrderType,
                                         String tag) {
        AfcWoStatusChange afcWoStatusChange = AfcWoStatusChange.builder()
            .workOrderId(workOrderId)
            .roomId(roomId)
            .orderItemId(orderItemId)
            .workOrderType(afcWorkOrderType)
            .workOrderStatus(AfcWOStatus.PENDING_HANDLING.getLabel())
            .build();
        mqProducer.asyncSend(RMQConstant.AFC_WORK_ORDER_STATUS_CHANGE_TOPIC, tag, afcWoStatusChange);
        log.info("send create work order msg, tag {}, msgBody {}", tag, afcWoStatusChange);
    }

    /**
     * 工单完结消息
     *
     * @param workOrderId
     * @param roomId
     * @param orderItemId
     * @param afcWorkOrderType
     * @param tag
     */
    public void asyncSendFinishWOMessage(String workOrderId,
                                         String roomId,
                                         String orderItemId,
                                         Integer afcWorkOrderType,
                                         String tag) {
        AfcWoStatusChange afcWoStatusChange = AfcWoStatusChange.builder()
            .workOrderId(workOrderId)
            .roomId(roomId)
            .orderItemId(orderItemId)
            .workOrderType(afcWorkOrderType)
            .workOrderStatus(AfcWOStatus.COMPLETED.getLabel())
            .build();
        mqProducer.asyncSend(RMQConstant.AFC_WORK_ORDER_STATUS_CHANGE_TOPIC, tag, afcWoStatusChange);
        log.info("send completion work order msg, tag {}, msgBody {}", tag, afcWoStatusChange);
    }

    /**
     * 关闭工单消息
     *
     * @param workOrderId
     * @param roomId
     * @param orderItemId
     * @param afcWorkOrderType
     * @param tag
     */
    public void asyncSendCloseWOMessage(String workOrderId,
                                        String roomId,
                                        String orderItemId,
                                        Integer afcWorkOrderType,
                                        String tag) {
        AfcWoStatusChange afcWoStatusChange = AfcWoStatusChange.builder()
            .workOrderId(workOrderId)
            .roomId(roomId)
            .orderItemId(orderItemId)
            .workOrderStatus(AfcWOStatus.CLOSED.getLabel())
            .workOrderType(afcWorkOrderType)
            .build();
        mqProducer.asyncSend(RMQConstant.AFC_WORK_ORDER_STATUS_CHANGE_TOPIC, tag, afcWoStatusChange);
        log.info("send close work order msg, tag:{}, msgBody {}", tag, afcWoStatusChange);
    }
}
