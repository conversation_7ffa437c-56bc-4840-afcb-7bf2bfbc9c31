package com.pxb7.mall.trade.ass.domain.model;

import com.pxb7.mall.pay.client.enums.AccountTradeTypeEnum;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WalletToPayBO {

    /**
     * 入账用户
     */
    @NotNull
    private String userId;

    /**
     * 业务单据ID
     */
    @NotNull
    private String voucherId;

    /**
     * 金额
     */
    @NotNull
    private Long amount;

    /**
     * 不填走默认的
     */
    private String callback;

    private String orderId;

    @NotNull
    private AccountTradeTypeEnum accountTradeTypeEnum;
}
