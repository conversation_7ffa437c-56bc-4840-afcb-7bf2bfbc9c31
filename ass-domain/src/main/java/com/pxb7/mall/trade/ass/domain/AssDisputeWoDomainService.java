package com.pxb7.mall.trade.ass.domain;

import org.springframework.stereotype.Service;

import com.alibaba.cola.exception.BizException;
import com.pxb7.mall.trade.ass.infra.enums.ErrorCode;
import com.pxb7.mall.trade.ass.infra.repository.db.AssDisputeWoRepository;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.AssDisputeWo;

import jakarta.annotation.Resource;

/**
 * 售后纠纷工单
 *
 * <AUTHOR>
 * @since: 2024-08-09 20:47
 **/
@Service
public class AssDisputeWoDomainService {

    @Resource
    private AssDisputeWoRepository assDisputeWoRepository;

    /**
     * 查询纠纷工单编号
     *
     * @param orderNo
     * @return
     */
    public String getAssDisputeId(String orderNo) {
        AssDisputeWo wo = assDisputeWoRepository.lambdaQuery().eq(AssDisputeWo::getOrderItemId, orderNo)
            .orderByDesc(AssDisputeWo::getId).last("limit 1").one();
        if (wo == null) {
            throw new BizException(ErrorCode.PARAM_EMPTY.getErrCode(), "工单编号不存在");
        }
        return wo.getAssDisputeId();
    }

}
