package com.pxb7.mall.trade.ass.domain.thread;

import com.alibaba.cola.exception.BizException;
import com.pxb7.mall.trade.ass.domain.thread.impl.ExtremeThreadPoolExecutor;
import com.pxb7.mall.trade.ass.domain.thread.impl.NormalThreadPoolExecutor;
import org.springframework.scheduling.concurrent.CustomizableThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2025/6/25 19:40
 */
public class ThreadPoolExecutorFactory {

    /**
     * 创建线程池
     */
    public static ThreadPoolExecutor create(String biz, int poolSize, int maxPoolSize, int queueSize, ThreadPoolTypeEnum threadPoolType) {
        // 统一创建线程工厂，为线程设置有意义的名称
        CustomizableThreadFactory threadFactory = new CustomizableThreadFactory(biz + "-pool-");
        switch (threadPoolType) {
            case NORMAL -> {
                // 正常线程池
                return new NormalThreadPoolExecutor(poolSize, maxPoolSize, 5L, TimeUnit.MINUTES, queueSize, threadFactory);
            }
            case EXTREME -> {
                // 激进线程池
                return new ExtremeThreadPoolExecutor(poolSize, maxPoolSize, 5L, TimeUnit.MINUTES, queueSize, threadFactory);
            }
            default -> throw new BizException("Unexpected threadPoolType: " + threadPoolType);
        }
    }

}
