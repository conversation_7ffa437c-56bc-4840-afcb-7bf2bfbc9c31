package com.pxb7.mall.trade.ass.domain.violate.manager;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.cola.exception.BizException;
import com.pxb7.mall.components.redis.redisson.RLockUtils;
import com.pxb7.mall.trade.ass.client.enums.violate.*;
import com.pxb7.mall.trade.ass.client.lock.ViolateLockConstant;
import com.pxb7.mall.trade.ass.domain.violate.ViolateDomainService;
import com.pxb7.mall.trade.ass.domain.violate.ViolateOrderDomainService;
import com.pxb7.mall.trade.ass.infra.enums.ErrorCode;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.ViolateOrder;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Set;
import java.util.concurrent.TimeUnit;

import static com.pxb7.mall.trade.ass.domain.violate.constant.OrderViolateConstant.VIOLATE_RECEIPT_STOP;
import static com.pxb7.mall.trade.ass.infra.enums.ErrorCode.VIOLATE_RECEIPT_STOP_ERROR;

@Service
@Slf4j
public class ViolateDealManager {
    @Resource
    private ViolateOrderDomainService violateOrderDomainService;
    @Resource
    private ViolateDomainService violateDomainService;

    public void deal(String orderItemId, String refundVoucherId) {
        // 获取待处理的违约单
        String violateId = checkRefundViolateGet(refundVoucherId);
        if (StrUtil.isEmpty(violateId)) {
            return;
        }
        String lockKey = ViolateLockConstant.getOrderLockKey(orderItemId);
        RLockUtils.of(lockKey, () -> dealHandler(violateId))
                .withWaitTime(2).withTimeUnit(TimeUnit.SECONDS)
                .orElseThrow(() -> new BizException(ErrorCode.REPEAT_OPERATION.getErrCode(), ErrorCode.REPEAT_OPERATION.getErrDesc()));
    }

    public void receiptStop(String violateId) {
        String lockKey = ViolateLockConstant.getViolateLockKey(violateId);
        RLockUtils.of(lockKey, () -> receiptStopHandler(violateId))
                .withWaitTime(2).withTimeUnit(TimeUnit.SECONDS)
                .orElseThrow(() -> new BizException(ErrorCode.REPEAT_OPERATION.getErrCode(), ErrorCode.REPEAT_OPERATION.getErrDesc()));
    }

    private String checkRefundViolateGet(String refundVoucherId) {
        ViolateOrder violateOrder = violateOrderDomainService.violateWaitingByRefundId(refundVoucherId);
        if (ObjectUtil.isNull(violateOrder)) {
            return null;
        }

        return violateOrder.getViolateId();
    }

    /**
     * 退款完成-开始处理违约单
     * 1、更新违约单状态:待处理->处理中
     * 2、买家违约:给卖家打款MQ; 卖家违约:发起卖家钱包扣款MQ
     */
    public boolean dealHandler(String violateId) {
        ViolateOrder violateOrder = dealCheckViolateAndGet(violateId);
        if (ObjectUtil.isEmpty(violateOrder) || violateOrder == null) {
            return false;
        }

        return violateDomainService.violateDealStart(violateOrder);
    }

    /**
     * 开始处理违约单-校验:
     * 违约单状态:待处理&&待收款&&待打款
     */
    private ViolateOrder dealCheckViolateAndGet(String violateId) {
        ViolateOrder violateOrder = violateOrderDomainService.getDetailById(violateId);
        if (ObjectUtil.isEmpty(violateOrder)) {
            log.warn("[处理违约单] 失败 : violate is empty, 违约单id:{}", violateId);
            return null;
        }
        if (!ViolateStatusEnum.INIT.eq(violateOrder.getViolateStatus())) {
            log.warn("[处理违约单] 失败 : violateStatus is not init, 违约单id:{}", violateId);
            return null;
        }
        if (!ViolateReceiptStatusEnum.INIT.eq(violateOrder.getReceiptStatus())) {
            log.warn("[处理违约单] 失败 : violateReceiptStatus is not init, 违约单id:{}", violateId);
            return null;
        }
        if (!ViolateTransferStatusEnum.WAIT.eq(violateOrder.getTransferStatus())) {
            log.warn("[处理违约单] 失败 : violateTransferStatus is not init, 违约单id:{}", violateId);
            return null;
        }

        return violateOrder;
    }

    /**
     * 超时收款中断
     */
    public boolean receiptStopHandler(String violateId) {
        ViolateOrder violateOrder = violateOrderDomainService.getEffectiveDetailById(violateId);
        Set<Integer> completedStatus = Set.of(
                ViolateReceiptStatusEnum.STOP.getValue(), ViolateReceiptStatusEnum.SUCCESS.getValue());
        if (ObjectUtil.isEmpty(violateOrder)
                || !ViolateStatusEnum.DEALING.eq(violateOrder.getViolateStatus())
                || completedStatus.contains(violateOrder.getReceiptStatus())) {
            log.warn("[违约金收款中断] 失败:empty or status error,violateId:{}", violateId);
            return false;
        }

        Assert.isTrue(
                LocalDateTime.now().isAfter(violateOrder.getReceiptStopTime())
                        || LocalDateTime.now().isAfter(violateOrder.getDealTime().plusSeconds(VIOLATE_RECEIPT_STOP))
                , VIOLATE_RECEIPT_STOP_ERROR.getErrDesc());

        violateDomainService.receiptStatusStop(violateOrder);

        return true;

    }


}
