package com.pxb7.mall.trade.ass.domain.model.assSchedule;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@Accessors(chain = true)
public class ReqCreateAssScheduleBO implements Serializable {
    /**
     * 房间ID
     */
    private String roomId;
    /**
     * 游戏ID
     */
    private String gameId;
    /**
     * 订单ID
     */
    private String orderNo;
    /**
     * 售后类型1找回2纠纷
     */
    private Integer assType;
    /**
     * 来源1用户2客服
     */
    private Integer sourceType;
    /**
     * 来源: 登录用户ID
     */
    private String loginUserId;
    /**
     * 售后客服id
     */
    private String recvCustomerId;


}
