package com.pxb7.mall.trade.ass.domain;


import java.util.List;

import cn.hutool.core.util.StrUtil;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import com.pxb7.mall.trade.ass.domain.model.AssWorkOrderLogReqBO;
import com.pxb7.mall.trade.ass.domain.model.AssWorkOrderLogRespBO;
import com.pxb7.mall.trade.ass.domain.mapping.AssWorkOrderLogDomainMapping;
import com.pxb7.mall.trade.ass.infra.model.AssWorkOrderLogReqPO;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.AssWorkOrderLog;
import com.pxb7.mall.trade.ass.infra.repository.db.AssWorkOrderLogRepository;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;


/**
 * 售后工单日志明细domain服务
 *
 * <AUTHOR>
 * @since 2025-07-31 10:21:35
 */
@Service
public class AssWorkOrderLogDomainService {

    @Resource
    private AssWorkOrderLogRepository assWorkOrderLogRepository;





    public boolean insert(AssWorkOrderLogReqBO.AddBO param) {
        AssWorkOrderLogReqPO.AddPO addPO = AssWorkOrderLogDomainMapping.INSTANCE.assWorkOrderLogBO2AddPO(param);
        return assWorkOrderLogRepository.insert(addPO);
    }

    public boolean update(AssWorkOrderLogReqBO.UpdateBO param) {
        AssWorkOrderLogReqPO.UpdatePO updatePO = AssWorkOrderLogDomainMapping.INSTANCE.assWorkOrderLogBO2UpdatePO(param);
        return assWorkOrderLogRepository.update(updatePO);
    }

    public boolean deleteById(AssWorkOrderLogReqBO.DelBO param) {
        AssWorkOrderLogReqPO.DelPO delPO = AssWorkOrderLogDomainMapping.INSTANCE.assWorkOrderLogBO2DelPO(param);
        return assWorkOrderLogRepository.deleteById(delPO);
    }

    public AssWorkOrderLogRespBO.DetailBO findById(Long id) {
        AssWorkOrderLog entity = assWorkOrderLogRepository.findById(id);
        return AssWorkOrderLogDomainMapping.INSTANCE.assWorkOrderLogPO2DetailBO(entity);

    }

    public List<AssWorkOrderLogRespBO.DetailBO> list(AssWorkOrderLogReqBO.SearchBO param) {
        AssWorkOrderLogReqPO.SearchPO searchPO = AssWorkOrderLogDomainMapping.INSTANCE.assWorkOrderLogBO2SearchPO(param);
        List<AssWorkOrderLog> list = assWorkOrderLogRepository.list(searchPO);
        return AssWorkOrderLogDomainMapping.INSTANCE.assWorkOrderLogPO2ListBO(list);
    }

    public Page<AssWorkOrderLogRespBO.DetailBO> page(AssWorkOrderLogReqBO.PageBO param) {
        AssWorkOrderLogReqPO.PagePO pagePO = AssWorkOrderLogDomainMapping.INSTANCE.assWorkOrderLogBO2PagePO(param);
        Page<AssWorkOrderLog> page = assWorkOrderLogRepository.page(pagePO);
        return AssWorkOrderLogDomainMapping.INSTANCE.assWorkOrderLogPO2PageBO(page);
    }

}

