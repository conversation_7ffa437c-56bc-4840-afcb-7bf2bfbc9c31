package com.pxb7.mall.trade.ass.domain.thread;

import com.alibaba.cola.exception.BizException;

import java.util.Objects;
import java.util.concurrent.Callable;
import java.util.concurrent.Future;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 线程池实现
 */
public class ThreadExecutorImpl implements ThreadExecutor {

    /**
     * 异步执行器
     */
    private final ThreadPoolExecutor executorService;

    /**
     * 构造函数
     * @param biz 线程池名称
     * @param corePoolSize 核心线程数
     * @param maxPoolSize 最大线程数
     * @param queueSize 队列大小
     * @param threadPoolType 线程池类型
     */
    ThreadExecutorImpl(String biz, int corePoolSize, int maxPoolSize, int queueSize, ThreadPoolTypeEnum threadPoolType) {
        validateParameters(biz, corePoolSize, maxPoolSize, queueSize, threadPoolType);
        executorService = ThreadPoolExecutorFactory.create(biz, corePoolSize, maxPoolSize, queueSize, threadPoolType);
        executorService.prestartCoreThread();
    }

    /**
     * 参数验证
     */
    private static void validateParameters(String biz, int corePoolSize, int maxPoolSize, int queueSize, ThreadPoolTypeEnum threadPoolType) {
        if (biz == null || biz.trim().isEmpty()) {
            throw new IllegalArgumentException("业务名称不能为空");
        }
        if (corePoolSize <= 0) {
            throw new IllegalArgumentException("核心线程数必须大于0，当前值: " + corePoolSize);
        }
        if (maxPoolSize < corePoolSize) {
            throw new IllegalArgumentException("最大线程数不能小于核心线程数，核心线程数: " + corePoolSize + ", 最大线程数: " + maxPoolSize);
        }
        if (queueSize <= 0) {
            throw new IllegalArgumentException("队列大小必须大于0，当前值: " + queueSize);
        }
        if (threadPoolType == null) {
            throw new IllegalArgumentException("线程池类型不能为空");
        }
    }

    @Override
    public void setCorePoolSize(Integer size) {
        executorService.setCorePoolSize(size);
    }

    @Override
    public void execute(Executor executor) {
        this.execute(executor, null);
    }

    @Override
    public void execute(Executor executor, ExecutionReject executionReject) {
        if (executor == null) {
            return;
        }
        executorService.execute(new ExecutorRunnable(executor, executionReject));
    }

    @Override
    public <V> Future<V> submit(Callable<V> task) {
        return this.executorService.submit(task);
    }

    @Override
    public ThreadPoolExecutor getThreadPoolExecutor() {
        if (Objects.isNull(executorService)) {
            throw new BizException("thread pool is null");
        }
        return executorService;
    }

}
