package com.pxb7.mall.trade.ass.domain.thread;

import org.springframework.scheduling.concurrent.CustomizableThreadFactory;

import java.util.concurrent.*;

/**
 * 线程池实现
 *
 * <AUTHOR>
 */
public class ThreadExecutorImpl implements ThreadExecutor {

    /**
     * 异步执行器
     */
    private final ThreadPoolExecutor executorService;

    ThreadExecutorImpl(String biz, int poolSize, int maxPoolSize, int queueSize) {
        ThreadFactory springThreadFactory = new CustomizableThreadFactory(biz + "-pool-");
        /*
         * 拒绝策略
         */
        RejectedExecutionHandler exeHandler = (r, executor) -> {
            if (r instanceof ExecutorRunnable) {
                ((ExecutorRunnable) r).rejectedExecution(executor);
            }
        };
        executorService = new ThreadPoolExecutor(poolSize, maxPoolSize, 0L, TimeUnit.MILLISECONDS,
                new LinkedBlockingQueue<>(queueSize), springThreadFactory, exeHandler);
    }

    @Override
    public void setCorePoolSize(Integer size) {
        executorService.setCorePoolSize(size);
    }

    @Override
    public void execute(Executor executor) {
        this.execute(executor, null);
    }

    @Override
    public void execute(Executor executor, ExecutionReject executionReject) {
        if (executor == null) {
            return;
        }
        executorService.execute(new ExecutorRunnable(executor, executionReject));
    }

    @Override
    public <V> Future<V> submit(Callable<V> task) {
        return this.executorService.submit(task);
    }
}
