package com.pxb7.mall.trade.ass.domain.refund.message.provider;

import java.time.Duration;

import org.springframework.stereotype.Service;

import com.alibaba.fastjson2.JSON;
import com.pxb7.mall.trade.ass.domain.refund.model.SinceritySellOrderRefundMessage;
import com.pxb7.mall.trade.ass.infra.messaging.MQProducer;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

/**
 * 诚心卖订单退款
 */

@Slf4j
@Service
public class SinceritySellOrderRefundMessageService {

    @Resource
    private MQProducer mqProducer;

    /**
     * 发送线上诚心卖退款自动审核消息
     */
    public void sendRefundAuditMessage(SinceritySellOrderRefundMessage message) {
        long seconds = 60;
        mqProducer.asyncSendDelay("refund_audit:sincerity_sell_online_auto_audit", JSON.toJSONString(message),
            Duration.ofSeconds(seconds));
        log.info("发送线上诚心卖退款自动审核消息:{}, 延迟:{} seconds", JSON.toJSONString(message), seconds);
    }
}
