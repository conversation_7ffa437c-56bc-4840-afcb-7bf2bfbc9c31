package com.pxb7.mall.trade.ass.domain;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.pxb7.mall.trade.ass.infra.repository.db.AssRetrieveWoRepository;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.AssRetrieveWo;

import jakarta.annotation.Resource;

/**
 * 售后找回工单
 *
 * <AUTHOR>
 * @since: 2024-08-09 20:47
 **/
@Service
public class AssRetrieveWoDomainService {
    @Resource
    private AssRetrieveWoRepository assRetrieveWoRepository;

    public Boolean setDealUser(String assRetrieveId, String dealUserId) {
        return assRetrieveWoRepository.setDealUser(assRetrieveId, dealUserId);
    }

    public Boolean closeRetrieveWo(String assRetrieveId) {
        return assRetrieveWoRepository.closeRetrieveWo(assRetrieveId);
    }



    public Boolean userCancel(String assRetrieveId) {
        return assRetrieveWoRepository.closeRetrieveWo(assRetrieveId);
    }



    public AssRetrieveWo findOneById(String id) {
        if (StringUtils.isBlank(id)) {
            return null;
        }
        return assRetrieveWoRepository.getById(id);
    }

}
