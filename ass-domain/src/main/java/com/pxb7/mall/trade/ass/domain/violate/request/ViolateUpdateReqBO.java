package com.pxb7.mall.trade.ass.domain.violate.request;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class ViolateUpdateReqBO {
    /**
     * 违约单id
     */
    @NotNull(message = "请选择违约单")
    private String violateId;
    /**
     * 违约金
     */
    @NotNull(message = "违约金必填")
    @Positive(message = "违约金必须大于0")
    private Long violateAmount;
    /**
     * 守约金
     */
    @NotNull(message = "守约金必填")
    @Positive(message = "守约金必须大于0")
    private Long promiseAmount;

    private String createUserId;
    private String createUserName;

}