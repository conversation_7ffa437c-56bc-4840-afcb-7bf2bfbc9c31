package com.pxb7.mall.trade.ass.domain.refund.model;

import com.pxb7.mall.trade.ass.infra.enums.RefundCallBackStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RefundInvokeRespBO {
    /**
     * 是否成功
     */
    private RefundCallBackStatusEnum statusEnum;

    /**
     * 响应结果字符串
     */
    private String callBackStr;
}
