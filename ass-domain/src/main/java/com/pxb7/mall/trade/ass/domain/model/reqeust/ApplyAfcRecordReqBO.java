package com.pxb7.mall.trade.ass.domain.model.reqeust;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * Copyright (C), 2002-2025, 螃蟹游戏服务网
 *
 * @fileName: ApplyAfcRecordReqBO.java
 * @description: 申请售后记录
 * @author: guo<PERSON><PERSON><PERSON>
 * @email: <EMAIL>
 * @date: 2025/4/11 15:05
 * @history: //修改记录
 * <author> <time> <version> <desc>
 * 修改人姓名 修改时间 版本号 描述
 */
@Data
@Accessors(chain = true)
public class ApplyAfcRecordReqBO {

    /**
     * 提交记录id
     */
    private String submitRecordId;
    /**
     * 订单ID
     */
    private String orderItemId;

    /**
     * 游戏id
     */
    private String gameId;

    /**
     * 游戏名称
     */
    private String gameName;
    /**
     * 商品id
     */
    private String productId;

    /**
     * 商品编码
     */
    private String productUniqueNo;

    /**
     * 售后问题id
     */
    private String questionId;

    /**
     * 备注(其他问题，用户输入的内容)
     */
    private String remark;

    /**
     * 交付房间id
     */
    private String roomId;

    /**
     * 售后类型 1:找回 2:纠纷
     */
    private Integer afcType;

    /**
     * 用户昵称
     */
    private String nickname;
    /**
     * 手机号
     */
    private String phone;

    /**
     * 创建人ID
     */
    private String createUserId;

    /**
     * 更新人id
     */
    private String updateUserId;
}
