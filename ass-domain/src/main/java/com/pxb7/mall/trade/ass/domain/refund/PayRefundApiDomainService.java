package com.pxb7.mall.trade.ass.domain.refund;

import com.alibaba.cola.exception.BizException;
import com.alibaba.cola.exception.SysException;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.ImmutableSet;
import com.pxb7.mall.components.redis.redisson.RLockUtils;
import com.pxb7.mall.trade.ass.client.dto.model.refund.PayChannelEnum;
import com.pxb7.mall.trade.ass.client.dto.model.refund.RefundTradeStatusEnum;
import com.pxb7.mall.trade.ass.domain.PayCompanyAccountDomainService;
import com.pxb7.mall.trade.ass.domain.PayLogDomainService;
import com.pxb7.mall.trade.ass.domain.command.CommandEnum;
import com.pxb7.mall.trade.ass.domain.helper.CommandHelper;
import com.pxb7.mall.trade.ass.domain.refund.message.provider.RefundMessageService;
import com.pxb7.mall.trade.ass.domain.refund.model.*;
import com.pxb7.mall.trade.ass.infra.constant.PayConstant;
import com.pxb7.mall.trade.ass.infra.enums.ErrorCode;
import com.pxb7.mall.trade.ass.infra.enums.RefundCallBackStatusEnum;
import com.pxb7.mall.trade.ass.infra.exception.RefundSysException;
import com.pxb7.mall.trade.ass.infra.model.mq.OriginalRefundMessage;
import com.pxb7.mall.trade.ass.infra.model.mq.PartRefundMessage;
import com.pxb7.mall.trade.ass.infra.model.mq.RefundNotifyMessage;
import com.pxb7.mall.trade.ass.infra.model.mq.RefundQueryMessage;
import com.pxb7.mall.trade.ass.infra.remote.model.refund.JdRefundPO;
import com.pxb7.mall.trade.ass.infra.remote.service.http.JdApiService;
import com.pxb7.mall.trade.ass.infra.repository.db.PayChannelRepository;
import com.pxb7.mall.trade.ass.infra.repository.db.PayLogRepository;
import com.pxb7.mall.trade.ass.infra.repository.db.PaymentRepository;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.CommandDO;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.PayChannel;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.PayLog;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.Payment;
import com.pxb7.mall.trade.ass.infra.util.ObjectMapperUtil;
import com.pxb7.mall.trade.order.client.enums.order.ChannelOrderTypeEnum;
import com.pxb7.mall.trade.order.client.enums.pay.PayStatusEnum;
import com.pxb7.mall.trade.order.client.enums.pay.TradeModeEnum;
import com.pxb7.mall.trade.order.client.enums.pay.TradeStatusEnum;
import com.pxb7.mall.trade.order.client.lock.PayLockConstant;
import jakarta.annotation.Resource;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.springframework.stereotype.Service;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class PayRefundApiDomainService {

    @Resource
    private PayLogRepository payLogRepository;
    @Resource
    private PayChannelRepository payChannelRepository;
    @Resource
    private PaymentRepository paymentRepository;
    @Resource
    private PayCompanyAccountDomainService payCompanyAccountDomainService;
    @Resource
    private PayLogDomainService payLogDomainService;
    @Resource
    private RefundInvokeService refundInvokeService;
    @Resource
    private RefundMessageService refundMessageService;
    @Resource
    private CommandHelper commandHelper;
    @Resource
    private RefundSyncDomainService refundSyncDomainService;
    @Resource
    private JdApiService jdApiService;

    private static final Set<Integer> filterStatus = ImmutableSet.<Integer>builder()
            .add(PayStatusEnum.PAY_SUCCESS.getValue(), PayStatusEnum.PAY_FAIL.getValue())
            .build();

    /**
     * 抢单支付/重复支付 原路退（原payLog退）
     */
    public void originalRefund(OriginalRefundMessage message, boolean delayFlag) {
        String lockKey = String.format(PayLockConstant.PREFIX, message.getPaymentId());
        RLockUtils.of(lockKey, () -> {
                doOriginalRefund(message, delayFlag);
                return true;
            })
            .withWaitTime(5).withTimeUnit(TimeUnit.SECONDS)
            .orElseThrow(() -> new SysException(ErrorCode.REPEAT_OPERATION.getErrCode(),
                ErrorCode.REPEAT_OPERATION.getErrDesc()));
    }

    /**
     * 抢单支付/重复支付 原路退（原payLog退）
     */
    private void doOriginalRefund(OriginalRefundMessage message, boolean delayFlag) {
        PayLog payLog = payLogRepository.getPayLogById(message.getPayLogId());
        if (Objects.isNull(payLog) || StringUtils.isBlank(payLog.getPaymentId())) {
            log.error("[线上原路退款] 发起线上原路退款失败，payLogId = {}", message.getPayLogId());
            return;
        }
        if (!delayFlag && !TradeModeEnum.ONLINE.eq(payLog.getTradeMode())) {
            log.warn("[线上原路退款]  发起线上原路退款失败，不是线上支付类型，payLog = {}",
                payLog);
            return;
        }
        if (TradeModeEnum.SYSTEM_REFUND.eq(payLog.getTradeMode()) && filterStatus.contains(payLog.getPayStatus())) {
            log.warn("[线上原路退款]  发起线上原路退款失败，系统退款已成功或失败，payLog = {}", payLog);
            return;
        }
        PayChannel channel = payChannelRepository.getPayChannelById(payLog.getPayChannelId());
        if (Objects.isNull(channel)) {
            log.error("[线上原路退款] 发起线上原路退款失败，PayChannel 不存在，payLog = {}", payLog);
            return;
        }
        Payment payment = paymentRepository.getByPaymentId(payLog.getPaymentId());
        if (Objects.isNull(payment)) {
            log.error("[线上原路退款] 发起线上原路退款失败，payment 不存在，paymentId = {}", payLog.getPaymentId());
            return;
        }
        PayLog updatePayLog = new PayLog();
        updatePayLog.setPayLogId(payLog.getPayLogId());
        updatePayLog.setOutTradeNo(message.getPayOutTradeNo());
        updatePayLog.setCallbackResult(message.getCallbackResult());
        updatePayLog.setTradeMode(TradeModeEnum.SYSTEM_REFUND.getValue());
        // 执行延迟消息发送  TS原路退款需要支付成功60S后再发起，TODO 易宝退款要求支付清账完1-2S后发起，间隔时间太短会报：订单状态不合法。这里暂时统一用70s方案吧
        if (!delayFlag && (Objects.equals(channel.getChannel(), PayChannelEnum.TSPAY.getValue()) || Objects.equals(channel.getChannel(), PayChannelEnum.YEE_PAY.getValue()))) {
            //更新 payLog 状态：系统退款 - 待执行
            updatePayLog.setPayStatus(PayStatusEnum.PAY_PENDING.getValue());
            payLogRepository.updateById(updatePayLog);
            // 发送延迟消息
            refundMessageService.sendDelayOriginalRefundMessage(message, 70);
            return;
        }
        //更新 payLog 状态：系统退款 - 进行中
        updatePayLog.setPayStatus(PayStatusEnum.PAY_IN_PROGRESS.getValue());
        if (StringUtils.isNotBlank(message.getRefundTradeNo())) {
            updatePayLog.setExtInfo(buildTradeNoToExtInfo(payLog.getExtInfo(), message.getRefundTradeNo()));
        }
        payLogRepository.updateById(updatePayLog);

        // 发送退款查询消息
        refundMessageService.sendRefundQueryMessage(new RefundQueryMessage()
            .setRefundLogId(payLog.getPayLogId()).setPayLogId(payLog.getPayLogId()));

        RefundInvokeReqBO invokeReqBO = buildRefundInvokeReqBO(payLog, channel, payment, message);

        // 执行退款
        invokeRefund(invokeReqBO, payLog, payLog);
    }


    private RefundInvokeReqBO buildRefundInvokeReqBO(PayLog payLog, PayChannel payChannel,
                                                     Payment payment,
                                                     OriginalRefundMessage message) {
        String outPaymentParam = payCompanyAccountDomainService.getDecryptOutParamByAccountId(
            payChannel.getPayCompanyAccountId());
        PayChannelEnum payChannelEnum = PayChannelEnum.getEnum(payChannel.getChannel());
        RefundInvokeReqBO refundInvokeReqBO = new RefundInvokeReqBO();
        refundInvokeReqBO.setOutPayParam(outPaymentParam);
        refundInvokeReqBO.setRefundAmount(payLog.getTradeAmount());
        refundInvokeReqBO.setRefundTradeNo(message.getRefundTradeNo());
        refundInvokeReqBO.setPayType(payChannel.getPayType());
        refundInvokeReqBO.setTotalAmount(payLog.getTradeAmount());
        refundInvokeReqBO.setUserId(payment.getPayUserId());
        refundInvokeReqBO.setPayLogId(payLog.getPayLogId());
        refundInvokeReqBO.setAlreadyRefundAmount(0L);//全额退，所以默认 0
        refundInvokeReqBO.setPayChannel(payChannelEnum.getValue());
        refundInvokeReqBO.setPayClientType(payLog.getPayClientType());
        refundInvokeReqBO.setPayMethod(payChannel.getPayMethod());

        log.info("buildRefundInvokeReqBO 获取退款参数：refundInvokeReqDTO = {}", refundInvokeReqBO);
        return refundInvokeReqBO;
    }

    public void refundPartly(PartRefundMessage message) {
        String lockKey = String.format(PayLockConstant.PREFIX, message.getRefundVoucherId());
        RLockUtils.of(lockKey, () -> {
                doRefundPartly(message);
                return true;
            })
            .withWaitTime(5).withTimeUnit(TimeUnit.SECONDS)
            .orElseThrow(() -> new SysException(ErrorCode.REPEAT_OPERATION.getErrCode(),
                ErrorCode.REPEAT_OPERATION.getErrDesc()));
    }

    public void doRefundPartly(PartRefundMessage message) {
        if (Objects.isNull(message.getRefundAmount()) || message.getRefundAmount() <= 0) {
            sendRefundFailedMsg(message);
            return;
        }

        if(message.getChannelOrderType() != null){
            this.doRefundPartlyChannel(message, message.getChannelOrderType(), message.getChannelExtMap());
        }else{
            this.doRefundPartlyNormal(message);
        }

    }

    /**
     * 渠道订单退款
     *
     * @param message
     * @param type
     * @param ext
     */
    private void doRefundPartlyChannel(PartRefundMessage message, Integer type,
                                       Map<String, Object> ext) {
        if (!ChannelOrderTypeEnum.JD.getValue().equals(type)) {
            throw new BizException("暂不支持该渠道的退款");
        }
        try {
            String jdOrderId = ext.get("outOrderId").toString();
            String jdBuyerId = ext.get("jdBuyerId").toString();
            // 发送退款查询消息
            refundMessageService.sendRefundQueryMessage(new RefundQueryMessage()
                    .setOrderItemId(message.getOrderItemId())
                    .setRefundLogId(message.getRefundVoucherId())
                    .setRefundVoucherId(message.getRefundVoucherId())
                    .setChannelType(type)
                    .setChannelExtMap(ext));
            //执行退款
            jdApiService.refund(new JdRefundPO().setJdOrderId(Long.parseLong(jdOrderId))
                .setJdBuyerId(jdBuyerId)
                .setRefundUuid(message.getRefundVoucherId())
                .setRefundAmount(message.getRefundAmount()));
        } catch (BizException e) {
            throw e;
        } catch (Exception e) {
            throw new BizException("京东退款调用发生异常", e);
        }
    }

    /**
     * 普通支付模式的退款流程(存在payLog流程)
     *
     * @param message 消息体
     */
    private void doRefundPartlyNormal(PartRefundMessage message) {
        // 幂等
        Payment refundPayment = paymentRepository.getByVoucherId(message.getRefundVoucherId());
        if (Objects.nonNull(refundPayment)) {
            log.error("[线上部分退款] 去退款 该退款已处理 refundVoucherId={}, payPaymentId={}",
                message.getRefundVoucherId(), message.getPayPaymentId());
            return;
        }

        // 支付原单payment
        Payment payPayment = paymentRepository.getByPaymentId(message.getPayPaymentId());
        if (Objects.isNull(payPayment)) {
            log.error("[线上部分退款] 去退款 支付原单不存在,支付原单id:{}", message.getPayPaymentId());
            sendRefundFailedMsg(message);
            return;
        }
        if (!TradeStatusEnum.SUCCESS.eq(payPayment.getTradeStatus()) ||
            StringUtils.isBlank(payPayment.getBusinessId())) {
            log.error("[线上部分退款] 去退款 原支付成功订单不存在,支付原单id:{}", message.getPayPaymentId());
            sendRefundFailedMsg(message);
            return;
        }
        PayLog payPayLog = payLogRepository.getPayLogById(payPayment.getBusinessId());
        if (Objects.isNull(payPayLog)) {
            log.error("原支付成功订单不存在,payLogId:{}", payPayment.getBusinessId());
            sendRefundFailedMsg(message);
            return;
        }
        PayChannel channel = payChannelRepository.getPayChannelById(payPayLog.getPayChannelId());
        if (Objects.isNull(channel)) {
            log.error("[线上部分退款] 去退款 发起部分退款失败，PayChannel 不存在，payLog = {}", payPayLog);
            sendRefundFailedMsg(message);
            return;
        }
        String outPaymentParam = payCompanyAccountDomainService.getDecryptOutParamByAccountId(
            channel.getPayCompanyAccountId());
        if (StringUtils.isBlank(outPaymentParam)) {
            log.error("[线上部分退款] 去退款 -- 支付渠道不存在,支付渠道id:{}", channel.getPayCompanyAccountId());
            sendRefundFailedMsg(message);
            return;
        }
        // 校验金额
        if (!validateRefundAmount(message, payPayment.getTradeAmount())) {
            sendRefundFailedMsg(message);
            return;
        }
        //TODO 修复：原路退 三方userId校验失败问题，这里替换为支付payment.payUserId
        String userId = message.getUserId();
        if (StringUtils.isNotBlank(payPayment.getPayUserId())) {
            userId = payPayment.getPayUserId();
        }

        // save refund payment And PayLog
        RefundPayPaymentBO paymentBO = new RefundPayPaymentBO().setOutPayParam(outPaymentParam)
            .setPayLogId(payPayLog.getPayLogId()).setPayMoney(payPayment.getTradeAmount())
            .setPayType(channel.getPayType()).setChannel(channel.getChannel())
            .setChannelId(channel.getChannelId())
            .setCompanyAccountId(channel.getPayCompanyAccountId())
            .setUserId(userId).setRefundAmount(message.getRefundAmount())
            .setRefundVoucherId(message.getRefundVoucherId()).setOrderId(message.getOrderId())
                .setPayMethod(channel.getPayMethod()).setOrderItemId(message.getOrderItemId());
        PayLog refundPayLog = payLogDomainService.saveRefundPaymentAndPayLog(paymentBO);


        RefundInvokeReqBO invokeReqBO =
            buildRefundInvokeReqBO(paymentBO, refundPayLog.getPayLogId(),
                message.getSuccessRefundAmount(),payPayLog.getPayClientType());

        // 退款支付域 状态进行中
        payLogDomainService.updatePayStatusToExecuting(JSON.toJSONString(invokeReqBO),
            refundPayLog.getPaymentId(),
            refundPayLog.getPayLogId(),
            buildTradeNoToExtInfo(refundPayLog.getExtInfo(), refundPayLog.getPayLogId()));

        invokeReqBO.setOutPayParam(paymentBO.getOutPayParam());
        // 发送退款查询消息
        refundMessageService.sendRefundQueryMessage(new RefundQueryMessage()
            .setRefundLogId(refundPayLog.getPayLogId())
            .setPayLogId(payPayLog.getPayLogId())
            .setHistoryRefundPaymentIds(message.getHistoryRefundPaymentIds()));

        // 执行退款
        invokeRefund(invokeReqBO, refundPayLog, payPayLog);
    }


    private void invokeRefund(RefundInvokeReqBO invokeReqBO, PayLog refundPayLog,
                              PayLog payPayLog) {
        RefundInvokeRespBO refundInvokeRespBO = null;
        try {
            refundInvokeRespBO = refundInvokeService.doInvokeRefund(invokeReqBO);
        } catch (RefundSysException e) {
            log.warn("去退款 发生异常 判断是否需要重试 参数:{}", invokeReqBO, e);
            // 系统异常，重试
            boolean tsRefundChannelSysError = Objects.equals(ErrorCode.TS_REFUND_CHANNEL_SYS_ERROR.getErrCode(), e.getErrCode());
            // 是否为需要重新退异常
            boolean refundNeedRetry = Objects.equals(ErrorCode.REFUND_NEED_RETRY.getErrCode(), e.getErrCode());
            if (tsRefundChannelSysError || refundNeedRetry) {
                CommandDO commandDO = commandHelper.createAndSave(CommandEnum.REFUND_CHANEL_RETRY, invokeReqBO.getPayLogId(), ObjectMapperUtil.toJsonStr(invokeReqBO));
                log.warn("退款已自动发起重试,invokeReqBO:{},errorCode:{},commandId:{}",
                    JSON.toJSONString(invokeReqBO), e.getErrCode(), commandDO.getCommandId());
            }
        }

        // 处理失败
        handleRefundFail(refundInvokeRespBO, refundPayLog, payPayLog);
    }

    private void handleRefundFail(RefundInvokeRespBO refundInvokeRespBO, PayLog refundPayLog,
                                  PayLog payPayLog) {
        // 处理失败
        if (Objects.isNull(refundInvokeRespBO) ||
            !RefundCallBackStatusEnum.FAIL.equals(refundInvokeRespBO.getStatusEnum())) {
            return;
        }

        RefundQueryRespBO refundQueryRespBO = new RefundQueryRespBO();
        refundQueryRespBO.setRefundTradeStatus(RefundTradeStatusEnum.FAIL.getValue());

        RefundCallBackBO build = RefundCallBackBO.builder()
            .refundLogId(refundPayLog.getPayLogId())
            .payLogId(payPayLog.getPayLogId())
            .refundQueryRespBO(refundQueryRespBO)
            .build();
        refundSyncDomainService.handleRefundCallBack(build);
    }

    private boolean validateRefundAmount(PartRefundMessage message, long payMoney) {
        List<String> paymentIdList = message.getHistoryRefundPaymentIds();
        long allRefundMoney = 0L;
        if (CollectionUtils.isNotEmpty(paymentIdList)) {
            List<Payment> refundPaymentList = paymentRepository.getListByPaymentIds(paymentIdList);
            if (CollectionUtils.isEmpty(refundPaymentList)) {
                log.error("历史退款数据未找到, paymentIdList:{}", paymentIdList);
                return false;
            }
            allRefundMoney = refundPaymentList.stream().mapToLong(Payment::getActualAmount).sum();
        }
        if ((message.getRefundAmount() + allRefundMoney) > payMoney) {
            log.error("总退款金额大于实付金额");
            return false;
        }
        message.setSuccessRefundAmount(allRefundMoney);
        return true;
    }

    private RefundInvokeReqBO buildRefundInvokeReqBO(RefundPayPaymentBO paymentBO,
                                                     String refundLogId, Long alreadyRefundAmount,Integer payClientType) {
        PayChannelEnum payChannelEnum = PayChannelEnum.getEnum(paymentBO.getChannel());
        RefundInvokeReqBO refundInvokeReqBO = new RefundInvokeReqBO();
        //refundInvokeReqBO.setOutPayParam(paymentBO.getOutPayParam());
        refundInvokeReqBO.setPayLogId(paymentBO.getPayLogId());
        refundInvokeReqBO.setPayType(paymentBO.getPayType());
        refundInvokeReqBO.setUserId(paymentBO.getUserId());
        refundInvokeReqBO.setRefundTradeNo(refundLogId);
        refundInvokeReqBO.setAlreadyRefundAmount(alreadyRefundAmount);
        refundInvokeReqBO.setRefundAmount(paymentBO.getRefundAmount());
        refundInvokeReqBO.setTotalAmount(paymentBO.getPayMoney());
        refundInvokeReqBO.setPayChannel(payChannelEnum.getValue());
        refundInvokeReqBO.setPayClientType(payClientType);
        refundInvokeReqBO.setPayMethod(paymentBO.getPayMethod());
        return refundInvokeReqBO;
    }

    /**
     * @param message
     */
    private void sendRefundFailedMsg(PartRefundMessage message) {
        refundMessageService.sendRefundNotifyMessage(new RefundNotifyMessage()
            .setRefundVoucherId(message.getRefundVoucherId())
            .setTradeStatus(TradeStatusEnum.FAIL.getValue()));
    }

    /**
     * @param extInfo
     * @param refundTradeNo
     * @return
     */
    private String buildTradeNoToExtInfo(String extInfo, String refundTradeNo) {
        JSONObject jsonObject;
        if (StringUtils.isNotBlank(extInfo) && extInfo.contains("{") && extInfo.contains("}")) {
            jsonObject = JSONObject.parseObject(extInfo);
        } else {
            jsonObject = new JSONObject();
        }
        jsonObject.put(PayConstant.REFUND_TRADE_NO, refundTradeNo);
        return jsonObject.toJSONString();
    }
}
