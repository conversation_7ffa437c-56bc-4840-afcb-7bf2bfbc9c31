package com.pxb7.mall.trade.ass.domain.command;

import lombok.Getter;
import lombok.ToString;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 指令枚举
 *
 * <AUTHOR>
 */
@Getter
@ToString
public enum CommandEnum {

    ROCKET_MQ_MESSAGE_SEND("ROCKET_MQ_MESSAGE_SEND", "rocketmq message"),

    REFUND_CHANEL_RETRY("REFUND_CHANEL_RETRY", "渠道退款重试"),

    ;
    private final String code;

    private final String desc;

    CommandEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 任务扫描的时候，不要扫 ROCKET_MQ_MESSAGE_SEND
     */
    public static List<String> getTypeList() {
        return Arrays.stream(values())
                .filter(e -> e != ROCKET_MQ_MESSAGE_SEND) // 过滤掉 ROCKET_MQ_MESSAGE_SEND
                .map(CommandEnum::getCode)
                .collect(Collectors.toList());
    }
}
