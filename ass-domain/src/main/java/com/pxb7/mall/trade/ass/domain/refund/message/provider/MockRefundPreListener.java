package com.pxb7.mall.trade.ass.domain.refund.message.provider;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.pxb7.mall.trade.ass.infra.config.nacos.NacosYamlConfigListener;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class MockRefundPreListener implements NacosYamlConfigListener {

    private String preInvoke;

    @Override
    public void onRefresh(Object newConfig) {
        preInvoke = String.valueOf(newConfig);
    }

    @Override
    public String configPath() {
        return "mock_refund_invoke_pre";
    }

    public MockConfig getPreInvoke() {
        if(StringUtils.isEmpty(this.preInvoke)){
            return null;
        }
        try{
            return JSON.parseObject(this.preInvoke, MockConfig.class);
        }catch(Exception e){
            log.warn("MockRefundPreListener error", e);
        }
        return null;
    }

    @Data
    public static class MockConfig{
        /**
         * 退款前的查询动作请求超时
         */
        private boolean tsMockQuerySysException = false;

        /**
         * 退款请求超时
         */
        private boolean tsMockSysException = false;

        /**
         * 模拟泰山发布导致的BadGateway
         */
        private boolean tsMockBadGateway = false;

        /**
         * 退款接口返回结果
         */
        private JSONObject resonse;
    }
}
