package com.pxb7.mall.trade.ass.domain.mapping;

import com.pxb7.mall.trade.ass.domain.model.response.ComplaintWorkOrderDetailRespBO;
import com.pxb7.mall.trade.ass.domain.model.response.ComplaintWorkOrderLogRespBO;
import com.pxb7.mall.trade.ass.domain.model.response.ComplaintWorkOrderRespBO;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.ComplaintWork;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.ComplaintWorkLog;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * Copyright (C), 2002-2024, 螃蟹游戏服务网
 *
 * @fileName: ComplaintWorkOrderDomainMapping.java
 * @description: 客诉工单
 * @author: guominqiang
 * @email: <EMAIL>
 * @date: 2024/9/20 21:13
 * @history: //修改记录
 * <author> <time> <version> <desc>
 * 修改人姓名 修改时间 版本号 描述
 */
@Mapper
public interface ComplaintWorkOrderDomainMapping {
    ComplaintWorkOrderDomainMapping INSTANCE = Mappers.getMapper(ComplaintWorkOrderDomainMapping.class);

    ComplaintWork transformComplaintWork(ComplaintWorkOrderDetailRespBO complaintWorkOrderDetailRespBO);

    ComplaintWorkOrderDetailRespBO toComplaintWorkOrderDetail(ComplaintWork complaintWork);

    List<ComplaintWorkOrderLogRespBO> toComplaintWorkOrderLogist(List<ComplaintWorkLog> complaintWorkLogList);

    List<ComplaintWorkOrderRespBO> toComplaintWorkOrderRespBOList(List<ComplaintWork> complaintWorkList);
}
