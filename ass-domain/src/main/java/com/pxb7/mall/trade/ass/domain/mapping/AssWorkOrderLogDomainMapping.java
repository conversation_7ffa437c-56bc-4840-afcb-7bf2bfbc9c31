package com.pxb7.mall.trade.ass.domain.mapping;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.pxb7.mall.trade.ass.client.enums.AssScheduleNode;
import com.pxb7.mall.trade.ass.client.enums.AssWoType;
import com.pxb7.mall.trade.ass.domain.model.AssWorkOrderLogReqBO;
import com.pxb7.mall.trade.ass.domain.model.AssWorkOrderLogRespBO;
import com.pxb7.mall.trade.ass.domain.model.assSchedule.ReqCreateAssScheduleBO;
import com.pxb7.mall.trade.ass.domain.model.reqeust.ApplyAfcRecordReqBO;
import com.pxb7.mall.trade.ass.domain.model.reqeust.RecordBO;
import com.pxb7.mall.trade.ass.infra.model.AssWorkOrderLogReqPO;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.AssWorkOrderLog;

import java.util.List;

import com.pxb7.mall.trade.ass.infra.util.IdGenUtil;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;


@Mapper
public interface AssWorkOrderLogDomainMapping {

    AssWorkOrderLogDomainMapping INSTANCE = Mappers.getMapper(AssWorkOrderLogDomainMapping.class);


    AssWorkOrderLogReqPO.AddPO assWorkOrderLogBO2AddPO(AssWorkOrderLogReqBO.AddBO source);

    AssWorkOrderLogReqPO.UpdatePO assWorkOrderLogBO2UpdatePO(AssWorkOrderLogReqBO.UpdateBO source);

    AssWorkOrderLogReqPO.DelPO assWorkOrderLogBO2DelPO(AssWorkOrderLogReqBO.DelBO source);

    AssWorkOrderLogReqPO.SearchPO assWorkOrderLogBO2SearchPO(AssWorkOrderLogReqBO.SearchBO source);

    AssWorkOrderLogReqPO.PagePO assWorkOrderLogBO2PagePO(AssWorkOrderLogReqBO.PageBO source);

    AssWorkOrderLogRespBO.DetailBO assWorkOrderLogPO2DetailBO(AssWorkOrderLog source);

    List<AssWorkOrderLogRespBO.DetailBO> assWorkOrderLogPO2ListBO(List<AssWorkOrderLog> source);

    Page<AssWorkOrderLogRespBO.DetailBO> assWorkOrderLogPO2PageBO(Page<AssWorkOrderLog> source);

    default AssWorkOrderLogReqBO.AddBO toAssWorkOrderLogBO(ApplyAfcRecordReqBO applyAfcRecordReqBO, ReqCreateAssScheduleBO applyAfcScheduleBO){
        String desc = AssWoType.fromCode(applyAfcRecordReqBO.getAfcType()).getDesc();
        String content = StrUtil.format(AssScheduleNode.INITIATE_AFTER_SALE.getUserMsg(), desc);
        AssWorkOrderLogReqBO.AddBO addBO = new AssWorkOrderLogReqBO.AddBO()
                .setWorkOrderLogId(IdGenUtil.generateId())
                .setOrderItemId(applyAfcRecordReqBO.getOrderItemId())
                .setContent(content)
                .setAdminContent(StrUtil.EMPTY)
                .setAddWay(1)
                .setNodeId(AssScheduleNode.INITIATE_AFTER_SALE.name())
                .setNodeDesc(AssScheduleNode.INITIATE_AFTER_SALE.getDesc() + desc)
                .setShowType(1)
                .setAssType(applyAfcRecordReqBO.getAfcType())
                .setCreateUserId(applyAfcRecordReqBO.getCreateUserId())
                .setUpdateUserId(applyAfcRecordReqBO.getUpdateUserId())
                .setTitle(StrUtil.EMPTY);

        return addBO;

    };

}


