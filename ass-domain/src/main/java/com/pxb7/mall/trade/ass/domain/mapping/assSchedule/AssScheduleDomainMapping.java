package com.pxb7.mall.trade.ass.domain.mapping.assSchedule;

import com.pxb7.mall.trade.ass.domain.model.assSchedule.ReqCreateAssScheduleBO;
import com.pxb7.mall.trade.ass.domain.model.reqeust.ApplyAfcRecordReqBO;
import com.pxb7.mall.trade.ass.infra.model.AfcSubmitRecordReqPO;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.AssSchedule;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface AssScheduleDomainMapping {
    AssScheduleDomainMapping INSTANCE = Mappers.getMapper(AssScheduleDomainMapping.class);

    AssSchedule toAssSchedule(ReqCreateAssScheduleBO bo);

    AfcSubmitRecordReqPO.AddPO toAfcSubmitRecordPO(ApplyAfcRecordReqBO applyAfcRecordReqBO);
}
