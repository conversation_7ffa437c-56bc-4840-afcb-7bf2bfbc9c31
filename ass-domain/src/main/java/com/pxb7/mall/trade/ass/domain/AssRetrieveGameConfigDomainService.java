package com.pxb7.mall.trade.ass.domain;


import java.util.List;

import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import com.pxb7.mall.trade.ass.domain.model.AssRetrieveGameConfigReqBO;
import com.pxb7.mall.trade.ass.domain.model.AssRetrieveGameConfigRespBO;
import com.pxb7.mall.trade.ass.domain.mapping.AssRetrieveGameConfigDomainMapping;
import com.pxb7.mall.trade.ass.infra.model.AssRetrieveGameConfigReqPO;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.AssRetrieveGameConfig;
import com.pxb7.mall.trade.ass.infra.repository.db.AssRetrieveGameConfigRepository;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;


/**
 * 售后找回游戏配置domain服务
 *
 * <AUTHOR>
 * @since 2025-08-01 14:11:59
 */
@Service
public class AssRetrieveGameConfigDomainService {

    @Resource
    private AssRetrieveGameConfigRepository assRetrieveGameConfigRepository;

    public boolean insert(AssRetrieveGameConfigReqBO.AddBO param) {
        AssRetrieveGameConfigReqPO.AddPO addPO = AssRetrieveGameConfigDomainMapping.INSTANCE.assRetrieveGameConfigBO2AddPO(param);
        return assRetrieveGameConfigRepository.insert(addPO);
    }

    public boolean update(AssRetrieveGameConfigReqBO.UpdateBO param) {
        AssRetrieveGameConfigReqPO.UpdatePO updatePO = AssRetrieveGameConfigDomainMapping.INSTANCE.assRetrieveGameConfigBO2UpdatePO(param);
        return assRetrieveGameConfigRepository.update(updatePO);
    }

    public boolean deleteById(AssRetrieveGameConfigReqBO.DelBO param) {
        AssRetrieveGameConfigReqPO.DelPO delPO = AssRetrieveGameConfigDomainMapping.INSTANCE.assRetrieveGameConfigBO2DelPO(param);
        return assRetrieveGameConfigRepository.deleteById(delPO);
    }

    public AssRetrieveGameConfigRespBO.DetailBO findById(Long id) {
        AssRetrieveGameConfig entity = assRetrieveGameConfigRepository.findById(id);
        return AssRetrieveGameConfigDomainMapping.INSTANCE.assRetrieveGameConfigPO2DetailBO(entity);

    }

    public List<AssRetrieveGameConfigRespBO.DetailBO> list(AssRetrieveGameConfigReqBO.SearchBO param) {
        AssRetrieveGameConfigReqPO.SearchPO searchPO = AssRetrieveGameConfigDomainMapping.INSTANCE.assRetrieveGameConfigBO2SearchPO(param);
        List<AssRetrieveGameConfig> list = assRetrieveGameConfigRepository.list(searchPO);
        return AssRetrieveGameConfigDomainMapping.INSTANCE.assRetrieveGameConfigPO2ListBO(list);
    }

    public Page<AssRetrieveGameConfigRespBO.DetailBO> page(AssRetrieveGameConfigReqBO.PageBO param) {
        AssRetrieveGameConfigReqPO.PagePO pagePO = AssRetrieveGameConfigDomainMapping.INSTANCE.assRetrieveGameConfigBO2PagePO(param);
        Page<AssRetrieveGameConfig> page = assRetrieveGameConfigRepository.page(pagePO);
        return AssRetrieveGameConfigDomainMapping.INSTANCE.assRetrieveGameConfigPO2PageBO(page);
    }

}

