package com.pxb7.mall.trade.ass.domain.refund.invoke3rd;

import com.alibaba.cola.extension.ExtensionPointI;
import com.pxb7.mall.trade.ass.client.dto.request.refund.RefundInvokeReqDTO;
import com.pxb7.mall.trade.ass.client.dto.request.refund.RefundQueryReqDTO;
import com.pxb7.mall.trade.ass.client.dto.response.refund.RefundInvokeRespDTO;
import com.pxb7.mall.trade.ass.client.dto.response.refund.RefundQueryRespDTO;
import com.pxb7.mall.trade.ass.domain.refund.model.*;

public interface PayRefundExtPt extends ExtensionPointI {

    RefundInvokeRespDTO refund(RefundInvokeReqDTO refundInvokeReqDTO) throws Exception;

    RefundQueryRespDTO refundQuery(RefundQueryReqDTO refundQueryReqDTO) throws Exception;

    RefundInvokeRespBO refundV2(RefundInvokeReqBO reqBO) throws Exception;

    RefundQueryRespBO refundQueryV2(RefundQueryReqBO reqBO) throws Exception;

    /**
     * 退款回调验签
     */
    RefundCallBackBO refundCallbackSign(RefundCallBackReqBO reqBO) throws Exception;
}
