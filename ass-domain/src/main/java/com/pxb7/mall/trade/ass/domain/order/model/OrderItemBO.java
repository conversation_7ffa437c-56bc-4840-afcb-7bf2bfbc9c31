package com.pxb7.mall.trade.ass.domain.order.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class OrderItemBO {


    /**
     * 主订单id
     */
    private String orderId;
    /**
     * 子订单id,业务主键
     */
    private String orderItemId;
    /**
     * 商品业务主键id
     */
    private String productId;
    /**
     * 游戏ID
     */
    private String gameId;
    /**
     * 买家id
     */
    private String buyerId;
    /**
     * 卖家id,诚心卖的卖家是系统
     */
    private String sellerId;
    /**
     * 商品类型: 1账号 2充值 3金币 4装备 5初始号 20诚心卖曝光劵 21诚心卖服务
     */
    private Integer productType;
    /**
     * 卖家商品类型: 1账号 2充值 3金币 4装备 5初始号 10金币出售 11 金币回收 20诚心卖曝光劵 21诚心卖服务
     */
    private Integer sellerProductType;
    /**
     * 1待付款 2交易中 3待结算 4已成交 5已取消 6退款后取消 (待支付是买家, 其他都是卖家状态)
     */
    private Integer orderItemStatus;
    /**
     * 买家订单状态: 1待付款 2交易中 3已成交 4已取消 5退款后已取消
     */
    private Integer buyerStatus;
    /**
     * 卖家订单状态: 0初始值（不能展示在卖家订单列表） 1交易中 2待结算 3已成交 4已取消 5退款后已取消
     */
    private Integer sellerStatus;

    /**
     * 订单行金额（商品售价 + 买家承担的包赔原费用 + 买家承担的手续费原费用）
     */
    private Long orderItemAmount;
    /**
     * 订单行应付款金额（商品实付价格+包赔实付费用+买家实际承担手续费）
     */
    private Long orderItemPayAmount;
    /**
     * 订单行实付金额
     */
    private Long orderItemActualPayAmount;
    /**
     * 商品原价(不包含包赔)
     */
    private Long productOriginalPrice;
    /**
     * 是否议价: 0否 1是
     */
    private Boolean bargain;
    /**
     * 商品销售价格(求降价之后的价格)
     */
    private Long productSalePrice;
    /**
     * 商品优惠金额, 是号价优惠了多少钱
     */
    private Long productCouponAmount;
    /**
     * 商品应付价格(实际支付的商品的钱)(是商品不包含包赔)
     */
    private Long productPayAmount;
    /**
     * 购买的商品数量(诚心卖服务订单以天为单位)
     */
    private Integer productQuantity;
    /**
     * 当前可放款金额
     */
    private Long payoutAmount;
    /**
     * 最大可放款金额（买家实付-买家退款，包含手续费和包赔的金额）
     */
    private Long maxPayoutAmount;
    /**
     * 订单完结时间
     */
    private LocalDateTime completeTime;
    /**
     * 订单取消时间
     */
    private LocalDateTime cancelTime;
    /**
     * 最新收款单状态 1待收款 2进行中 3 已收款 4 收款失败
     */
    private Integer receiptStatus;
    /**
     * 最新退款单状态 1待审核, 2退款中, 3退款成功, 4退款失败, 5退款关闭
     */
    private Integer refundStatus;
    /**
     * 最新放款单状态 1 已制单 2 放款中 3放款成功 4放款失败
     */
    private Integer payoutStatus;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    /**
     * 逻辑删除，0:删除，1:正常
     */
    private Boolean deleted;

    /**
     * 订单行扩展信息
     */
    private OrderItemExtendBO orderItemExtend;
}
