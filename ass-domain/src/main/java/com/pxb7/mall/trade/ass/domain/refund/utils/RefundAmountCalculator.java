package com.pxb7.mall.trade.ass.domain.refund.utils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Objects;

import com.pxb7.mall.trade.order.client.enums.order.ResponsibleEnum;
import org.springframework.util.CollectionUtils;

import com.alibaba.cola.exception.Assert;
import com.alibaba.cola.exception.BizException;
import com.alibaba.fastjson2.JSONObject;
import com.pxb7.mall.trade.ass.client.dto.model.order.OrderItemAmountInfo;
import com.pxb7.mall.trade.ass.infra.enums.ErrorCode;
import com.pxb7.mall.trade.ass.infra.remote.model.refund.OrderItemReceiptVoucherPO;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.OrderItemFee;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.RefundVoucherDetail;
import com.pxb7.mall.trade.order.client.enums.order.OrderItemFeeTypeEnum;
import com.pxb7.mall.trade.order.client.enums.order.ReceiptResponsibleEnum;

import lombok.extern.slf4j.Slf4j;

/**
 * 计价中心业务服务
 */
@Slf4j
public class RefundAmountCalculator {
    /**
     * 计算部分退款应退金额 该方法根据成功收款单和成功退款单，计算部分退款金额详情 包括商品金额、红包金额、包赔金额以及手续费金额和总金额
     *
     */
    public static OrderItemAmountInfo getPartRefundAmount(String orderItemId, OrderItemAmountInfo wholeReceiptInfo,
        OrderItemAmountInfo successRefundAmount, Long applyProductRefundAmount, OrderItemFee orderItemFee) {
        OrderItemAmountInfo partRefundAmount = new OrderItemAmountInfo();
        if (wholeReceiptInfo.getProductAmount() == 0L || applyProductRefundAmount == null
            || applyProductRefundAmount >= wholeReceiptInfo.getProductAmount()
                - successRefundAmount.getProductAmount()) {
            // 商品价格为0 或 申请退款金额大于等于 商品号价剩余可退金额
            throw new BizException(ErrorCode.REFUND_AMOUNT_ERROR.getErrCode(),
                ErrorCode.REFUND_AMOUNT_ERROR.getErrDesc());
        }
        BigDecimal applyProductRefundAmountBD = BigDecimal.valueOf(applyProductRefundAmount);
        BigDecimal totalProductAmount = BigDecimal.valueOf(wholeReceiptInfo.getProductAmount());
        // 1、计算商品实付应退金额 = 商品实付金额 / (商品实付金额 + 红包支付金额) * 申请退款金额
        // 2、计算红包应退金额 = 退款金额 - 商品实付应退金额
        if (wholeReceiptInfo.getRedPacketAmount() > 0) {
            BigDecimal totalRedPacketAmount = BigDecimal.valueOf(wholeReceiptInfo.getRedPacketAmount());
            partRefundAmount.setProductAmount(totalProductAmount.multiply(applyProductRefundAmountBD)
                .divide(totalProductAmount.add(totalRedPacketAmount), RoundingMode.FLOOR).longValue());
            partRefundAmount.setRedPacketAmount(applyProductRefundAmount - partRefundAmount.getProductAmount());
        } else {
            partRefundAmount.setProductAmount(applyProductRefundAmount);
        }
        // 3、计算包赔应退金额 = （申请退商品金额/商品售价）* 包赔实付
        BigDecimal totalIndemnityAmount = BigDecimal.valueOf(wholeReceiptInfo.getIndemnityAmount());
        if (wholeReceiptInfo.getIndemnityAmount() > 0) { // 买家承担包赔
            partRefundAmount.setIndemnityAmount(applyProductRefundAmountBD.multiply(totalIndemnityAmount)
                .divide(totalProductAmount, RoundingMode.FLOOR).longValue());
        }
        // 4、重新计算号价退款后的手续费，算出当前剩余手续费和新手续费的差额，作为退款的手续费
        if (wholeReceiptInfo.getFeeAmount() > 0) { // 买家承担手续费
            partRefundAmount.setFeeAmount(calculateFeeAmount4PartRefund(applyProductRefundAmountBD, totalProductAmount,
                wholeReceiptInfo.getFeeAmount(), orderItemFee, successRefundAmount));
        }
        // 计算总金额
        partRefundAmount.setTotalAmount(partRefundAmount.getProductAmount() + partRefundAmount.getRedPacketAmount()
            + partRefundAmount.getIndemnityAmount() + partRefundAmount.getFeeAmount());
        // 校验总金额是否可以退
        checkTotalRefundAmount(orderItemId, partRefundAmount.getTotalAmount(), successRefundAmount, wholeReceiptInfo);
        log.info(
            "getPartRefundAmount orderItemId:{}, applyAmount:{},wholeReceiptInfo:{},successRefundAmount:{},"
                + " orderFee:{}, result:{}",
            orderItemId, applyProductRefundAmount, JSONObject.toJSONString(wholeReceiptInfo),
            JSONObject.toJSONString(successRefundAmount), JSONObject.toJSONString(orderItemFee),
            JSONObject.toJSONString(partRefundAmount));
        return partRefundAmount;
    }

    private static long calculateFeeAmount4PartRefund(BigDecimal applyProductRefundAmountBD,
        BigDecimal totalProductAmount, long totalFeeAmount, OrderItemFee orderItemFee,
        OrderItemAmountInfo refundAmountInfo) {
        Assert.notNull(orderItemFee, ErrorCode.ORDER_FEE_NOT_FOUND_ERROR.getErrCode(),
            ErrorCode.ORDER_FEE_NOT_FOUND_ERROR.getErrDesc());
        if (Objects.equals(orderItemFee.getFeeType(), OrderItemFeeTypeEnum.FIX.getValue())) {
            // 如果是固定手续费，则直接返回0
            return 0;
        }
        // 重新计算退款后号价的手续费
        long newFeeAmount = totalProductAmount.subtract(BigDecimal.valueOf(refundAmountInfo.getProductAmount()))
            .subtract(applyProductRefundAmountBD).multiply(BigDecimal.valueOf(orderItemFee.getFeeRatio()))
            .divide(BigDecimal.valueOf(100), RoundingMode.HALF_UP).longValue();
        newFeeAmount = Math.min(newFeeAmount, orderItemFee.getFeeAmountMax());
        newFeeAmount = Math.max(newFeeAmount, orderItemFee.getFeeAmountMin());

        long refundFee = Math.max(totalFeeAmount - refundAmountInfo.getFeeAmount() - newFeeAmount, 0L);
        log.info(
            "calculateFeeAmount4PartRefund orderItemId:{},totalProductAmount:{}, totalFeeAmount:{}, orderItemFee:{},"
                + "successRefundAmountInfo:{}}, applyProductAmount:{}," + "toRefundFee:{}",
            orderItemFee.getOrderItemId(), totalProductAmount.longValue(), totalFeeAmount,
            JSONObject.toJSONString(totalFeeAmount), JSONObject.toJSONString(refundAmountInfo),
            applyProductRefundAmountBD.longValue(), refundFee);
        return refundFee;
    }

    private static void checkTotalRefundAmount(String orderItemId, long applyAmount,
        OrderItemAmountInfo successRefundAmount, OrderItemAmountInfo wholeReceiptInfo) {
        long totalSuccessRefund = successRefundAmount.getTotalAmount();
        long totalMaxRefund = wholeReceiptInfo.getTotalAmount();
        long remainRefundAmount = totalMaxRefund - totalSuccessRefund;
        if (remainRefundAmount <= applyAmount || applyAmount <= 0L) {
            log.info("orderItemId:{},applyAmount:{},totalSuccessRefund:{},totalMaxRefund:{}", orderItemId, applyAmount,
                totalSuccessRefund, totalMaxRefund);
            throw new BizException(ErrorCode.REFUND_AMOUNT_ERROR.getErrCode(),
                ErrorCode.REFUND_AMOUNT_ERROR.getErrDesc());
        }
    }

    /**
     * 校验退款金额明细
     */
    public static void checkRefundAmount(String orderItemId, OrderItemAmountInfo applyAmount,
        OrderItemAmountInfo wholeAmount) {
        if (wholeAmount.getTotalAmount() <= applyAmount.getTotalAmount()
            || wholeAmount.getProductAmount() < applyAmount.getProductAmount()
            || wholeAmount.getFeeAmount() < applyAmount.getFeeAmount()
            || wholeAmount.getIndemnityAmount() < applyAmount.getIndemnityAmount()
            || applyAmount.getTotalAmount() <= 0L) {
            log.info("orderItemId:{},applyAmount:{},wholeAmount:{}", orderItemId, applyAmount, wholeAmount);
            throw new BizException(ErrorCode.REFUND_AMOUNT_ERROR.getErrCode(),
                ErrorCode.REFUND_AMOUNT_ERROR.getErrDesc());
        }
    }

    /**
     * 计算整单退款金额信息 该方法根据成功的收款单和成功的退款单列表，计算出剩余整单的退款金额信息
     *
     */
    public static OrderItemAmountInfo getWholeRefundAmount(String orderItemId, OrderItemAmountInfo wholeReceiptAmount,
        OrderItemAmountInfo successRefundInfo) {
        OrderItemAmountInfo refundAmountInfo = wholeReceiptAmount.subtract(successRefundInfo);
        log.info(
            "getWholeRefundAmount orderItemId:{},wholeReceiptAmount:{},sucRefundInfo:{}," + "curRefundAmountInfo:{}",
            orderItemId, JSONObject.toJSONString(wholeReceiptAmount), JSONObject.toJSONString(successRefundInfo),
            JSONObject.toJSONString(refundAmountInfo));
        return refundAmountInfo;
    }

    public static OrderItemAmountInfo getWholeReceiptAmount(List<OrderItemReceiptVoucherPO> receiptVouchers) {
        if (CollectionUtils.isEmpty(receiptVouchers)) {
            throw new BizException(ErrorCode.REFUND_AMOUNT_ERROR.getErrCode(),
                ErrorCode.REFUND_AMOUNT_ERROR.getErrDesc());
        }
        long productAmount =
            receiptVouchers.stream().mapToLong(e -> e.getProductAmount() == null ? 0 : e.getProductAmount()).sum();
        long feeAmount = receiptVouchers.stream()
            .filter(e -> Objects.equals(e.getFeeResponsibleUser(), ReceiptResponsibleEnum.BUYER.getValue()))
            .mapToLong(e -> e.getFeeAmount() == null ? 0 : e.getFeeAmount()).sum();
        long indemnityAmount = receiptVouchers.stream()
            .filter(e -> Objects.equals(e.getIndemnityResponsibleUser(), ReceiptResponsibleEnum.BUYER.getValue()))
            .mapToLong(e -> e.getIndemnityAmount() == null ? 0 : e.getIndemnityAmount()).sum();
        long redPacketAmount =
            receiptVouchers.stream().mapToLong(e -> e.getRedPacketAmount() == null ? 0 : e.getRedPacketAmount()).sum();
        long total =
            receiptVouchers.stream().mapToLong(e -> e.getMaxRefundAmount() == null ? 0 : e.getMaxRefundAmount()).sum();
        return new OrderItemAmountInfo().setProductAmount(productAmount).setFeeAmount(feeAmount)
            .setIndemnityAmount(indemnityAmount).setRedPacketAmount(redPacketAmount).setTotalAmount(total);
    }

    public static OrderItemAmountInfo getSuccessRefundAmount(List<RefundVoucherDetail> refundVoucherDetails) {
        if (CollectionUtils.isEmpty(refundVoucherDetails)) {
            return new OrderItemAmountInfo().setProductAmount(0).setFeeAmount(0).setIndemnityAmount(0)
                .setRedPacketAmount(0).setTotalAmount(0);
        }
        long productAmount =
            refundVoucherDetails.stream().mapToLong(e -> e.getProductAmount() == null ? 0 : e.getProductAmount()).sum();
        long feeAmount =
                refundVoucherDetails.stream().filter(e -> e.getFeeResponsibleUser() == null || ResponsibleEnum.BUYER.eq(e.getFeeResponsibleUser()))
                        .mapToLong(e -> e.getFeeAmount() == null ? 0 : e.getFeeAmount()).sum();
        long indemnityAmount = refundVoucherDetails.stream().filter(e -> ResponsibleEnum.BUYER.eq(e.getIndemnityResponsibleUser()))
                .mapToLong(e -> e.getIndemnityAmount() == null ? 0 : e.getIndemnityAmount()).sum();

        long redPacketAmount = refundVoucherDetails.stream()
            .mapToLong(e -> e.getRedPacketAmount() == null ? 0 : e.getRedPacketAmount()).sum();
        long total = productAmount + feeAmount + indemnityAmount + redPacketAmount;
        return new OrderItemAmountInfo().setProductAmount(productAmount).setFeeAmount(feeAmount)
            .setIndemnityAmount(indemnityAmount).setRedPacketAmount(redPacketAmount).setTotalAmount(total);
    }
}
