package com.pxb7.mall.trade.ass.domain.model.response;

import java.io.Serializable;

import com.pxb7.mall.trade.ass.client.enums.AssWoType;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/8/12
 */
@Data
public class AssScheduleRespBO implements Serializable {

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 主键id
     */
    private String scheduleId;

    /**
     * 工单id
     */
    private String workOrderId;

    /**
     * 售后类型1找回2纠纷
     */
    private AssWoType assType;

    /**
     * 接待客服id
     */
    private String recvCustomerId;

    /**
     * 审核客服id
     */
    private String auditCustomerId;

    /**
     * 房间id
     */
    private String roomId;

    /**
     * 是否完结
     */
    private Boolean finish;

}
