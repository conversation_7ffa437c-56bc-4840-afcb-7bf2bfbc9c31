package com.pxb7.mall.trade.ass.domain.mapping;

import com.pxb7.mall.trade.ass.domain.command.sub.CommandDTO;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.CommandDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface CommandMapping {

    CommandMapping INSTANCE = Mappers.getMapper(CommandMapping.class);

    CommandDTO to(CommandDO commandDO);
}
