package com.pxb7.mall.trade.ass.domain.violate;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.cola.exception.Assert;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.pxb7.mall.trade.ass.client.enums.violate.*;
import com.pxb7.mall.trade.ass.domain.violate.mapping.ViolateOrderDomainMapping;
import com.pxb7.mall.trade.ass.domain.violate.model.ViolateOrderBO;
import com.pxb7.mall.trade.ass.domain.violate.request.ViolateCreateReqBO;
import com.pxb7.mall.trade.ass.infra.enums.ErrorCode;
import com.pxb7.mall.trade.ass.infra.model.ViolateOrderReqPO;
import com.pxb7.mall.trade.ass.infra.repository.db.ViolateOrderRepository;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.ViolateOrder;
import com.pxb7.mall.trade.ass.infra.util.IdGenUtil;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

import static com.pxb7.mall.trade.ass.client.enums.violate.ViolateStatusEnum.*;
import static com.pxb7.mall.trade.ass.domain.violate.constant.OrderViolateConstant.VIOLATE_RECEIPT_STOP;
import static com.pxb7.mall.trade.ass.infra.enums.ErrorCode.VIOLATE_UPDATE_STATUS_ERROR;
import static com.pxb7.mall.trade.ass.infra.enums.ErrorCode.VIOLATE_UPDATE_T_STATUS_ERROR;
import static com.pxb7.mall.trade.ass.infra.util.DateUtil.getExpireTime;
import static com.pxb7.mall.trade.order.client.constants.OrderNumberConstant.WY_NO;

@Service
public class ViolateOrderDomainService {
    @Resource
    private ViolateOrderRepository violateOrderRepository;
    @Resource
    private ViolateOrderDomainMapping violateOrderDomainMapping;


    // 创建违约单
    public ViolateOrder create(ViolateCreateReqBO po) {
        ViolateOrder violate = violateOrderDomainMapping.toViolateOrder(po);
        violate.setViolateId(WY_NO + IdGenUtil.getShardingColumnId(po.getOrderItemId()))
                .setPlatformAmount(po.getViolateAmount() - po.getPromiseAmount());
        Assert.isTrue(violateOrderRepository.save(violate), ErrorCode.VIOLATE_CREATE_ERROR.getErrCode(), ErrorCode.VIOLATE_CREATE_ERROR.getErrDesc());
        return violate;
    }

    // 获取退款单待处理的违约单
    public ViolateOrder violateWaitingByRefundId(String refundVoucherId) {
        ViolateOrderReqPO.SearchPO po = new ViolateOrderReqPO.SearchPO()
                .setRefundVoucherId(refundVoucherId)
                .setViolateStatus(INIT.getValue());
        return violateOrderRepository.find(po);
    }

    public ViolateOrder getDetailById(String violateId) {
        ViolateOrderReqPO.SearchPO po = new ViolateOrderReqPO.SearchPO().setViolateId(violateId);
        return violateOrderRepository.find(po);
    }

    public ViolateOrder getEffectiveDetailById(String violateId) {
        ViolateOrderReqPO.SearchPO po = new ViolateOrderReqPO.SearchPO()
                .setViolateId(violateId)
                .setViolateStatus(DEALING.getValue());
        return violateOrderRepository.find(po);
    }

    // 获取订单所有的有效违约单(处理中||处理成功) && 退款单待处理的违约单(为了兼容可能存在待处理未取消成功的异常情况)
    public boolean hasViolateByOrderIdAndRefundId(String orderItemId,String refundVoucherId) {
        ViolateOrderReqPO.SearchPO po = new ViolateOrderReqPO.SearchPO()
                .setOrderItemId(orderItemId)
                .setViolateStatuses(ViolateStatusEnum.getEffectiveStatus());
        long orderViolateNum = violateOrderRepository.count(po);
        if(orderViolateNum > 0){
            return true;
        }
        if(StrUtil.isNotBlank(refundVoucherId)){
            return ObjectUtil.isNotEmpty(violateWaitingByRefundId(refundVoucherId));
        }
        return false;
    }

    // 获取订单流程中的违约单
    public ViolateOrder violateDealingByOrder(String orderItemId) {
        ViolateOrderReqPO.SearchPO po = new ViolateOrderReqPO.SearchPO()
                .setOrderItemId(orderItemId)
                .setViolateStatuses(ViolateStatusEnum.getDealingStatus());
        return violateOrderRepository.find(po);
    }

    // 开始违约单处理
    public boolean violateDeal(ViolateOrder violateOrder,boolean buyerSkipReceipt) {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime receiptStopTime = getExpireTime(now, VIOLATE_RECEIPT_STOP);
        Integer violateStatus = DEALING.getValue();
        LambdaUpdateWrapper<ViolateOrder> update = new LambdaUpdateWrapper<ViolateOrder>()
                .eq(ViolateOrder::getViolateId, violateOrder.getViolateId())
                .eq(ViolateOrder::getViolateStatus, ViolateStatusEnum.INIT.getValue())
                .set(ViolateOrder::getDealTime, now)
                .set(ViolateOrder::getReceiptStopTime, receiptStopTime);
        if (ViolateUserTypeEnum.BUYER.eq(violateOrder.getViolateUserType()) && buyerSkipReceipt) {
            update.set(ViolateOrder::getReceiptStatus, ViolateReceiptStatusEnum.SUCCESS.getValue());
            if (violateOrder.getPromiseAmount() <= 0) {
                update.set(ViolateOrder::getTransferStatus, ViolateTransferStatusEnum.SUCCESS.getValue())
                        .set(ViolateOrder::getCompletedTime, now);
                violateStatus = SUCCESS.getValue();
            }
        }
        update.set(ViolateOrder::getViolateStatus, violateStatus);

        return violateOrderRepository.update(update);
    }

    // 违约单打款
    public void violateTransferStart(String violateId) {
        LambdaUpdateWrapper<ViolateOrder> update = new LambdaUpdateWrapper<ViolateOrder>()
                .eq(ViolateOrder::getViolateId, violateId)
                .eq(ViolateOrder::getTransferStatus, ViolateTransferStatusEnum.WAIT.getValue())
                .set(ViolateOrder::getTransferStatus, ViolateTransferStatusEnum.DEALING.getValue());
        Assert.isTrue(violateOrderRepository.update(update));
    }

    // 违约单收款成功
    public void violateDeductionSuccess(String violateId, boolean noTransferFlag) {
        LambdaUpdateWrapper<ViolateOrder> update = new LambdaUpdateWrapper<ViolateOrder>()
                .eq(ViolateOrder::getViolateId, violateId)
                .notIn(ViolateOrder::getReceiptStatus, ViolateReceiptStatusEnum.getCompleted())
                .set(ViolateOrder::getReceiptStatus, ViolateReceiptStatusEnum.SUCCESS.getValue());
        if (noTransferFlag) {
            // 不需要打守约金,直接打款成功
            update.set(ViolateOrder::getTransferStatus, ViolateTransferStatusEnum.SUCCESS.getValue())
                    .set(ViolateOrder::getViolateStatus, ViolateStatusEnum.SUCCESS.getValue())
                    .set(ViolateOrder::getCompletedTime, LocalDateTime.now());
        }
        Assert.isTrue(violateOrderRepository.update(update), VIOLATE_UPDATE_STATUS_ERROR.getErrDesc());
    }

    // 违约单收款中断
    public void violateReceiptStop(String violateId) {
        LambdaUpdateWrapper<ViolateOrder> update = new LambdaUpdateWrapper<ViolateOrder>()
                .eq(ViolateOrder::getViolateId, violateId)
                .ne(ViolateOrder::getReceiptStatus, ViolateReceiptStatusEnum.SUCCESS.getValue())
                .set(ViolateOrder::getReceiptStopTime, LocalDateTime.now())
                .set(ViolateOrder::getReceiptStatus, ViolateReceiptStatusEnum.STOP.getValue());
        Assert.isTrue(violateOrderRepository.update(update), VIOLATE_UPDATE_STATUS_ERROR.getErrDesc());
    }

    // 违约单打款完成
    public void violatetransferSuccess(String violateId) {
        LambdaUpdateWrapper<ViolateOrder> update = new LambdaUpdateWrapper<ViolateOrder>()
                .eq(ViolateOrder::getViolateId, violateId)
                .eq(ViolateOrder::getTransferStatus, ViolateTransferStatusEnum.DEALING.getValue())
                .set(ViolateOrder::getTransferStatus, ViolateTransferStatusEnum.SUCCESS.getValue())
                .set(ViolateOrder::getViolateStatus, ViolateStatusEnum.SUCCESS.getValue())
                .set(ViolateOrder::getCompletedTime, LocalDateTime.now());
        Assert.isTrue(violateOrderRepository.update(update), VIOLATE_UPDATE_T_STATUS_ERROR.getErrDesc());
    }

    // 重新编辑
    public void violateEdit(ViolateOrder violateOrder) {
        LambdaUpdateWrapper<ViolateOrder> update = new LambdaUpdateWrapper<ViolateOrder>()
                .eq(ViolateOrder::getViolateId, violateOrder.getViolateId())
                .in(ViolateOrder::getReceiptStatus, ViolateReceiptStatusEnum.getFailed())
                .set(ViolateOrder::getReceiptStatus, ViolateReceiptStatusEnum.INIT.getValue())
                .set(ViolateOrder::getViolateAmount, violateOrder.getViolateAmount())
                .set(ViolateOrder::getPromiseAmount, violateOrder.getPromiseAmount())
                .set(ViolateOrder::getPlatformAmount, violateOrder.getPlatformAmount())
                .set(ViolateOrder::getUpdateUserId, violateOrder.getUpdateUserId())
                .set(ViolateOrder::getUpdateUsername, violateOrder.getUpdateUsername());
        Assert.isTrue(violateOrderRepository.update(update), VIOLATE_UPDATE_STATUS_ERROR.getErrDesc());
    }

    public ViolateOrderBO getByViolateId(String violateId) {
        ViolateOrderReqPO.SearchPO po = new ViolateOrderReqPO.SearchPO().setViolateId(violateId);
        ViolateOrder violateOrder = violateOrderRepository.find(po);
        return violateOrderDomainMapping.toViolateOrderBO(violateOrder);
    }

    public List<ViolateOrderBO> getEffectiveListByOrderItemId(String orderItemId) {
        // 查询状态为：DEALING,SUCCESS,FAIL的违约单
        List<Integer> effectiveStatus = List.of(ViolateStatusEnum.DEALING.getValue(), ViolateStatusEnum.SUCCESS.getValue(), ViolateStatusEnum.FAIL.getValue());
        LambdaQueryWrapper<ViolateOrder> query = new LambdaQueryWrapper<>();
        query.in(ViolateOrder::getViolateStatus, effectiveStatus);
        query.eq(ViolateOrder::getOrderItemId, orderItemId);
        query.orderBy(true, false, ViolateOrder::getCreateTime);
        List<ViolateOrder> list = violateOrderRepository.list(query);
        return violateOrderDomainMapping.toViolateOrderBOList(list);
    }

    // 取消违约单
    public void cancelViolate(String violateId) {
        LambdaUpdateWrapper<ViolateOrder> update = new LambdaUpdateWrapper<ViolateOrder>()
                .eq(ViolateOrder::getViolateId, violateId)
                .eq(ViolateOrder::getViolateStatus, ViolateStatusEnum.INIT.getValue())
                .set(ViolateOrder::getViolateStatus, ViolateStatusEnum.CANCEL.getValue());
        Assert.isTrue(violateOrderRepository.update(update), VIOLATE_UPDATE_STATUS_ERROR.getErrDesc());
    }

}
