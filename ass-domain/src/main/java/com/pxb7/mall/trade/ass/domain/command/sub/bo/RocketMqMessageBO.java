package com.pxb7.mall.trade.ass.domain.command.sub.bo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RocketMqMessageBO {
    private String topic;

    private String tag;

    /**
     * messageKey
     */
    private String key;
    @Deprecated
    // 是否延迟消息
    private boolean isDelay;

    private String content;

    // 延迟时间
    private long seconds;

    /**
     * 消息分组 用于延迟消息
     */
    private String messageGroup;

    private MessageTypeEnum messageType;

    public enum MessageTypeEnum {
        DELAY,
        NORMAL,
        FIFO
    }
}
