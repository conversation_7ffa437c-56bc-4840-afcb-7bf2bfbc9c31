package com.pxb7.mall.trade.ass.domain.refund.model;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class RefundPayPaymentBO {
    /**
     * 资金账户支付参数
     */
    private String outPayParam;
    /**
     * 支付时的内部单号
     */
    private String payLogId;
    /**
     * 支付金额
     */
    private Long payMoney;

    /**
     * 支付类别, 1:微信，2：支付宝 3: 银行卡
     */
    private Integer payType;

    /**
     * 支付渠道
     */
    private Integer channel;

    /**
     * 支付渠道id
     */
    private String channelId;
    /**
     * 螃蟹资金账户id
     */
    private String companyAccountId;
    /**
     * 原交易付款方user_id(连连支付需要)
     */
    private String userId;
    /**
     * 退款金额，单位分
     */
    private Long refundAmount;
    /**
     * 退款单据id
     */
    private String refundVoucherId;
    /**
     * 主订单id
     */
    private String orderId;
    /**
     * 子订单id,业务主键
     */
    private String orderItemId;

    /**
     * 跳转方式 1-拉起支付宝/微信原生app,2-跳转H5页面之后拉起原生app,3-iframe渲染出二维码-扫码,4-链接渲染成二维码-扫码,5-链接直接打开跳转-泰山h5
     * @see com.pxb7.mall.trade.order.client.enums.pay.PayMethodEnum
     */
    private Integer payMethod;

    /**
     * 退款单据类型
     */
    private Integer refundVoucherType;

}
