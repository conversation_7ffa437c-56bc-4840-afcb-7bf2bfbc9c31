package com.pxb7.mall.trade.ass.domain.model;

import com.pxb7.mall.pay.client.enums.AccountTradeTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WalletPayInboundBO {
    /**
     * 入账用户
     */
    private String userId;

    /**
     * 业务单据ID
     */
    private String voucherId;

    /**
     * 入账金额
     */
    private Long amount;

    /**
     * 回调
     */
    private String callback;

    private String orderId;

    private AccountTradeTypeEnum accountTradeTypeEnum;
}
