package com.pxb7.mall.trade.ass.domain.violate;

import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.cola.exception.Assert;
import com.alibaba.cola.exception.BizException;
import com.pxb7.mall.pay.client.dto.response.AccountTradeInfoRespDTO;
import com.pxb7.mall.pay.client.dto.response.PayAccountTradeResp;
import com.pxb7.mall.pay.client.enums.AccountTradeStatusEnum;
import com.pxb7.mall.pay.client.enums.AccountTradeTypeEnum;
import com.pxb7.mall.trade.ass.client.enums.RiskBlackOperateType;
import com.pxb7.mall.trade.ass.client.enums.violate.ViolateChannelTypeEnum;
import com.pxb7.mall.trade.ass.client.enums.violate.ViolateUserTypeEnum;
import com.pxb7.mall.trade.ass.domain.OrderOperateDomainService;
import com.pxb7.mall.trade.ass.domain.WalletPayDomainService;
import com.pxb7.mall.trade.ass.domain.model.WalletPayInboundBO;
import com.pxb7.mall.trade.ass.domain.model.WalletToPayBO;
import com.pxb7.mall.trade.ass.domain.order.OrderItemDomainService;
import com.pxb7.mall.trade.ass.domain.order.model.OrderItemExtendBO;
import com.pxb7.mall.trade.ass.domain.violate.model.ViolateOrderBO;
import com.pxb7.mall.trade.ass.domain.violate.mq.ViolateMessageService;
import com.pxb7.mall.trade.ass.domain.violate.mq.message.ViolatePayRecordMessage;
import com.pxb7.mall.trade.ass.domain.violate.request.ViolateCreateReqBO;
import com.pxb7.mall.trade.ass.domain.violate.response.ViolateWalletDeductionRespBO;
import com.pxb7.mall.trade.ass.infra.enums.ErrorCode;
import com.pxb7.mall.trade.ass.infra.model.RiskAddBlackRecordPO;
import com.pxb7.mall.trade.ass.infra.model.RiskRemoveBlackRecordPO;
import com.pxb7.mall.trade.ass.infra.model.UserCertInfoRespPO;
import com.pxb7.mall.trade.ass.infra.remote.dubbo.PayServiceGateway;
import com.pxb7.mall.trade.ass.infra.repository.db.OrderItemRepository;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.ViolateOrder;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.ViolatePayRecord;
import com.pxb7.mall.trade.ass.infra.repository.gateway.risk.RiskBlackGatewayRepository;
import com.pxb7.mall.trade.ass.infra.repository.gateway.user.UserGatewayRepository;
import com.pxb7.mall.trade.order.client.enums.order.OrderOperateTypeEnum;
import com.pxb7.mall.trade.order.client.enums.order.OrderOptUserTypeEnum;
import com.pxb7.mall.trade.order.client.enums.order.ReceiptVoucherStatusEnum;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Objects;
import java.util.Optional;
import java.util.Set;

import static com.pxb7.mall.trade.ass.domain.violate.constant.OrderViolateConstant.VIOLATE_RECEIPT_STOP;
import static com.pxb7.mall.trade.ass.infra.enums.ErrorCode.*;

@Service
@Slf4j
public class ViolateDomainService {
    @Resource
    private ViolateMessageService violateMessageService;
    @Resource
    private ViolateOrderDomainService violateOrderDomainService;
    @Resource
    private ViolatePayRecordDomainService violatePayRecordDomainService;
    @Resource
    private OrderOperateDomainService orderOperateDomainService;
    @Resource
    private OrderItemDomainService orderItemDomainService;
    @Resource
    private RiskBlackGatewayRepository riskBlackGatewayRepository;
    @Resource
    private UserGatewayRepository userGatewayRepository;
    @Resource
    private PayServiceGateway payServiceGateway;
    @Resource
    private OrderItemRepository orderItemRepository;
    @Resource
    private WalletPayDomainService walletPayDomainService;

    @Transactional(rollbackFor = Throwable.class, timeout = 5)
    public boolean violateDealStart(ViolateOrder violateOrder) {
        return violateStart(violateOrder, true);
    }

//    public boolean violateStart(ViolateOrder violateOrder, boolean buyerSkipReceipt) {
//        // 更新违约单状态
//        Assert.isTrue(violateOrderDomainService.violateDeal(violateOrder,buyerSkipReceipt), ErrorCode.VIOLATE_CREATE_ERROR.getErrDesc());
//
//        // 更新订单收款状态
//        orderItemRepository.updateReceiptVoucherStatus(violateOrder.getOrderItemId(), ReceiptVoucherStatusEnum.PENDING.getValue());
//
//        // 记录操作日志
//        orderOperateDomainService.addViolateOperate(
//                violateOrder.getOrderItemId()
//                , violateOrder
//                , OrderOperateTypeEnum.ORDER_VIOLATE_CREATE
//                , OrderOptUserTypeEnum.SERVICE
//                , violateOrder.getCreateUserId()
//                , violateOrder.getCreateUsername()
//        );
//
//        // (买家)违约打款 or 卖家违约扣款
//        ViolatePayRecordMessage violatePayRecordMessage = new ViolatePayRecordMessage()
//                .setViolateId(violateOrder.getViolateId())
//                .setCreateUserId(violateOrder.getCreateUserId())
//                .setCreateUserName(violateOrder.getCreateUsername());
//        if (ViolateUserTypeEnum.BUYER.getValue().equals(violateOrder.getViolateUserType())) {
//            // 违约-打款一致,都走MQ处理,要注意用同一把锁
//            sendTransfer(violateOrder);
//
//        } else {
//            // 创建钱包收款单
//            ViolatePayRecord payRecord = violatePayRecordDomainService.deduction(violateOrder, ViolateChannelTypeEnum.WALLET.getValue());
//            violatePayRecordMessage.setViolateReceiptPayRecordId(payRecord.getRecordId());
//            // 钱包收款MQ
//            violateMessageService.sendDealDeduction(violatePayRecordMessage);
//            // 延迟队列 24h收款中断
//            violateMessageService.sendDealReceiptStop(violatePayRecordMessage, VIOLATE_RECEIPT_STOP);
//
//        }
//        log.info("[violateStart] 违约单开始处理,违约单id:{},时间:{}", violateOrder.getViolateId(), LocalDateTime.now());
//        return true;
//    }

    @Transactional(rollbackFor = Throwable.class, timeout = 5)
    public ViolateOrder violateCreateAndDeal(ViolateCreateReqBO po) {
        // create
        ViolateOrder violateOrder = violateOrderDomainService.create(po);

        // deal
        Assert.isTrue(violateStart(violateOrder, false), ErrorCode.VIOLATE_HAND_DEAL_ERROR.getErrDesc());

        return violateOrder;
    }

    /**
     * 处理违约单:
     *
     * @param violateOrder     违约单信息
     * @param buyerSkipReceipt 是否跳过收款
     */
    public boolean violateStart(ViolateOrder violateOrder, boolean buyerSkipReceipt) {
        String violateId = violateOrder.getViolateId();
        String orderItemId = violateOrder.getOrderItemId();

        log.info("[违约单处理-开始] violateId:{}, orderItemId:{}, buyerSkipReceipt:{}, violateUserType:{}",
                violateId, orderItemId, buyerSkipReceipt, violateOrder.getViolateUserType());

        // 更新违约单状态
        Assert.isTrue(violateOrderDomainService.violateDeal(violateOrder, buyerSkipReceipt), ErrorCode.VIOLATE_CREATE_ERROR.getErrDesc());

        // 订单记录操作日志
        orderOperateDomainService.addViolateOperate(
                orderItemId,
                violateOrder,
                OrderOperateTypeEnum.ORDER_VIOLATE_CREATE,
                OrderOptUserTypeEnum.SERVICE,
                violateOrder.getCreateUserId(),
                violateOrder.getCreateUsername()
        );

        // 构建MQ消息
        ViolatePayRecordMessage violatePayRecordMessage = buildViolatePayRecordMessage(violateOrder);

        // 根据违约方类型处理收付款
        if (ViolateUserTypeEnum.BUYER.getValue().equals(violateOrder.getViolateUserType()) && buyerSkipReceipt) {
            // 买家违约&&不需要收款 - 打款处理
            handleBuyerViolate(violateOrder);
        } else {
            // 需要收款,更新订单收款状态:待收款
            Assert.isTrue(orderItemRepository.updateReceiptVoucherStatus(orderItemId, ReceiptVoucherStatusEnum.PENDING.getValue()), ErrorCode.VIOLATE_RECEIPT_UPDATE_ORDER_ERROR.getErrDesc());

            // 卖家违约 || 买家违约需要收款 - 收款处理
            handleSellerViolate(violateOrder, violatePayRecordMessage);
        }

        log.info("[违约单处理-成功] violateId:{}, orderItemId:{}", violateId, orderItemId);
        return true;

    }

    /**
     * 构建违约支付记录消息
     */
    private ViolatePayRecordMessage buildViolatePayRecordMessage(ViolateOrder violateOrder) {
        return new ViolatePayRecordMessage()
                .setViolateId(violateOrder.getViolateId())
                .setCreateUserId(violateOrder.getCreateUserId())
                .setCreateUserName(violateOrder.getCreateUsername());
    }

    /**
     * 处理买家违约 - 打款流程
     */
    private void handleBuyerViolate(ViolateOrder violateOrder) {
        log.info("[买家违约处理-发起打款开始] violateId:{}", violateOrder.getViolateId());
        sendTransfer(violateOrder);
        log.info("[买家违约处理-发起打款完成] violateId:{}", violateOrder.getViolateId());
    }

    /**
     * 处理买/卖家违约 - 收款流程
     */
    private void handleSellerViolate(ViolateOrder violateOrder, ViolatePayRecordMessage message) {
        log.info("[违约处理-发起收款开始] violateId:{}", violateOrder.getViolateId());

        // 1. 创建钱包收款单
        ViolatePayRecord payRecord = violatePayRecordDomainService.deduction(
                violateOrder,
                ViolateChannelTypeEnum.WALLET.getValue()
        );
        message.setViolateReceiptPayRecordId(payRecord.getRecordId());

        // 2. 发送钱包收款MQ
        violateMessageService.sendDealDeduction(message);

        // 3. 发送延迟队列,24h收款中断
        violateMessageService.sendDealReceiptStop(message, VIOLATE_RECEIPT_STOP);

        log.info("[违约处理-发起收款完成] violateId:{}, payRecordId:{}", violateOrder.getViolateId(), payRecord.getRecordId());
    }

    @Transactional(rollbackFor = Throwable.class, timeout = 5)
    public boolean violateUpdate(ViolateOrder oldViolateOrder, ViolateOrder newViolateOrder) {
        // 更新违约单数据
        violateOrderDomainService.violateEdit(newViolateOrder);

        // 更新订单的收款单状态
        Assert.isTrue(orderItemRepository.updateReceiptVoucherStatus(newViolateOrder.getOrderItemId(), ReceiptVoucherStatusEnum.PENDING.getValue())
                , ErrorCode.VIOLATE_EDIT_ERROR.getErrDesc());

        // 记录操作日志
        orderOperateDomainService.addViolateOperate(
                oldViolateOrder.getOrderItemId()
                , newViolateOrder
                , OrderOperateTypeEnum.ORDER_VIOLATE_EDIT
                , OrderOptUserTypeEnum.SERVICE
                , newViolateOrder.getUpdateUserId()
                , newViolateOrder.getUpdateUsername()
        );

        // 直接收款
        ViolatePayRecordMessage violatePayRecordMessage = new ViolatePayRecordMessage()
                .setViolateId(newViolateOrder.getViolateId())
                .setCreateUserId(newViolateOrder.getUpdateUserId())
                .setCreateUserName(newViolateOrder.getUpdateUsername());
        violateMessageService.sendDealReceipt(violatePayRecordMessage);

        return true;

    }

    @Transactional(rollbackFor = Exception.class, timeout = 5)
    public void transferDeal(ViolateOrder violateOrder) {
        // 更新打款状态:待打款->打款中
        violateOrderDomainService.violateTransferStart(violateOrder.getViolateId());

        // 创建违约金打款单
        ViolatePayRecord payRecord = violatePayRecordDomainService.transfer(violateOrder);

        // 发送打款MQ---钱包
//        PayAccountTradeMessage accountMessage = new PayAccountTradeMessage();
//        accountMessage.setVoucherId(payRecord.getRecordId());
//        accountMessage.setOrderId(payRecord.getOrderItemId());
//        accountMessage.setUserId(payRecord.getUserId());
//        accountMessage.setAmount(payRecord.getAmount());
//        accountMessage.setTradeType(AccountTradeTypeEnum.WYJ_TRANSFER.code);
//        violateMessageService.sendTransferToWallet(accountMessage);

        WalletPayInboundBO walletPayInboundBO = WalletPayInboundBO.builder()
                .voucherId(payRecord.getRecordId())
                .orderId(payRecord.getOrderItemId())
                .userId(payRecord.getUserId())
                .amount(payRecord.getAmount())
                .accountTradeTypeEnum(AccountTradeTypeEnum.WYJ_TRANSFER)
                .build();
        walletPayDomainService.walletPayInBound(walletPayInboundBO);
    }

    // 收款成功
    @Transactional(rollbackFor = Exception.class, timeout = 5)
    public boolean receiptSuccess(ViolatePayRecord payRecord, ViolateOrder violateOrder, String outTradeNo) {
        boolean transferFlag = violateOrder.getPromiseAmount() > 0;
        // 违约单收款成功
        violateOrderDomainService.violateDeductionSuccess(payRecord.getViolateId(), !transferFlag);
        // 收款支付单处理成功
        violatePayRecordDomainService.paid(payRecord.getRecordId(), outTradeNo);
        // 订单收款状态:收款成功
        orderItemRepository.updateReceiptVoucherStatus(payRecord.getOrderItemId(), ReceiptVoucherStatusEnum.SUCCESS.getValue());
        // 收款成功日志
        orderOperateDomainService.addViolateOperate(
                violateOrder.getOrderItemId()
                , violateOrder
                , OrderOperateTypeEnum.ORDER_VIOLATE_DEDUCTION
                , OrderOptUserTypeEnum.SYSTEM
                , null
                , OrderOptUserTypeEnum.SYSTEM.getLabel()
        );

        // 扣款成功,发起打款MQ
        sendTransfer(violateOrder);
        return true;
    }

    @Transactional(rollbackFor = Throwable.class, timeout = 5)
    public void receiptStatusStop(ViolateOrder violateOrder) {
        violateOrderDomainService.violateReceiptStop(violateOrder.getViolateId());

        // 记录操作日志
        orderOperateDomainService.addViolateOperate(
                violateOrder.getOrderItemId()
                , violateOrder
                , OrderOperateTypeEnum.ORDER_VIOLATE_RECEIPT_STOP
                , OrderOptUserTypeEnum.SYSTEM
                , null
                , OrderOptUserTypeEnum.SYSTEM.getLabel()
        );

        // 拉黑 账号
        violateMessageService.sendViolateRiskBlack(violateOrder.getViolateId(), RiskBlackOperateType.ADD.getCode());
    }


    public void addViolateBlackRecord(String violateId) {

        // 通过 violateId  获取 违约单信息
        ViolateOrderBO violateOrderBO = violateOrderDomainService.getByViolateId(violateId);
        if (null == violateOrderBO) {
            return;
        }
        ViolateUserTypeEnum violateUserTypeEnum = ViolateUserTypeEnum.getEnum(violateOrderBO.getViolateUserType());
        if (null == violateUserTypeEnum) {
            return;
        }

        // 通过违约单中的 orderItemId 获取 订单明细信息 及拓展信息
        OrderItemExtendBO orderItemExtendBO = orderItemDomainService.getOrderItemExtend(violateOrderBO.getOrderItemId());
        if (null == orderItemExtendBO) {
            return;
        }

        RiskAddBlackRecordPO riskAddBlackRecordPO = new RiskAddBlackRecordPO();
        riskAddBlackRecordPO.setGameAccount(orderItemExtendBO.getGameAccount());

        if (ViolateUserTypeEnum.BUYER.eq(violateOrderBO.getViolateUserType())) {
            riskAddBlackRecordPO.setPhone(orderItemExtendBO.getBuyerPhone());
        } else if (ViolateUserTypeEnum.SELLER.eq(violateOrderBO.getViolateUserType())) {
            riskAddBlackRecordPO.setPhone(orderItemExtendBO.getSellerPhone());
        }
        UserCertInfoRespPO userCertInfo = userGatewayRepository.getUserCertInfo(violateOrderBO.getViolateUserId());
        riskAddBlackRecordPO.setCertNo(Optional.ofNullable(userCertInfo).map(UserCertInfoRespPO::getCertId).orElse(null));
        riskAddBlackRecordPO.setVoucherId(violateOrderBO.getViolateId());
        riskBlackGatewayRepository.addBlackRecord(riskAddBlackRecordPO);
    }

    public void removeViolateBlackRecord(String violateId) {

        RiskRemoveBlackRecordPO riskRemoveBlackRecordPO = new RiskRemoveBlackRecordPO();
        riskRemoveBlackRecordPO.setVoucherId(violateId);
        riskBlackGatewayRepository.removeBlackRecord(riskRemoveBlackRecordPO);
    }

    /**
     * 扣款:
     * 成功-结束
     * 失败-取消单据,创建新单据,发起收款卡片
     * 打结果日志
     */
    public ViolateWalletDeductionRespBO deductionDubboToWallet(ViolatePayRecord payRecord) {
        // 结果
        ViolateWalletDeductionRespBO respBO = new ViolateWalletDeductionRespBO();

        // 1.查询
        SingleResponse<AccountTradeInfoRespDTO> queryResp = payServiceGateway.queryWalletDeduction(payRecord.getRecordId(), payRecord.getUserId());

        // getData 无数据则表示未扣过款,进入2
        if (queryResp.isSuccess() && Objects.nonNull(queryResp.getData())) {
            AccountTradeInfoRespDTO queryTradeRes = queryResp.getData();
            // 处理中,等待重试
            Assert.isTrue(Objects.equals(queryTradeRes.getStatus(), AccountTradeStatusEnum.PROCESSING.getCode()), VIOLATE_WALLET_WAIT_ERROR.getErrDesc());

            // 查询扣款成功,返回成功
            if (Objects.equals(queryTradeRes.getStatus(), AccountTradeStatusEnum.SUCCESS.getCode())) {
                respBO.setStatus(true).setOutTradeId(queryTradeRes.getTradeId());

            } else if (Objects.equals(queryTradeRes.getStatus(), AccountTradeStatusEnum.FAIL.getCode())) {
                respBO.setStatus(false).setOutTradeId(queryTradeRes.getTradeId());
            }

            // 查询有结果,直接返回
            return respBO;
        }

        // 2.去扣款
        try {

            WalletToPayBO walletToPayBO = WalletToPayBO.builder().userId(payRecord.getUserId())
                    .voucherId(payRecord.getRecordId())
                    .amount(payRecord.getAmount())
                    .orderId(payRecord.getOrderItemId())
                    .accountTradeTypeEnum(AccountTradeTypeEnum.WYJ_DEDUCTION)
                    .build();

            PayAccountTradeResp tradeRes = walletPayDomainService.toPayWallet(walletToPayBO);

//            PayAccountTradeResp tradeRes = payServiceGateway.walletDeduction(
//                    payRecord.getOrderItemId()
//                    , payRecord.getRecordId()
//                    , payRecord.getUserId()
//                    , payRecord.getAmount()
//                    , AccountTradeTypeEnum.WYJ_DEDUCTION.getCode());
            // 扣款处理中,等待重试
            Assert.isFalse(Objects.equals(AccountTradeStatusEnum.PROCESSING.getCode(), tradeRes.getTradeStatus()), VIOLATE_WALLET_WAIT_ERROR.getErrDesc());

            if (Objects.equals(tradeRes.getTradeStatus(), AccountTradeStatusEnum.SUCCESS.getCode())) {
                // 扣款成功
                respBO.setStatus(true).setOutTradeId(tradeRes.getTradeId());
            } else if (Objects.equals(tradeRes.getTradeStatus(), AccountTradeStatusEnum.FAIL.getCode())) {
                // 扣款失败
                respBO.setStatus(false).setOutTradeId(tradeRes.getTradeId());
            }

            // 扣款结果返回
            return respBO;
        } catch (BizException e) {
            // 重试:dubboAssertCode && 钱包errorCode=500 ; 其他异常 : 钱包扣款失败
            Set<String> waitCodes = Set.of(PAY_GATEWAY_WALLET_TRADE_ERROR.getErrCode(), "500");
            Assert.isFalse(waitCodes.contains(e.getErrCode()), VIOLATE_WALLET_TRADE_REQ_ERROR.getErrDesc());

            respBO.setStatus(false);

            // 扣款结果返回
            return respBO;
        }
    }

    @Transactional(rollbackFor = Throwable.class, timeout = 5)
    public void walletConvertOnline(ViolateOrder violateOrder, ViolatePayRecord payRecord) {
        // 钱包扣款失败:取消当前钱包扣款支付单
        violatePayRecordDomainService.cancelPayRecord(payRecord.getRecordId(), "钱包扣款失败");
        // 转在线收款MQ
        ViolatePayRecordMessage violatePayRecordMessage = new ViolatePayRecordMessage()
                .setOrderItemId(violateOrder.getOrderItemId())
                .setViolateId(violateOrder.getViolateId())
                .setCreateUserId(violateOrder.getCreateUserId())
                .setCreateUserName(violateOrder.getCreateUsername());
        violateMessageService.sendDealReceipt(violatePayRecordMessage);
        log.info("[钱包扣款失败转在线收款],违约单ID:{}", violateOrder.getViolateId());
    }

    // 打款成功
    @Transactional(rollbackFor = Throwable.class, timeout = 5)
    public void transferSuccessUpdate(ViolateOrder violateOrder, String payRecordId, String outTradeNo) {
        violateOrderDomainService.violatetransferSuccess(violateOrder.getViolateId());
        violatePayRecordDomainService.paid(payRecordId, outTradeNo);
        // 记录操作日志
        orderOperateDomainService.addViolateOperate(
                violateOrder.getOrderItemId()
                , violateOrder
                , OrderOperateTypeEnum.ORDER_VIOLATE_TRANSFER
                , OrderOptUserTypeEnum.SYSTEM
                , null
                , OrderOptUserTypeEnum.SYSTEM.getLabel()
        );
    }

    private void sendTransfer(ViolateOrder violateOrder) {
        // 扣款成功,发起打款MQ
        if (violateOrder.getPromiseAmount() > 0) {
            ViolatePayRecordMessage violatePayRecordMessage = new ViolatePayRecordMessage()
                    .setViolateId(violateOrder.getViolateId());
            violateMessageService.sendDealTransfer(violatePayRecordMessage);
        } else {
            // 无守约金,直接打款成功
            orderOperateDomainService.addViolateOperate(
                    violateOrder.getOrderItemId()
                    , violateOrder
                    , OrderOperateTypeEnum.ORDER_VIOLATE_COMPLETED
                    , OrderOptUserTypeEnum.SYSTEM
                    , null
                    , OrderOptUserTypeEnum.SYSTEM.getLabel()
            );
        }
    }
}
