package com.pxb7.mall.trade.ass.domain.model.response;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024/8/13
 */
@Data
@AllArgsConstructor
public class AssScheduleLogRespBO {

    /**
     * 节点id
     */
    private String nodeId;

    /**
     * 当前节点状态描述
     */
    private String nodeDesc;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建用户编号
     */
    private String createUserId;

    /**
     * 其他数据
     */
    private String data;
}
