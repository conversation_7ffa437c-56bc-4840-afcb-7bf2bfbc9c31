package com.pxb7.mall.trade.ass.domain.refund;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.cola.exception.Assert;
import com.alibaba.cola.exception.BizException;
import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Lists;
import com.pxb7.mall.trade.ass.client.dto.model.order.OrderItemAmountInfo;
import com.pxb7.mall.trade.ass.domain.mapping.OrderRefundDomainMapping;
import com.pxb7.mall.trade.ass.domain.model.OrderItemAmountInfoBO;
import com.pxb7.mall.trade.ass.domain.refund.mapping.RefundDomainMapping;
import com.pxb7.mall.trade.ass.domain.refund.message.provider.RefundIndemnityChangeMessageService;
import com.pxb7.mall.trade.ass.domain.refund.model.RefundOrderItemPageRespBO;
import com.pxb7.mall.trade.ass.domain.refund.model.ServiceSubmitRefundBO;
import com.pxb7.mall.trade.ass.domain.refund.utils.RefundAmountCalculator;
import com.pxb7.mall.trade.ass.domain.violate.ViolateOrderDomainService;
import com.pxb7.mall.trade.ass.domain.violate.model.ViolateOrderBO;
import com.pxb7.mall.trade.ass.infra.enums.*;
import com.pxb7.mall.trade.ass.infra.model.RefundFeeDetailPO;
import com.pxb7.mall.trade.ass.infra.model.RefundIndemnityDetailPO;
import com.pxb7.mall.trade.ass.infra.model.mq.IndemnityChangeTradeFinishedMessage;
import com.pxb7.mall.trade.ass.infra.remote.model.refund.OrderItemReceiptVoucherPO;
import com.pxb7.mall.trade.ass.infra.repository.db.*;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.*;
import com.pxb7.mall.trade.ass.infra.repository.gateway.order.OrderChangeAmountGatewayRepository;
import com.pxb7.mall.trade.ass.infra.util.IdGenUtil;
import com.pxb7.mall.trade.ass.infra.util.StringUtil;
import com.pxb7.mall.trade.order.client.dto.response.order.dubbo.OrderInfoDubboRespDTO;
import com.pxb7.mall.trade.order.client.enums.extPayment.ExtVoucherStatusEnum;
import com.pxb7.mall.trade.order.client.enums.order.*;
import com.pxb7.mall.trade.order.client.enums.pay.VoucherTypeEnum;
import jakarta.annotation.Resource;
import java.util.HashSet;
import lombok.extern.slf4j.Slf4j;
import org.apache.curator.shaded.com.google.common.collect.Sets;
import org.apache.dubbo.common.utils.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class RefundDomainService {
    @Resource
    private RefundVoucherRepository refundVoucherRepository;
    @Resource
    private RefundVoucherDetailRepository refundVoucherDetailRepository;
    @Resource
    private ReceiptVoucherRepository receiptVoucherRepository;
    @Resource
    private ReceiptVoucherDetailRepository receiptVoucherDetailRepository;
    @Resource
    private OrderItemRepository orderItemRepository;
    @Resource
    private OrderItemFeeRepository orderItemFeeRepository;
    @Resource
    private OrderOperateRepository orderOperateRepository;
    @Resource
    private ViolateOrderDomainService violateOrderDomainService;
    @Resource
    private RefundIndemnityChangeMessageService refundIndemnityChangeMessageService;
    @Resource
    private RefundReasonRepository refundReasonRepository;
    @Resource
    private OrderChangeAmountGatewayRepository orderChangeAmountGatewayRepository;


    @Transactional(rollbackFor = Exception.class)
    public RefundVoucher saveUserRefundApply(Integer productType, String orderItemId,
                                             String refundReasonId, String refundReason,
                                             Integer refundType, OrderItemAmountInfoBO refundAmountInfo,
                                             String userId) {
        log.info("保存退款单，orderItemId = {}，refundReasonId = {}，refundReason = {}", orderItemId, refundReasonId, refundReason);
        // 更新orderItem退款状态
        boolean suc = orderItemRepository.startRefund(orderItemId);
        Assert.isTrue(suc, ErrorCode.REFUND_DB_SAVE_ERROR.getErrCode(), ErrorCode.REFUND_DB_SAVE_ERROR.getErrDesc());
        // 保存退款申请数据
        return insertRefundSubmit(productType, orderItemId, refundReasonId, refundReason,
                RefundWholeEnum.WHOLE.getValue(), refundType, refundAmountInfo, RefundActionTypeEnum.USER.getValue(),
                RefundAuditStatusEnum.WAIT_SERVICE_AUDIT.getValue(), userId, RefundStatusEnum.WAIT_APPLY.getValue());
    }

    /**
     * 诚心卖保存退款单
     */
    @Transactional(rollbackFor = Exception.class)
    public RefundVoucher saveSinceritySellRefundApply(String orderItemId, Integer productType, Integer refundWhole,
                                                      Long refundAmount, Integer refundActionType, String userId, String buyerId, String extInfo) {
        // 更新orderItem退款状态
        // boolean suc = orderItemRepository.updateRefundStatusToRefunding(orderItemId);
        boolean suc = orderItemRepository.sinceritySellStartRefund(orderItemId);
        Assert.isTrue(suc, ErrorCode.REFUND_DB_UPDATE_ERROR.getErrCode(),
                ErrorCode.REFUND_DB_UPDATE_ERROR.getErrDesc());
        // 保存退款申请数据
        return insertSinceritySellRefundSubmit(productType, orderItemId, refundWhole,
                RefundTypeEnum.ONLINE_REFUND.getValue(), refundAmount, refundActionType,
                RefundAuditStatusEnum.PASS.getValue(), buyerId, userId, extInfo);
    }

    @Transactional(rollbackFor = Exception.class)
    public String saveServiceRefundSubmit(ServiceSubmitRefundBO req,
                                          OrderItemAmountInfoBO refundAmountInfo, Integer nextAuditStatus, String serviceId) {

        // 是否为卖家发起的包赔变更
        boolean sellerIndemnityChange = RefundWholeEnum.INDEMNITY.getValue().equals(req.getWholeRefund())
                && ResponsibleEnum.SELLER.eq(req.getResponseUser());

        // 更新orderItem退款状态
        if (!sellerIndemnityChange) {
            boolean suc = orderItemRepository.startRefund(req.getOrderItemId());
            Assert.isTrue(suc, ErrorCode.REFUND_DB_SAVE_ERROR.getErrCode(), ErrorCode.REFUND_DB_SAVE_ERROR.getErrDesc());
        }

        // 初始状态
        Integer initRefundStatus = sellerIndemnityChange ? RefundStatusEnum.SUCCESS.getValue() : RefundStatusEnum.WAIT_APPLY.getValue();

        RefundVoucher refundVoucher = insertRefundSubmit(ProductTypeEnum.ACCOUNT.getValue(),
                req.getOrderItemId(), req.getRefundReasonId(), "",
                req.getWholeRefund(), req.getRefundType(), refundAmountInfo,
                RefundActionTypeEnum.SERVICE.getValue(), nextAuditStatus, serviceId, initRefundStatus);
        return refundVoucher.getRefundVoucherId();
    }

    @Transactional(rollbackFor = Exception.class)
    public void userCancelRefund(String refundVoucherId, String orderItemId, RefundActionTypeEnum cancelRole, Integer refundWholeType) {
        // 更新orderItem退款状态
        boolean suc = orderItemRepository.updateRefundStatus(orderItemId, RefundStatusEnum.WAIT_APPLY.getValue(),
                RefundStatusEnum.CLOSE.getValue());
        Assert.isTrue(suc, ErrorCode.REFUND_DB_SAVE_ERROR.getErrCode(), ErrorCode.REFUND_DB_SAVE_ERROR.getErrDesc());

        // 买家取消退款，关闭退款
        RefundVoucher toUpdate =
                new RefundVoucher().setRefundVoucherId(refundVoucherId).setRefundStatus(RefundStatusEnum.CLOSE.getValue())
                        .setAuditStatus(RefundAuditStatusEnum.REJECT.getValue()).setCloseType(cancelRole.getValue())
                        .setRejectReason(cancelRole.getLabel() + "取消").setFinishTime(LocalDateTime.now());
        boolean refundVoucherSuc = refundVoucherRepository.updateByRefundVoucherId(toUpdate);
        Assert.isTrue(refundVoucherSuc, ErrorCode.REFUND_DB_SAVE_ERROR.getErrCode(),
                ErrorCode.REFUND_DB_SAVE_ERROR.getErrDesc());

        // 退款取消后置业务操作
        refundCancelToBusiness(refundVoucherId, refundWholeType);
    }

    public RefundVoucher insertRefundSubmit(Integer productType, String orderItemId,
                                            String refundReasonId, String refundReason,
                                            Integer isWholeRefund, Integer refundType,
                                            OrderItemAmountInfoBO amountInfo, Integer submitterType,
                                            Integer refundAuditStatus, String submitter, Integer initRefundStatus) {
        // 保存退款单
        RefundVoucher RefundVoucher = insertOrderRefund(productType, orderItemId, refundReasonId, refundReason,
                isWholeRefund, refundType, amountInfo.getTotalAmount(), submitterType, refundAuditStatus, submitter, initRefundStatus);
        // 保存退款单明细
        insertRefundVoucherDetail(RefundVoucher, amountInfo);
        return RefundVoucher;
    }

    /**
     * 生成诚心卖退款单保存入库
     */
    public RefundVoucher insertSinceritySellRefundSubmit(Integer productType, String orderItemId, Integer isWholeRefund,
                                                         Integer refundType, Long refundAmount, Integer submitterType, Integer refundAuditStatus, String buyerId,
                                                         String submitter, String extInfo) {
        // 生成初始退款单
        RefundVoucher refundVoucher = new RefundVoucher()
                .setRefundVoucherId(IdGenUtil.getTKOrderId(buyerId))
                .setOrderItemId(orderItemId).setProductType(productType)
                .setRefundStatus(RefundStatusEnum.WAIT_APPLY.getValue()).setAuditStatus(refundAuditStatus)
                .setWholeRefund(isWholeRefund).setRefundType(refundType).setRefundAmount(refundAmount)
                .setSubmitter(submitter).setSubmitterType(submitterType).setApplyTime(LocalDateTime.now())
                .setCreateUserId(submitter).setUpdateUserId(submitter).setExtInfo(extInfo);

        boolean success = refundVoucherRepository.save(refundVoucher);
        Assert.isTrue(success, ErrorCode.REFUND_DB_SAVE_ERROR.getErrCode(),
                ErrorCode.REFUND_DB_SAVE_ERROR.getErrDesc());
        return refundVoucher;
    }

    private void insertRefundVoucherDetail(RefundVoucher RefundVoucher, OrderItemAmountInfoBO amountInfo) {

        // 包赔默认买家承担
        Integer indemnityResponseUser = null == amountInfo.getIndemnityResponseUser() ? ResponsibleEnum.BUYER.getValue() : amountInfo.getIndemnityResponseUser();

        // 生成初始退款单
        RefundVoucherDetail RefundVoucherDetail = new RefundVoucherDetail()
                .setRefundVoucherId(RefundVoucher.getRefundVoucherId())
                .setOrderItemId(RefundVoucher.getOrderItemId())
                .setProductAmount(amountInfo.getProductAmount())
                .setIndemnityAmount(amountInfo.getIndemnityAmount())
                .setFeeAmount(amountInfo.getFeeAmount())
                .setRedPacketAmount(amountInfo.getRedPacketAmount())
                .setIndemnityResponsibleUser(indemnityResponseUser)
                .setViolateAmount(amountInfo.getBuyerViolateAmount())
                .setFeeResponsibleUser(amountInfo.getFeeResponseUser())
                .setExtraPram(CollectionUtils.isEmpty(amountInfo.getRefundIndemnityDetails()) ?
                        "" : JSON.toJSONString(amountInfo.getRefundIndemnityDetails()));

        boolean success = refundVoucherDetailRepository.save(RefundVoucherDetail);
        Assert.isTrue(success, ErrorCode.REFUND_DB_SAVE_ERROR.getErrCode(),
                ErrorCode.REFUND_DB_SAVE_ERROR.getErrDesc());
    }

    private RefundVoucher insertOrderRefund(Integer productType, String orderItemId, String refundReasonId,
                                            String refundReason, Integer isWholeRefund, Integer refundType, Long totalAmount, Integer submitterType,
                                            Integer refundAuditStatus, String submitter, Integer initRefundStatus) {
        // 生成初始退款单
        RefundVoucher refundVoucher = new RefundVoucher()
                .setRefundVoucherId(IdGenUtil.getTKOrderId(orderItemId))
                .setOrderItemId(orderItemId)
                .setProductType(productType)
                .setRefundStatus(initRefundStatus)
                .setAuditStatus(refundAuditStatus)
                .setRefundReasonId(refundReasonId)
                .setWholeRefund(isWholeRefund)
                .setRefundType(refundType)
                .setRefundAmount(totalAmount)
                .setSubmitter(submitter)
                .setSubmitterType(submitterType)
                .setApplyTime(LocalDateTime.now())
                .setCreateUserId(submitter)
                .setUpdateUserId(submitter);
        if (StringUtil.isBlank(refundReason) && StringUtil.isNotBlank(refundReasonId)) {
            //获取 退款原因content
            refundReason = refundReasonRepository.getReasonContent(refundReasonId);
        }
        refundVoucher.setRefundReason(refundReason);
        boolean success = refundVoucherRepository.save(refundVoucher);
        Assert.isTrue(success, ErrorCode.REFUND_DB_SAVE_ERROR.getErrCode(), ErrorCode.REFUND_DB_SAVE_ERROR.getErrDesc());
        return refundVoucher;
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateRefundSubmit(String refundId, String refundReasonId, Integer isWholeRefund, Integer refundType,
                                   OrderItemAmountInfoBO amountInfo, Integer refundAuditStatus, String serviceId, Integer fromAuditStatus) {
        updateRefundVoucher(refundId, refundReasonId, isWholeRefund, refundType,
                amountInfo != null ? amountInfo.getTotalAmount() : null, refundAuditStatus, serviceId, fromAuditStatus);
        if (amountInfo != null) {
            updateRefundVoucherDetail(refundId, amountInfo);
        }
    }

    private void updateRefundVoucherDetail(String refundId, OrderItemAmountInfoBO amountInfo) {
        RefundVoucherDetail toUpdate = new RefundVoucherDetail().setRefundVoucherId(refundId)
                .setProductAmount(amountInfo.getProductAmount()).setIndemnityAmount(amountInfo.getIndemnityAmount())
                .setFeeAmount(amountInfo.getFeeAmount()).setRedPacketAmount(amountInfo.getRedPacketAmount())
                .setViolateAmount(amountInfo.getBuyerViolateAmount());
        boolean success = refundVoucherDetailRepository.updateByRefundId(toUpdate);
        Assert.isTrue(success, ErrorCode.REFUND_DB_SAVE_ERROR.getErrCode(),
                ErrorCode.REFUND_DB_SAVE_ERROR.getErrDesc());
    }

    private void updateRefundVoucher(String refundId, String refundReasonId, Integer isWholeRefund, Integer refundType,
                                     Long totalAmount, Integer refundAuditStatus, String serviceId, Integer fromAuditStatus) {
        //获取 退款原因content
        RefundVoucher toUpdate = new RefundVoucher()
                .setRefundVoucherId(refundId).setAuditStatus(refundAuditStatus)
                .setWholeRefund(isWholeRefund).setRefundType(refundType)
                .setRefundAmount(totalAmount).setApplyTime(LocalDateTime.now()).setSubmitter(serviceId)
                .setUpdateUserId(serviceId);
        if (StringUtil.isNotBlank(refundReasonId)) {
            String reasonContent = refundReasonRepository.getReasonContentIncludeDeleted(refundReasonId);
            toUpdate.setRefundReasonId(refundReasonId).setRefundReason(reasonContent);
        }
        boolean success = refundVoucherRepository.updateByRefundId(toUpdate, fromAuditStatus);
        Assert.isTrue(success, ErrorCode.REFUND_DB_SAVE_ERROR.getErrCode(),
                ErrorCode.REFUND_DB_SAVE_ERROR.getErrDesc());
    }

    /**
     * 校验是否订单行状态是交易中.且不存在进行中的收款单、退款单、放款单
     */
    public void checkOrderItem(OrderItem orderItem) {
        if (orderItem == null) {
            throw new BizException(ErrorCode.ORDER_STATUS_FAIL.getErrCode(), ErrorCode.ORDER_STATUS_FAIL.getErrDesc());
        }

        if (ObjectUtil.notEqual(OrderItemStatusEnum.DEALING.getValue(), orderItem.getOrderItemStatus())) {
            throw new BizException(ErrorCode.ORDER_STATUS_FAIL.getErrCode(), ErrorCode.ORDER_STATUS_FAIL.getErrDesc());
        }

        if (ObjectUtil.equal(RefundStatusEnum.WAIT_APPLY.getValue(), orderItem.getRefundStatus())
                || ObjectUtil.equal(RefundStatusEnum.APPLYING.getValue(), orderItem.getRefundStatus())) {
            throw new BizException(ErrorCode.ORDER_HAVE_OTHER_REFUND_RECEIPT_FAIL.getErrCode(),
                    ErrorCode.ORDER_HAVE_OTHER_REFUND_RECEIPT_FAIL.getErrDesc());
        }

        if (ObjectUtil.equal(ExtVoucherStatusEnum.CREATED.getValue(), orderItem.getPayoutStatus())
                || ObjectUtil.equal(ExtVoucherStatusEnum.PROCESSING.getValue(), orderItem.getPayoutStatus())) {
            throw new BizException(ErrorCode.ORDER_HAVE_OTHER_EXT_PAYMENT_FAIL.getErrCode(),
                    ErrorCode.ORDER_HAVE_OTHER_EXT_PAYMENT_FAIL.getErrDesc());
        }

        if (ObjectUtil.equal(ReceiptVoucherStatusEnum.PENDING.getValue(), orderItem.getReceiptStatus())
                || ObjectUtil.equal(ReceiptVoucherStatusEnum.IN_PROGRESS.getValue(), orderItem.getReceiptStatus())) {
            throw new BizException(ErrorCode.ORDER_HAVE_OTHER_COLLECTION_RECEIPT_FAIL.getErrCode(),
                    ErrorCode.ORDER_HAVE_OTHER_COLLECTION_RECEIPT_FAIL.getErrDesc());
        }

    }

    /**
     * 诚心卖退款校验
     *
     * @param orderItem
     */
    public void checkSinceritySellOrderItem(OrderItem orderItem) {
        if (orderItem == null) {
            throw new BizException(ErrorCode.ORDER_STATUS_FAIL.getErrCode(), ErrorCode.ORDER_STATUS_FAIL.getErrDesc());
        }

        if (ObjectUtil.notEqual(OrderItemStatusEnum.DEALING.getValue(), orderItem.getOrderItemStatus())
                && ObjectUtil.notEqual(OrderItemStatusEnum.DEAL_SUCCESS.getValue(), orderItem.getOrderItemStatus())) {
            throw new BizException(ErrorCode.ORDER_STATUS_FAIL.getErrCode(), ErrorCode.ORDER_STATUS_FAIL.getErrDesc());
        }

        if (ObjectUtil.equal(RefundStatusEnum.WAIT_APPLY.getValue(), orderItem.getRefundStatus())
                || ObjectUtil.equal(RefundStatusEnum.APPLYING.getValue(), orderItem.getRefundStatus())) {
            throw new BizException(ErrorCode.ORDER_HAVE_OTHER_REFUND_RECEIPT_FAIL.getErrCode(),
                    ErrorCode.ORDER_HAVE_OTHER_REFUND_RECEIPT_FAIL.getErrDesc());
        }
    }

    /**
     * 获取整单退款金额明细
     */
    public OrderItemAmountInfoBO getWholeRefundAmountInfo(String orderItemId, List<RefundVoucher> successRefundVoucher,
                                                        List<OrderItemReceiptVoucherPO> successReceiptVoucherDetail) {
        return getRefundAmountInfo(orderItemId, successRefundVoucher, successReceiptVoucherDetail,
                RefundWholeEnum.WHOLE.getValue(), null, RefundCalculateTypeEnum.BY_PRODUCT_AMOUNT.getValue());
    }

    /**
     * 根据成功退款单和成功收款单计算退款金额信息 在计算过程中，如果遇到无效的退款类型，将抛出业务异常
     *
     * @param successRefundVoucher        成功的退款凭证列表
     * @param successReceiptVoucherDetail 成功的收款凭证详情列表
     * @param wholeRefund                 退款类型，表示是全额退款还是部分退款
     * @param applyRefundAmount           退款金额，当为部分退款时此参数有效
     * @param calculateType               退款计算方式，0 退商品差价, 1 退订单金额 ，当为部分退款时此参数有效
     * @return RefundAmountInfo 退款金额信息对象，包含计算后的退款金额详情
     * @throws BizException 如果退款类型非法，抛出业务异常
     */
    public OrderItemAmountInfoBO getRefundAmountInfo(String orderItemId, List<RefundVoucher> successRefundVoucher,
                                                   List<OrderItemReceiptVoucherPO> successReceiptVoucherDetail, Integer wholeRefund,
                                                   OrderItemAmountInfo applyRefundAmount, int calculateType) {

        if (CollectionUtils.isEmpty(successReceiptVoucherDetail)) {
            throw new BizException(ErrorCode.REFUND_AMOUNT_ERROR.getErrCode(),
                    ErrorCode.REFUND_AMOUNT_ERROR.getErrDesc());
        }
        Integer feeResponsibleUser = successReceiptVoucherDetail.get(0).getFeeResponsibleUser();

        OrderItemAmountInfo refundAmountInfo;
        OrderItemAmountInfoBO refundAmountInfoBO;
        OrderItemAmountInfo successRefundInfo = getSuccessRefundAmountInfo(successRefundVoucher);
        OrderItemAmountInfo wholeReceiptInfo =
                RefundAmountCalculator.getWholeReceiptAmount(successReceiptVoucherDetail);
        if (Objects.equals(wholeRefund, RefundWholeEnum.WHOLE.getValue())) {
            refundAmountInfo =
                    RefundAmountCalculator.getWholeRefundAmount(orderItemId, wholeReceiptInfo, successRefundInfo);
            refundAmountInfoBO = OrderRefundDomainMapping.INSTANCE.toOrderItemAmountInfoBO(refundAmountInfo);
        } else if (Objects.equals(wholeRefund, RefundWholeEnum.PART.getValue())
                || Objects.equals(wholeRefund, RefundWholeEnum.INDEMNITY.getValue())) {
            // 部分退和退包赔是一样的处理
            if (Objects.equals(RefundCalculateTypeEnum.BY_PRODUCT_AMOUNT.getValue(), calculateType)) {
                OrderItemFee orderItemFee = orderItemFeeRepository.getInfoByOrderItemId(orderItemId);
                refundAmountInfo = RefundAmountCalculator.getPartRefundAmount(orderItemId, wholeReceiptInfo,
                        successRefundInfo, applyRefundAmount.getProductAmount(), orderItemFee);
                refundAmountInfoBO = OrderRefundDomainMapping.INSTANCE.toOrderItemAmountInfoBO(refundAmountInfo);
            } else if (Objects.equals(RefundCalculateTypeEnum.BY_INPUT_AMOUNT.getValue(), calculateType)) {
                OrderItemAmountInfo wholeRefundInfo =
                        RefundAmountCalculator.getWholeRefundAmount(orderItemId, wholeReceiptInfo, successRefundInfo);
                applyRefundAmount
                        .setTotalAmount(applyRefundAmount.getProductAmount() + applyRefundAmount.getIndemnityAmount()
                                + applyRefundAmount.getFeeAmount() + applyRefundAmount.getRedPacketAmount());

                // 退包赔不校验
                if (!wholeRefund.equals(RefundWholeEnum.INDEMNITY.getValue())) {
                    RefundAmountCalculator.checkRefundAmount(orderItemId, applyRefundAmount, wholeRefundInfo);
                }

                refundAmountInfo = applyRefundAmount;
                refundAmountInfoBO = OrderRefundDomainMapping.INSTANCE.toOrderItemAmountInfoBO(refundAmountInfo);
                // 包赔变更退款 不用要处理
                if (!RefundWholeEnum.INDEMNITY.getValue().equals(wholeRefund)) {
                    long couponAmount = successReceiptVoucherDetail.stream()
                            .mapToLong(e -> e.getCouponAmount() == null ? 0 : e.getCouponAmount()).sum();
                    this.fulfillRefundIndemnityDetails(wholeRefundInfo, applyRefundAmount, refundAmountInfoBO, orderItemId, couponAmount);
                    this.fulfillRefundFeeAmount(wholeRefundInfo, applyRefundAmount, refundAmountInfoBO, orderItemId, couponAmount);
                }
            } else {
                throw new BizException(ErrorCode.REFUND_TYPE_VALID_FAIL_ERROR.getErrCode(),
                        ErrorCode.REFUND_TYPE_VALID_FAIL_ERROR.getErrDesc());
            }
        } else {
            throw new BizException(ErrorCode.REFUND_TYPE_VALID_FAIL_ERROR.getErrCode(),
                    ErrorCode.REFUND_TYPE_VALID_FAIL_ERROR.getErrDesc());
        }
        if (refundAmountInfoBO != null && refundAmountInfoBO.getFeeResponseUser() == null) {
            refundAmountInfoBO.setFeeResponseUser(feeResponsibleUser);
        }
        return refundAmountInfoBO;
    }

    /**
     * 填充退款时，随之变动的包赔金额
     */
    private void fulfillRefundIndemnityDetails(OrderItemAmountInfo wholeRefundInfo,
                                               OrderItemAmountInfo applyRefundAmount,
                                               OrderItemAmountInfoBO refundAmountInfoBO,
                                               String orderItemId,
                                               long couponAmount) {
        if (null == refundAmountInfoBO || null == wholeRefundInfo || null == applyRefundAmount) {
            return;
        }
        long newProductSalePrice = wholeRefundInfo.getProductAmount() - applyRefundAmount.getProductAmount() + couponAmount;
        // 计算退款相关的分摊包赔费用
        List<RefundIndemnityDetailPO> refundIndemnityDetailPOS = orderChangeAmountGatewayRepository.calculateRefundIndemnityDetails(orderItemId,
                -applyRefundAmount.getIndemnityAmount(), newProductSalePrice);
        if (CollectionUtils.isEmpty(refundIndemnityDetailPOS)) {
            refundAmountInfoBO.setIndemnityResponseUser(null);
            refundAmountInfoBO.setRefundIndemnityDetails(Collections.emptyList());
        } else {
            Integer responsibleUser = refundIndemnityDetailPOS.get(0).getResponsibleUser();
            refundAmountInfoBO.setIndemnityResponseUser(responsibleUser);
            refundAmountInfoBO.setRefundIndemnityDetails(OrderRefundDomainMapping.INSTANCE.toRefundIndemnityDetailBOList(refundIndemnityDetailPOS));
            long indemnityRealAmountSum = refundIndemnityDetailPOS.stream().mapToLong(r -> r.getIndemnityRealAmount() == null ? 0L : r.getIndemnityRealAmount()).sum();
            refundAmountInfoBO.setIndemnityAmount(indemnityRealAmountSum);
        }
    }

    /**
     * 填充退款时，随之变动的手续费金额
     */
    private void fulfillRefundFeeAmount(OrderItemAmountInfo wholeRefundInfo,
                                        OrderItemAmountInfo applyRefundAmount,
                                        OrderItemAmountInfoBO refundAmountInfoBO,
                                        String orderItemId,
                                        long couponAmount) {
        if (null == refundAmountInfoBO || null == wholeRefundInfo || null == applyRefundAmount) {
            return;
        }
        long newProductSalePrice = wholeRefundInfo.getProductAmount() - applyRefundAmount.getProductAmount() + couponAmount;
        //计算退款相关的手续费
        RefundFeeDetailPO refundFeeDetailPO = orderChangeAmountGatewayRepository.calculateRefundFeeChangeAmount(orderItemId,
                -applyRefundAmount.getFeeAmount(), newProductSalePrice);
        refundAmountInfoBO.setFeeAmount(Optional.ofNullable(refundFeeDetailPO).map(RefundFeeDetailPO::getChangeFeeRealAmount).orElse(0L));
        refundAmountInfoBO.setFeeResponseUser(Optional.ofNullable(refundFeeDetailPO).map(RefundFeeDetailPO::getResponsibleUser).orElse(null));
    }

    public OrderItemAmountInfo getSuccessRefundAmountInfo(List<RefundVoucher> successRefundVoucher) {
        List<RefundVoucherDetail> refundVoucherDetails = Lists.newArrayList();
        if (!CollectionUtils.isEmpty(successRefundVoucher)) {
            refundVoucherDetails = refundVoucherDetailRepository
                    .getByRefundVoucherIds(successRefundVoucher.stream().map(RefundVoucher::getRefundVoucherId).toList());
        }
        return RefundAmountCalculator.getSuccessRefundAmount(refundVoucherDetails);
    }

    /**
     * 根据orderItemId查询成功的收款单及明细
     */
    public List<OrderItemReceiptVoucherPO> getSuccessReceiptVoucherDetail(String orderItemId,
                                                                          List<RefundVoucher> successRefundVoucher) {
        // 查询orderItem关联的收款单明细
        List<ReceiptVoucherDetail> receiptVoucherDetails =
                receiptVoucherDetailRepository.getListByOrderItemId(orderItemId);
        Set<String> receiptVoucherIds =
                receiptVoucherDetails.stream().map(ReceiptVoucherDetail::getReceiptVoucherId).collect(Collectors.toSet());
        // 查询收款成功的收款单
        List<ReceiptVoucher> successReceiptVouchers = CollectionUtils.isEmpty(receiptVoucherIds) ? List.of()
                : receiptVoucherRepository.getSuccessByReceiptId(receiptVoucherIds.stream().toList());
        Map<String, ReceiptVoucher> successReceiptVoucherMap =
                successReceiptVouchers.stream().collect(Collectors.toMap(ReceiptVoucher::getReceiptVoucherId, e -> e));
        // 查询退款成功的退款单，只用于计算收款单剩余退款金额
        List<RefundVoucher> successRefund = successRefundVoucher == null ? List.of() : successRefundVoucher;
        Map<String,
                Long> alreadRefundAmountMap = successRefund.stream()
                .filter(e -> Objects.equals(e.getRefundType(), RefundTypeEnum.ONLINE_REFUND.getValue())
                        && StringUtils.isNotEmpty(e.getReceiptVoucherId()))
                .collect(Collectors.groupingBy(RefundVoucher::getReceiptVoucherId,
                        Collectors.summingLong(RefundVoucher::getActualRefundAmount)));
        List<OrderItemReceiptVoucherPO> result = receiptVoucherDetails.stream()
                .filter(e -> Objects.nonNull(successReceiptVoucherMap.get(e.getReceiptVoucherId()))).map(e -> {
                    OrderItemReceiptVoucherPO po = OrderRefundDomainMapping.INSTANCE
                            .toReceiptVoucherPO(successReceiptVoucherMap.get(e.getReceiptVoucherId()), e);
                    long maxRefundAmount = 0L;
                    if (e.getProductAmount() != null) {
                        maxRefundAmount += e.getProductAmount();
                    }
                    if (e.getFeeAmount() != null
                            && Objects.equals(ReceiptResponsibleEnum.BUYER.getValue(), e.getFeeResponsibleUser())) {
                        maxRefundAmount += e.getFeeAmount();
                    }
                    if (e.getIndemnityAmount() != null
                            && Objects.equals(ReceiptResponsibleEnum.BUYER.getValue(), e.getIndemnityResponsibleUser())) {
                        maxRefundAmount += e.getIndemnityAmount();
                    }
                    po.setMaxRefundAmount(maxRefundAmount);
                    po.setAlreadyRefundAmount(alreadRefundAmountMap.get(e.getReceiptVoucherId()));
                    return po;
                }).collect(Collectors.toList());
        checkSuccessReceiptVoucherDetail(result, successRefundVoucher);
        return result;
    }

    /**
     * 校验是否存在可退款的收款单
     */
    public void checkSuccessReceiptVoucherDetail(List<OrderItemReceiptVoucherPO> result,
                                                 List<RefundVoucher> successRefundVoucher) {
        if (CollectionUtils.isEmpty(result)) {
            throw new BizException(ErrorCode.RECEIPT_VOUCHER_NOT_FOUND.getErrCode(),
                    ErrorCode.RECEIPT_VOUCHER_NOT_FOUND.getErrDesc());
        }
        long maxRefundAmount = result.stream().mapToLong(OrderItemReceiptVoucherPO::getMaxRefundAmount).sum();
        long alreadyRefundAmount = successRefundVoucher.stream().mapToLong(RefundVoucher::getActualRefundAmount).sum();
        if (alreadyRefundAmount >= maxRefundAmount) {
            throw new BizException(ErrorCode.AMOUNT_INVALID_FAIL.getErrCode(),
                    ErrorCode.AMOUNT_INVALID_FAIL.getErrDesc());
        }
    }

    /**
     * 根据收款凭证和退款金额，确定支持的退款类型
     * @param order 子订单信息
     * @param receiptVouchers 收款凭证列表，包含订单项的收款详情
     * @param refundAmount    本次退款金额
     * @return 支持的退款类型集合，可能包含多种退款类型
     * <p>
     * 如果存在单张凭证足以覆盖全部退款金额的情况，则可以支持在线退款、线下打款退款； 如果存在挂账，则只能挂账； 其他情况走线下打款
     */
    public Set<Integer> supportRefundType(OrderInfoDubboRespDTO order, List<OrderItemReceiptVoucherPO> receiptVouchers, Long refundAmount) {
        if (CollectionUtils.isEmpty(receiptVouchers)) {
            throw new BizException(ErrorCode.NOT_FOUND_PAY_MODEL_ERROR.getErrCode(),
                    ErrorCode.NOT_FOUND_PAY_MODEL_ERROR.getErrDesc());
        }

        if(order.getChannelOrder() != null && ChannelOrderTypeEnum.JD.getValue().equals(order.getChannelOrder().getType())){
            return new HashSet<>(List.of(RefundTypeEnum.ONLINE_REFUND.getValue()));
        }

        boolean hasCredit = false;
        boolean canOneReceiptCover = false;
        for (OrderItemReceiptVoucherPO receiptVoucher : receiptVouchers) {
            long maxRefundAmount =
                    receiptVoucher.getMaxRefundAmount() == null ? 0 : receiptVoucher.getMaxRefundAmount();
            long alreadyRefundAmount =
                    receiptVoucher.getAlreadyRefundAmount() == null ? 0 : receiptVoucher.getAlreadyRefundAmount();
            if (receiptVoucher.getPayMode().equals(RefundTypeEnum.ONLINE_REFUND.getValue())
                    && maxRefundAmount - alreadyRefundAmount >= refundAmount) {
                canOneReceiptCover = true;
            }
            if (receiptVoucher.getPayMode().equals(RefundTypeEnum.CREDIT.getValue())) {
                hasCredit = true;
            }
        }

        Set<Integer> supportRefundType;
        if (canOneReceiptCover) {
            supportRefundType = Sets.newHashSet(RefundTypeEnum.ONLINE_REFUND.getValue(), RefundTypeEnum.OFFLINE_PAYOUT.getValue());
        } else if (hasCredit) {
            supportRefundType = Sets.newHashSet(RefundTypeEnum.CREDIT.getValue());
        } else {
            supportRefundType = Sets.newHashSet(RefundTypeEnum.OFFLINE_PAYOUT.getValue());
        }

        /*
         * 如果有两笔收款单，那么首次整单退款金额100%不能被单笔收款单覆盖，因此该场景第一次退款时必然为线下支付模式
         * 而如果是线下模式，往往财务会选择去渠道后台操作原单退，此时会导致原单金额变少
         * 第二次发起退款时，如走线上退剩余全部金额，会因为原单金额不足而导致退款失败一直卡退款中
         * 因此做一个优化，如果存在多笔成功收款的收款单，则不再允许线上模式
         */
        if (receiptVouchers.size() > 1) {
            supportRefundType.remove(RefundTypeEnum.ONLINE_REFUND.getValue());
        }

        // 一年前的单子不允许在线退款 用创建时间
        if (Objects.nonNull(order.getCreateTime())
            && order.getCreateTime().isBefore(LocalDateTime.now().minusYears(1))) {
            supportRefundType.remove(RefundTypeEnum.ONLINE_REFUND.getValue());
        }

        return supportRefundType;
    }

    public void addRefundOrderOperate(String orderItemId, String refundVoucherId, OrderOperateTypeEnum type,
                                      OrderOptUserTypeEnum role, String userId, String userName) {
        try {
            OrderItem orderItem = orderItemRepository.getOrderItem(orderItemId);
            String optContentTmp = "%s%s %s,退款单号:%s";
            OrderOperate orderOperate = new OrderOperate();
            orderOperate.setOrderId(orderItem.getOrderId());
            orderOperate.setOrderItemId(orderItemId);
            orderOperate.setOptType(type.getValue());
            orderOperate.setOptContent(
                    String.format(optContentTmp, role.getLabel(), userName, type.getLabel(), refundVoucherId));
            orderOperate.setOptUserType(role.getValue());
            orderOperate.setOptUserId(userId);

            orderOperateRepository.save(orderOperate);
        } catch (Exception e) {
            log.error("addAuditRefundOrderOperate error, orderItemId: {},refundVoucherId:{}", orderItemId,
                    refundVoucherId, e);
        }
    }

    public void checkOrderCanRefund(OrderItem orderItem) {
        if (LocalDateTime.now().isAfter(orderItem.getCreateTime().plusYears(1))) {
            throw new BizException(ErrorCode.ORDER_EXPIRE_REFUND_FAIL.getErrCode(), ErrorCode.ORDER_EXPIRE_REFUND_FAIL.getErrDesc());
        }
    }

    public void checkOrderCanRefund(String orderItemId) {
        OrderItem orderItem = orderItemRepository.getOrderItem(orderItemId);
        checkOrderCanRefund(orderItem);
    }

    public List<RefundOrderItemPageRespBO> getOrderRefundList(String orderItemId) {

        List<RefundVoucher> refundVouchers = refundVoucherRepository.getItemRefundList(orderItemId);
        List<RefundVoucherDetail> refundVoucherDetails = refundVoucherDetailRepository.getByOrderItemId(orderItemId);
        Map<String, RefundVoucherDetail> refundId2OrderRefundVoucher = refundVoucherDetails.stream().collect(Collectors.toMap(RefundVoucherDetail::getRefundVoucherId, e -> e));

        List<ViolateOrderBO> violateOrderBOList = violateOrderDomainService.getEffectiveListByOrderItemId(orderItemId);
        Map<String, ViolateOrderBO> refundIdToViolateOrderMap = violateOrderBOList.stream()
                .collect(Collectors.toMap(ViolateOrderBO::getRefundVoucherId, Function.identity(), (e1, e2) -> e1));

        List<RefundOrderItemPageRespBO> refundOrderItemList = new ArrayList<>();
        refundVouchers.forEach(refund -> {
            RefundOrderItemPageRespBO orderItemPageRespBO = RefundDomainMapping.INSTANCE.toRefundOrderItemPageRespBO(refund,
                    refundId2OrderRefundVoucher.get(refund.getRefundVoucherId()));

            ViolateOrderBO violateOrderBO = refundIdToViolateOrderMap.get(refund.getRefundVoucherId());
            if (null != violateOrderBO) {
                String violateId = violateOrderBO.getViolateId();
                orderItemPageRespBO.setViolateId(violateId);
            }
            refundOrderItemList.add(orderItemPageRespBO);
        });
        return refundOrderItemList;
    }

    /**
     * 退款失败-回调业务
     */
    private void refundCancelToBusiness(String refundVoucherId, Integer refundWholeType) {
        log.info("[退款取消] refundCancelToBusiness 回调业务, 退款单id:{}", refundVoucherId);

        if (WholeRefundTypeEnum.INDEMNITY.getValue().equals(refundWholeType)) {
            refundFailIndemnityCallback(refundVoucherId);
        }
    }

    /**
     * 退款失败-变更包赔退款, 取消包赔变更
     */
    private void refundFailIndemnityCallback(String refundVoucherId) {
        // MQ 取消更新包赔逻辑
        IndemnityChangeTradeFinishedMessage indemnityChangeMessage = new IndemnityChangeTradeFinishedMessage()
                .setVoucherId(refundVoucherId)
                .setVoucherType(VoucherTypeEnum.REFUND.getValue())
                .setTradeSuccess(false);
        refundIndemnityChangeMessageService.refundSendIndemnityChange(indemnityChangeMessage);
    }


}
