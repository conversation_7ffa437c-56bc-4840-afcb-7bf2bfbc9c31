package com.pxb7.mall.trade.ass.domain.model;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class OrderItemAmountInfoBO {
    /**
     * 商品号价金额
     */
    private long productAmount;
    /**
     * 包赔金额
     */
    private long indemnityAmount;

    /**
     * 包赔承担方
     */
    private Integer indemnityResponseUser;


    private List<RefundIndemnityDetailBO> refundIndemnityDetails;


    /**
     * 手续费金额
     */
    private long feeAmount;

    /**
     * 手续费承担方
     */
    private Integer feeResponseUser;

    /**
     * 红包
     */
    private long redPacketAmount;
    /**
     * 买家违约金
     */
    private long buyerViolateAmount;
    /**
     * 总金额
     */
    private long totalAmount;

    /**
     * 发送违约金MQ
     */
    private boolean sendViolateMQFlag;
}
