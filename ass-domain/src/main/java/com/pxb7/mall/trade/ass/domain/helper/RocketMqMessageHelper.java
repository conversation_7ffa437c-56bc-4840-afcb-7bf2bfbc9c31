package com.pxb7.mall.trade.ass.domain.helper;


import com.pxb7.mall.trade.ass.domain.command.CommandEnum;
import com.pxb7.mall.trade.ass.domain.command.sub.bo.RocketMqMessageBO;
import com.pxb7.mall.trade.ass.domain.mapping.CommandMapping;
import com.pxb7.mall.trade.ass.domain.model.RocketMqMessageDTO;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.CommandDO;
import com.pxb7.mall.trade.ass.infra.util.ObjectMapperUtil;
import com.pxb7.mall.trade.ass.infra.util.PxTransactionUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class RocketMqMessageHelper {

    @Resource
    private CommandHelper commandHelper;

    public void saveAndSend(RocketMqMessageDTO messageDTO) {
        // 调用方存在事务，则在事务中保存消息
        CommandDO baseCommandDO = saveMessage(messageDTO);
        PxTransactionUtils.execAfterCommit(() -> commandHelper.execute(CommandMapping.INSTANCE.to(baseCommandDO)));
    }

    private CommandDO saveMessage(RocketMqMessageDTO rocketMqMessageDTO) {

        return commandHelper.createAndSave(CommandEnum.ROCKET_MQ_MESSAGE_SEND, rocketMqMessageDTO.getBizId(), ObjectMapperUtil.toJsonStr(buildCommandContent(rocketMqMessageDTO)));
    }

    private RocketMqMessageBO buildCommandContent(RocketMqMessageDTO mqMessageDTO) {
        RocketMqMessageBO rocketMqMessageBO = new RocketMqMessageBO();
        rocketMqMessageBO.setTopic(mqMessageDTO.getTopic());
        rocketMqMessageBO.setTag(mqMessageDTO.getTag());
        //rocketMqMessageBO.setDelay(mqMessageDTO.isDelay());
        rocketMqMessageBO.setContent(mqMessageDTO.getContent());
        rocketMqMessageBO.setSeconds(mqMessageDTO.getSeconds());
        //目前倾向使用bizId作为key，不用改造
        rocketMqMessageBO.setKey(mqMessageDTO.getBizId());
        rocketMqMessageBO.setMessageType(mqMessageDTO.getMessageType());
        rocketMqMessageBO.setMessageGroup(mqMessageDTO.getMessageGroup());
        if (mqMessageDTO.isDelay()){
            rocketMqMessageBO.setMessageType(RocketMqMessageBO.MessageTypeEnum.DELAY);
        }
        return rocketMqMessageBO;
    }
}
