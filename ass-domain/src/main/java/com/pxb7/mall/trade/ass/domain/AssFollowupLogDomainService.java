package com.pxb7.mall.trade.ass.domain;

import org.springframework.stereotype.Service;

import com.pxb7.mall.trade.ass.domain.mapping.AssFollowupLogDomainMapping;
import com.pxb7.mall.trade.ass.domain.model.reqeust.RecordBO;
import com.pxb7.mall.trade.ass.infra.repository.db.AssFollowupLogRepository;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.AssFollowupLog;
import com.pxb7.mall.trade.ass.infra.util.IdGenUtil;

import jakarta.annotation.Resource;

/**
 * 售后跟进记录
 *
 * <AUTHOR>
 * @since: 2024-09-21 15:00
 **/
@Service
public class AssFollowupLogDomainService {
    @Resource
    private AssFollowupLogRepository assFollowupLogRepository;

    public boolean recordLog(RecordBO recordBO) {
        AssFollowupLog assFollowupLog = AssFollowupLogDomainMapping.INSTANCE.toEntity(recordBO);
        assFollowupLog.setFollowupId(IdGenUtil.generateId());
        assFollowupLog.setCreateUserId(recordBO.getOperatorId());
        assFollowupLog.setUpdateUserId(recordBO.getOperatorId());
        return assFollowupLogRepository.save(assFollowupLog);

    }
}
