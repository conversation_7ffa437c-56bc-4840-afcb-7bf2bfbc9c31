package com.pxb7.mall.trade.ass.domain;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.pxb7.mall.trade.ass.domain.refund.model.RefundPayPaymentBO;
import com.pxb7.mall.trade.ass.infra.constant.PayConstant;
import com.pxb7.mall.trade.ass.infra.enums.OrderTypeEnum;
import com.pxb7.mall.trade.ass.infra.enums.PayBusinessTypeEnum;
import com.pxb7.mall.trade.ass.infra.repository.db.PayLogRepository;
import com.pxb7.mall.trade.ass.infra.repository.db.PaymentRepository;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.PayLog;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.Payment;
import com.pxb7.mall.trade.ass.infra.util.IdGenUtil;
import com.pxb7.mall.trade.order.client.enums.pay.PayStatusEnum;
import com.pxb7.mall.trade.order.client.enums.pay.TradeModeEnum;
import com.pxb7.mall.trade.order.client.enums.pay.TradeStatusEnum;
import com.pxb7.mall.trade.order.client.enums.pay.VoucherTypeEnum;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Objects;

@Service
@Slf4j
public class PayLogDomainService {

    @Resource
    private PayLogRepository payLogRepository;

    @Resource
    private PaymentRepository paymentRepository;

    /**
     * 退款前操作 状态：待执行
     *
     * @param bo
     * @return
     */
    @Transactional(rollbackFor = Exception.class, timeout = 5)
    public PayLog saveRefundPaymentAndPayLog(RefundPayPaymentBO bo) {

        String paymentId = IdGenUtil.getShardingColumnId(bo.getRefundVoucherId());
        // payLog
        PayLog payLog = saveInitRefundPayLog(bo, paymentId);

        // payment
        Payment payment = new Payment();
        payment.setPaymentId(paymentId);
        payment.setVoucherId(bo.getRefundVoucherId());
        payment.setVoucherType(bo.getRefundVoucherType());
        payment.setTradeMode(TradeModeEnum.REFUND.getValue());
        payment.setBusinessType(PayBusinessTypeEnum.REFUND.getValue());
        payment.setBusinessId(payLog.getPayLogId());
        payment.setTradeAmount(bo.getRefundAmount());
        payment.setTradeStatus(TradeStatusEnum.UNEXECUTED.getValue());
        payment.setOrderId(Objects.nonNull(bo.getOrderId()) ? bo.getOrderId() : PayConstant.ZERO);
        payment.setOrderItemId(Objects.nonNull(bo.getOrderItemId()) ? bo.getOrderItemId() : PayConstant.ZERO);
        payment.setPayUserId(bo.getUserId());
        paymentRepository.save(payment);
        return payLog;
    }

    @Transactional(rollbackFor = Exception.class, timeout = 5)
    public void updatePayStatusToExecuting(String requestParam, String paymentId,
                                           String pagLogId, String extInfo) {
        PayLog toUpdate = new PayLog();
        toUpdate.setPayLogId(pagLogId);
        toUpdate.setPayStatus(TradeStatusEnum.EXECUTING.getValue());
        toUpdate.setRequestParam(requestParam);
        if (StringUtils.isNotBlank(extInfo)) {
            toUpdate.setExtInfo(extInfo);
        }
        payLogRepository.updateById(toUpdate);

        Payment toUpdatePayment = new Payment();
        toUpdatePayment.setPaymentId(paymentId);
        toUpdatePayment.setTradeStatus(TradeStatusEnum.EXECUTING.getValue());
        paymentRepository.updateById(toUpdatePayment);
    }

    public PayLog saveInitRefundPayLog(RefundPayPaymentBO bo, String paymentId) {
        PayLog payLog = new PayLog();
        payLog.setPayLogId(IdGenUtil.getShardingColumnId(paymentId));
        payLog.setPayChannelId(bo.getChannelId());
        payLog.setPayMerchantId(bo.getCompanyAccountId());
        payLog.setPaymentId(paymentId);
        payLog.setPayStatus(PayStatusEnum.PAY_PENDING.getValue());
        payLog.setTradeMode(TradeModeEnum.REFUND.getValue());
        payLog.setTradeAmount(bo.getRefundAmount());
        payLog.setPaymentType(bo.getPayType());
        payLog.setPayChannelId(bo.getChannelId());
        payLog.setPayMerchantId(bo.getCompanyAccountId());
        payLogRepository.save(payLog);
        return payLog;
    }

    /**
     * 更改 payment 和 pay_log的状态
     */
    @Transactional(rollbackFor = Exception.class, timeout = 5)
    public boolean updatePayWithStatus(PayLog refundPayLog, Payment refundPayment, Integer tradeStatus,
                                       String outTradeNo, Long refundAmount, String callbackResult) {
        log.info("[退款结果]  更改 payment 和 pay_log的状态，refundPayLog:{},tradeStatus:{}", refundPayLog, tradeStatus);
        boolean res = true;
        if (Objects.nonNull(refundPayment)) {
            // 更改 payment 和 pay_log的状态
            LambdaUpdateWrapper<Payment> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
            lambdaUpdateWrapper.eq(Payment::getPaymentId, refundPayment.getPaymentId());
            lambdaUpdateWrapper.eq(Payment::getTradeStatus, refundPayment.getTradeStatus());
            lambdaUpdateWrapper.set(Payment::getTradeStatus, tradeStatus);
            if (StringUtils.isNotBlank(outTradeNo)) {
                lambdaUpdateWrapper.set(Payment::getOutTradeNo, outTradeNo);
            }
            if (Objects.nonNull(refundAmount)) {
                lambdaUpdateWrapper.set(Payment::getActualAmount, refundAmount);
            }
            res = paymentRepository.update(lambdaUpdateWrapper);
        }

        LambdaUpdateWrapper<PayLog> lambdaWrapper = new LambdaUpdateWrapper<>();
        lambdaWrapper.eq(PayLog::getPayLogId, refundPayLog.getPayLogId());
        lambdaWrapper.eq(PayLog::getPayStatus, refundPayLog.getPayStatus());
        lambdaWrapper.set(PayLog::getPayStatus, tradeStatus);
        if (StringUtils.isNotBlank(outTradeNo)) {
            lambdaWrapper.set(PayLog::getOutTradeNo, outTradeNo);
        }
        //存储退款 三方结果
        if (StringUtils.isNotBlank(callbackResult)) {
            lambdaWrapper.set(PayLog::getCallbackResult, callbackResult);
        }
        return res && payLogRepository.update(lambdaWrapper);
    }
}
