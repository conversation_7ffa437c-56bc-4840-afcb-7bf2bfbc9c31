package com.pxb7.mall.trade.ass.domain.processor;

import org.springframework.stereotype.Component;

import com.pxb7.mall.im.client.dto.response.AddGroupMemberRespDTO;
import com.pxb7.mall.trade.ass.client.enums.AssScheduleSourceTypeEnums;
import com.pxb7.mall.trade.ass.domain.model.assSchedule.ReqCreateAssScheduleBO;
import com.pxb7.mall.trade.ass.domain.model.reqeust.ApplyAfcRecordReqBO;
import com.pxb7.mall.trade.ass.infra.remote.dubbo.ImRpcGateway;
import com.pxb7.mall.trade.ass.infra.remote.dubbo.UserRpcGateway;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.AfcQuestionConf;
import com.pxb7.mall.trade.ass.infra.util.IdGenUtil;
import com.pxb7.mall.user.dto.response.user.UserShortInfoDTO;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class AfcApplyDataConvertProcessor {
    @Resource
    private ImRpcGateway imRpcGateway;
    @Resource
    private UserRpcGateway userRpcGateway;

    public ReqCreateAssScheduleBO buildApplyAfcScheduleBO(ReqCreateAssScheduleBO applyAfcScheduleBO,
                                                          AfcQuestionConf afcQuestionConf,
                                                          String deliveryRoomId,
                                                          String userId) {
        applyAfcScheduleBO.setLoginUserId(userId);
        applyAfcScheduleBO.setRoomId(deliveryRoomId);
        applyAfcScheduleBO.setSourceType(AssScheduleSourceTypeEnums.SourceType_1.getCode());
        applyAfcScheduleBO.setAssType(afcQuestionConf.getQuestionType());

        AddGroupMemberRespDTO addGroupMemberRespDTO = imRpcGateway.addGroupMember(deliveryRoomId, applyAfcScheduleBO.getGameId(), applyAfcScheduleBO.getOrderNo(), afcQuestionConf.getQuestionType());
        applyAfcScheduleBO.setRecvCustomerId(addGroupMemberRespDTO.getCustomerCareId());
        return applyAfcScheduleBO;
    }

    public ApplyAfcRecordReqBO buildApplyAfcRecordBO(ApplyAfcRecordReqBO applyAfcRecordReqBO,
                                                     AfcQuestionConf afcQuestionConf,
                                                     String deliveryRoomId,
                                                     String userId) {
        applyAfcRecordReqBO.setSubmitRecordId(IdGenUtil.generateId());
        applyAfcRecordReqBO.setRoomId(deliveryRoomId);
        applyAfcRecordReqBO.setCreateUserId(userId);
        applyAfcRecordReqBO.setUpdateUserId(userId);
        applyAfcRecordReqBO.setAfcType(afcQuestionConf.getQuestionType());
        UserShortInfoDTO userShortInfoDTO = userRpcGateway.getBaseUserInfo(userId);
        applyAfcRecordReqBO.setPhone(userShortInfoDTO.getPhone());
        applyAfcRecordReqBO.setNickname(userShortInfoDTO.getNickname());

        return applyAfcRecordReqBO;
    }
}
