package com.pxb7.mall.trade.ass.domain.processor;

import com.pxb7.mall.im.client.dto.response.AddGroupMemberRespDTO;
import com.pxb7.mall.trade.ass.client.enums.AssScheduleSourceTypeEnums;
import com.pxb7.mall.trade.ass.domain.model.assSchedule.ReqCreateAssScheduleBO;
import com.pxb7.mall.trade.ass.domain.model.reqeust.ApplyAfcRecordReqBO;
import com.pxb7.mall.trade.ass.infra.remote.dubbo.ImRpcGateway;
import com.pxb7.mall.trade.ass.infra.remote.dubbo.UserRpcGateway;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.AfcQuestionConf;
import com.pxb7.mall.trade.ass.infra.util.IdGenUtil;
import com.pxb7.mall.user.dto.response.user.UserShortInfoDTO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * Copyright (C), 2002-2025, 螃蟹游戏服务网
 *
 * @fileName: AfcApplyDataConvertProcessor.java
 * @description: 售后申请数据转化处理器
 * @author: guominqiang
 * @email: <EMAIL>
 * @date: 2025/4/11 16:54
 * @history: //修改记录
 * <author> <time> <version> <desc>
 * 修改人姓名 修改时间 版本号 描述
 */
@Component
@Slf4j
public class AfcApplyDataConvertProcessor {
    @Resource
    private ImRpcGateway imRpcGateway;
    @Resource
    private UserRpcGateway userRpcGateway;

    public ReqCreateAssScheduleBO buildApplyAfcScheduleBO(ReqCreateAssScheduleBO applyAfcScheduleBO,
                                                          AfcQuestionConf afcQuestionConf,
                                                          String deliveryRoomId,
                                                          String userId) {
        applyAfcScheduleBO.setLoginUserId(userId);
        applyAfcScheduleBO.setRoomId(deliveryRoomId);
        applyAfcScheduleBO.setSourceType(AssScheduleSourceTypeEnums.SourceType_1.getCode());
        applyAfcScheduleBO.setAssType(afcQuestionConf.getQuestionType());

        AddGroupMemberRespDTO addGroupMemberRespDTO = imRpcGateway.addGroupMember(deliveryRoomId, applyAfcScheduleBO.getGameId(), applyAfcScheduleBO.getOrderNo(), afcQuestionConf.getQuestionType());
        applyAfcScheduleBO.setRecvCustomerId(addGroupMemberRespDTO.getCustomerCareId());
        return applyAfcScheduleBO;
    }

    public ApplyAfcRecordReqBO buildApplyAfcRecordBO(ApplyAfcRecordReqBO applyAfcRecordReqBO,
                                                     AfcQuestionConf afcQuestionConf,
                                                     String deliveryRoomId,
                                                     String userId) {
        applyAfcRecordReqBO.setSubmitRecordId(IdGenUtil.generateId());
        applyAfcRecordReqBO.setRoomId(deliveryRoomId);
        applyAfcRecordReqBO.setCreateUserId(userId);
        applyAfcRecordReqBO.setUpdateUserId(userId);
        applyAfcRecordReqBO.setAfcType(afcQuestionConf.getQuestionType());
        UserShortInfoDTO userShortInfoDTO = userRpcGateway.getBaseUserInfo(userId);
        applyAfcRecordReqBO.setPhone(userShortInfoDTO.getPhone());
        applyAfcRecordReqBO.setNickname(userShortInfoDTO.getNickname());

        return applyAfcRecordReqBO;
    }
}
