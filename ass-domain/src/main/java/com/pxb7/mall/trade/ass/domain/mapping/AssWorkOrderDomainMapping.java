package com.pxb7.mall.trade.ass.domain.mapping;

import cn.hutool.core.util.StrUtil;
import com.pxb7.mall.trade.ass.domain.model.AssWorkOrderReqBO;
import com.pxb7.mall.trade.ass.domain.model.AssWorkOrderRespBO;
import com.pxb7.mall.trade.ass.domain.model.assSchedule.ReqCreateAssScheduleBO;
import com.pxb7.mall.trade.ass.domain.model.reqeust.ApplyAfcRecordReqBO;
import com.pxb7.mall.trade.ass.infra.model.AssInfoReqPO;
import com.pxb7.mall.trade.ass.infra.model.AssWorkOrderReqPO;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.AssWorkOrder;

import java.time.LocalDateTime;
import java.util.List;

import com.pxb7.mall.trade.ass.infra.repository.es.entity.AssInfoAggregation;
import com.pxb7.mall.trade.ass.infra.util.IdGenUtil;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;


@Mapper
public interface AssWorkOrderDomainMapping {

    AssWorkOrderDomainMapping INSTANCE = Mappers.getMapper(AssWorkOrderDomainMapping.class);


    AssWorkOrderReqPO.AddPO assWorkOrderBO2AddPO(AssWorkOrderReqBO.AddBO source);

    AssWorkOrderReqPO.UpdatePO assWorkOrderBO2UpdatePO(AssWorkOrderReqBO.UpdateBO source);

    AssWorkOrderReqPO.DelPO assWorkOrderBO2DelPO(AssWorkOrderReqBO.DelBO source);

    AssWorkOrderReqPO.SearchPO assWorkOrderBO2SearchPO(AssWorkOrderReqBO.SearchBO source);

    AssWorkOrderReqPO.PagePO assWorkOrderBO2PagePO(AssWorkOrderReqBO.PageBO source);

    AssWorkOrderRespBO.DetailBO assWorkOrderPO2DetailBO(AssWorkOrder source);

    List<AssWorkOrderRespBO.DetailBO> assWorkOrderPO2ListBO(List<AssWorkOrder> source);

    Page<AssWorkOrderRespBO.DetailBO> assWorkOrderPO2PageBO(Page<AssWorkOrder> source);


    AssInfoReqPO.PagePO assWorkOrderPageBO2PagePO(AssWorkOrderReqBO.PageAssOrderBO param);

    List<AssWorkOrderRespBO.AssInfoPageDetailBO> convertPageRecords(List<AssInfoAggregation> records);

    AssWorkOrderRespBO.AssInfoPageDetailBO convertPageRecord(AssInfoAggregation assInfo);


    AssWorkOrderReqPO.AddPO InitWorkOrderAndLogBO2AddPO(AssWorkOrderReqBO.InitWorkOrderAndLogBO param);
}


