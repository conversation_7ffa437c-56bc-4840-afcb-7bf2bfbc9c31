package com.pxb7.mall.trade.ass.domain.refund.message.provider;

import com.alibaba.fastjson2.JSON;
import com.pxb7.mall.trade.ass.domain.helper.RocketMqMessageHelper;
import com.pxb7.mall.trade.ass.domain.model.RocketMqMessageDTO;
import com.pxb7.mall.trade.ass.domain.refund.model.RefundAuditMessage;
import com.pxb7.mall.trade.ass.infra.config.nacos.NacosYamlConfigListener;
import com.pxb7.mall.trade.ass.infra.constant.RMQConstant;
import com.pxb7.mall.trade.ass.infra.messaging.MQProducer;
import com.pxb7.mall.trade.ass.infra.model.mq.OriginalRefundMessage;
import com.pxb7.mall.trade.ass.infra.model.mq.RefundCallbackMessage;
import com.pxb7.mall.trade.ass.infra.model.mq.RefundNotifyMessage;
import com.pxb7.mall.trade.ass.infra.model.mq.RefundQueryMessage;
import com.pxb7.mall.trade.ass.infra.util.ObjectMapperUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.Duration;

import static com.pxb7.mall.trade.ass.infra.constant.RMQConstant.ASS_REFUND_CENTER_TOPIC;
import static com.pxb7.mall.trade.ass.infra.constant.RMQConstant.REFUND_CALLBACK_TAG;

@Slf4j
@Service
public class RefundMessageService implements NacosYamlConfigListener {

    @Resource
    private MQProducer mqProducer;
    @Resource
    private RocketMqMessageHelper  rocketMqMessageHelper;

    private long seconds = 300;

    /**
     * 发送线上退款自动审核消息
     */
    public void sendRefundAuditMessage(RefundAuditMessage message) {
        mqProducer.asyncSendDelay("refund_audit:online_auto_audit", JSON.toJSONString(message),
                Duration.ofSeconds(seconds));
        log.info("发送线上退款自动审核消息:{}, 延迟:{} seconds", message, seconds);
    }

    /**
     * @param message
     */
    public void sendRefundQueryMessage(RefundQueryMessage message) {
        // 第一次两分钟 后面5分钟
//        int minutes = Objects.equals(message.getTimes(), 1) ? 2 : message.getMinutes();
        //1,2,4,5,5,5.....
        int minutes = message.getMinutes();
        if(message.getTimes() != null && message.getTimes() <= 3){
            minutes = (int) Math.pow(2, message.getTimes() - 1);
        }
        String messageId = mqProducer.sendDelay(
            RMQConstant.REFUND_QUERY_DELAY_TOPIC + ":" + RMQConstant.REFUND_QUERY_DELAY_TAG, message,
            Duration.ofMinutes(minutes));
        log.info("发送线上退款查询消息:{}, messageId:{}", message, messageId);
    }

    /**
     * @param message
     */
    public void sendRefundNotifyMessage(RefundNotifyMessage message) {
        mqProducer.send("ass_refund_center_topic:ass_refund_callback", message);
        log.info("发送线上退款回调消息:{}", message);
    }

    /**
     * @param message
     */
    public void sendRefundNotifyMessage(RefundNotifyMessage message, String tag) {
        String msgId = mqProducer.send(ASS_REFUND_CENTER_TOPIC, tag, message);
        log.info("发送线上退款回调消息:{},tag:{},msgId:{}", message, tag, msgId);
    }

    /**
     * 发送线上原路退款延迟消息
     */
    public void sendDelayOriginalRefundMessage(OriginalRefundMessage message, long s) {
        mqProducer.sendDelay("ass_refund_center_delay_topic:ass_original_refund", message,
                Duration.ofSeconds(s));
        log.info("发送线上原路退款延迟消息:{}", message);
    }

    /**
     * 发送退款回调消息
     */
    public void sendRefundCallbackMessage(RefundCallbackMessage message) {
        RocketMqMessageDTO messageDTO = RocketMqMessageDTO.builder()
                .topic(ASS_REFUND_CENTER_TOPIC)
                .tag(REFUND_CALLBACK_TAG)
                .isDelay(false)
                .content(ObjectMapperUtil.toJsonStr(message))
                .bizId(String.valueOf(message.getChannel().getValue()))
                .isSharding(false)
                .build();
        // 本地消息
        rocketMqMessageHelper.saveAndSend(messageDTO);
        log.info("发送退款回调消息:{},tag:{}", message, REFUND_CALLBACK_TAG);
//        String msgId = mqProducer.send(ASS_REFUND_CENTER_TOPIC, REFUND_CALLBACK_TAG, message);
//        log.info("发送退款回调消息:{},tag:{},msgId:{}", message, REFUND_CALLBACK_TAG, msgId);
    }

    @Override
    public void onRefresh(Object newConfig) {
        this.seconds = Long.parseLong(newConfig.toString());
    }

    @Override
    public String configPath() {
        return "refund_auto_audit_seconds";
    }
}
