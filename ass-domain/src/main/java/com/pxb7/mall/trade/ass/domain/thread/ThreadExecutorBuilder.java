package com.pxb7.mall.trade.ass.domain.thread;

import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * 线程池构造器
 */
@Slf4j
public final class ThreadExecutorBuilder {

    public static final Map<String, ThreadExecutor> POOL_CACHE = new HashMap<>();

    private ThreadExecutorBuilder() {
    }

    public static ThreadExecutor build(ThreadPoolEnum threadPool) {
        return build(threadPool.getBiz());
    }

    public static ThreadExecutor build(String biz) {
        return POOL_CACHE.get(biz);
    }

    public static void addThreadPool(ThreadPoolEnum threadPoolType) {
        addThreadPool(threadPoolType.getBiz(), threadPoolType.getCorePoolSize(), threadPoolType.getMaxPoolSize(),
                threadPoolType.getMaxValue(), threadPoolType.getThreadPoolType());
    }

    public static void addThreadPool(String biz, int corePoolSize, int maxPoolSize, int maxValue, ThreadPoolTypeEnum threadPoolType) {
        POOL_CACHE.put(biz, new ThreadExecutorImpl(biz, corePoolSize, maxPoolSize, maxValue, threadPoolType));
    }

    static {
        Arrays.stream(ThreadPoolEnum.values()).forEach(ThreadExecutorBuilder::addThreadPool);
    }

}
