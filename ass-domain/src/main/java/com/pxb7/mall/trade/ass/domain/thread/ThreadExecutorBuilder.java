package com.pxb7.mall.trade.ass.domain.thread;

import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 线程池构造器
 *
 * <AUTHOR>
 */
@Slf4j
public final class ThreadExecutorBuilder {

    public static final Map<String, ThreadExecutor> POOL_CACHE = new ConcurrentHashMap<>();

    /**
     * 异步执行初始核心线程数
     */
    private static final int POOL_SIZE = 10;
    /**
     * 异步执行最大线程数
     */
    private static final int MAX_POOL_SIZE = 30;
    /**
     * 异步执行队列可容纳的最大数
     */
    private static final int MAX_VALUE = 9999;

    private ThreadExecutorBuilder() {
    }

    public static ThreadExecutor build(String biz) {
        return build(biz, POOL_SIZE, MAX_POOL_SIZE, MAX_VALUE);
    }

    public static ThreadExecutor build(ThreadPoolEnum threadPool) {
        return build(threadPool.getBiz(), threadPool.getPoolSize(), threadPool.getMaxPoolSize(),
                threadPool.getMaxValue());
    }

    public static ThreadExecutor build(String biz, int poolSize, int maxPoolSize, int maxValue) {

        if (null == POOL_CACHE.get(biz)) {
            POOL_CACHE.put(biz, new ThreadExecutorImpl(biz, poolSize, maxPoolSize, maxValue));
        }
        return POOL_CACHE.get(biz);
    }

}
