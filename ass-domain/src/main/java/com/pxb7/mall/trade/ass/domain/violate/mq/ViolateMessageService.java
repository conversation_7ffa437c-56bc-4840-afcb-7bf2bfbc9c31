package com.pxb7.mall.trade.ass.domain.violate.mq;

import com.pxb7.mall.pay.client.constants.PayMessageTopics;
import com.pxb7.mall.pay.client.dto.model.PayAccountTradeMessage;
import com.pxb7.mall.trade.ass.domain.helper.RocketMqMessageHelper;
import com.pxb7.mall.trade.ass.domain.model.RocketMqMessageDTO;
import com.pxb7.mall.trade.ass.domain.violate.mq.message.ViolateApplyMessage;
import com.pxb7.mall.trade.ass.domain.violate.mq.message.ViolatePayRecordMessage;
import com.pxb7.mall.trade.ass.domain.violate.mq.message.ViolateRiskBlackMessage;
import com.pxb7.mall.trade.ass.infra.util.ObjectMapperUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import static com.pxb7.mall.trade.ass.infra.constant.RMQConstant.*;


@Slf4j
@Service
public class ViolateMessageService {

    @Resource
    private RocketMqMessageHelper rocketMqMessageHelper;

    /**
     * 发送:创建违约单
     */
    public void sendCreateViolate(ViolateApplyMessage message) {
        log.info("[sendCreateViolate] 发送 创建违约单消息，message = {}", message);
        RocketMqMessageDTO messageDTO = RocketMqMessageDTO.builder()
                .topic(VIOLATE_TOPIC)
                .tag(VIOLATE_CREATE_TAG)
                .isDelay(false)
                .content(ObjectMapperUtil.toJsonStr(message))
                .bizId(message.getOrderItemId())
                .isSharding(true)
                .build();
        // 本地消息
        rocketMqMessageHelper.saveAndSend(messageDTO);
    }

    /**
     * 发送:处理违约打款
     */
    public void sendDealTransfer(ViolatePayRecordMessage message) {
        log.info("[sendDealTransfer] 发送 处理违约打款消息，message = {}", message);
        RocketMqMessageDTO messageDTO = RocketMqMessageDTO.builder()
                .topic(VIOLATE_PAY_RECORD_TOPIC)
                .tag(VIOLATE_TRANSFER_TAG)
                .isDelay(false)
                .content(ObjectMapperUtil.toJsonStr(message))
                .bizId(message.getViolateId())
                .isSharding(true)
                .build();
        // 本地消息
        rocketMqMessageHelper.saveAndSend(messageDTO);
    }

    /**
     * 发送:处理违约扣款
     */
    public void sendDealDeduction(ViolatePayRecordMessage message) {
        log.info("[sendDealDeduction] 发送 处理违约扣款消息，message = {}", message);
        RocketMqMessageDTO messageDTO = RocketMqMessageDTO.builder()
                .topic(VIOLATE_PAY_RECORD_TOPIC)
                .tag(VIOLATE_DEDUCTION_TAG)
                .isDelay(false)
                .content(ObjectMapperUtil.toJsonStr(message))
                .bizId(message.getViolateId())
                .isSharding(true)
                .build();
        // 本地消息
        rocketMqMessageHelper.saveAndSend(messageDTO);
    }

    /**
     * 发送:处理违约收款
     */
    public void sendDealReceipt(ViolatePayRecordMessage message) {
        log.info("[sendDealReceipt] 发送 处理违约收款消息，message = {}", message);
        RocketMqMessageDTO messageDTO = RocketMqMessageDTO.builder()
                .topic(VIOLATE_PAY_RECORD_TOPIC)
                .tag(VIOLATE_RECEIPT_TAG)
                .isDelay(false)
                .content(ObjectMapperUtil.toJsonStr(message))
                .bizId(message.getViolateId())
                .isSharding(true)
                .build();
        // 本地消息
        rocketMqMessageHelper.saveAndSend(messageDTO);
    }

    /**
     * 发送:处理违约收款中断
     */
    public void sendDealReceiptStop(ViolatePayRecordMessage message, Long delayTime) {
        log.info("[sendDealReceiptStop] 发送 处理违约收款中断消息，message = {}", message);
        RocketMqMessageDTO messageDTO = RocketMqMessageDTO.builder()
                .topic(VIOLATE_RECEIPT_DELAY_TOPIC)
                .tag(VIOLATE_RECEIPT_STOP_TAG)
                .isDelay(true)
                .seconds(delayTime)
                .content(ObjectMapperUtil.toJsonStr(message))
                .bizId(message.getViolateId())
                .isSharding(true)
                .build();
        // 本地消息
        rocketMqMessageHelper.saveAndSend(messageDTO);
    }

    /**
     * 发送打款请求-钱包
     */
    public void sendTransferToWallet(PayAccountTradeMessage message) {
        log.info("[sendViolateTransfer] 发送钱包 违约打款消息，message = {}", message);
        RocketMqMessageDTO messageDTO = RocketMqMessageDTO.builder()
                .topic(PayMessageTopics.AccountTrade.TOPIC)
                .tag(PayMessageTopics.AccountTrade.TAG)
                .isDelay(false)
                .content(ObjectMapperUtil.toJsonStr(message))
                .bizId(message.getVoucherId())
                .isSharding(true)
                .build();
        // 本地消息
        rocketMqMessageHelper.saveAndSend(messageDTO);
    }


    /**
     * 发送:违约单风控消息
     */
    public void sendViolateRiskBlack(String violateId, Integer operationType) {
        ViolateRiskBlackMessage message = new ViolateRiskBlackMessage();
        message.setViolateId(violateId);
        message.setOperationType(operationType);
        log.info("[sendViolateRiskBlack] 发送 违约单风控消息，message = {}", message);
        RocketMqMessageDTO messageDTO = RocketMqMessageDTO.builder()
                .topic(VIOLATE_TOPIC)
                .tag(VIOLATE_RISK_BLACK_TAG)
                .isDelay(false)
                .content(ObjectMapperUtil.toJsonStr(message))
                .bizId(message.getViolateId())
                .isSharding(true)
                .build();
        // 本地消息
        rocketMqMessageHelper.saveAndSend(messageDTO);
    }
}
