package com.pxb7.mall.trade.ass.domain;


import java.util.List;

import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import com.pxb7.mall.trade.ass.domain.model.AssRetrieveGameConfigDetailReqBO;
import com.pxb7.mall.trade.ass.domain.model.AssRetrieveGameConfigDetailRespBO;
import com.pxb7.mall.trade.ass.domain.mapping.AssRetrieveGameConfigDetailDomainMapping;
import com.pxb7.mall.trade.ass.infra.model.AssRetrieveGameConfigDetailReqPO;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.AssRetrieveGameConfigDetail;
import com.pxb7.mall.trade.ass.infra.repository.db.AssRetrieveGameConfigDetailRepository;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;


/**
 * 售后找回游戏配置明细表domain服务
 *
 * <AUTHOR>
 * @since 2025-07-30 13:49:18
 */
@Service
public class AssRetrieveGameConfigDetailDomainService {

    @Resource
    private AssRetrieveGameConfigDetailRepository assRetrieveGameConfigDetailRepository;

    public boolean insert(AssRetrieveGameConfigDetailReqBO.AddBO param) {
        AssRetrieveGameConfigDetailReqPO.AddPO addPO = AssRetrieveGameConfigDetailDomainMapping.INSTANCE.assRetrieveGameConfigDetailBO2AddPO(param);
        return assRetrieveGameConfigDetailRepository.insert(addPO);
    }

    public boolean update(AssRetrieveGameConfigDetailReqBO.UpdateBO param) {
        AssRetrieveGameConfigDetailReqPO.UpdatePO updatePO = AssRetrieveGameConfigDetailDomainMapping.INSTANCE.assRetrieveGameConfigDetailBO2UpdatePO(param);
        return assRetrieveGameConfigDetailRepository.update(updatePO);
    }

    public boolean deleteById(AssRetrieveGameConfigDetailReqBO.DelBO param) {
        AssRetrieveGameConfigDetailReqPO.DelPO delPO = AssRetrieveGameConfigDetailDomainMapping.INSTANCE.assRetrieveGameConfigDetailBO2DelPO(param);
        return assRetrieveGameConfigDetailRepository.deleteById(delPO);
    }

    public AssRetrieveGameConfigDetailRespBO.DetailBO findById(Long id) {
        AssRetrieveGameConfigDetail entity = assRetrieveGameConfigDetailRepository.findById(id);
        return AssRetrieveGameConfigDetailDomainMapping.INSTANCE.assRetrieveGameConfigDetailPO2DetailBO(entity);

    }

    public List<AssRetrieveGameConfigDetailRespBO.DetailBO> list(AssRetrieveGameConfigDetailReqBO.SearchBO param) {
        AssRetrieveGameConfigDetailReqPO.SearchPO searchPO = AssRetrieveGameConfigDetailDomainMapping.INSTANCE.assRetrieveGameConfigDetailBO2SearchPO(param);
        List<AssRetrieveGameConfigDetail> list = assRetrieveGameConfigDetailRepository.list(searchPO);
        return AssRetrieveGameConfigDetailDomainMapping.INSTANCE.assRetrieveGameConfigDetailPO2ListBO(list);
    }

    public Page<AssRetrieveGameConfigDetailRespBO.DetailBO> page(AssRetrieveGameConfigDetailReqBO.PageBO param) {
        AssRetrieveGameConfigDetailReqPO.PagePO pagePO = AssRetrieveGameConfigDetailDomainMapping.INSTANCE.assRetrieveGameConfigDetailBO2PagePO(param);
        Page<AssRetrieveGameConfigDetail> page = assRetrieveGameConfigDetailRepository.page(pagePO);
        return AssRetrieveGameConfigDetailDomainMapping.INSTANCE.assRetrieveGameConfigDetailPO2PageBO(page);
    }

}

