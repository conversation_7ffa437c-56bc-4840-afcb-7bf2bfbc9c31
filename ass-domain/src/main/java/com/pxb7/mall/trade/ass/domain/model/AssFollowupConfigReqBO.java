package com.pxb7.mall.trade.ass.domain.model;

import java.util.Date;
import java.time.*;
import java.math.BigDecimal;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.ToString;


/**
 * 跟进结果类型配置表(AssFollowupConfig)实体类
 *
 * <AUTHOR>
 * @since 2025-07-30 13:41:26
 */
public class AssFollowupConfigReqBO {

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class AddBO {

        /**
         * 业务主键
         */
        private String followupConfigId;

        /**
         * 售后类型1找回2纠纷
         */
        private Integer assType;

        /**
         * 跟进结果类型
         */
        private String resultDesc;

        /**
         * 售后进度展示文案
         */
        private String progressDesc;

        /**
         * 提醒用户进群文案
         */
        private String joinGroupDesc;

        /**
         * 发送通知类型1:追回账号，待买家换绑2:追回号款，待买家提供收款账号3:售后到期，待买家接收赔付
         */
        private Integer noticeType;

        /**
         * 排序
         */
        private Integer sort;

        /**
         * 创建人id
         */
        private String createUserId;

        /**
         * 更新人id
         */
        private String updateUserId;

    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class UpdateBO {

        /**
         * 自增id
         */
        private Long id;


        /**
         * 业务主键
         */
        private String followupConfigId;


        /**
         * 售后类型1找回2纠纷
         */
        private Integer assType;


        /**
         * 跟进结果类型
         */
        private String resultDesc;


        /**
         * 售后进度展示文案
         */
        private String progressDesc;


        /**
         * 提醒用户进群文案
         */
        private String joinGroupDesc;


        /**
         * 发送通知类型1:追回账号，待买家换绑2:追回号款，待买家提供收款账号3:售后到期，待买家接收赔付
         */
        private Integer noticeType;


        /**
         * 排序
         */
        private Integer sort;


        /**
         * 创建人id
         */
        private String createUserId;


        /**
         * 更新人id
         */
        private String updateUserId;


    }


    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class DelBO {
        private Long id;
    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class SearchBO {
        /**
         * 业务主键
         */
        private String followupConfigId;

        /**
         * 售后类型1找回2纠纷
         */
        private Integer assType;

        /**
         * 跟进结果类型
         */
        private String resultDesc;

        /**
         * 售后进度展示文案
         */
        private String progressDesc;

        /**
         * 提醒用户进群文案
         */
        private String joinGroupDesc;

        /**
         * 发送通知类型1:追回账号，待买家换绑2:追回号款，待买家提供收款账号3:售后到期，待买家接收赔付
         */
        private Integer noticeType;

        /**
         * 排序
         */
        private Integer sort;

        /**
         * 创建人id
         */
        private String createUserId;

        /**
         * 更新人id
         */
        private String updateUserId;

    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class PageBO {

        /**
         * 业务主键
         */
        private String followupConfigId;

        /**
         * 售后类型1找回2纠纷
         */
        private Integer assType;

        /**
         * 跟进结果类型
         */
        private String resultDesc;

        /**
         * 售后进度展示文案
         */
        private String progressDesc;

        /**
         * 提醒用户进群文案
         */
        private String joinGroupDesc;

        /**
         * 发送通知类型1:追回账号，待买家换绑2:追回号款，待买家提供收款账号3:售后到期，待买家接收赔付
         */
        private Integer noticeType;

        /**
         * 排序
         */
        private Integer sort;

        /**
         * 创建人id
         */
        private String createUserId;

        /**
         * 更新人id
         */
        private String updateUserId;


        /**
         * 页码，从1开始
         */
        private Integer pageIndex;
        /**
         * 每页数量
         */
        private Integer pageSize;

    }

}

