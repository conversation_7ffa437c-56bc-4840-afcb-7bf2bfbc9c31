package com.pxb7.mall.trade.ass.domain;

import static com.pxb7.mall.trade.ass.client.enums.AssScheduleNode.CREATE_WORK_ORDER;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.pxb7.mall.components.idgen.IdGen;
import com.pxb7.mall.trade.ass.client.enums.AssScheduleNode;
import com.pxb7.mall.trade.ass.client.enums.AssWoType;
import com.pxb7.mall.trade.ass.domain.model.reqeust.AssScheduleReqBO;
import com.pxb7.mall.trade.ass.domain.model.response.AssScheduleLogRespBO;
import com.pxb7.mall.trade.ass.infra.repository.db.AssScheduleLogRepository;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.AssScheduleLog;

import cn.hutool.core.collection.CollUtil;
import jakarta.annotation.Resource;

/**
 * <AUTHOR>
 */
@Service
public class AssScheduleLogDomainService {

    @Resource
    private IdGen idGen;
    @Resource
    private AssScheduleLogRepository assScheduleLogRepository;

    public void record(AssScheduleReqBO bo, String userId) {
        AssScheduleLog assScheduleLog = new AssScheduleLog();
        assScheduleLog.setScheduleLogId(idGen.nextId() + "");
        assScheduleLog.setScheduleId(bo.getScheduleId());
        assScheduleLog.setNodeId(bo.getNode().name());
        assScheduleLog.setData(bo.getData());
        assScheduleLog.setIsShow(bo.getNode().isShow());
        String nodeDesc = bo.getNode().getDesc() + bo.getAssType().getDesc();
        assScheduleLog.setNodeDesc(nodeDesc);
        assScheduleLog.setSourceType(bo.getSourceType());
        assScheduleLog.setCreateUserId(userId);
        assScheduleLogRepository.save(assScheduleLog);
    }

    public boolean record(String scheduleId, Integer assType, String userId, Integer sourceType) {
        AssScheduleLog assScheduleLog = new AssScheduleLog();
        assScheduleLog.setScheduleLogId(idGen.nextId() + "");
        assScheduleLog.setScheduleId(scheduleId);
        assScheduleLog.setNodeId(AssScheduleNode.INITIATE_AFTER_SALE.name());
        assScheduleLog.setIsShow(AssScheduleNode.INITIATE_AFTER_SALE.isShow());
        AssWoType assWoType = AssWoType.fromCode(assType);
        assScheduleLog.setNodeDesc(AssScheduleNode.INITIATE_AFTER_SALE.getDesc() + assWoType.getDesc());
        assScheduleLog.setSourceType(sourceType);
        assScheduleLog.setCreateUserId(userId);
        assScheduleLog.setUpdateUserId(userId);
        return assScheduleLogRepository.save(assScheduleLog);
    }

    public void record(String scheduleId, String result, AssScheduleNode node, String userId, Integer sourceType,
        String data) {
        AssScheduleLog asl = new AssScheduleLog();
        asl.setScheduleLogId(idGen.nextId() + "");
        asl.setScheduleId(scheduleId);
        asl.setData(data);
        asl.setNodeId(node.name());
        asl.setNodeDesc(node.getDesc() + result);
        asl.setIsShow(node.isShow());
        asl.setSourceType(sourceType);
        asl.setCreateUserId(userId);
        asl.setUpdateUserId(userId);
        assScheduleLogRepository.save(asl);
    }

    public void add(String scheduleId, AssScheduleNode node, String data, AssWoType assWoType, String userId,
        String extendDesc) {
        AssScheduleLog asl = new AssScheduleLog();
        asl.setScheduleLogId(idGen.nextId() + "");
        asl.setScheduleId(scheduleId);
        asl.setData(data);
        asl.setNodeId(node.name());
        String nodeDesc;
        if (CREATE_WORK_ORDER == node) {
            nodeDesc = node.getDesc() + assWoType.getDesc() + extendDesc;
        } else {
            nodeDesc = node.getDesc() + extendDesc;
        }
        asl.setNodeDesc(nodeDesc);
        asl.setIsShow(node.isShow());
        asl.setCreateUserId(userId);
        asl.setUpdateUserId(userId);
        assScheduleLogRepository.save(asl);
    }

    public List<AssScheduleLogRespBO> find(String scheduleId) {
        return assScheduleLogRepository.lambdaQuery().eq(AssScheduleLog::getScheduleId, scheduleId)
            .eq(AssScheduleLog::getIsShow, Boolean.TRUE).list().stream()
            .map(asl -> new AssScheduleLogRespBO(asl.getNodeId(), asl.getNodeDesc(), asl.getCreateTime(),
                asl.getCreateUserId(), asl.getData()))
            .toList();
    }

    public List<AssScheduleLog> findListByScheduleId(String scheduleId) {
        if (StringUtils.isBlank(scheduleId)) {
            return CollUtil.newArrayList();
        }
        return assScheduleLogRepository.findListByScheduleId(scheduleId);
    }

    public boolean delByScheduleIdAndNodeIds(String scheduleId, List<String> nodeIds) {
        return assScheduleLogRepository.delByScheduleIdAndNodeIds(scheduleId, nodeIds);
    }

    public boolean delByScheduleId(String scheduleId) {
        return assScheduleLogRepository.delByScheduleId(scheduleId);
    }

}
