package com.pxb7.mall.trade.ass.domain.model;

import java.util.Date;
import java.time.*;
import java.math.BigDecimal;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.ToString;


/**
 * 售后找回游戏配置明细表(AssRetrieveGameConfigDetail)实体类
 *
 * <AUTHOR>
 * @since 2025-07-30 13:49:18
 */
public class AssRetrieveGameConfigDetailReqBO {

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class AddBO {

        /**
         * 业务主键
         */
        private String detailId;

        /**
         * ass_retrieve_game_config.game_config_id
         */
        private String gameConfigId;

        /**
         * 找回处理时间单位天
         */
        private Integer retrieveDays;

        /**
         * 游戏id
         */
        private String gameId;

        /**
         * 用户端进群开关 1:已开启 0:未开启
         */
        private Boolean groupOpen;

        /**
         * 创建人id
         */
        private String createUserId;

        /**
         * 更新人id
         */
        private String updateUserId;

    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class UpdateBO {

        /**
         * 自增id
         */
        private Long id;


        /**
         * 业务主键
         */
        private String detailId;


        /**
         * ass_retrieve_game_config.game_config_id
         */
        private String gameConfigId;


        /**
         * 找回处理时间单位天
         */
        private Integer retrieveDays;


        /**
         * 游戏id
         */
        private String gameId;


        /**
         * 用户端进群开关 1:已开启 0:未开启
         */
        private Boolean groupOpen;


        /**
         * 创建人id
         */
        private String createUserId;


        /**
         * 更新人id
         */
        private String updateUserId;


    }


    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class DelBO {
        private Long id;
    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class SearchBO {
        /**
         * 业务主键
         */
        private String detailId;

        /**
         * ass_retrieve_game_config.game_config_id
         */
        private String gameConfigId;

        /**
         * 找回处理时间单位天
         */
        private Integer retrieveDays;

        /**
         * 游戏id
         */
        private String gameId;

        /**
         * 用户端进群开关 1:已开启 0:未开启
         */
        private Boolean groupOpen;

        /**
         * 创建人id
         */
        private String createUserId;

        /**
         * 更新人id
         */
        private String updateUserId;

    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class PageBO {

        /**
         * 业务主键
         */
        private String detailId;

        /**
         * ass_retrieve_game_config.game_config_id
         */
        private String gameConfigId;

        /**
         * 找回处理时间单位天
         */
        private Integer retrieveDays;

        /**
         * 游戏id
         */
        private String gameId;

        /**
         * 用户端进群开关 1:已开启 0:未开启
         */
        private Boolean groupOpen;

        /**
         * 创建人id
         */
        private String createUserId;

        /**
         * 更新人id
         */
        private String updateUserId;


        /**
         * 页码，从1开始
         */
        private Integer pageIndex;
        /**
         * 每页数量
         */
        private Integer pageSize;

    }

}

