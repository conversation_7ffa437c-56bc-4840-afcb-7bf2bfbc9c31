package com.pxb7.mall.trade.ass.domain.helper;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.StrUtil;
import com.pxb7.mall.trade.ass.domain.command.AbstractCommand;
import com.pxb7.mall.trade.ass.domain.command.CommandEnum;
import com.pxb7.mall.trade.ass.domain.command.CommandFactory;
import com.pxb7.mall.trade.ass.domain.command.sub.CommandDTO;
import com.pxb7.mall.trade.ass.domain.mapping.CommandMapping;
import com.pxb7.mall.trade.ass.domain.thread.ThreadExecutorBuilder;
import com.pxb7.mall.trade.ass.domain.thread.ThreadPoolEnum;
import com.pxb7.mall.trade.ass.infra.repository.db.CommandRepository;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.CommandDO;
import com.pxb7.mall.trade.ass.infra.util.CommandStatusEnum;
import com.pxb7.mall.trade.ass.infra.util.IdGenUtil;
import com.pxb7.mall.trade.ass.infra.util.PxTransactionUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.shardingsphere.infra.hint.HintManager;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class CommandHelper {
    private static final int PAGE_SIZE = 100;

    private static final int INTERVAL_MINUTES = -2;

    @Resource
    private CommandRepository commandRepository;

    @Value("${spring.profiles.active}")
    private String env;

    public void process(String databaseName) {
        log.info("[ASS异步任务] start to process command, databaseName:{}", databaseName);
        try (HintManager hintManager = HintManager.getInstance()) {
            // 指定数据源(Sharding-JDBC 会路由到指定的数据源)
            hintManager.setDataSourceName(databaseName);

            process();

        }
        log.info("[ASS异步任务] end to process command, databaseName:{}", databaseName);
    }

    /**
     * 指令处理
     */
    public void process() {
        //查询XX分钟之前的数据，避免查询到正在执行的任务
        String end = LocalDateTimeUtil.formatNormal(
                LocalDateTimeUtil.offset(LocalDateTime.now(), INTERVAL_MINUTES, ChronoUnit.MINUTES));
        int commandStatus = CommandStatusEnum.WAIT.getValue();
        //起始ID
        Long startId = 0L;
        int total = commandRepository.queryCount(commandStatus, null, end, CommandEnum.getTypeList(), env).intValue();
        if (total == 0) {
            log.info("[ASS异步任务] there is no command need to be processed!");
            return;
        }
        //计算有多少页
        int pages = (total + PAGE_SIZE - 1) / PAGE_SIZE;
        //遍历每页数据
        for (int i = 1; i <= pages; i++) {
            List<CommandDO> commandList = commandRepository.listCommands(startId, commandStatus, null,
                    end, PAGE_SIZE, CommandEnum.getTypeList(), env);
            //设置起始id，列表是排序了的，所以可以取集合最后一个元素的id
            startId = commandList.get(commandList.size() - 1).getId();
            //处理每条消息
            for (CommandDO commandDO : commandList) {
                //执行异步任务
                CommandDTO commandDTO = CommandMapping.INSTANCE.to(commandDO);
                execute(commandDTO);
            }
        }
    }

    /**
     * 执行指令
     *
     * @param commandDTO 指令DTO
     */
    public void execute(CommandDTO commandDTO) {
        ThreadExecutorBuilder.build(ThreadPoolEnum.CORE_BIZ).execute(() -> {
            //任务处理
            AbstractCommand<?> commandHandler = CommandFactory.getInstance(commandDTO.getCommandType());
            if (null == commandHandler) {
                log.error("[ASS] unsupported command, {}", commandDTO);
                return;
            }
            commandHandler.handle(commandDTO);
        });
    }

    /**
     * 创建指令
     *
     * @param commandType    指令类型
     * @param commandContent 指令内容
     */
    public CommandDO create(CommandEnum commandType, String bizOrderId, String commandContent, LocalDateTime executeTime) {
        CommandDO commandDO = new CommandDO();
        commandDO.setBizId(bizOrderId);
        commandDO.setCommandId(IdGenUtil.getShardingColumnId(bizOrderId));
        commandDO.setCommandType(commandType.getCode());
        commandDO.setCommandContent(commandContent);
        commandDO.setRemark(commandType.getDesc());
        if (StrUtil.isNotBlank(env)) {
            commandDO.setEnv(env);
        }
        if (Objects.nonNull(executeTime)) {
            commandDO.setExecuteTime(executeTime);
        }
        return commandDO;
    }

    /**
     * 创建指令
     *
     * @param commandType    指令类型
     * @param commandContent 指令内容
     */
    public CommandDO createAndSave(CommandEnum commandType, String bizOrderId, String commandContent) {
        CommandDO commandDO = create(commandType, bizOrderId, commandContent, null);
        commandRepository.save(commandDO);
        return commandDO;
    }

    public CommandDO createAndSave(CommandEnum commandType, String bizOrderId, String commandContent, LocalDateTime executeTime) {
        CommandDO commandDO = create(commandType, bizOrderId, commandContent, executeTime);
        commandRepository.save(commandDO);
        return commandDO;
    }

    public void saveAndExecute(CommandEnum commandType, String bizOrderId, String commandContent) {
        CommandDO andSave = createAndSave(commandType, bizOrderId, commandContent);
        PxTransactionUtils.execAfterCommit(() -> execute(CommandMapping.INSTANCE.to(andSave)));
    }
}
