package com.pxb7.mall.trade.ass.domain.model;

import java.util.Date;
import java.time.*;
import java.math.BigDecimal;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.ToString;


/**
 * 驳回原因配置表(AssRejectReasonConfig)实体类
 *
 * <AUTHOR>
 * @since 2025-07-30 13:49:17
 */
public class AssRejectReasonConfigRespBO {


    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class DetailBO {
        /**
         * 自增id
         */
        private Long id;
        /**
         * 业务主键
         */
        private String reasonConfigId;
        /**
         * 售后类型1找回2纠纷
         */
        private Integer assType;
        /**
         * 驳回原因文案
         */
        private String reason;
        /**
         * 用户测文案
         */
        private String userDesc;
        /**
         * 排序
         */
        private Integer sort;
        /**
         * 创建人id
         */
        private String createUserId;
        /**
         * 创建时间
         */
        private LocalDateTime createTime;
        /**
         * 更新人id
         */
        private String updateUserId;
        /**
         * 更新时间
         */
        private LocalDateTime updateTime;
        /**
         * 是否删除 1:已删除 0:未删除
         */
        private Boolean deleted;
    }
}

