package com.pxb7.mall.trade.ass.domain.violate.request;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class ViolateDeductionToWalletReqBO {
    /**
     * 订单ID
     */
    private String orderItemId;
    /**
     * 违约收款单id
     */
    private String recordId;
    /**
     * 违约方userid
     */
    private String violateUserId;
    /**
     * 违约金额
     */
    private Long violateAmount;

}