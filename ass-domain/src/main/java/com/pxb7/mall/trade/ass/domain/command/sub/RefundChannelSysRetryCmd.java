package com.pxb7.mall.trade.ass.domain.command.sub;

import com.alibaba.cola.exception.BizException;
import com.pxb7.mall.components.redis.redisson.RLockUtils;
import com.pxb7.mall.trade.ass.domain.command.AbstractCommand;
import com.pxb7.mall.trade.ass.domain.command.CommandEnum;
import com.pxb7.mall.trade.ass.domain.refund.RefundInvokeService;
import com.pxb7.mall.trade.ass.domain.refund.model.RefundInvokeReqBO;
import com.pxb7.mall.trade.order.client.dto.ErrorCode;
import com.pxb7.mall.trade.order.client.lock.PayLockConstant;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

@Component
@Slf4j
public class RefundChannelSysRetryCmd extends AbstractCommand<RefundInvokeReqBO> {
    @Resource
    private RefundInvokeService refundInvokeService;

    @Override
    protected Class<?> getCommandClass() {
        return RefundInvokeReqBO.class;
    }

    @Override
    protected CommandEnum getCommandType() {
        return CommandEnum.REFUND_CHANEL_RETRY;
    }

    @Override
    public void execute(RefundInvokeReqBO extVoucherReqBo) {
        log.info("[ASS异步任务] [渠道退款重试] 开始执行 execute data:{}", extVoucherReqBo);

        extVoucherReqBo.setRetry(true);
        String lockKey = String.format(PayLockConstant.PREFIX, extVoucherReqBo.getPayLogId());
        RLockUtils.of(lockKey,
                        () -> {
                            refundInvokeService.doInvokeRefund(extVoucherReqBo);
                            return null;
                        })
                .withWaitTime(6).withTimeUnit(TimeUnit.SECONDS)
                .orElseThrow(() -> new BizException(ErrorCode.REPEAT_OPERATION.getErrCode(), ErrorCode.REPEAT_OPERATION.getErrDesc()));
    }
}
