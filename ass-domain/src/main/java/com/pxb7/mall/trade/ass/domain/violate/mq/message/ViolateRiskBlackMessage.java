package com.pxb7.mall.trade.ass.domain.violate.mq.message;

import com.pxb7.mall.trade.ass.client.enums.RiskBlackOperateType;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class ViolateRiskBlackMessage {
    /**
     * 违约单ID
     */
    private String violateId;

    /**
     * 操作类型: 1-添加,2-移除
     *
     * @see RiskBlackOperateType
     */
    private Integer operationType;
}
