package com.pxb7.mall.trade.ass.domain.config;

import com.yeepay.yop.sdk.base.auth.credentials.provider.YopFixedCredentialsProvider;
import com.yeepay.yop.sdk.base.config.YopAppConfig;
import com.yeepay.yop.sdk.config.enums.CertStoreType;
import com.yeepay.yop.sdk.config.provider.file.YopCertConfig;
import com.yeepay.yop.sdk.security.CertTypeEnum;

import java.util.ArrayList;
import java.util.List;

public class CustomFixedCredentialsProvider extends YopFixedCredentialsProvider {
    private final String rasPrivate;

    public CustomFixedCredentialsProvider(String rasPrivate) {
        this.rasPrivate = rasPrivate;
    }

    @Override
    protected YopAppConfig loadAppConfig(String appKey) {
        YopAppConfig yopAppConfig = new YopAppConfig();
        yopAppConfig.setAppKey(appKey);

        // RSA example
        YopCertConfig rsaCertConfig = new YopCertConfig();
        rsaCertConfig.setCertType(CertTypeEnum.RSA2048);
        rsaCertConfig.setStoreType(CertStoreType.STRING);
        rsaCertConfig.setAppKey(appKey);
        rsaCertConfig.setValue(rasPrivate);

        // load into sdk config
        List<YopCertConfig> isvPrivateKeys = new ArrayList<>();
        isvPrivateKeys.add(rsaCertConfig);
        yopAppConfig.setIsvPrivateKey(isvPrivateKeys);


        return yopAppConfig;
    }
}
