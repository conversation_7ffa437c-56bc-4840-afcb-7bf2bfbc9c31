package com.pxb7.mall.trade.ass.domain.refund.model;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class YeePayToRefundResponse {
    private String code;
    private BigDecimal residualAmount;
    private String orderId;
    private BigDecimal refundMerchantFee;

    private String deductionFundDate;

    private String refundRequestId;
    private String message;

    private String refundCsFinishDate;

    private String uniqueRefundNo;
    private String parentMerchantNo;
    private String refundAccountDetail; // JSON 数组字符串，可转换为对象列表

    private String refundRequestDate;

    private BigDecimal refundAmount;
    private String status;
    private String merchantNo;
}
