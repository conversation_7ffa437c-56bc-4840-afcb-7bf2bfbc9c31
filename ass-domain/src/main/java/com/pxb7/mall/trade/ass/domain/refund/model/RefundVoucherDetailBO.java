package com.pxb7.mall.trade.ass.domain.refund.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class RefundVoucherDetailBO {

    private Long id;
    /**
     * 退款单业务主键
     */
    private String refundVoucherId;
    /**
     * 订单行id
     */
    private String orderItemId;
    /**
     * 商品号价退款金额
     */
    private Long productAmount;
    /**
     * 包赔金额
     */
    private Long indemnityAmount;
    /**
     * 手续费金额
     */
    private Long feeAmount;
    /**
     * 买家违约金额
     */
    private Long violateAmount;
    /**
     * 红包
     */
    private Long redPacketAmount;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    /**
     * 是否删除
     */
    private Boolean deleted;
    /**
     * 包赔承担方
     */
    private Integer indemnityResponsibleUser;

}
