package com.pxb7.mall.trade.ass.domain.violate.request;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class ViolateCreateReqBO {
    /**
     * 订单ID
     */
    private String orderItemId;
    /**
     * 退款单ID
     */
    private String refundVoucherId;
    /**
     * 违约方: 1买家、2卖家
     */
    private Integer violateUserType;
    /**
     * 违约方UserId
     */
    private String violateUserId;
    /**
     * 守约方UserId
     */
    private String promiseUserId;
    /**
     * 违约金
     */
    private Long violateAmount;
    /**
     * 守约金
     */
    private Long promiseAmount;
    /**
     * 状态
     */
    private Integer violateStatus;

    private String createUserId;
    private String createUserName;
    private String updateUserId;
    private String updateUserName;

}