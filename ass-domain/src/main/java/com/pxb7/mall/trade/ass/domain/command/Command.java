package com.pxb7.mall.trade.ass.domain.command;


import com.fasterxml.jackson.core.JsonProcessingException;
import com.pxb7.mall.trade.ass.domain.command.sub.CommandDTO;

/**
 * 指令接口
 *
 * @param <T> 数据对象
 * <AUTHOR>
 */
public interface Command<T> {

    /**
     * 解析指令数据
     *
     * @param commandDTO 指令信息
     * @return T
     */
    T parseData(CommandDTO commandDTO) throws JsonProcessingException;

    /**
     * 执行指令
     *
     * @param data 数据
     */
    void execute(T data);


}
