package com.pxb7.mall.trade.ass.domain.thread;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 线程池枚举，定义线程池 线程数量等
 */
@Getter
@AllArgsConstructor
public enum ThreadPoolEnum {

    /**
     * 通用业务线程池
     */
    COMMON_BIZ("common_biz", 10, 30, 1024, "通用业务线程池", ThreadPoolTypeEnum.NORMAL);

    private final String biz;

    private final int corePoolSize;

    private final int maxPoolSize;

    private final int maxValue;

    private final String desc;

    /**
     * 线程池类型
     */
    private final ThreadPoolTypeEnum threadPoolType;

}
