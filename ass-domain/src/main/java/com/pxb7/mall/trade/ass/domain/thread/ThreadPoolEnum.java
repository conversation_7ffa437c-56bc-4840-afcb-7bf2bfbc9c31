package com.pxb7.mall.trade.ass.domain.thread;

import lombok.Getter;

/**
 * 线程池枚举，定义线程池 线程数量等
 *
 * <AUTHOR>
 */
@Getter
public enum ThreadPoolEnum {

    /**
     * 核心业务线程池
     */
    CORE_BIZ("core_biz", 10, 30, 9999, "核心业务线程池"),

    /**
     * 通用业务线程池
     */
    COMMON_BIZ("common_biz", 10, 30, 1024, "通用业务线程池"),
    ;

    private final String biz;

    private final int poolSize;

    private final int maxPoolSize;

    private final int maxValue;

    private final String desc;


    ThreadPoolEnum(String biz, int poolSize, int maxPoolSize, int maxValue, String desc) {
        this.biz = biz;
        this.poolSize = poolSize;
        this.maxPoolSize = maxPoolSize;
        this.maxValue = maxValue;
        this.desc = desc;
    }
}
