package com.pxb7.mall.trade.ass.domain.model.reqeust;

import java.io.Serial;
import java.io.Serializable;

import com.pxb7.mall.trade.ass.client.enums.AssLogAddWay;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 记录日志
 *
 * <AUTHOR>
 * @since: 2024-09-21 15:13
 **/
@Data
@Accessors(chain = true)
public class RecordBO implements Serializable {

    @Serial
    private static final long serialVersionUID = 9059060410775979695L;
    /**
     * 工单ID
     */
    private String workOrderId;
    /**
     * 售后类型1找回2纠纷
     */
    private Integer assType;
    /**
     * 标题
     */
    private String title;
    /**
     * 操作内容
     */
    private String content;
    /**
     * 图片
     */
    private String image;
    /**
     * 操作人
     */
    private String operatorId;

    /**
     * 日志添加方式1:系统自动产生的日志 2:手动添加的日志
     * 
     * @see AssLogAddWay
     */
    private Integer addWay = AssLogAddWay.AUTO.getCode();

    /**
     * 飞书名称
     */
    private String feiShuName;

}
