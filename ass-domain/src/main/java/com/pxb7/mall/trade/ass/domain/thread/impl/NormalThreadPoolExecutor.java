package com.pxb7.mall.trade.ass.domain.thread.impl;

import com.pxb7.mall.trade.ass.domain.thread.ExecutorRunnable;
import java.util.concurrent.*;

/**
 * <AUTHOR>
 * @date 2025/6/25 19:59
 * @desc 正常线程池
 */
public class NormalThreadPoolExecutor extends ThreadPoolExecutor {

    /**
     * 构造函数
     */
    public NormalThreadPoolExecutor(int poolSize, int maxPoolSize, long keepAliveTime, TimeUnit unit, int queueSize, ThreadFactory threadFactory) {
        super(poolSize, maxPoolSize, keepAliveTime, unit, new LinkedBlockingQueue<>(queueSize), threadFactory, rejectHandler);

    }

    /**
     * 自定义拒绝策略
     */
    private static final RejectedExecutionHandler rejectHandler = (r, executor) -> {
        if (r instanceof ExecutorRunnable) {
            // ExecutorRunnable使用自定义策略
            ((ExecutorRunnable) r).rejectedExecution(executor);
        } else {
            // 使用当前线程执行
            new CallerRunsPolicy().rejectedExecution(r, executor);
        }
    };

}
