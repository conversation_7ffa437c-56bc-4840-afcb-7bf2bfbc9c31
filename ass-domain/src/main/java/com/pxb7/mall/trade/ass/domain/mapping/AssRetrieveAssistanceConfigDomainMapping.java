package com.pxb7.mall.trade.ass.domain.mapping;

import com.pxb7.mall.trade.ass.domain.model.AssRetrieveAssistanceConfigReqBO;
import com.pxb7.mall.trade.ass.domain.model.AssRetrieveAssistanceConfigRespBO;
import com.pxb7.mall.trade.ass.infra.model.AssRetrieveAssistanceConfigReqPO;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.AssRetrieveAssistanceConfig;

import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;


@Mapper
public interface AssRetrieveAssistanceConfigDomainMapping {

    AssRetrieveAssistanceConfigDomainMapping INSTANCE = Mappers.getMapper(AssRetrieveAssistanceConfigDomainMapping.class);


    AssRetrieveAssistanceConfigReqPO.AddPO assRetrieveAssistanceConfigBO2AddPO(AssRetrieveAssistanceConfigReqBO.AddBO source);

    AssRetrieveAssistanceConfigReqPO.UpdatePO assRetrieveAssistanceConfigBO2UpdatePO(AssRetrieveAssistanceConfigReqBO.UpdateBO source);

    AssRetrieveAssistanceConfigReqPO.DelPO assRetrieveAssistanceConfigBO2DelPO(AssRetrieveAssistanceConfigReqBO.DelBO source);

    AssRetrieveAssistanceConfigReqPO.SearchPO assRetrieveAssistanceConfigBO2SearchPO(AssRetrieveAssistanceConfigReqBO.SearchBO source);

    AssRetrieveAssistanceConfigReqPO.PagePO assRetrieveAssistanceConfigBO2PagePO(AssRetrieveAssistanceConfigReqBO.PageBO source);

    AssRetrieveAssistanceConfigRespBO.DetailBO assRetrieveAssistanceConfigPO2DetailBO(AssRetrieveAssistanceConfig source);

    List<AssRetrieveAssistanceConfigRespBO.DetailBO> assRetrieveAssistanceConfigPO2ListBO(List<AssRetrieveAssistanceConfig> source);

    Page<AssRetrieveAssistanceConfigRespBO.DetailBO> assRetrieveAssistanceConfigPO2PageBO(Page<AssRetrieveAssistanceConfig> source);

}


