package com.pxb7.mall.trade.ass.domain.model.response;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Copyright (C), 2002-2024, 螃蟹游戏服务网
 *
 * @fileName: ComplaintWorkOrderLogRespBO.java
 * @description:
 * @author: guo<PERSON><PERSON><PERSON>
 * @email: <EMAIL>
 * @date: 2024/9/23 10:46
 * @history: //修改记录
 * <author> <time> <version> <desc>
 * 修改人姓名 修改时间 版本号 描述
 */
@Data
public class ComplaintWorkOrderLogRespBO implements Serializable {
    /**
     * 工单编号
     */
    private String complaintWorkId;
    /**
     * 日志类型（1:创建；2:转交；3:完结）
     */
    private Integer logType;
    /**
     * 当前处理人ID
     */
    private String currentProcessorId;
    /**
     * 被转交人id(log_type=2时有值)
     */
    private String transfereeId;

    /**
     * 转交备注(log_type=2时有值)
     */
    private String transferNote;
    /**
     * 修正内容
     */
    private String modification;
    /**
     * 操作时间
     */
    private LocalDateTime createTime;

}
