package com.pxb7.mall.trade.ass.domain.model.reqeust.mq;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

/**
 * Copyright (C), 2002-2025, 螃蟹游戏服务网
 *
 * @fileName: AfcWoStatusChange.java
 * @description: 售后工单状态变更
 * @author: guo<PERSON><PERSON><PERSON>
 * @email: <EMAIL>
 * @date: 2025/4/21 17:19
 * @history: //修改记录
 * <author> <time> <version> <desc>
 * 修改人姓名 修改时间 版本号 描述
 */
@Data
@AllArgsConstructor
@Builder
public class AfcWoStatusChange {

    /**
     * 售后工单ID
     */
    private String workOrderId;
    /**
     * 房间ID
     */
    private String roomId;

    /**
     * 订单id
     */
    private String orderItemId;

    /**
     * 工单类型 1:找回 2:纠纷 3:投诉
     */
    private Integer workOrderType;

    /**
     * 售后工单状态 1:待处理 2:已完结 3:关闭
     */
    private Integer workOrderStatus;
}
