package com.pxb7.mall.trade.ass.domain.order.model;


import lombok.Data;

import java.time.LocalDateTime;

@Data
public class OrderItemExtendBO {

    /**
     * 主键
     */
    private Long id;
    /**
     * 子订单id
     */
    private String orderItemId;
    /**
     * 交易客服ID
     */
    private String tradeCustomerId;
    /**
     * 交付客服id
     */
    private String deliveryCustomerId;
    /**
     * 交付客服名称
     */
    private String deliveryCustomer;
    /**
     * 交易客服名称
     */
    private String tradeCustomer;
    /**
     * 商品名称
     */
    private String productName;
    /**
     * 商品图片
     */
    private String productPic;
    /**
     * 商品属性
     */
    private String productAttr;
    /**
     * 账号信息
     */
    private String gameAccount;
    /**
     * 商品亮点
     */
    private String productHighlight;
    /**
     * 游戏名称
     */
    private String gameName;
    /**
     * 游戏属性
     */
    private String gameAttr;
    /**
     * 是否顺手买商品 0否 1是
     */
    private Boolean easyBuy;
    /**
     * 咨询房间id, 私聊房间
     */
    private String roomId;
    /**
     * 交付群聊房间id, 智能交付是新群交付, 中介订单是原群交付
     */
    private String deliveryRoomId;
    /**
     * 买家手机号
     */
    private String buyerPhone;
    /**
     * 买家身份 1散户 2号商
     */
    private Integer buyerUserType;
    /**
     * 卖家手机号
     */
    private String sellerPhone;
    /**
     * 卖家身份 0系统 1散户 2号商
     */
    private Integer sellerUserType;
    /**
     * 买商品的时候,它是否有生效的诚心卖服务, 0没有 1有
     */
    private Boolean sinceritySell;
    /**
     * 买家的商家id
     */
    private String buyerMerchantId;
    /**
     * 买家的商家部门id
     */
    private String buyerMerchantDeptId;
    /**
     * 卖家的商家id
     */
    private String sellerMerchantId;
    /**
     * 卖家的商家部门id
     */
    private String sellerMerchantDeptId;
    /**
     * 是否签署合同了 0否 1是
     */
    private Boolean addContract;
    /**
     * 买家确认收货时间
     */
    private LocalDateTime buyerReceiptTime;
    /**
     * 自动收货最晚时间
     */
    private LocalDateTime autoReceiptTime;
    /**
     * 买家订单备注
     */
    private String buyerRemark;
    /**
     * 卖家订单备注
     */
    private String sellerRemark;
    /**
     * 客服订单备注
     */
    private String customerRemark;
    /**
     * 是否续包
     */
    private Boolean guarantee;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    /**
     * 逻辑删除，0:删除，1:正常
     */
    private Boolean deleted;
    /**
     * 商品编码
     */
    private String productUniqueNo;
    /**
     * 充值卖家状态 1 待发货 2 准备发货 3 已发货
     */
    private Integer rechargeSellerStatus;
}
