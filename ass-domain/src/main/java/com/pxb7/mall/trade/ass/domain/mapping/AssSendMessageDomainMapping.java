package com.pxb7.mall.trade.ass.domain.mapping;

import com.pxb7.mall.common.client.request.message.SpecialRedirectUrlDTO;
import com.pxb7.mall.trade.ass.infra.model.InternalMsgRedirectReqPO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 功能描述:消息通知 
 * 作者：白春韬 
 * 创建日期：2025/08/08 
 * 公司名称：金华博淳网络科技有限公司 
 * 域名： www.pxb7.com
 */
@Mapper
public interface AssSendMessageDomainMapping {
    AssWorkOrderDomainMapping INSTANCE = Mappers.getMapper(AssWorkOrderDomainMapping.class);

    List<SpecialRedirectUrlDTO> internalMsgRedirectReqPO2SpecialRedirectUrlDTO(List<InternalMsgRedirectReqPO> urlPO);
}
