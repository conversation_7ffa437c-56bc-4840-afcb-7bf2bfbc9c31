package com.pxb7.mall.trade.ass.domain.refund.message.provider;

import com.pxb7.mall.trade.ass.infra.config.nacos.NacosYamlConfigListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class MockRefundAfterListener implements NacosYamlConfigListener {

    private String afterInvoke;

    @Override
    public void onRefresh(Object newConfig) {
        this.afterInvoke = String.valueOf(newConfig);
    }

    @Override
    public String configPath() {
        return "mock_refund_invoke_after";
    }


    public String getAfterInvoke() {
        return afterInvoke;
    }
}
