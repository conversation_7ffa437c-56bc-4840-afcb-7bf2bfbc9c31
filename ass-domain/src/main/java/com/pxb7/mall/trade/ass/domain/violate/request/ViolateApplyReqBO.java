package com.pxb7.mall.trade.ass.domain.violate.request;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class ViolateApplyReqBO {
    /**
     * 订单ID
     */
    private String orderItemId;
    /**
     * 退款单ID
     */
    private String refundVoucherId;
    /**
     * 违约方:1买家 2卖家
     */
    private Integer violateUserType;
    /**
     * 违约方userid
     */
    private String violateUserId;
    /**
     * 守约方userid
     */
    private String promiseUserId;
    /**
     * 违约金额
     */
    private Long violateAmount;
    /**
     * 守约金
     */
    private Long promiseAmount;
    private String createUserId;
    private String createUserName;

}