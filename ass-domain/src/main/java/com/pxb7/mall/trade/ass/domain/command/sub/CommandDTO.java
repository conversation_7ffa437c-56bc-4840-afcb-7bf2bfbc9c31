package com.pxb7.mall.trade.ass.domain.command.sub;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 指令表
 *
 * <AUTHOR>
 */
@Data
public class CommandDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = 6827694945415884476L;

    /**
     * 指令ID
     */
    private String commandId;

    /**
     * 业务单据ID
     */
    private String bizId;

    /**
     * 指令类型
     */
    private String commandType;

    /**
     * 指令数据内容
     */
    private String commandContent;

    /**
     * 指令状态 0:未处理 1:已处理 2:处理异常
     *
     * @see com.pxb7.mall.trade.ass.infra.util.CommandStatusEnum
     */
    private Integer commandStatus;

    /**
     * 失败次数
     */
    private Integer failCount;
}
