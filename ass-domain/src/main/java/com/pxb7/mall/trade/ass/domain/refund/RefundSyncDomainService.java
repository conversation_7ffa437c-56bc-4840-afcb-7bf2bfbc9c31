package com.pxb7.mall.trade.ass.domain.refund;

import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.ImmutableSet;
import com.pxb7.mall.trade.ass.client.dto.model.refund.PayChannelEnum;
import com.pxb7.mall.trade.ass.client.dto.model.refund.RefundTradeStatusEnum;
import com.pxb7.mall.trade.ass.domain.PayCompanyAccountDomainService;
import com.pxb7.mall.trade.ass.domain.PayLogDomainService;
import com.pxb7.mall.trade.ass.domain.refund.message.provider.RefundMessageService;
import com.pxb7.mall.trade.ass.domain.refund.model.RefundCallBackBO;
import com.pxb7.mall.trade.ass.domain.refund.model.RefundQueryReqBO;
import com.pxb7.mall.trade.ass.domain.refund.model.RefundQueryRespBO;
import com.pxb7.mall.trade.ass.infra.annotation.RedisLock;
import com.pxb7.mall.trade.ass.infra.constant.PayConstant;
import com.pxb7.mall.trade.ass.infra.model.mq.RefundNotifyMessage;
import com.pxb7.mall.trade.ass.infra.model.mq.RefundQueryMessage;
import com.pxb7.mall.trade.ass.infra.remote.service.http.JdApiService;
import com.pxb7.mall.trade.ass.infra.repository.db.PayChannelRepository;
import com.pxb7.mall.trade.ass.infra.repository.db.PayLogRepository;
import com.pxb7.mall.trade.ass.infra.repository.db.PaymentRepository;
import com.pxb7.mall.trade.ass.infra.repository.db.RefundVoucherRepository;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.PayChannel;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.PayLog;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.Payment;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.RefundVoucher;
import com.pxb7.mall.trade.order.client.enums.order.ChannelOrderTypeEnum;
import com.pxb7.mall.trade.order.client.enums.order.RefundStatusEnum;
import com.pxb7.mall.trade.order.client.enums.order.RefundTypeEnum;
import com.pxb7.mall.trade.order.client.enums.pay.PayStatusEnum;
import com.pxb7.mall.trade.order.client.enums.pay.TradeModeEnum;
import jakarta.annotation.Resource;
import java.util.Arrays;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.apache.dubbo.common.utils.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.Set;

import static com.pxb7.mall.trade.ass.infra.constant.RMQConstant.ASS_REFUND_CALLBACK_TAG;
import static com.pxb7.mall.trade.ass.infra.constant.RMQConstant.PROMISE_REFUND_CALLBACK_TAG;
import static com.pxb7.mall.trade.order.client.constants.OrderNumberConstant.LY_REFUND_VOUCHER_BATCH_NO;
import static com.pxb7.mall.trade.order.client.constants.OrderNumberConstant.LY_REFUND_VOUCHER_NO;

@Slf4j
@Service
public class RefundSyncDomainService {

    // 待执行或执行中
    private static final Set<Integer> WAIT_QUERY_STATUS = ImmutableSet.<Integer>builder()
            .add(PayStatusEnum.PAY_IN_PROGRESS.getValue()).add(PayStatusEnum.PAY_PENDING.getValue())
            .build();
    // 系统退款或退款
    private static final Set<Integer> REFUND_MODE = ImmutableSet.<Integer>builder()
            .add(TradeModeEnum.SYSTEM_REFUND.getValue()).add(TradeModeEnum.REFUND.getValue())
            .build();
    @Resource
    private PayLogRepository payLogRepository;
    @Resource
    private RefundMessageService refundMessageService;

    @Resource
    private RefundInvokeService refundInvokeService;
    @Resource
    private PayChannelRepository payChannelRepository;
    @Resource
    private PayCompanyAccountDomainService payCompanyAccountDomainService;
    @Resource
    private PaymentRepository paymentRepository;

    @Resource
    private PayLogDomainService payLogDomainService;

    @Resource
    private RefundVoucherRepository refundVoucherRepository;

    @Resource
    private JdApiService jdApiService;

    /**
     * 退款查询处理逻辑
     * @param message 消息体
     */
    public void handleRefundResult(RefundQueryMessage message){
        //非渠道订单直接走老逻辑
        if(message.getChannelType() == null){
            this.syncRefundResult(message);
        }else{
            //京东渠道订单走新逻辑
            this.syncChannelRefundResult(message);
        }
    }

    @RedisLock(lockName = "syncRefundResult", key = "#message.refundLogId", expire = 12000)
    public void syncRefundResult(RefundQueryMessage message) {
        if (message.getTimes() > message.getMaxTimes()) {
            return;
        }
        PayLog refundPayLog = payLogRepository.getPayLogById(message.getRefundLogId());

        // 交易模式必须是退款
        if (Objects.isNull(refundPayLog) || !REFUND_MODE.contains(refundPayLog.getTradeMode())) {
            log.error("[退款结果查询] 异常，payLog = {}", refundPayLog);
            return;
        }

        // 幂等处理 不为待执行或执行中
        if (!WAIT_QUERY_STATUS.contains(refundPayLog.getPayStatus())) {
            log.info("[退款结果查询] 已处理 payLog = {}", refundPayLog);
            return;
        }

        // 查询退款结果
        RefundQueryRespBO queryRespBO = refundQuery(refundPayLog, message);

        log.info("[退款结果查询] 结果 = {}", queryRespBO);
        // 根据查询结果进行不同处理
        if (RefundTradeStatusEnum.EXECUTING.getValue().equals(queryRespBO.getRefundTradeStatus())) {
            log.info("[退款结果查询] 继续执行 payLogId = {}", refundPayLog.getPayLogId());
            // 退款执行中：可继续查询，重发消息
            message.setTimes(message.getTimes() + 1);
            refundMessageService.sendRefundQueryMessage(message);
            return;
        }

        RefundCallBackBO refundCallBackBO = RefundCallBackBO.builder()
                .refundLogId(message.getRefundLogId())
                .payLogId(message.getPayLogId())
                .refundQueryRespBO(queryRespBO).build();

        // 结果处理
        handleRefundCallBack(refundCallBackBO);
    }

    @RedisLock(lockName = "syncChannelRefundResult", key = "#message.refundVoucherId", expire = 12000)
    public void syncChannelRefundResult(RefundQueryMessage message){
        if (message.getTimes() > message.getMaxTimes()) {
            return;
        }
        if(!ChannelOrderTypeEnum.JD.getValue().equals(message.getChannelType())){
            return;
        }

        String refundVoucherId = message.getRefundVoucherId();
        RefundVoucher refundVoucher = refundVoucherRepository.getByRefundId(refundVoucherId);

        if(!RefundStatusEnum.APPLYING.getValue().equals(refundVoucher.getRefundStatus())){
            log.info("退款单当前结果无需查询, refundVoucherId:{}, status:{}",
                refundVoucherId, refundVoucher.getRefundStatus());
            return;
        }

        JdApiService.JdRefundStatusEnum jdRefundStatus = JdApiService.JdRefundStatusEnum.REFUND_WAIT;
        Long refundAmount = null;
        try{
            String jdOrderId = message.getChannelExtMap().get("outOrderId").toString();
            JdApiService.JdRefund
                jdRefund = jdApiService.getRefund(Long.parseLong(jdOrderId), message.getRefundVoucherId());
            log.info("退款结果查询, refundVoucherId:{}, jdRefund:{}", refundVoucherId, jdRefund);
            jdRefundStatus = jdRefund.getRefundStatus();
            refundAmount = jdRefund.getRefundMoney();
        }catch(Exception e){
            log.error("京东退款查询发生异常", e);
        }

        // 根据查询结果进行不同处理
        if (Arrays.asList(JdApiService.JdRefundStatusEnum.REFUND_WAIT,
            JdApiService.JdRefundStatusEnum.REFUND_REVIEW).contains(jdRefundStatus)) {
            log.info("[退款结果查询] 继续执行 refundVoucherId = {}", refundVoucherId);
            // 退款执行中：可继续查询，重发消息
            message.setTimes(message.getTimes() + 1);
            refundMessageService.sendRefundQueryMessage(message);
            return;
        }

        Integer tradeStatus = PayStatusEnum.PAY_FAIL.getValue();
        if(JdApiService.JdRefundStatusEnum.REFUND_SUCCESS.equals(jdRefundStatus)){
            tradeStatus = PayStatusEnum.PAY_SUCCESS.getValue();
        }

        RefundNotifyMessage refundMessage = new RefundNotifyMessage()
            .setRefundVoucherId(refundVoucherId).setTradeStatus(tradeStatus)
            .setRefundType(RefundTypeEnum.ONLINE_REFUND.getValue()).setRefundAmount(refundAmount);
        // 发送回调消息
        refundMessageService.sendRefundNotifyMessage(refundMessage, getCallbackTag(refundMessage.getRefundVoucherId()));

    }

    public void handleRefundCallBack(RefundCallBackBO refundCallBackBO) {
        log.info("[退款结果] 开始处理 {}", refundCallBackBO);
        RefundQueryRespBO queryRespBO = refundCallBackBO.getRefundQueryRespBO();
        //获取 支付单
        PayLog payLog = payLogRepository.getPayLogById(refundCallBackBO.getPayLogId());
        if (payLog == null) {
            log.error("[退款结果] 处理失败， 退款支付单不存在，refundCallBackBO = {}", refundCallBackBO);
            return;
        }
        //获取 退款单
        PayLog refundPayLog;
        if (TradeModeEnum.SYSTEM_REFUND.eq(payLog.getTradeMode())) {
            //若支付单 退款模式为系统退款，则支付单就是退款单
            refundPayLog = payLog;
        } else {
            //获取 退款单
            refundPayLog = payLogRepository.getPayLogById(refundCallBackBO.getRefundLogId());
        }
        log.info("[退款结果] 退款单 = {}", refundPayLog);
        Integer tradeStatus;
        String refundOutTradeNo = queryRespBO.getRefundOutTradeNo();
        Long refundAmount = null;
        if (RefundTradeStatusEnum.FAIL.getValue().equals(queryRespBO.getRefundTradeStatus())) {
            //退款失败：更新 payLog 状态  - 执行失败
            tradeStatus = PayStatusEnum.PAY_FAIL.getValue();
        } else {
            //退款成功：更新 payLog 状态  - 执行成功
            tradeStatus = PayStatusEnum.PAY_SUCCESS.getValue();
            refundAmount = queryRespBO.getRefundAmount().getFen();
        }
        Payment refundPayment = null;
        // 系统退款（没有退款单据）==SYSTEM_REFUND   普通退款=REFUND
        if (TradeModeEnum.REFUND.eq(refundPayLog.getTradeMode())) {
            refundPayment = paymentRepository.getByPaymentId(refundPayLog.getPaymentId());
        }
        boolean res = payLogDomainService.updatePayWithStatus(refundPayLog, refundPayment, tradeStatus,
                refundOutTradeNo, refundAmount,queryRespBO.getResult());
        // 执行退款回调
        if (res && Objects.nonNull(refundPayment)) {
            Payment payPayment = paymentRepository.getByPaymentId(payLog.getPaymentId());
            if (Objects.isNull(payPayment)) {
                return;
            }
            RefundNotifyMessage refundMessage = new RefundNotifyMessage()
                    .setRefundVoucherId(refundPayment.getVoucherId()).setTradeStatus(tradeStatus)
                    .setRefundType(RefundTypeEnum.ONLINE_REFUND.getValue()).setRefundAmount(refundAmount)
                    .setReceiptVoucherId(payPayment.getVoucherId()).setRefundPaymentId(refundPayment.getPaymentId());
            // 发送回调消息
            refundMessageService.sendRefundNotifyMessage(refundMessage, getCallbackTag(refundMessage.getRefundVoucherId()));
        }
    }

    /**
     * 区分 不同业务回调TAG
     */
    private String getCallbackTag(String refundVoucherId) {
        //默认 回调订单tag
        String tag = ASS_REFUND_CALLBACK_TAG;
        if (StringUtils.isNotBlank(refundVoucherId) &&
                (refundVoucherId.contains(LY_REFUND_VOUCHER_NO) || refundVoucherId.contains(LY_REFUND_VOUCHER_BATCH_NO))) {
            //回调 履约tag
            tag = PROMISE_REFUND_CALLBACK_TAG;
        }
        return tag;
    }

    private RefundQueryRespBO refundQuery(PayLog refundPayLog, RefundQueryMessage message) {
        RefundQueryReqBO queryReqBO = new RefundQueryReqBO();
        queryReqBO.setRefundStart(true);
        PayChannel payChannel = payChannelRepository.getPayChannelById(refundPayLog.getPayChannelId());
        String outPaymentParam = payCompanyAccountDomainService.getDecryptOutParamByAccountId(payChannel.getPayCompanyAccountId());
        PayChannelEnum payChannelEnum = PayChannelEnum.getEnum(payChannel.getChannel());
        queryReqBO.setPayChannel(payChannelEnum.getValue());
        queryReqBO.setOutPayParam(outPaymentParam);
        queryReqBO.setPayLogId(message.getPayLogId());
        queryReqBO.setRefundAmount(refundPayLog.getTradeAmount());
        JSONObject jsonObject = JSONObject.parseObject(refundPayLog.getExtInfo());
        queryReqBO.setRefundTradeNo(jsonObject.getString(PayConstant.REFUND_TRADE_NO));
        queryReqBO.setAlreadyRefundAmount(getSuccessRefundAmount(message));
        return refundInvokeService.refundQuery(queryReqBO);
    }

    private long getSuccessRefundAmount(RefundQueryMessage message) {
        List<String> paymentIdList = message.getHistoryRefundPaymentIds();
        long successRefundAmount = 0L;
        if (CollectionUtils.isNotEmpty(paymentIdList)) {
            List<Payment> refundPaymentList = paymentRepository.getListByPaymentIds(paymentIdList);
            successRefundAmount = refundPaymentList.stream().mapToLong(Payment::getActualAmount).sum();
        }
        return successRefundAmount;
    }
}
