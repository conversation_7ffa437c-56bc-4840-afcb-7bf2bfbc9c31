package com.pxb7.mall.trade.ass.domain;


import java.util.List;

import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import com.pxb7.mall.trade.ass.domain.model.AssRetrieveAssistanceConfigReqBO;
import com.pxb7.mall.trade.ass.domain.model.AssRetrieveAssistanceConfigRespBO;
import com.pxb7.mall.trade.ass.domain.mapping.AssRetrieveAssistanceConfigDomainMapping;
import com.pxb7.mall.trade.ass.infra.model.AssRetrieveAssistanceConfigReqPO;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.AssRetrieveAssistanceConfig;
import com.pxb7.mall.trade.ass.infra.repository.db.AssRetrieveAssistanceConfigRepository;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;


/**
 * 找回协助类型配置表domain服务
 *
 * <AUTHOR>
 * @since 2025-07-30 13:49:17
 */
@Service
public class AssRetrieveAssistanceConfigDomainService {

    @Resource
    private AssRetrieveAssistanceConfigRepository assRetrieveAssistanceConfigRepository;

    public boolean insert(AssRetrieveAssistanceConfigReqBO.AddBO param) {
        AssRetrieveAssistanceConfigReqPO.AddPO addPO = AssRetrieveAssistanceConfigDomainMapping.INSTANCE.assRetrieveAssistanceConfigBO2AddPO(param);
        return assRetrieveAssistanceConfigRepository.insert(addPO);
    }

    public boolean update(AssRetrieveAssistanceConfigReqBO.UpdateBO param) {
        AssRetrieveAssistanceConfigReqPO.UpdatePO updatePO = AssRetrieveAssistanceConfigDomainMapping.INSTANCE.assRetrieveAssistanceConfigBO2UpdatePO(param);
        return assRetrieveAssistanceConfigRepository.update(updatePO);
    }

    public boolean deleteById(AssRetrieveAssistanceConfigReqBO.DelBO param) {
        AssRetrieveAssistanceConfigReqPO.DelPO delPO = AssRetrieveAssistanceConfigDomainMapping.INSTANCE.assRetrieveAssistanceConfigBO2DelPO(param);
        return assRetrieveAssistanceConfigRepository.deleteById(delPO);
    }

    public AssRetrieveAssistanceConfigRespBO.DetailBO findById(Long id) {
        AssRetrieveAssistanceConfig entity = assRetrieveAssistanceConfigRepository.findById(id);
        return AssRetrieveAssistanceConfigDomainMapping.INSTANCE.assRetrieveAssistanceConfigPO2DetailBO(entity);

    }

    public List<AssRetrieveAssistanceConfigRespBO.DetailBO> list(AssRetrieveAssistanceConfigReqBO.SearchBO param) {
        AssRetrieveAssistanceConfigReqPO.SearchPO searchPO = AssRetrieveAssistanceConfigDomainMapping.INSTANCE.assRetrieveAssistanceConfigBO2SearchPO(param);
        List<AssRetrieveAssistanceConfig> list = assRetrieveAssistanceConfigRepository.list(searchPO);
        return AssRetrieveAssistanceConfigDomainMapping.INSTANCE.assRetrieveAssistanceConfigPO2ListBO(list);
    }

    public Page<AssRetrieveAssistanceConfigRespBO.DetailBO> page(AssRetrieveAssistanceConfigReqBO.PageBO param) {
        AssRetrieveAssistanceConfigReqPO.PagePO pagePO = AssRetrieveAssistanceConfigDomainMapping.INSTANCE.assRetrieveAssistanceConfigBO2PagePO(param);
        Page<AssRetrieveAssistanceConfig> page = assRetrieveAssistanceConfigRepository.page(pagePO);
        return AssRetrieveAssistanceConfigDomainMapping.INSTANCE.assRetrieveAssistanceConfigPO2PageBO(page);
    }

}

