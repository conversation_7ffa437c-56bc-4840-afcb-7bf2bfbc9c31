package com.pxb7.mall.trade.ass.client.dto.model.orderitemimdemnity;

import lombok.Data;

/**
 * 订单包赔
 *
 * <AUTHOR>
 * @date 2025/5/28 17:12
 */
@Data
public class OrderItemIndemnityDTO {
    /**
     * 包赔配置id
     */
    private String indemnityConfigId;
    /**
     * 包赔名称
     */
    private String indemnityName;


    private Integer indemnityStatus;

    /**
     * 包赔承担方 0买家 1卖家
     */
    private Integer responsibleUser;
    private String responsibleUserId;

    /**
     * 一级包赔类型 10 普通 20 增值 --indemnity_type.type_code
     */
    private Integer indemnityTypeLev1;
    /**
     * 二级包赔类型 11 免费 12 单倍 13 双倍 14 三倍 15 新增包 21 实名 22 人脸 23 充值 24 装备 --indemnity_type.type_code
     */
    private Integer indemnityTypeLev2;

    /**
     * 包赔金额
     */
    private Long indemnityAmount;
    /**
     * 包赔实付金额
     */
    private Long indemnityRealAmount;

    /**
     * 包赔优惠金额
     */
    private Long indemnityCoupon;
    /**
     * 赔付比例
     */
    private Integer percentCompensation;

    /**
     * 包赔号商折扣
     */
    private Long indemnityDiscount;

    /**
     * 商品id
     */
    private String productId;
}
