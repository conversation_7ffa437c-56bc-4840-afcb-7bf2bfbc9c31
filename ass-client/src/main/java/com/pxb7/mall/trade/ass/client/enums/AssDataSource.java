package com.pxb7.mall.trade.ass.client.enums;

import java.util.Objects;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 数据来源
 *
 * <AUTHOR>
 * @since: 2024-08-09 20:25
 **/
@AllArgsConstructor
@Getter
public enum AssDataSource {
    DEFAULT(0, "IM群聊"), IM(1, "IM私聊"), ASS_RETRIEVE(2, "企业微信"), COMPLAIN(3, "投诉转交"), OTHER(4, "其他");

    private final Integer code;
    private final String desc;

    public static AssDataSource fromCode(Integer code) {
        for (AssDataSource e : AssDataSource.values()) {
            if (Objects.equals(e.getCode(), code)) {
                return e;
            }
        }
        return null;
    }
}
