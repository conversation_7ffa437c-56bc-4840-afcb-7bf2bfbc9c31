package com.pxb7.mall.trade.ass.client.dto.response.refund;

import java.io.Serializable;

import com.alibaba.cola.dto.DTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class RefundInvokeRespDTO extends DTO {

    /**
     * 是否成功
     */
    private Boolean success;

    /**
     * 响应结果字符串
     */
    private String result;
}
