package com.pxb7.mall.trade.ass.client.api;

import com.alibaba.cola.dto.SingleResponse;
import com.pxb7.mall.trade.ass.client.dto.request.refund.RefundInvokeReqDTO;
import com.pxb7.mall.trade.ass.client.dto.request.refund.RefundQueryReqDTO;
import com.pxb7.mall.trade.ass.client.dto.response.refund.RefundInvokeRespDTO;
import com.pxb7.mall.trade.ass.client.dto.response.refund.RefundQueryRespDTO;

public interface Invoke3rdRefundServiceI {

    /**
     * 发起退款
     */
    SingleResponse<RefundInvokeRespDTO> invoke3rdRefund(RefundInvokeReqDTO refundInvokeReqDTO);

    /**
     * 查询退款结果
     */
    SingleResponse<RefundQueryRespDTO> doInvokeRefundQuery(RefundQueryReqDTO refundQueryReqDTO);

}
