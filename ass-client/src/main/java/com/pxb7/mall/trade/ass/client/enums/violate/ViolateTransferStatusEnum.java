package com.pxb7.mall.trade.ass.client.enums.violate;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 违约打款状态枚举：
 * 1-待打款,2-打款中，3-打款成功，4-打款失败
 */
@Getter
@AllArgsConstructor
public enum ViolateTransferStatusEnum {
    /**
     * 待打款
     */
    WAIT(1, "待打款"),
    /**
     * 打款中
     */
    DEALING(2, "打款中"),
    /**
     * 打款成功
     */
    SUCCESS(3, "打款成功"),
    /**
     * 打款失败
     */
    FAIL(4, "打款失败"),
    ;

    private final Integer value;

    private final String label;

    public Boolean eq(Integer value) {
        return this.getValue().equals(value);
    }
}
