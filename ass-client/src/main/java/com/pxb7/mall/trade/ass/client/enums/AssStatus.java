package com.pxb7.mall.trade.ass.client.enums;

import java.util.Objects;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 售后工单状态
 *
 * <AUTHOR>
 * @since: 2024-08-10 17:12
 **/
@AllArgsConstructor
@Getter
public enum AssStatus {
    DETERMINE(0, "待石锤"), PROCESSING(1, "处理中"), FINISH(2, "处理完成"), CLOSE(3, "已关闭");

    private final Integer code;
    private final String desc;

    public static AssStatus fromCode(Integer value) {
        for (AssStatus e : AssStatus.values()) {
            if (Objects.equals(e.getCode(), value)) {
                return e;
            }
        }
        return null;
    }
}
