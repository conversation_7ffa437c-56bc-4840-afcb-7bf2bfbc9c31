package com.pxb7.mall.trade.ass.client.enums;

import java.util.Objects;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 赔付状态
 *
 * <AUTHOR>
 * @since: 2024-08-10 17:36
 **/
@AllArgsConstructor
@Getter
public enum AssWoClaimStatus {
    NONE(0, "无"), AUDIT(1, "待审核"), SUCCESS(2, "已赔付");

    private final Integer code;
    private final String desc;

    public static AssWoClaimStatus fromCode(Integer value) {
        for (AssWoClaimStatus e : AssWoClaimStatus.values()) {
            if (Objects.equals(e.getCode(), value)) {
                return e;
            }
        }
        return null;
    }
}
