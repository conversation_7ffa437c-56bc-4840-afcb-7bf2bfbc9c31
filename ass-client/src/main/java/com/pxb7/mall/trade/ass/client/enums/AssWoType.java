package com.pxb7.mall.trade.ass.client.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 售后工单类型
 *
 * <AUTHOR>
 **/
@AllArgsConstructor
@Getter
public enum AssWoType {
    RETRIEVE(1, "找回"),
    DISPUTE(2, "纠纷"),
    COMPLAINT(3, "投诉");

    private final Integer code;
    private final String desc;

    public static AssWoType fromCode(Integer code) {
        for (AssWoType value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
