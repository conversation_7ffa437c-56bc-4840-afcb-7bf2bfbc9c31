package com.pxb7.mall.trade.ass.client.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 是否为协助工单
 *
 **/
@AllArgsConstructor
@Getter
public enum AssAssistance {
    NO(0, "（非协助）"), YES(1, "（协助）");

    private final Integer code;
    private final String desc;

    public static AssAssistance fromCode(Integer code) {
        for (AssAssistance value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
