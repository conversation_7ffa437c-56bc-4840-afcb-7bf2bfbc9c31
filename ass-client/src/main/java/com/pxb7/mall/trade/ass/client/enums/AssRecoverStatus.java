package com.pxb7.mall.trade.ass.client.enums;

import java.util.Objects;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum AssRecoverStatus {
    // 0:无 1:部分追回 2:全追回
    NO(0, "无"), PART(1, "部分追回"), ALL(2, "全追回");

    private final Integer code;
    private final String desc;

    public static AssRecoverStatus fromCode(Integer code) {
        for (AssRecoverStatus e : AssRecoverStatus.values()) {
            if (Objects.equals(e.getCode(), code)) {
                return e;
            }
        }
        return null;
    }
}
