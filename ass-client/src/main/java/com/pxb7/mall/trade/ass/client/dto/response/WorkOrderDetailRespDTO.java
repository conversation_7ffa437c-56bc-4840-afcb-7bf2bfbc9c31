package com.pxb7.mall.trade.ass.client.dto.response;

import com.alibaba.cola.dto.DTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Copyright (C), 2002-2024, 螃蟹游戏服务网
 *
 * @fileName: WorkOrderDetailRespDTO.java
 * @description: 客诉工单详情返回的DTO
 * @author: guo<PERSON><PERSON><PERSON>
 * @email: <EMAIL>
 * @date: 2024/9/20 18:03
 * @history: //修改记录
 * <author> <time> <version> <desc>
 * 修改人姓名 修改时间 版本号 描述
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class WorkOrderDetailRespDTO extends DTO {
    /**
     * 工单id
     */
    private String complaintWorkId;
    /**
     * 投诉标题
     */
    private String complaintTitle;
    /**
     * 订单id
     */
    private String orderItemId;
    /**
     * 投诉人手机号
     */
    private String complaintPhone;
    /**
     * 手机号注册状态 0未注册 1正常 2注销
     */
    private Integer phoneStatus;
    /**
     * 投诉人身份1买家 2卖家 3其他
     */
    private Integer complaintRole;
    /**
     * 投诉级别: 1-6 1一级 2:二级 ...6:六级
     */
    private Integer complaintLevel;

    /**
     * 投诉文字内容
     */
    private String complaintContent;

    /**
     * 图片url
     */
    private String complaintImg;
    /**
     * 处理结果
     */
    private String dealResult;

    /**
     * 备注
     */
    private String note;

    /**
     * 问题部门名称
     */
    private String departmentName;

    /**
     * 问题类型
     */
    private String questionType;

    /**
     * 问题级别:1-6 1:一级 2:二级 ...6:六级
     */
    private Integer questionLevel;

    /**
     * 责任人
     */
    private String responsiblePerson;

    /**
     * 是否有责 0:否 1是
     */
    private Boolean responsibility;

    /**
     * 工单状态1待处理2已完成
     */
    private Integer workOrderStatus;

    /**
     * 提交时间
     */
    private LocalDateTime createTime;
}
