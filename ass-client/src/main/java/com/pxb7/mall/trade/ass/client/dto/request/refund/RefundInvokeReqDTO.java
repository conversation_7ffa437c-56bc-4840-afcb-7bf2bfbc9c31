package com.pxb7.mall.trade.ass.client.dto.request.refund;

import com.alibaba.cola.dto.DTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class RefundInvokeReqDTO extends DTO {
    /**
     * 退款渠道 1：支付宝官方直连 2: 连连支付 3:泰山支付
     */
    private Integer payChannel;

    /**
     * 资金账户支付参数
     */
    @ToString.Exclude
    private String outPayParam;

    /**
     * 支付单的内部单号
     */
    private String innerTradeNo;

    /**
     * 退款金额，单位分
     */
    private Long refundAmount;

    /**
     * 历史退款金额，单位分，部分退款时对账使用，(泰山支付需要)
     */
    private Long alreadyRefundAmount;

    /**
     * 退款的 innerTradeNo
     */
    private String refundTradeNo;

    /**
     * 原支付金额，单位分(连连支付需要)
     */
    private Long totalAmount;

    /**
     * 原交易付款方user_id(连连支付需要)
     */
    private String userId;

    /**
     * 支付类别, 1支付宝 2微信 3银行卡
     */
    private Integer payType;
}
