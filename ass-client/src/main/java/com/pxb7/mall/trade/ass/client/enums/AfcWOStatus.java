package com.pxb7.mall.trade.ass.client.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Copyright (C), 2002-2025, 螃蟹游戏服务网
 *
 * @fileName: AfcWOStatus.java
 * @description: 售后工单状态
 * @author: g<PERSON><PERSON><PERSON><PERSON>
 * @email: <EMAIL>
 * @date: 2025/4/21 17:36
 * @history: //修改记录
 * <author> <time> <version> <desc>
 * 修改人姓名 修改时间 版本号 描述
 */
@Getter
@AllArgsConstructor
public enum AfcWOStatus {

    PENDING_HANDLING(1, "待处理"),

    COMPLETED(2, "已完结"),

    CLOSED(3, "已关闭"),
    ;
    private final Integer label;
    private final String desc;
}
