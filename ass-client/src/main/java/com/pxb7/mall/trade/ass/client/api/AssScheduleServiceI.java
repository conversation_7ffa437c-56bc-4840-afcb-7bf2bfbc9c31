package com.pxb7.mall.trade.ass.client.api;

import java.util.List;

import com.alibaba.cola.dto.MultiResponse;
import com.pxb7.mall.trade.ass.client.dto.request.AssStatusReqDTO;
import com.pxb7.mall.trade.ass.client.dto.response.AssStatusRespDTO;

/**
 * <AUTHOR>
 * @date 2024/8/22
 */
public interface AssScheduleServiceI {

    MultiResponse<AssStatusRespDTO> checkAss(List<AssStatusReqDTO> list);

}
