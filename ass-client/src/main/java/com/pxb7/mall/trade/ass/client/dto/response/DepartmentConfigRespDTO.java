package com.pxb7.mall.trade.ass.client.dto.response;

import com.alibaba.cola.dto.DTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * Copyright (C), 2002-2024, 螃蟹游戏服务网
 *
 * @fileName: DepartmentConfigRespDTO.java
 * @description: 问题部门配置列表
 * @author: guo<PERSON><PERSON><PERSON>
 * @email: <EMAIL>
 * @date: 2024/9/20 22:24
 * @history: //修改记录
 * <author> <time> <version> <desc>
 * 修改人姓名 修改时间 版本号 描述
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DepartmentConfigRespDTO extends DTO {
    /**
     * 客诉部门id
     */
    private String complaintDepartmentId;
    /**
     * 部门名称
     */
    private String departmentName;

    /**
     * 问题类型
     */
    private String questionType;

    /**
     * 员工配置列表
     */
    private List<EmployeeConfigRespDTO> complaintEmployeeList;
}
