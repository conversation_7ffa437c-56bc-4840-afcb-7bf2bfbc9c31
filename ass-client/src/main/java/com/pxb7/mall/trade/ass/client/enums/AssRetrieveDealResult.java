package com.pxb7.mall.trade.ass.client.enums;

import java.util.Objects;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 找回工单处理结果
 *
 * <AUTHOR>
 * @since: 2024-08-15 14:28
 **/
@AllArgsConstructor
@Getter
public enum AssRetrieveDealResult {
    DEFAULT(0, "无"), ACCOUNT(1, "追回账号"), AMOUNT(2, "追回号款"), NONE(3, "无结果");

    private final Integer code;
    private final String desc;

    public static AssRetrieveDealResult fromCode(Integer value) {
        for (AssRetrieveDealResult e : AssRetrieveDealResult.values()) {
            if (Objects.equals(e.getCode(), value)) {
                return e;
            }
        }
        return null;
    }

}
