package com.pxb7.mall.trade.ass.client.dto.request;

import java.io.Serial;
import java.io.Serializable;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2024/11/06 22:14
 **/
@Data
@Accessors(chain = true)
public class AssStatusReqDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = -1104890364705029074L;

    /**
     * 订单ID
     */
    private String orderItemId;
    /**
     * 交付群id
     */
    private String roomId;
}
