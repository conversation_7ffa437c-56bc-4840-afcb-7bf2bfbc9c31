package com.pxb7.mall.trade.ass.client.dto.response.afc;

import com.pxb7.mall.trade.ass.client.enums.AfcRetrieveWOStatus;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Copyright (C), 2002-2025, 螃蟹游戏服务网
 *
 * @fileName: RetrieveWORespDTO.java
 * @description: 售后工单返回对象
 * @author: guo<PERSON><PERSON><PERSON>
 * @email: <EMAIL>
 * @date: 2025/4/22 10:32
 * @history: //修改记录
 * <author> <time> <version> <desc>
 * 修改人姓名 修改时间 版本号 描述
 */
@Data
@Accessors(chain = true)
public class RetrieveWORespDTO implements Serializable {

    /**
     * 工单ID
     */
    private String workOrderId;

    /**
     * 工单编号
     */
    private String workOrderNo;

    /**
     * 订单ID
     */
    private String orderItemId;

    /**
     * 商品ID
     */
    private String productId;

    /**
     * 商品唯一编号
     */
    private String productUniqueNo;

    /**
     * 商品类型: 1-账号 2-充值 3-金币 4-装备 5-初始号 20-诚心卖
     */
    private Integer productType;

    /**
     * 游戏ID
     */
    private String gameId;

    /**
     * 工单状态  0:待石锤 1:处理中 2:处理完成 3:已关闭
     *
     * @see AfcRetrieveWOStatus
     */
    private Integer status;

    /**
     * 申请人id
     */
    private String proposerUserId;

    /**
     * 接待客服id
     */
    private String recvCustomerId;

    /**
     * 审核客服id
     */
    private String auditCustomerId;

    /**
     * 当前处理人员
     */
    private String dealUserId;

    /**
     * 预计完成时间 默认15天
     */
    private LocalDateTime expectedTime;

    /**
     * 工单完成时间
     */
    private LocalDateTime completeTime;

    /**
     * 工单创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建用户ID
     */
    private String createUserId;
}
