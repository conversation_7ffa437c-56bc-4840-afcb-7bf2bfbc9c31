package com.pxb7.mall.trade.ass.client.enums;

import com.pxb7.mall.trade.ass.client.enums.violate.ViolateReceiptStatusEnum;
import com.pxb7.mall.trade.ass.client.enums.violate.ViolateStatusEnum;
import com.pxb7.mall.trade.ass.client.enums.violate.ViolateTransferStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum ViolateAggregationStatusEnum {


    /**
     *
     * 发起退款时就生成了违约单，但是不一定会生效
     *
     * 待生效 (?? 不使用)  violateStatus=1
     * 待缴款       receiptStatus=1
     * 缴款失败     receiptStatus=3
     * 缴款中断     receiptStatus =4
     * 打款中       transferStatus=2 and receiptStatus=2
     * 打款成功     transferStatus=3
     * 打款失败     transferStatus=4
     * 取消即失效（?? 不使用） violateStatus =5
     *
     */


    WAIT_EFFECT(1,"待生效"),
    WAIT_RECEIPT(2,"待缴款"),
    RECEIPT_FAILURE(3,"缴款失败"),
    RECEIPT_INTERRUPTED(4,"缴款中断"),
    TRANSFER_PROCESS(5,"打款中"),
    TRANSFER_SUCCESS(6,"打款成功"),
    TRANSFER_FAILURE(7,"打款失败"),
    CANCELLED(8,"取消(失效)")
    ;


    private final Integer value;
    private final String label;


    static Map<Integer, ViolateAggregationStatusEnum> map;

    static {
        map = new HashMap<>();
        map = Arrays.stream(ViolateAggregationStatusEnum.values())
            .collect(Collectors.toMap(ViolateAggregationStatusEnum::getValue, Function.identity()));
    }


    public static ViolateAggregationStatusEnum getEnum(Integer value) {
        return map.get(value);
    }

    public boolean eq(Integer value) {
        return this.value.equals(value);
    }


    /**
     *
     * 发起退款时就生成了违约单，但是不一定会生效
     *
     * 待生效       violateStatus=1
     * 待缴款       receiptStatus=1
     * 缴款失败     receiptStatus=3
     * 缴款中断     receiptStatus =4
     * 打款中       transferStatus=2 and receiptStatus=2
     * 打款成功     transferStatus=3
     * 打款失败     transferStatus=4
     * 取消即失效   violateStatus =5
     *
     */
    public static Integer getViolateAggregationStatus(Integer violateStatus,Integer receiptStatus,Integer transferStatus){
        if (ViolateStatusEnum.INIT.eq(violateStatus)){
            return ViolateAggregationStatusEnum.WAIT_EFFECT.getValue();
        }
        if (ViolateStatusEnum.CANCEL.eq(violateStatus)){
            return ViolateAggregationStatusEnum.CANCELLED.getValue();
        }
        if (ViolateReceiptStatusEnum.INIT.eq(receiptStatus)){
            return ViolateAggregationStatusEnum.WAIT_RECEIPT.getValue();
        }
        if (ViolateReceiptStatusEnum.FAIL.eq(receiptStatus)){
            return ViolateAggregationStatusEnum.RECEIPT_FAILURE.getValue();
        }
        if (ViolateReceiptStatusEnum.STOP.eq(receiptStatus)){
            return ViolateAggregationStatusEnum.RECEIPT_INTERRUPTED.getValue();
        }
        if ((ViolateTransferStatusEnum.WAIT.eq(transferStatus) || ViolateTransferStatusEnum.DEALING.eq(transferStatus))
            && ViolateReceiptStatusEnum.SUCCESS.eq(receiptStatus)) {
            return ViolateAggregationStatusEnum.TRANSFER_PROCESS.getValue();
        }
        if (ViolateTransferStatusEnum.SUCCESS.eq(transferStatus)){
            return ViolateAggregationStatusEnum.TRANSFER_SUCCESS.getValue();
        }
        if (ViolateTransferStatusEnum.FAIL.eq(transferStatus)){
            return ViolateAggregationStatusEnum.TRANSFER_FAILURE.getValue();
        }

        return null;
    }
}
