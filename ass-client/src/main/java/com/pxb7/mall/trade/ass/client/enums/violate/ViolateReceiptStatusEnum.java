package com.pxb7.mall.trade.ass.client.enums.violate;

import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;

/**
 * 违约收款状态枚举：
 * 1-待收款，2-收款成功，3-收款失败，4-收款中断,5-取消
 */
@Getter
@AllArgsConstructor
public enum ViolateReceiptStatusEnum {
    /**
     * 待收款
     */
    INIT(1, "待收款"),
    /**
     * 收款成功
     */
    SUCCESS(2, "收款成功"),
    /**
     * 收款失败
     */
    FAIL(3, "收款失败"),
    /**
     * 收款中断
     */
    STOP(4, "收款中断"),
//    /**
//     * 取消
//     */
//    CANCEL(5, "取消"),

    ;

    private final Integer value;

    private final String label;


    /**
     * 获取收款终态:收款成功 、 取消---不可继续收款
     */
    @Getter
    private static final List<Integer> completed = Lists.newArrayListWithExpectedSize(10);


    /**
     * 收款失败 or 中断 --- 可重新编辑 继续收款
     */
    @Getter
    private static final List<Integer> failed = Lists.newArrayListWithExpectedSize(10);

    static {
        completed.add(SUCCESS.value);
        completed.add(FAIL.value);

        failed.add(FAIL.value);
        failed.add(STOP.value);
    }

    public Boolean eq(Integer value) {
        return this.getValue().equals(value);
    }

}
