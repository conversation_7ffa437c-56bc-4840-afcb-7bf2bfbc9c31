package com.pxb7.mall.trade.ass.client.enums;

import java.util.Objects;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum AssPaymentChannel {
    // 1:线上 2:线下
    ONLINE(1, "线上"), OFFLINE(2, "线下");

    private final Integer code;
    private final String desc;

    public static AssPaymentChannel getByCode(Integer code) {
        for (AssPaymentChannel e : AssPaymentChannel.values()) {
            if (Objects.equals(e.getCode(), code)) {
                return e;
            }
        }
        return null;
    }
}
