package com.pxb7.mall.trade.ass.client.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 售后操作日志
 *
 * <AUTHOR>
 * @since: 2024-09-21 17:35
 **/
@Getter
@AllArgsConstructor
public enum AssOperatorLog {
    ADD_WORD_ORDER("创建工单 "), SET_DEAL_USER("设置处理人员 "), COMPLETE_WO("完结工单 "), DELETE_RETRIEVE_WO("关闭找回工单 "),
    DELETE_DISPUTE_WO("关闭纠纷工单"), NEW_CLAIM("生成赔付单 "), CLOSE_PAYMENT("关闭收款单 "), AUDIT_CLAIM("业务审核赔付单  "),
    FINANCE_AUDIT("财务审核赔付单  "), ADD_PROOF("添加证据材料  "), DELETE_PROOF("删除证据材料 "), UPDATE_CLAIM("修改赔付单 "),
    OFFLINE_PAY("收款单线下支付 "), DELETE_PAYMENT("删除收款单 "), ANSWER_QUESTION("用户回答找回问题 "), FINANCE_OFFLINE_PAY("财务线下放款 "),
    NEW_PAYMENT("生成收款单  "), HAND_OVER("转交备注: "), CHANGE_DEAL_USER("变更处理人员 "), CLAIM_CALL_SUCCESS("赔付单线上放款 "),
    PAY_CALL_SUCCESS("收款单线上支付 "), CONFIRM_RETRIEVE_SET_DEAL_USER("确认找回，设置处理人"), FINANCE_OFFLINE_AUDIT("财务审核线下赔付单"),
    FINANCE_ONLINE_AUDIT("财务审核线上赔付单  "), RESULT("结果: "), REASON("原因: "), REMARK("备注: ");

    private final String desc;
}
