package com.pxb7.mall.trade.ass.client.dto.response.order;

import java.io.Serializable;

import com.alibaba.cola.dto.DTO;
import com.pxb7.mall.trade.ass.client.dto.model.order.OrderItemAmountInfo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class OrderItemAmountRespDTO extends DTO {
    /**
     * 订单商品剩余金额信息，成功收款-成功退款的金额
     */
    private OrderItemAmountInfo currentAmount;
}
