package com.pxb7.mall.trade.ass.client.enums.violate;

import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;

/**
 * 违约状态枚举：
 * 1-待处理,2-处理中，3-处理成功，4-处理失败，5-取消
 */
@Getter
@AllArgsConstructor
public enum ViolateStatusEnum {
    /**
     * 待处理
     */
    INIT(1, "待处理"),
    /**
     * 处理中
     */
    DEALING(2, "处理中"),
    /**
     * 处理成功
     */
    SUCCESS(3, "处理成功"),
    /**
     * 处理失败
     */
    FAIL(4, "处理失败"),
    /**
     * 取消
     */
    CANCEL(5, "取消"),

    ;

    private final Integer value;

    private final String label;


    @Getter
    private static final List<Integer> effectiveStatus = Lists.newArrayListWithExpectedSize(10);
    @Getter
    private static final List<Integer> dealingStatus = Lists.newArrayListWithExpectedSize(10);

    static{
        effectiveStatus.add(DEALING.value);
        effectiveStatus.add(SUCCESS.value);

        dealingStatus.add(INIT.value);
        dealingStatus.add(DEALING.value);

    }

    public Boolean eq(Integer value) {
        return this.getValue().equals(value);
    }

}
