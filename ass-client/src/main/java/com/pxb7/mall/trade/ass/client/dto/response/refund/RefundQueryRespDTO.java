package com.pxb7.mall.trade.ass.client.dto.response.refund;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.alibaba.cola.dto.DTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class RefundQueryRespDTO extends DTO {

    /**
     * 退款的 innerTradeNo(连连、支付宝需要)
     */
    private String refundTradeNo;

    /**
     * 支付的 innerTradeNo
     */
    private String payTradeNo;
    /**
     * 三方外部支付单号
     */
    private String outTradeNo;
    /**
     * 2执行中 3执行成功 4执行失败 999 已执行
     */
    private Integer refundTradeStatus;

    /**
     * 结果字符串
     */
    private String result;

    /**
     * 退款成功金额
     */
    private Long refundAmount;

    /**
     * 退款时间
     */
    private LocalDateTime payTime;

    /**
     * 已退款金额
     */
    private Long alreadyRefundAmount;

}
