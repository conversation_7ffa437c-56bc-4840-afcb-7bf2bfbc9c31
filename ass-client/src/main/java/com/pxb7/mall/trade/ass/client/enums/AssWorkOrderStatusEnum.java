package com.pxb7.mall.trade.ass.client.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

@Getter
@AllArgsConstructor
public enum AssWorkOrderStatusEnum {
    PROCESS(0, "处理中"), FINISH(1, "已完成"), CANCELED(2, "已取消");

    private final Integer code;
    private final String desc;

    public static AssWorkOrderStatusEnum fromCode(Integer code) {
        for (AssWorkOrderStatusEnum e : AssWorkOrderStatusEnum.values()) {
            if (Objects.equals(e.getCode(), code)) {
                return e;
            }
        }
        return null;
    }
}
