package com.pxb7.mall.trade.ass.client.dto.request;

import com.alibaba.cola.dto.DTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * Copyright (C), 2002-2024, 螃蟹游戏服务网
 *
 * @fileName: CreateWorkOrderReqDTO.java
 * @description: 创建客诉工单请求DTO
 * @author: guo<PERSON><PERSON><PERSON>
 * @email: <EMAIL>
 * @date: 2024/9/20 17:14
 * @history: //修改记录
 * <author> <time> <version> <desc>
 * 修改人姓名 修改时间 版本号 描述
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CreateWorkOrderReqDTO extends DTO {
    /**
     * 订单id
     */
    private String orderItemId;
    /**
     * 房间id
     */
    private String roomId;
    /**
     * 投诉标题
     */
    private String complaintTitle;

    /**
     * 投诉渠道 1:IM 2支付宝 3闲鱼 4:12315 5消费宝 6连连支付 7电话 8反诈邮箱 9外部门升级 10黑猫投诉 11工商局 12工信部
     */
    private Integer complaintChannel;

    /**
     * 投诉人手机号
     */
    private String complaintPhone;
    /**
     * 投诉人userId
     */
    private String userId;

    /**
     * 投诉人身份1买家 2卖家 3其他
     */
    private Integer complaintRole;
    /**
     * 投诉级别: 1-6 1一级 2:二级 ...6:六级
     */
    private Integer complaintLevel;
    /**
     * 投诉文字内容
     */
    private String complaintContent;

    /**
     * 图片url，最多允许六张图片,逗号分隔
     */
    private String complaintImg;
    /**
     * 工单处理人id
     */
    private String processorId;

    /**
     * 创建来源(1:系统,2:IM)
     */
    private Integer complaintSource = 2;

}
