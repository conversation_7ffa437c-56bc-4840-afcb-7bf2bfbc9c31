package com.pxb7.mall.trade.ass.client.dto.response;

import java.io.Serial;
import java.io.Serializable;

import com.pxb7.mall.trade.ass.client.enums.AssApplyStatusEnum;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 订单是否申请过售后
 *
 * <AUTHOR>
 * @since: 2024-10-03 14:59
 **/
@Data
@Accessors(chain = true)
public class AssStatusRespDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = -1104890364705029074L;
    /**
     * 订单ID
     */
    private String orderItemId;
    /**
     * 交付群id
     */
    private String roomId;
    /**
     * 0:未申请 1：申请中(未完结) 2：已完结
     * 
     * @see AssApplyStatusEnum
     */
    private Integer apply;
}
