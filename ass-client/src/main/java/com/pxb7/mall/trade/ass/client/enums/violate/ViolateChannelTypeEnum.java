package com.pxb7.mall.trade.ass.client.enums.violate;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 违约支付渠道类型
 */
@Getter
@AllArgsConstructor
public enum ViolateChannelTypeEnum {
    /**
     * 在线支付
     */
    ONLINE(1, "在线支付"),
    /**
     * 钱包
     */
    WALLET(2, "钱包"),

    ;

    private final Integer value;

    private final String label;
    public Boolean eq(Integer value) {
        return this.getValue().equals(value);
    }

}
