package com.pxb7.mall.trade.ass.client.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum AssPayWay {
    // 1:支付宝 2:微信 3:银行卡
    ALIPAY(1, "支付宝"), WECHAT(2, "微信"), BANKCARD(3, "银行卡");

    private final Integer code;
    private final String desc;

    public static AssPayWay fromCode(Integer code) {
        for (AssPayWay e : AssPayWay.values()) {
            if (e.code.equals(code)) {
                return e;
            }
        }
        return null;
    }
}
