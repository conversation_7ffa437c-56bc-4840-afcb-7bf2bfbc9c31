package com.pxb7.mall.trade.ass.client.dto.response;

import com.alibaba.cola.dto.DTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * Copyright (C), 2002-2024, 螃蟹游戏服务网
 *
 * @fileName: WorkOrderLogRespDTO.java
 * @description: 客诉工单日志
 * @author: guominqiang
 * @email: <EMAIL>
 * @date: 2024/9/21 17:52
 * @history: //修改记录
 * <author> <time> <version> <desc>
 * 修改人姓名 修改时间 版本号 描述
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
public class WorkOrderLogRespDTO extends DTO {

    /**
     * 工单编号
     */
    private String complaintWorkId;
    /**
     * 日志类型（1:创建；2:转交；3:完结）
     */
    private Integer logType;
    /**
     * 当前处理人ID
     */
    private String currentProcessorId;

    /**
     * 当前处理人名称
     */
    private String currentProcessorName;
    /**
     * 被转交人id(log_type=2时有值)
     */
    private String transfereeId;

    /**
     * 被转交人名称(log_type=2时有值)
     */
    private String transfereeName;

    /**
     * 转交备注(log_type=2时有值)
     */
    private String transferNote;

    /**
     * 修正内容
     */
    private List<WorkOrderLogmodificationRespDTO> modification;


    /**
     * 操作时间
     */
    private LocalDateTime createTime;

}
