package com.pxb7.mall.trade.ass.client.enums;

import java.util.Objects;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum AssApplyStatusEnum {
    // 0:未申请, 1:申请中(未完结), 2:已完结
    NOT_APPLY(0, "未申请"), UNCOMPLETED(1, "申请中(未完结)"), FINISHED(2, "已完结");

    private final Integer code;
    private final String desc;

    public static AssApplyStatusEnum fromCode(Integer code) {
        for (AssApplyStatusEnum e : AssApplyStatusEnum.values()) {
            if (Objects.equals(e.getCode(), code)) {
                return e;
            }
        }
        return null;
    }
}
