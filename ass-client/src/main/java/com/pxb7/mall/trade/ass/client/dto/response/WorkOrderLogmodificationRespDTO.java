package com.pxb7.mall.trade.ass.client.dto.response;

import com.alibaba.cola.dto.DTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Copyright (C), 2002-2024, 螃蟹游戏服务网
 *
 * @fileName: WorkOrderLogRespDTO.java
 * @description: 客诉工单日志
 * @author: guo<PERSON><PERSON><PERSON>
 * @email: <EMAIL>
 * @date: 2024/9/21 17:52
 * @history: //修改记录
 * <author> <time> <version> <desc>
 * 修改人姓名 修改时间 版本号 描述
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
public class WorkOrderLogmodificationRespDTO extends DTO {

    /**
     * 字段名称
     */
    private String field;
    /**
     * 修改前的值
     */
    private String before;

    /**
     * 修改后的值
     */
    private String after;


}
