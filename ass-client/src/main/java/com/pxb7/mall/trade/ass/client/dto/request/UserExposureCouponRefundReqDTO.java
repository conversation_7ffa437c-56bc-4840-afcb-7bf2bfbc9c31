package com.pxb7.mall.trade.ass.client.dto.request;

import com.alibaba.cola.dto.DTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/8/22
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class UserExposureCouponRefundReqDTO extends DTO {

    /**
     * 订单行id
     */
    private String orderItemId;

    /**
     * 优惠券ids JsonString
     */
    private String userCouponIds;

    /**
     * 退款金额
     */
    private Long sumApportionAmount;

    /**
     * 退款来源 1 用户 2 客服
     */
    private Integer refundSource;

    /**
     * 客服id
     */
    private String systemUserId;

}
