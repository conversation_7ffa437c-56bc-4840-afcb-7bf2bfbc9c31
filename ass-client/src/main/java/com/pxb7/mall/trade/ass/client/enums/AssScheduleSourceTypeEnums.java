package com.pxb7.mall.trade.ass.client.enums;

import java.util.Objects;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum AssScheduleSourceTypeEnums {

    SourceType_1(1, "c端用户"), SourceType_2(2, "客服"), SourceType_3(3, "admin用户");

    private final Integer code;
    private final String desc;

    public static AssScheduleSourceTypeEnums fromCode(Integer value) {
        for (AssScheduleSourceTypeEnums e : AssScheduleSourceTypeEnums.values()) {
            if (Objects.equals(e.getCode(), value)) {
                return e;
            }
        }
        return null;
    }
}
