package com.pxb7.mall.trade.ass.client.dto.model.refund;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum PayChannelEnum {

    /**
     * 支付宝
     */
    ALIPAY(1, "支付宝官方直连", "alipayRefund"),

    /**
     * 连连支付
     */
    LIANLIANPAY(2, "连连支付", "lianLianPayRefund"),

    /**
     * 泰山支付
     */
    TSPAY(3, "泰山支付", "tsPayRefund"),

    /**
     * 泰山支付
     */
    YEE_PAY(5, "易宝支付", "yeePayRefund"),

    /**
     * 京东支付
     */
    JD_PAY(6, "京东支付", "jdPayRefund"),
    ;

    static Map<Integer, PayChannelEnum> map;

    static {
        map = Arrays.stream(PayChannelEnum.values())
                .collect(Collectors.toMap(PayChannelEnum::getValue, Function.identity()));
    }

    private final Integer value;
    private final String label;
    private final String refundBizNo;

    public static String getLabel(Integer value) {
        return map.get(value) == null ? "" : map.get(value).getLabel();
    }

    public static PayChannelEnum getEnum(Integer value) {
        return map.get(value);
    }
}
