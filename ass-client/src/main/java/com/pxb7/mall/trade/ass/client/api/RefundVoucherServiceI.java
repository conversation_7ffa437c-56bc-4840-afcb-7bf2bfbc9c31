package com.pxb7.mall.trade.ass.client.api;


import com.alibaba.cola.dto.SingleResponse;
import com.pxb7.mall.trade.ass.client.dto.request.refund.CancelRefundVoucherReqDTO;
import com.pxb7.mall.trade.ass.client.dto.request.refund.IndemnityChangeServiceRefundReqDTO;
import com.pxb7.mall.trade.ass.client.dto.response.refund.IndemnityChangeServiceRefundRespDTO;

/**
 * 退款单相关接口
 * <p>
 * 后续退款单相关的接口可以放在此处
 */
public interface RefundVoucherServiceI {

    /**
     * 创建包赔变更退款单
     *
     * @param request 请求参数
     * @return 返回结果
     */
    SingleResponse<IndemnityChangeServiceRefundRespDTO> createIndemnityChangeRefund(IndemnityChangeServiceRefundReqDTO request);

    /**
     * 取消包赔变更退款单
     *
     * @param request 请求参数
     * @return 取消结果
     */
    SingleResponse<Boolean> cancelIndemnityChangeRefund(CancelRefundVoucherReqDTO request);
}
