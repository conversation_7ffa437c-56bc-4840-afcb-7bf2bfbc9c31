package com.pxb7.mall.trade.ass.client.dto.model.refund;

import lombok.Getter;

@Getter
public enum RefundErrorCode {
    /**
     * 退款异常
     */
    REFUND_ERROR("tra10001", "退款异常"), REFUND_THIRD_FAIL("tra110202", "退款三方请求失败"),
    PAY_CHANNEL_NOT_EXIST_ERROR("tra00003", "支付渠道不存在"),
    ALIPAY_REQUEST_EXCEPTION("tra00004", "请求支付宝异常"),
    REFUND_FAIL_ERROR("assRefund10001","可以直接置失败的错误码"),
    ;

    private final String errCode;
    private final String errDesc;

    RefundErrorCode(String errCode, String errDesc) {
        this.errCode = errCode;
        this.errDesc = errDesc;
    }

}
