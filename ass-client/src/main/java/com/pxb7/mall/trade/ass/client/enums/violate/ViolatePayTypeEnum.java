package com.pxb7.mall.trade.ass.client.enums.violate;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 违约支付单类型
 */
@Getter
@AllArgsConstructor
public enum ViolatePayTypeEnum {
    /**
     * 扣违约金
     */
    DEDUCTION(1, "扣违约金", "WYS"),
    /**
     * 打守约金
     */
    TRANSFER(2, "打守约金", "WYD"),

    ;

    private final Integer value;

    private final String label;

    private final String prex;

    public boolean eq(int value) {
        return this.value == value;
    }

    public static String getPrex(int type) {
        return Arrays.stream(values())
                .filter(e -> e.eq(type))
                .map(ViolatePayTypeEnum::getPrex)
                .findFirst()
                .orElse(null);
    }

}
