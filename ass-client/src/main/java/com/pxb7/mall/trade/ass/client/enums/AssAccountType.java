package com.pxb7.mall.trade.ass.client.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum AssAccountType {
    // 1:个人 2:公司
    PERSONAL(1, "个人"), COMPANY(2, "公司");

    private final Integer code;
    private final String desc;

    public static AssAccountType fromCode(Integer code) {
        for (AssAccountType e : AssAccountType.values()) {
            if (e.code.equals(code)) {
                return e;
            }
        }
        return null;
    }
}
