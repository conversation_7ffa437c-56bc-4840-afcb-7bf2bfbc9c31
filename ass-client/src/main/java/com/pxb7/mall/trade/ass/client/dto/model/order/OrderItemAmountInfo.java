package com.pxb7.mall.trade.ass.client.dto.model.order;

import java.io.Serializable;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class OrderItemAmountInfo implements Serializable {
    /**
     * 商品号价金额
     */
    private long productAmount;
    /**
     * 包赔金额
     */
    private long indemnityAmount;

    /**
     * 包赔承担方
     */
    private Integer indemnityResponseUser;
    /**
     * 手续费金额
     */
    private long feeAmount;
    /**
     * 红包
     */
    private long redPacketAmount;
    /**
     * 买家违约金
     */
    private long buyerViolateAmount;
    /**
     * 总金额
     */
    private long totalAmount;

    /**
     * 发送违约金MQ
     */
    private boolean sendViolateMQFlag;

    public OrderItemAmountInfo subtract(OrderItemAmountInfo other) {
        return new OrderItemAmountInfo().setProductAmount(Math.max(productAmount - other.productAmount, 0))
                .setIndemnityAmount(Math.max(indemnityAmount - other.indemnityAmount, 0))
                .setFeeAmount(Math.max(feeAmount - other.feeAmount, 0))
                .setRedPacketAmount(Math.max(redPacketAmount - other.redPacketAmount, 0))
                .setTotalAmount(Math.max(totalAmount - other.totalAmount, 0));
    }
}
