package com.pxb7.mall.trade.ass.client.enums;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 售后流程节点
 */
@Getter
@AllArgsConstructor
public enum AssScheduleNode {

    // 发起售后申请(找回、纠纷工单)
    INITIATE_AFTER_SALE(true, false, "INITIATE_AFTER_SALE", "发起售后申请", "发起售后申请：", null, "客服介入", "客服介入"),
    // 发送问题(找回工单)
    SEND_QUESTION(true, false, "SEND_QUESTION", "发送问题", "问题已发送",
        "您好，老板，现在跟您先核实以下情况，请老板认真看完并如实告知，如有别的信息也请如实说，如果没有如实告知，"
            + "我们核实到有上述情况将不予归还账号及提供赔付，如查到恶意骗保所购买账号将全部失去包赔。经核实如有欺骗行为直接按诈骗罪起诉!!!",
        "", ""),
    // 发送问题失败(找回工单)
    SEND_QUESTION_FAIL(true, false, "SEND_QUESTION_FAIL", "发送问题失败", "发送失败，重新发送",
        "该游戏找回问题未配置，请手动发送问题给用户，同时将问题反馈给售后相关人员，让其完成配置", "", ""),
    // 驳回(找回、纠纷工单)
    REJECT(true, true, "REJECT", "驳回", "已驳回", null, "客服驳回", "客服驳回"),
    // 转接纠纷(找回工单)
    TRANSFER_DISPUTE(true, false, "TRANSFER_DISPUTE", "转接纠纷", "转接纠纷", null, "", ""),
    // 转接找回(纠纷工单)
    TRANSFER_RETRIEVE(true, false, "TRANSFER_RETRIEVE", "转接找回", "转接找回", null, "", ""),
    // 创建工单(找回、纠纷工单)
    CREATE_WORK_ORDER(true, false, "CREATE_WORK_ORDER", "创建工单", "创建工单：", null, "审核处理中", "纠纷售后处理中"),
    USER_CANCEL(true, true, "USER_CANCEL", "用户取消", "用户取消", null, "取消工单", "取消工单"),
    // 上传证据(找回工单)
    UPLOAD_EVIDENCE(true, false, "UPLOAD_EVIDENCE", "上传证据", "已上传证据", null, "", ""),
    // 关闭工单(找回、纠纷工单)
    CLOSE_WORK_ORDER(true, true, "CLOSE_WORK_ORDER", "关闭工单", "审核工单关闭", null, "找回售后不成立,关闭工单", ""),
    // 确认找回成立，转交找回处理人员跟进(找回工单)
    AFFIRM_RETRIEVE(true, false, "AFFIRM_RETRIEVE", "确认找回", "确认找回成立，转交找回处理人员跟进：", null, "售后人员处理中", ""),
    // 工单详情(找回工单)
    WORK_ORDER_INFO(true, false, "WORK_ORDER_INFO", "工单详情", "工单详情", null, "", ""),
    // 创建赔付单(找回、纠纷工单)
    CREATE_CLAIM_ORDER(false, false, "CREATE_CLAIM_ORDER", "创建赔付单", "创建赔付单", null, "已启动赔付程序", ""),
    // 结束赔付单(找回、纠纷工单)
    END_CLAIM_ORDER(false, false, "END_CLAIM_ORDER", "结束赔付单", "结束赔付单", null, "", ""),
    // 创建付款单(找回、纠纷工单)
    CREATE_PAY_ORDER(false, false, "CREATE_PAY_ORDER", "创建付款单", "创建付款单", null, "", ""),
    // 结束付款单(找回、纠纷工单)
    END_PAY_ORDER(false, false, "END_PAY_ORDER", "结束付款单", "结束付款单", null, "", ""),
    // 找回无结果(找回工单)
    RETRIEVE_NO_FINISH(true, true, "RETRIEVE_NO_FINISH", "找回无结果", "找回无结果", null, "", ""),
    // 找回未结束-追回号款(找回工单)
    RETRIEVE_NO_FINISH_BACK_AMOUNT(false, false, "RETRIEVE_NO_FINISH_BACK_AMOUNT", "处理结果", "处理结果：", null, "", ""),
    // 找回已结束-追回号款且赔付完成(找回工单已完结)
    RETRIEVE_FINISH_BACK_AMOUNT_CLAIM_FINISH(true, true, "RETRIEVE_FINISH_BACK_AMOUNT_CLAIM_FINISH", "处理结果", "处理结果：","",
        null, "赔付完成"),
    // 找回已结束-追回账号(找回工单完结)
    RETRIEVE_FINISH_BACK_ACCOUNT(true, true, "RETRIEVE_FINISH_BACK_ACCOUNT", "处理结果", "处理结果：", null, "已追回账号", ""),
    // 纠纷处理结果(纠纷工单完结)
    DISPUTE_AFTER_SALE(true, true, "DISPUTE_AFTER_SALE", "纠纷处理", "纠纷售后处理，处理结果：", null, "", "纠纷处理已完结"),
    // 纠纷处理结果(纠纷工单已赔付,后台放款则添加记录)
    DISPUTE_AFTER_CLAIM(false, false, "DISPUTE_AFTER_CLAIM", "纠纷处理已赔付", "纠纷处理已赔付", null, "", "纠纷处理已完结,已补偿用户");

    /**
     * im端是否展示
     */
    private final boolean show;
    /**
     * 节点是否完成
     */
    private final boolean finish;
    private final String id;
    private final String name;
    private final String desc;
    private final String msg;
    /**
     * 找回售后文案
     */
    private final String retrieveMsg;
    /**
     * 纠纷售后文案
     */
    private final String disputeMsg;
    public static List<AssScheduleNode> nextNode(AssScheduleNode node, AssWoType type) {
        if (node == null) {
            if (AssWoType.RETRIEVE == type) {
                return List.of(SEND_QUESTION);
            } else if (AssWoType.DISPUTE == type) {
                return List.of(REJECT, TRANSFER_RETRIEVE, CREATE_WORK_ORDER);
            }
        } else if (INITIATE_AFTER_SALE == node) {
            if (AssWoType.RETRIEVE == type) {
                return List.of(SEND_QUESTION);
            } else if (AssWoType.DISPUTE == type) {
                return List.of(REJECT, TRANSFER_RETRIEVE, CREATE_WORK_ORDER);
            }
        } else if (SEND_QUESTION == node) {
            if (AssWoType.RETRIEVE == type) {
                return List.of(REJECT, TRANSFER_DISPUTE, CREATE_WORK_ORDER);
            } else if (AssWoType.DISPUTE == type) {
                return List.of(REJECT, TRANSFER_RETRIEVE, CREATE_WORK_ORDER);
            }
        } else if (CREATE_WORK_ORDER == node) {
            if (AssWoType.RETRIEVE == type) {
                return List.of(UPLOAD_EVIDENCE, CLOSE_WORK_ORDER, AFFIRM_RETRIEVE);
            } else if (AssWoType.DISPUTE == type) {
                return List.of(DISPUTE_AFTER_SALE);
            }
        } else if (TRANSFER_RETRIEVE == node) {
            return List.of(UPLOAD_EVIDENCE, CLOSE_WORK_ORDER, AFFIRM_RETRIEVE);
        } else if (UPLOAD_EVIDENCE == node) {
            return List.of(UPLOAD_EVIDENCE, CLOSE_WORK_ORDER, AFFIRM_RETRIEVE);
        } else if (AFFIRM_RETRIEVE == node) {
            return List.of(WORK_ORDER_INFO);
        }
        return Collections.emptyList();
    }

    public static AssScheduleNode getById(String id) {
        for (AssScheduleNode value : values()) {
            if (Objects.equals(value.getId(), id)) {
                return value;
            }
        }
        return null;
    }

}
