package com.pxb7.mall.trade.ass.client.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;


@Getter
@AllArgsConstructor
public enum AssUserOperatorLog {
    USER_ADD_ASS("用户发起{}申请","用户发起售后"),
    IM_ADD_ASS("客服协助用户发起{}申请","客服在IM发起售后申请"),
    ADD_WORK_ORDER("平台受理{}申请 ","审核客服创建工单"),
    ADMIN_ADD_WORK_ORDER("客服协助用户发起{}申请 ","客服在后台发起售后"),
    SET_DEAL_USER("平台分配售后专员跟进","审核客服设置处理人"),
    ADD_PROOF("售后专员上传相关资料","添加证据材料"),
    TRANSFER_DISPUTE("经平台审核，修改售后类型为纠纷","客服端转接纠纷"),
    TRANSFER_RETRIEVE("经平台审核，修改售后类型为找回","客服端转接找回"),
    ADD_CLAIM("平台启动赔付程序","生成赔付单"),
    CLAIM_PAY_SUCCESS("平台赔付 ¥{}","赔付单放款成功"),
    IM_CLOSE_ORDER("平台取消","客服关闭售后"),
    USER_CLOSE_ORDER("用户取消售后","用户取消"),
    ;
    /**
     * 用户端展示文案
     */
    private final String userDesc;

    /**
     * 管理后台展示文案
     */
    private final String adminDesc;
}
