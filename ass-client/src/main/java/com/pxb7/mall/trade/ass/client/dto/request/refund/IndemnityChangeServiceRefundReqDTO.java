package com.pxb7.mall.trade.ass.client.dto.request.refund;

import com.alibaba.cola.dto.DTO;
import com.pxb7.mall.trade.ass.client.dto.model.orderitemimdemnity.OrderItemIndemnityDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 客服发起包赔变更退款
 *
 * <AUTHOR>
 * @date 2025/5/13 15:31
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class IndemnityChangeServiceRefundReqDTO extends DTO {
    /**
     * 订单编号
     */
    private String orderItemId;
    /**
     * 包赔变更申请ID
     */
    private String indemnityChangeId;
    /**
     * 发起人
     */
    private String changeUser;
    /**
     * 群聊ID
     */
    private String roomId;
    /**
     * 费用承担人ID
     */
    private String responseUserId;

    /**
     * 包赔承担方：0=买家；1=卖家
     */
    private Integer responseUser;
    /**
     * 退款金额
     */
    private Long refundAmount;
    /**
     * 交易方式：线上、线下等
     */
    private Integer payMode;

    /**
     * 新增的包赔
     */
    private List<OrderItemIndemnityDTO> addedIndemnityList;
    /**
     * 取消掉的包赔
     */
    private List<OrderItemIndemnityDTO> canceledIndemnityList;

}
