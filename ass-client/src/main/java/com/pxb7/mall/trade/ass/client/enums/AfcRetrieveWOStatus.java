package com.pxb7.mall.trade.ass.client.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Copyright (C), 2002-2025, 螃蟹游戏服务网
 *
 * @fileName: AfcRetrieveWOStatus.java
 * @description: 售后找回工单状态
 * @author: g<PERSON><PERSON><PERSON><PERSON>
 * @email: <EMAIL>
 * @date: 2025/4/21 16:54
 * @history: //修改记录
 * <author> <time> <version> <desc>
 * 修改人姓名 修改时间 版本号 描述
 */
@Getter
@AllArgsConstructor
public enum AfcRetrieveWOStatus {

    PENDING_CONFIRMED(0, "待实锤"),

    HANDLING(1, "处理中"),

    FINISHED(2, "处理成功"),

    CLOSED(3, "已关闭"),
    ;

    private final Integer label;
    private final String desc;
}
