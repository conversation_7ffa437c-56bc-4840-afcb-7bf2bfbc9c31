package com.pxb7.mall.trade.ass.client.dto.request.refund;

import com.alibaba.cola.dto.DTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class RefundQueryReqDTO extends DTO {
    /**
     * 退款渠道 1：支付宝官方直连 2: 连连支付 3:泰山支付
     */
    private Integer payChannel;
    /**
     * 资金账户支付参数
     */
    @ToString.Exclude
    private String outPayParam;

    /**
     * 退款的 innerTradeNo(连连、支付宝需要)
     */
    private String refundTradeNo;

    /**
     * 支付的 innerTradeNo
     */
    private String payTradeNo;

    /**
     * 历史退款金额，单位分，对账使用，(泰山支付需要)
     */
    private Long alreadyRefundAmount;

    /**
     * 本次发起的退款金额，单位分，对账使用，(泰山支付需要)
     */
    private Long refundAmount;

    /**
     * 是否已经发起退款，退前查询:false, 发起退款后查询:true ，对账使用，(泰山支付需要)
     */
    private boolean refundStart = true;

}
