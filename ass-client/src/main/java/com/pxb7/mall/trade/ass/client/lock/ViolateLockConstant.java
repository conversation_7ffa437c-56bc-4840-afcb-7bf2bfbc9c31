package com.pxb7.mall.trade.ass.client.lock;

import com.alibaba.cola.exception.BizException;
import com.pxb7.mall.trade.ass.client.dto.ErrorCode;
import org.apache.commons.lang3.StringUtils;

public class ViolateLockConstant {
    // 订单锁: key + orderItemId
    public final static String VIOLATE_ORDER_LOCK = "violate:order:lock:{%s}";
    // 违约单锁: key + violateId
    public final static String VIOLATE_ID_LOCK = "violate:id:lock:{%s}";


    /**
     * 获取订单锁
     */
    public static String getOrderLockKey(String orderItemId) {
        return getLockKey(orderItemId, null);
    }

    /**
     * 获取违约单锁
     */
    public static String getViolateLockKey(String violateId) {
        return getLockKey(null, violateId);
    }


    private static String getLockKey(String orderItemId, String violateId) {
        String key;
        if (StringUtils.isNotBlank(orderItemId)) {
            key = String.format(ViolateLockConstant.VIOLATE_ORDER_LOCK, orderItemId);
        } else if (StringUtils.isNotBlank(violateId)) {
            key = String.format(ViolateLockConstant.VIOLATE_ID_LOCK, orderItemId);
        } else {
            throw new BizException(ErrorCode.VIOLATE_GET_LOCK_ERROR.getErrDesc());
        }
        return key;
    }


}
