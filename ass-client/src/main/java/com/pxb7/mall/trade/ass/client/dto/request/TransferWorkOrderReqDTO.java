package com.pxb7.mall.trade.ass.client.dto.request;

import com.alibaba.cola.dto.DTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * Copyright (C), 2002-2024, 螃蟹游戏服务网
 *
 * @fileName: TransferWorkOrderReqDTO.java
 * @description: 工单转交请求的DTO
 * @author: guo<PERSON><PERSON><PERSON>
 * @email: <EMAIL>
 * @date: 2024/9/20 22:09
 * @history: //修改记录
 * <author> <time> <version> <desc>
 * 修改人姓名 修改时间 版本号 描述
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class TransferWorkOrderReqDTO extends DTO {
    /**
     * 房间id
     */
    private String roomId;

    /**
     * 工单id
     */
    private String complaintWorkId;

    /**
     * 订单id
     */
    private String orderItemId;
    /**
     * 处理人id
     */
    private String processorId;
    /**
     * 被转交人id
     */
    private String transfereeId;
    /**
     * 备注
     */
    private String note;
}
