package com.pxb7.mall.trade.ass.client.enums;

import java.util.Objects;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum AssClaimStatus {
    CLAIM(1, "待赔付审核"), FINANCE(2, "待财务审核"), ONLINE(3, "线上打款中"), SUCCESS(4, "打款成功"), FAIL(5, "打款失败");

    private final Integer code;
    private final String desc;

    public static AssClaimStatus fromCode(Integer code) {
        for (AssClaimStatus e : AssClaimStatus.values()) {
            if (Objects.equals(e.getCode(), code)) {
                return e;
            }
        }
        return null;
    }

}
