package com.pxb7.mall.trade.ass.client.dto.response.refund;

import com.alibaba.cola.dto.DTO;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 客服发起包赔变更退款 返回信息
 *
 * <AUTHOR>
 * @date 2025/5/13 15:34
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
public class IndemnityChangeServiceRefundRespDTO extends DTO {
    /**
     * 图款单ID
     */
    private String refundId;
}
