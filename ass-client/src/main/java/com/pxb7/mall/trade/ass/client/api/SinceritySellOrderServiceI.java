package com.pxb7.mall.trade.ass.client.api;

import com.alibaba.cola.dto.SingleResponse;
import com.pxb7.mall.trade.ass.client.dto.request.AssScheduleDubboReqDTO;
import com.pxb7.mall.trade.ass.client.dto.request.UserExposureCouponRefundReqDTO;

/**
 * <AUTHOR>
 */
public interface SinceritySellOrderServiceI {

   SingleResponse<Boolean> userExposureCouponRefund(UserExposureCouponRefundReqDTO param);

}
