package com.pxb7.mall.trade.ass.client.enums.violate;

import lombok.AllArgsConstructor;
import lombok.Getter;


import java.util.Arrays;

/**
 * 违约类型：
 */
@Getter
@AllArgsConstructor
public enum ViolateUserTypeEnum {
    /**
     * 买家
     */
    BUYER(1, "买家"),
    /**
     * 卖家
     */
    SELLER(2, "卖家"),

    ;

    private final Integer value;

    private final String label;

    public Boolean eq(Integer value) {
        return this.getValue().equals(value);
    }

    public static ViolateUserTypeEnum getEnum(Integer value) {
        if (null == value) {
            return null;
        }
        return Arrays.stream(ViolateUserTypeEnum.values()).filter(e -> value.equals(e.getValue())).findFirst()
            .orElse(null);
    }
}
