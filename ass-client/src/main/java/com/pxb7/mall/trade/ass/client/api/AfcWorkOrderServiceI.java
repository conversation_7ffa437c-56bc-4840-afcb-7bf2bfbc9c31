package com.pxb7.mall.trade.ass.client.api;

import com.alibaba.cola.dto.SingleResponse;
import com.pxb7.mall.trade.ass.client.dto.response.afc.ComplaintWORespDTO;
import com.pxb7.mall.trade.ass.client.dto.response.afc.DisputeWORespDTO;
import com.pxb7.mall.trade.ass.client.dto.response.afc.RetrieveWORespDTO;

/**
 * Copyright (C), 2002-2025, 螃蟹游戏服务网
 *
 * @fileName: AfcWorkOrderServiceI.java
 * @description: 查询售后工单
 * @author: guominqiang
 * @email: <EMAIL>
 * @date: 2025/4/22 10:29
 * @history: //修改记录
 * <author> <time> <version> <desc>
 * 修改人姓名 修改时间 版本号 描述
 */
public interface AfcWorkOrderServiceI {

    /**
     * 根据工单ID查询找回工单信息
     *
     * @param workOrderId 找回工单ID
     * @return {@link RetrieveWORespDTO}
     */
    SingleResponse<RetrieveWORespDTO> searchRetrieveWOById(String workOrderId);

    /**
     * 根据工单ID查询纠纷工单信息
     *
     * @param workOrderId 纠纷工单ID
     * @return {@link DisputeWORespDTO}
     */
    SingleResponse<DisputeWORespDTO> searchDisputeWOById(String workOrderId);

    /**
     * 根据工单ID查询投诉工单信息
     *
     * @param workOrderId 投诉工单ID
     * @return {@link ComplaintWORespDTO}
     */
    SingleResponse<ComplaintWORespDTO> searchComplaintWOById(String workOrderId);
}
