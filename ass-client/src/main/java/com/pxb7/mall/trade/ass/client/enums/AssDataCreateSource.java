package com.pxb7.mall.trade.ass.client.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 数据创建来源
 **/
@AllArgsConstructor
@Getter
public enum AssDataCreateSource {
    IM(1, "IM客服端"), ADMIN(2, "后台");

    private final Integer code;
    private final String desc;

    public static AssDataCreateSource fromCode(Integer code) {
        for (AssDataCreateSource e : AssDataCreateSource.values()) {
            if (Objects.equals(e.getCode(), code)) {
                return e;
            }
        }
        return null;
    }
}
