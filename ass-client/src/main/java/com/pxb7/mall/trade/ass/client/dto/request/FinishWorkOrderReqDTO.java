package com.pxb7.mall.trade.ass.client.dto.request;

import com.alibaba.cola.dto.DTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * Copyright (C), 2002-2024, 螃蟹游戏服务网
 *
 * @fileName: FinishWorkOrderReqDTO.java
 * @description: 工单完结请求的DTO
 * @author: guo<PERSON><PERSON><PERSON>
 * @email: <EMAIL>
 * @date: 2024/9/20 21:28
 * @history: //修改记录
 * <author> <time> <version> <desc>
 * 修改人姓名 修改时间 版本号 描述
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class FinishWorkOrderReqDTO extends DTO {

    /**
     * 房间id
     */
    private String roomId;

    /**
     * 工单id
     */
    private String complaintWorkId;

    /**
     * 订单id
     */
    private String orderItemId;

    /**
     * 处理结果
     */
    private String dealResult;

    /**
     * 备注
     */
    private String note;

    /**
     * 问题部门名称
     */
    private String departmentName;

    /**
     * 问题类型
     */
    private String questionType;

    /**
     * 问题级别:1-6 1:一级 2:二级 ...6:六级
     */
    private Integer questionLevel;

    /**
     * 责任人
     */
    private String responsiblePerson;

    /**
     * 是否有责 0:否 1是
     */
    private Boolean responsibility;

    /**
     * 处理人ID
     */
    private String processorId;

}
