package com.pxb7.mall.trade.ass.client.api;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.pxb7.mall.trade.ass.client.dto.request.CreateWorkOrderReqDTO;
import com.pxb7.mall.trade.ass.client.dto.request.FinishWorkOrderReqDTO;
import com.pxb7.mall.trade.ass.client.dto.request.TransferWorkOrderReqDTO;
import com.pxb7.mall.trade.ass.client.dto.response.DepartmentConfigRespDTO;
import com.pxb7.mall.trade.ass.client.dto.response.WorkOrderDetailRespDTO;
import com.pxb7.mall.trade.ass.client.dto.response.WorkOrderLogRespDTO;

/**
 * Copyright (C), 2002-2024, 螃蟹游戏服务网
 *
 * @fileName: ComplaintWorkServiceI.java
 * @description: 客诉工单dubbo接口
 * @author: guominqi<PERSON>
 * @email: <EMAIL>
 * @date: 2024/9/23 14:51
 * @history: //修改记录
 * <author> <time> <version> <desc>
 * 修改人姓名 修改时间 版本号 描述
 */
public interface ComplaintWorkServiceI {
    /**
     * 创建工单
     *
     * @param createWorkOrderReqDTO
     * @return
     */
    SingleResponse<Boolean> createWork(CreateWorkOrderReqDTO createWorkOrderReqDTO);

    /**
     * 完结工单
     *
     * @param finishWorkOrderReqDTO
     * @return
     */

    SingleResponse<Boolean> finishWork(FinishWorkOrderReqDTO finishWorkOrderReqDTO);

    /**
     * 转交工单
     *
     * @param transferWorkOrderReqDTO
     * @return
     */
    SingleResponse<Boolean> transferWork(TransferWorkOrderReqDTO transferWorkOrderReqDTO);

    /**
     * 查看工单详情
     *
     * @param complaintWorkId 工单id
     * @return
     */
    SingleResponse<WorkOrderDetailRespDTO> viewDetail(String complaintWorkId);

    /**
     * 查看工单日志
     *
     * @param complaintWorkId 工单id
     * @return
     */
    MultiResponse<WorkOrderLogRespDTO> viewLog(String complaintWorkId);

    /**
     * 部门列表
     *
     * @return
     */
    MultiResponse<DepartmentConfigRespDTO> departmentList();
}
