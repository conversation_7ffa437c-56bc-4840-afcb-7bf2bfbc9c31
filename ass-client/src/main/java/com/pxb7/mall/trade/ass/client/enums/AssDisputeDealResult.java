package com.pxb7.mall.trade.ass.client.enums;

import java.util.Objects;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 纠纷工单处理结果
 *
 * <AUTHOR>
 * @since: 2024-08-13 20:11
 **/
@AllArgsConstructor
@Getter
public enum AssDisputeDealResult {
    NORMAL(1, "正常完结"), RULE(2, "平台规则完结"), CLAIM(3, "赔付安抚完结");

    private final Integer code;
    private final String desc;

    public static AssDisputeDealResult fromCode(Integer value) {
        for (AssDisputeDealResult e : AssDisputeDealResult.values()) {
            if (Objects.equals(e.getCode(), value)) {
                return e;
            }
        }
        return null;
    }
}
