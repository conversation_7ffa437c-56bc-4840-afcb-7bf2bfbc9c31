package com.pxb7.mall.trade.ass.client.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 售后日志展示类型
 *
 * <AUTHOR>
 **/
@AllArgsConstructor
@Getter
public enum AssShowType {
    ALL(0, "全展示"),
    USER(1, "用户端"),
    ADMIN(2, "后台");

    private final Integer code;
    private final String desc;

    public static AssShowType fromCode(Integer code) {
        for (AssShowType value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
