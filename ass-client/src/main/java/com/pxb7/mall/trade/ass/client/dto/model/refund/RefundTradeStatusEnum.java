package com.pxb7.mall.trade.ass.client.dto.model.refund;

import java.util.Arrays;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum RefundTradeStatusEnum {

    /**
     * 执行中
     */
    EXECUTING(2, "执行中"),

    /**
     * 执行成功
     */
    SUCCESS(3, "执行成功"),

    /**
     * 执行失败
     */
    FAIL(4, "执行失败"),

    /**
     * 重复执行, 只在发起退款前查询结果，根据对账结果对比出现
     */
    REPEATED(999, "重复执行"),;

    private final Integer value;

    private final String label;

    public static String getLabel(Integer value) {
        return Arrays.stream(RefundTradeStatusEnum.values()).filter(e -> e.getValue().equals(value))
            .map(RefundTradeStatusEnum::getLabel).findAny().get();
    }

    public boolean eq(Integer value) {
        return this.value.equals(value);
    }

}
