package com.pxb7.mall.trade.ass.client.enums;

import java.util.Objects;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 数据来源
 *
 * <AUTHOR>
 * @since: 2024-08-10 15:13
 **/
@AllArgsConstructor
@Getter
public enum AssSourceType {
    WEB(1, "螃蟹主站"), ALIPAY(2, "支付宝小程序");

    private final Integer code;
    private final String desc;

    public static AssSourceType fromCode(Integer value) {
        for (AssSourceType e : AssSourceType.values()) {
            if (Objects.equals(e.getCode(), value)) {
                return e;
            }
        }
        return null;
    }
}
