package com.pxb7.mall.trade.ass.client.enums;

import java.util.Objects;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 售后跟进记录添加方式
 *
 * <AUTHOR>
 * @since: 2024-08-14 17:17
 **/
@AllArgsConstructor
@Getter
public enum AssLogAddWay {
    AUTO(1, "系统自动产生的日志"), MANUAL(2, "手动添加的日志");

    private final Integer code;
    private final String desc;

    public static AssLogAddWay fromCode(Integer value) {
        for (AssLogAddWay e : AssLogAddWay.values()) {
            if (Objects.equals(e.getCode(), value)) {
                return e;
            }
        }
        return null;
    }
}
