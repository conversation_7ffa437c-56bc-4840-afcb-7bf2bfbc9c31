package com.pxb7.mall.trade.ass.client.dto.response.afc;

import com.pxb7.mall.trade.ass.client.enums.AfcComplaintWOStatus;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Copyright (C), 2002-2025, 螃蟹游戏服务网
 *
 * @fileName: ComplaintWORespDTO.java
 * @description: 客诉工单响应对象
 * @author: guo<PERSON><PERSON><PERSON>
 * @email: <EMAIL>
 * @date: 2025/4/22 11:11
 * @history: //修改记录
 * <author> <time> <version> <desc>
 * 修改人姓名 修改时间 版本号 描述
 */
@Data
@Accessors(chain = true)
public class ComplaintWORespDTO implements Serializable {
    /**
     * 工单id
     */
    private String workOrderId;
    /**
     * 房间id
     */
    private String roomId;

    /**
     * 订单id
     */
    private String orderItemId;

    /**
     * 投诉标题
     */
    private String complaintTitle;

    /**
     * 投诉渠道1:IM 2支付宝 3闲鱼 4:12315 5消费宝 6连连支付 7电话 8反诈邮箱 9外部门升级 10黑猫投诉 11-工商局 12工信部
     */
    private Integer complaintChannel;

    /**
     * 工单状态1待处理2已完成
     *
     * @see AfcComplaintWOStatus
     */
    private Integer workOrderStatus;

    /**
     * 当前处理人ID
     */
    private String currentProcessorId;
    /**
     * 处理结果
     */
    private String dealResult;

    /**
     * 被转交人id
     */
    private String transfereeId;
    /**
     * 工单完结人id
     */
    private String finisherId;
    /**
     * 完结时间
     */
    private LocalDateTime finishTime;

    /**
     * 投诉级别:1-6
     */
    private Integer complaintLevel;

    /**
     * 问题级别:1-6
     */
    private Integer questionLevel;
    /**
     * 投诉文字内容
     */
    private String complaintContent;
    /**
     * 创建来源(1:系统,2:IM)
     */
    private Integer complaintSource;

    /**
     * 是否有效 0:否 1是
     */
    private Integer isValid;
}
