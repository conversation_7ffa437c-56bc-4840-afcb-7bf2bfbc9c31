package com.pxb7.mall.trade.ass.infra.repository.es.entity;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.experimental.FieldNameConstants;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.*;
import java.time.LocalDateTime;

/**
 * 售后列表聚合数据
 */
@Data
@Document(indexName = "ass_info_aggregation")
@Setting(shards = 3)
@FieldNameConstants
public class AssInfoAggregation {
    /*************************************************售后单信息****************************************************/
    /**
     * 售后单ID
     */
    @Field(type = FieldType.Keyword, value = "work_order_id")
    private String assWorkOrderId;

    /*************************************************订单信息****************************************************/
    /**
     * 订单ID
     */
    @Id
    @Field(type = FieldType.Keyword, value = "order_item_id")
    private String orderItemId;
    /**
     * 商品id
     */
    @Field(type = FieldType.Keyword, name = "product_id")
    private String productId;
    /**
     * 游戏ID
     */
    @Field(type = FieldType.Keyword, value = "game_id")
    private String gameId;
    /**
     * 商品类型: 1账号 2充值 3金币 4装备 5初始号 20诚心卖
     */
    @Field(type = FieldType.Integer, value = "product_type")
    private Integer productType;
    /**
     * 1待付款 2交易中 3待结算 4已成交 5已取消 (待支付是买家, 其他都是卖家状态) 6退款已取消
     */
    @Field(type = FieldType.Integer, value = "order_item_status")
    private Integer orderItemStatus;
    /**
     * 订单行金额
     */
    @Field(type = FieldType.Long, value = "order_item_amount")
    private Long orderItemAmount;
    /**
     * 订单行应付款金额
     */
    @Field(type = FieldType.Long, value = "order_item_pay_amount")
    private Long orderItemPayAmount;
    /**
     * 订单行实付金额
     */
    @Field(type = FieldType.Long, value = "order_item_actual_pay_amount")
    private Long orderItemActualPayAmount;
    /**
     * 购买的商品数量
     */
    @Field(value = "product_quantity")
    private Integer productQuantity;
    /**
     * 订单完结时间
     */
    @Field(type = FieldType.Date, format = DateFormat.date_hour_minute_second_fraction, value = "complete_time")
    private LocalDateTime completeTime;
    /**
     * 订单取消时间
     */
    @Field(type = FieldType.Date, format = DateFormat.date_hour_minute_second_fraction, value = "cancel_time")
    private LocalDateTime cancelTime;

    // 订单创建时间
    @Field(value = "create_time", type = FieldType.Date, format = DateFormat.date_hour_minute_second_fraction)
    private LocalDateTime createTime;

    // 索引更新时间
    @Field(value = "index_update_time", type = FieldType.Date, format = DateFormat.date_hour_minute_second_fraction)
    private LocalDateTime indexUpdateTime;
    /**
     * 商品名称
     */
    @Field(value = "product_name", type = FieldType.Text, analyzer = "ik_max_word")
    private String productName;
    /**
     * 商品短标
     */
    @Field(value = "product_short_name", type = FieldType.Text, analyzer = "ik_max_word")
    private String productShortName;
    /**
     * 商品图片
     */
    @Field(value = "product_pic", type = FieldType.Keyword)
    private String productPic;
    /**
     * 商品属性
     */
    @Field(value = "product_attr", type = FieldType.Keyword)
    private String productAttr;
    /**
     * 账号信息
     */
    @Field(value = "game_account", type = FieldType.Keyword)
    private String gameAccount;

    @Field(value = "product_unique_no", type = FieldType.Keyword)
    private String productUniqueNo;
    /**
     * 游戏名称
     */
    @Field(value = "game_name", type = FieldType.Text)
    private String gameName;
    /**
     * 游戏名称
     */
    @Field(value = "game_attr", type = FieldType.Keyword)
    private String gameAttr;
    /**
     * 交付群聊房间id, 智能交付是新群交付, 中介订单是原群交付
     */
    @Field(type = FieldType.Keyword, value = "delivery_room_id")
    private String deliveryRoomId;
    /**
     * 买家id
     */
    @Field(type = FieldType.Keyword,value = "buyer_id")
    private String buyerId;
    /**
     * 买家身份 1散户 2号商
     */
    @Field(value = "buyer_user_type",type = FieldType.Integer)
    private Integer buyerUserType;

    @Field(type = FieldType.Keyword, value = "buyer_merchant_id")
    private String buyerMerchantId;
    /**
     * 卖家id,诚心卖的卖家是系统
     */
    @Field(type = FieldType.Keyword,value = "seller_id")
    private String sellerId;
    /**
     * 卖家身份 0系统 1散户 2号商
     */
    @Field(value = "seller_user_type",type = FieldType.Integer)
    private Integer sellerUserType;

    @Field(type = FieldType.Keyword,value = "seller_merchant_id")
    private String sellerMerchantId;


    //售后工单模块

    /**
     * 最新售后工单日志
     */
    @Field(type = FieldType.Nested, value = "ass_work_order_log")
    private AssWorkOrderLog assLatestWorkOrderLog;

    /**
     * 售后类型: 1:找回 2:纠纷
     */
    @Field(type = FieldType.Integer, value = "ass_type")
    private Integer assType;

    /**
     * 售后状态: 0:处理中 1:已完成 2:已取消
     */
    @Field(type = FieldType.Integer, value = "ass_status")
    private Integer assStatus;

    /**
     * 售后工单状态备注
     */
    @Field(value = "ass_status_memo", type = FieldType.Text)
    private String assStatusMemo;

    /**
     * 售后子订单id
     */
    @Field(type = FieldType.Keyword, value = "ass_sub_order_id")
    private String assSubOrderId;
    /**
     * 售后完成时间
     */
    @Field(type = FieldType.Date, format = DateFormat.date_hour_minute_second_fraction, value = "ass_complete_time")
    private LocalDateTime assCompleteTime;
    /**
     * 售后期望完成时间
     */
    @Field(type = FieldType.Date, format = DateFormat.date_hour_minute_second_fraction, value = "ass_expected_time")
    private LocalDateTime assExpectedTime;

    /**
     * 售后工单是否已读 0:未读 1:已读
     */
    @Field(type = FieldType.Integer, value = "read_flag")
    private Integer readFlag;

    /**
     * 售后申请时间
     */
    @Field(type = FieldType.Date, format = DateFormat.date_hour_minute_second_fraction, value = "ass_apply_time")
    private LocalDateTime assApplyTime;


    /**
     * 售后单更新时间
     */
    @Field(type = FieldType.Date, format = DateFormat.date_hour_minute_second_fraction, value = "ass_update_time")
    private LocalDateTime assUpdateTime;

    /**
     * 售后创建人id
     */
    @Field(type = FieldType.Keyword, value = "ass_create_user_id")
    private String assCreateUserId;


    @Setter
    @Getter
    @FieldNameConstants
    @Accessors(chain = true)
    public static class AssWorkOrderLog {
        /**
         * 日志id
         */
        @Field(type = FieldType.Keyword, value = "work_order_log_id")
        private String workOrderLogId;

        /**
         * 日志标题
         */
        @Field(type = FieldType.Text, value = "title")
        private String title;

        /**
         * 售后类型1找回2纠纷
         */
        @Field(type = FieldType.Integer, value = "ass_type")
        private Integer assType;
        /**
         * 0:全展示 1:用户端展示 2:后台展示
         */
        @Field(type = FieldType.Integer, value = "show_type")
        private Integer showType;
        /**
         * 用户端展示操作内容
         */
        @Field(type = FieldType.Text, value = "content")
        private String content;
        /**
         * admin展示操作内容
         */
        @Field(type = FieldType.Text, value = "admin_content")
        private String adminContent;
        /**
         * 日志添加方式1系统自动产生的日志 2手动添加的日志
         */
        @Field(type = FieldType.Integer, value = "add_way")
        private Integer addWay;
        /**
         * 节点id
         */
        @Field(type = FieldType.Keyword, value = "node_id")
        private String nodeId;
        /**
         * 当前节点状态描述
         */
        @Field(type = FieldType.Text, value = "node_desc")
        private String nodeDesc;

        /**
         * 通知类型 0:不通知 1:追回账号,待卖家换绑2:追回号款,待卖家提供收款账号3:售后到期,待买家接受赔付
         */
        @Field(type = FieldType.Integer, value = "notice_type")
        private Integer noticeType;
        /**
         * 进群提醒文案
         */
        @Field(type = FieldType.Text, value = "join_group_msg")
        private String joinGroupMsg;
        /**
         * 创建人id
         */
        @Field(type = FieldType.Text, value = "create_user_id")
        private String createUserId;
        /**
         * 记录创建时间
         */
        @Field(type = FieldType.Date, format = DateFormat.date_hour_minute_second_fraction, value = "create_time")
        private LocalDateTime createTime;
        /**
         * 更新人id
         */
        @Field(type = FieldType.Text, value = "update_user_id")
        private String updateUserId;

    }

}
