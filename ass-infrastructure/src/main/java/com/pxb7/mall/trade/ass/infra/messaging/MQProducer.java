package com.pxb7.mall.trade.ass.infra.messaging;

import java.time.Duration;
import java.util.concurrent.CompletableFuture;
import java.util.function.Consumer;

/**
 * @description:
 * @author: heyc
 * @date: 2024/5/14 16:45
 * @Version 1.0
 **/
public interface MQProducer {

    /**
     * 同步发送普通消息
     *
     * @param destination
     * @param payload
     * @return 消息id
     */
    String send(String destination, Object payload);

    /**
     *
     * @param topic
     * @param tag
     * @param payload
     * @return
     */
    String send(String topic, String tag, Object payload);
    /**
     * 异步发送消息
     *
     * @param destination
     * @param payload
     * @return
     */
    CompletableFuture<String> asyncSend(String destination, Object payload);

    /**
     * 异步发送消息
     * @param topic
     * @param tag
     * @param payload
     * @return
     */
    CompletableFuture<String> asyncSend(String topic, String tag, Object payload);
    /**
     * 发送延时消息
     *
     * @param destination
     * @param payload
     * @param messageDelayTime
     * @return
     */
    String sendDelay(String destination, Object payload, Duration messageDelayTime);

    /**
     * 异步发送延时消息
     *
     * @param destination
     * @param payload
     * @param messageDelayTime
     * @return
     */
    CompletableFuture<String> asyncSendDelay(String destination, Object payload, Duration messageDelayTime);

    /**
     * 发送顺序消息
     *
     * @param destination
     * @param payload
     * @param group
     * @return
     */
    String sendOrder(String destination, Object payload, String group);

    /**
     * 异步发送顺序消息
     *
     * @param destination
     * @param payload
     * @param group
     * @return
     */
    CompletableFuture<String> asyncSendOrder(String destination, Object payload, String group);

    /**
     * 发送事务消息
     *
     * @param destination
     * @param payload
     * @param callback
     * @return
     */
    String sendInTransaction(String destination, Object payload, Consumer<String> callback);
}
