package com.pxb7.mall.trade.ass.infra.repository.db.entity;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.*;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 收款单(ReceiptVoucher)实体类
 *
 * <AUTHOR>
 * @since 2024-09-25 16:16:34
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
@TableName(value = "receipt_voucher")
public class ReceiptVoucher implements Serializable {
    private static final long serialVersionUID = -82077164647775053L;
    @TableField(value = "id")
    private Long id;
    /**
     * 主订单id
     */
    @TableField(value = "order_id")
    private String orderId;
    /**
     * 订单行id
     */
    @TableId(value = "order_item_id", type = IdType.INPUT)
    private String orderItemId;
    /**
     * 收款单业务id
     */
    @TableField(value = "receipt_voucher_id")
    private String receiptVoucherId;
    /**
     * 支付单id
     */
    @TableField(value = "payment_id")
    private String paymentId;
    /**
     * 支付模式 1线上支付 2线下支付 3挂账
     */
    @TableField(value = "pay_mode")
    private Integer payMode;
    /**
     * 1待收款 2进行中 3 已收款 4 收款失败
     */
    @TableField(value = "status")
    private String status;
    /**
     * 1待审核 2收款审核中 3人工审核通过 4系统自动审核通过 5审核失败 6已撤销
     */
    @TableField(value = "review_status")
    private Integer reviewStatus;
    /**
     * 需收款金额
     */
    @TableField(value = "need_pay_amount")
    private Long needPayAmount;
    /**
     * 实收金额
     */
    @TableField(value = "actual_pay_amount")
    private Long actualPayAmount;
    /**
     * 业务类型 0用户立即支付 1补收号价 2补收包赔费用 3补收违约金
     */
    @TableField(value = "receipt_type")
    private Integer receiptType;
    /**
     * 提交人id
     */
    @TableField(value = "submitter")
    private String submitter;
    /**
     * 提交人姓名
     */
    @TableField(value = "submitter_name")
    private String submitterName;
    /**
     * 客服备注
     */
    @TableField(value = "submit_remark")
    private String submitRemark;
    /**
     * 收款账户名称
     */
    @TableField(value = "company_account")
    private String companyAccount;
    /**
     * 支付截图
     */
    @TableField(value = "pay_screenshot")
    private String payScreenshot;
    /**
     * 审核人id
     */
    @TableField(value = "reviewer_id")
    private String reviewerId;
    /**
     * 审核人姓名
     */
    @TableField(value = "reviewer_name")
    private String reviewerName;
    /**
     * 审核备注
     */
    @TableField(value = "review_remark")
    private String reviewRemark;
    /**
     * 渠道id
     */
    @TableField(value = "channel_id")
    private String channelId;
    /**
     * 支付时间
     */
    @TableField(value = "payment_time")
    private LocalDateTime paymentTime;
    /**
     * 线下支付公司账户id
     */
    @TableField(value = "company_account_id")
    private String companyAccountId;
    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    @TableField(value = "update_time", update="now()")
    private LocalDateTime updateTime;
    /**
     * 逻辑删除，1:删除，0:正常
     */
    @TableLogic
    @TableField(value = "is_deleted")
    private Boolean deleted;

}
