package com.pxb7.mall.trade.ass.infra.repository.db;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pxb7.mall.trade.ass.infra.model.AssRejectReasonConfigReqPO;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.AssRejectReasonConfig;

/**
 * 驳回原因配置表(AssRejectReasonConfig)表服务接口
 *
 * <AUTHOR>
 * @since 2025-07-30 13:45:15
 */
public interface AssRejectReasonConfigRepository extends IService<AssRejectReasonConfig> {

    boolean insert(AssRejectReasonConfigReqPO.AddPO param);

    boolean update(AssRejectReasonConfigReqPO.UpdatePO param);

    boolean deleteById(AssRejectReasonConfigReqPO.DelPO param);

    AssRejectReasonConfig findById(Long id);

    List<AssRejectReasonConfig> list(AssRejectReasonConfigReqPO.SearchPO param);

    Page<AssRejectReasonConfig> page(AssRejectReasonConfigReqPO.PagePO param);


    List<AssRejectReasonConfig> getListByType(Integer assType);

}

