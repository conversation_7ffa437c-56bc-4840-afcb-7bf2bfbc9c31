package com.pxb7.mall.trade.ass.infra.repository.db.mapper;

import com.pxb7.mall.trade.ass.infra.repository.db.entity.AssRetrieveGameConfig;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 售后找回游戏配置(AssRetrieveGameConfig)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-08-01 14:11:25
 */
@Mapper
public interface AssRetrieveGameConfigMapper extends BaseMapper<AssRetrieveGameConfig> {
    /**
     * 批量新增数据
     *
     * @param entities List<AssRetrieveGameConfig> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<AssRetrieveGameConfig> entities);

}

