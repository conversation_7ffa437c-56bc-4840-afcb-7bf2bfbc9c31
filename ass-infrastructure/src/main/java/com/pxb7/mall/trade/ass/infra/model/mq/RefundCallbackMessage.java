package com.pxb7.mall.trade.ass.infra.model.mq;

import com.pxb7.mall.trade.ass.client.dto.model.refund.PayChannelEnum;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 退款回调 消息体
 */
@Data
@Accessors(chain = true)
public class RefundCallbackMessage implements Serializable {
    /**
     * 回调渠道：目前只有京东接入退款回调
     */
    private PayChannelEnum channel;
    /**
     * 回调返回的数据
     */
    private String callbackData;
}
