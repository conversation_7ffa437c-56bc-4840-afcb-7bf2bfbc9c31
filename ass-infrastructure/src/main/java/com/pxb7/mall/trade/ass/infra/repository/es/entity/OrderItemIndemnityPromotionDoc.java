package com.pxb7.mall.trade.ass.infra.repository.es.entity;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import java.io.Serializable;

/**
 * 子订单-包赔优惠表(OrderItemIndemnityPromotionDoc)es
 */
@Data
public class OrderItemIndemnityPromotionDoc implements Serializable {

    /**
     * 订单行包赔优惠业务id
     */
    @Id()
    @Field(value = "order_item_indemnity_promotion_id", type = FieldType.Keyword)
    private String order_item_indemnity_promotion_id;

    /**
     * 订单行包赔业务id
     */
    @Field(value = "order_item_indemnity_id", type = FieldType.Keyword)
    private String orderItemIndemnityId;

    /**
     * 订单包赔业务id
     */
    @Field(value = "order_item_id" ,type = FieldType.Keyword)
    private String orderItemId;

    /**
     * 优惠类型 1:包赔商家折扣 2:包赔限时折扣
     */
    @Field(value = "promotion_type", type = FieldType.Integer)
    private Integer promotionType;

    /**
     * 优惠关联id
     */
    @Field(value = "promotion_relation_id", type = FieldType.Keyword)
    private String promotionRelationId;

    /**
     * 优惠关联名称
     */
    @Field(value = "promotion_relation_name", type = FieldType.Text)
    private String promotionRelationName;

    /**
     * 优惠金额
     */
    @Field(value = "promotion_amount", type = FieldType.Long)
    private Long promotionAmount;

    /**
     * 优惠比例
     */
    @Field(value = "promotion_ratio", type = FieldType.Long)
    private Long promotionRatio;

    /**
     * 折扣上限金额
     */
    @Field(value = "promotion_limit_amount", type = FieldType.Long)
    private Long promotionLimitAmount;

}
