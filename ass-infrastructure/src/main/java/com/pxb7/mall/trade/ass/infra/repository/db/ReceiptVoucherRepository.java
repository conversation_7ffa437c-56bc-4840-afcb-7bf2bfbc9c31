package com.pxb7.mall.trade.ass.infra.repository.db;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.ReceiptVoucher;

/**
 * 收款单(ReceiptVoucher)表服务接口
 *
 * <AUTHOR>
 * @since 2024-09-25 16:16:37
 */
public interface ReceiptVoucherRepository extends IService<ReceiptVoucher> {
    /**
     * 根据receiptId查询收款成功的收款单
     *
     */
    List<ReceiptVoucher> getSuccessByReceiptId(List<String> receiptVoucherIds);
}
