package com.pxb7.mall.trade.ass.infra.enums.notis;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 功能描述:微信模版
 * 作者：白春韬
 * 创建日期：2025/08/06 
 * 公司名称：金华博淳网络科技有限公司 
 * 域名： www.pxb7.com
 */
@Getter
@AllArgsConstructor
public enum WechatEnum {
    /**
     * 售后工单创建成功
     */
    WECHAT_AFTER_SALE_WORK_ORDER_CREATE(0, "售后进度通知", "售后已受理，请在「我的-售后」中查看进度"),
    /**
     * 跟进记录-追回账号，待买家换绑
     */
    WECHAT_AFTER_SALE_WORK_ORDER_TO_BIND(1, "售后进度通知", "售后处理中，已追回账号，在我的售后中查看"),
    /**
     * 跟进记录-追回号款，待买家提供收款账号
     */
    WECHAT_AFTER_SALE_WORK_ORDER_TO_PROVIDE_ACCOUNT(2, "售后进度通知", "售后处理中，已追回号款，在我的售后中查看"),
    /**
     * 跟进记录-售后到期，待买家接收赔付
     */
    WECHAT_AFTER_SALE_WORK_ORDER_EXPIRED(3, "售后进度通知", "售后已到期，请在「我的-售后」中接收赔付"),

    ;

    private final Integer code;
    private final String wechatTemplateName;
    private final String wechatTemplateRemark;

    public static WechatEnum getWechatEnum(Integer code) {
        for (WechatEnum wechatEnum : WechatEnum.values()) {
            if (wechatEnum.getCode().equals(code)) {
                return wechatEnum;
            }
        }
        return null;
    }
}
