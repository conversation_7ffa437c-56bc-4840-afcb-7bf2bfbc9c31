package com.pxb7.mall.trade.ass.infra.util;

import static com.pxb7.mall.trade.ass.client.enums.AssWoType.DISPUTE;
import static com.pxb7.mall.trade.ass.client.enums.AssWoType.RETRIEVE;

import java.text.SimpleDateFormat;

import com.pxb7.mall.trade.ass.client.enums.AssWoType;

/**
 * 售后工单编号生成
 *
 * <AUTHOR>
 * @since: 2024-08-10 09:51
 **/
public class AssNoUtil {

    // 纠纷工单固定前缀
    public static final String PREFIX_DISPUTE = "NAD";
    // 找回工单固定前缀
    public static final String PREFIX_RETRIEVE = "NAF";

    public static String generateAssNo(AssWoType assWoType) {
        var format = new SimpleDateFormat("yyMMdd");
        String date = format.format(System.currentTimeMillis());
        if (DISPUTE == assWoType) {
            return PREFIX_DISPUTE + date + System.currentTimeMillis();
        } else if (RETRIEVE == assWoType) {
            return PREFIX_RETRIEVE + date + System.currentTimeMillis();
        }
        return "";
    }
}
