package com.pxb7.mall.trade.ass.infra.repository.gateway.user;

import cn.hutool.core.util.StrUtil;
import com.alibaba.cola.dto.MultiResponse;

import com.alibaba.cola.dto.SingleResponse;
import com.pxb7.mall.im.client.api.AdminImUserServiceI;
import com.pxb7.mall.im.client.dto.request.CustomerCareLastFeishuAuthReqDTO;
import com.pxb7.mall.im.client.dto.response.CustomerCareLastFeishuAuthRespDTO;
import com.pxb7.mall.trade.ass.infra.mapping.UserInfoMapping;
import com.pxb7.mall.trade.ass.infra.model.UserCertInfoRespPO;
import com.pxb7.mall.trade.ass.infra.util.DubboCatchUtil;
import com.pxb7.mall.user.api.UserServiceI;
import com.pxb7.mall.user.dto.response.user.UserCertInfoRespDTO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class UserGatewayRepository {

    @DubboReference(check = false)
    private UserServiceI userServiceI;

    @Resource
    private UserInfoMapping userInfoMapping;

    @DubboReference
    private AdminImUserServiceI adminImUserServiceI;


    public List<UserCertInfoRespPO> getUserCertInfoList(List<String> userIds) {
        MultiResponse<UserCertInfoRespDTO> response = userServiceI.getUserCertInfoList(userIds);
        if (Objects.isNull(response) || !response.isSuccess()) {
            return Collections.emptyList();
        }
        return userInfoMapping.toUserCertInfoRespPOList(response.getData());
    }


    public UserCertInfoRespPO getUserCertInfo(String userId) {
        List<UserCertInfoRespPO> userCertInfoList = this.getUserCertInfoList(List.of(userId));
        if (CollectionUtils.isEmpty(userCertInfoList)){
            return null;
        }
        return userCertInfoList.get(0);
    }


    public String queryFeiShuName(String userId) {
        CustomerCareLastFeishuAuthReqDTO customerCareLastFeishuAuthReqDTO = CustomerCareLastFeishuAuthReqDTO.builder().userId(userId).build();
        SingleResponse<CustomerCareLastFeishuAuthRespDTO> resp = DubboCatchUtil.catchException(() -> adminImUserServiceI.customerCareLastFeishuAuth(customerCareLastFeishuAuthReqDTO), "IM_USER_SERVICE_ERROR");
        if (Objects.isNull(resp) || !resp.isSuccess() || Objects.isNull(resp.getData())) {
            return null;
        }
        CustomerCareLastFeishuAuthRespDTO authRespDTO = resp.getData();
        String feiShuName = Objects.nonNull(authRespDTO)
                ? (authRespDTO.getFeishuName() + "(" + authRespDTO.getFeishuEmployeeNo() + ")")
                : StrUtil.EMPTY;
        return feiShuName;
    }


}
