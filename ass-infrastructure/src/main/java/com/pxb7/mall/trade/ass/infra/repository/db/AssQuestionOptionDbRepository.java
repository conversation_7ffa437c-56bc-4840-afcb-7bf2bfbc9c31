package com.pxb7.mall.trade.ass.infra.repository.db;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.AssQuestionOption;
import com.pxb7.mall.trade.ass.infra.repository.db.mapper.AssQuestionOptionMapper;

/**
 * 售后问答问题方案配置
 * 
 * <AUTHOR>
 * @since: 2024-08-13 16:01
 **/
@Service
public class AssQuestionOptionDbRepository extends ServiceImpl<AssQuestionOptionMapper, AssQuestionOption>
    implements AssQuestionOptionRepository {

    @Override
    public AssQuestionOption findOneByOptionId(String optionId) {
        if (StringUtils.isBlank(optionId)) {
            return null;
        }
        return this.lambdaQuery().eq(AssQuestionOption::getOptionId, optionId).eq(AssQuestionOption::getEnable, true)
            .orderByDesc(AssQuestionOption::getId).last(" limit 1 ").one();
    }
}
