package com.pxb7.mall.trade.ass.infra.model;

import java.util.List;

import com.pxb7.mall.trade.order.client.dto.PageDTO;

import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class RefundVoucherSearchPO extends PageDTO {

    /**
     * 周期 1本周内 2 当月 3 三个月内 4半年内 5 一年内
     */
    private Integer cycle;

    /**
     * 商品类型
     */
    private Integer productType;

    /**
     * 游戏
     */
    private String gameId;

    /**
     * 关键词
     */
    private String keyWords;

    /**
     * 身份 0 买家(我买的) 1 卖家（我卖的）
     */
    private Integer identity;
    /** 订单编号 */
    private String orderItemId;
    /** 手机号（子账号） */
    private String telephone;
    /** 商品名称 */
    private String productName;
    /** 商品id */
    private String productId;
    /** 商品编号 */
    private String productUniqueNo;
    /** 开始时间 */
    private String startTime;
    /** 结束时间 */
    private String endTime;
    // 用户id
    private String userId;
    /**
     * 退款状态筛选
     */
    private List<Integer> refundStatusList;
}
