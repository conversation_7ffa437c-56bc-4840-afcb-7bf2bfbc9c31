package com.pxb7.mall.trade.ass.infra.exception.ensure.extensions;

import com.pxb7.mall.trade.ass.infra.enums.ErrorCode;
import com.pxb7.mall.trade.ass.infra.exception.ExceptionFactory;

import java.util.Collection;

import cn.hutool.core.collection.CollectionUtil;

/**
 * 断言工具类
 *
 * <AUTHOR>
 * @date 2024/08/07 15:09
 **/
public class EnsureParamCollectionExtension extends EnsureParamObjectExtension<Collection<?>> {

    private final Collection<?> collection;

    public EnsureParamCollectionExtension(Collection<?> collection) {
        super(collection);
        this.collection = collection;
    }

    public EnsureParamCollectionExtension isNotEmpty(ErrorCode errorCode) {
        if (CollectionUtil.isEmpty(collection)) {
            throw ExceptionFactory.create(errorCode.getErrCode(), errorCode.getErrDesc());
        } else {
            return this;
        }
    }

    public EnsureParamCollectionExtension isEmpty(ErrorCode errorCode) {
        if (CollectionUtil.isNotEmpty(collection)) {
            throw ExceptionFactory.create(errorCode.getErrCode(), errorCode.getErrDesc());
        } else {
            return this;
        }
    }

}
