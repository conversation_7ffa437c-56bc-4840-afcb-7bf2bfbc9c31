package com.pxb7.mall.trade.ass.infra.repository.db.mapper;

import com.pxb7.mall.trade.ass.infra.repository.db.entity.ComplaintWorkLog;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 客诉工单操作日志表(ComplaintWorkLog)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-09-20 16:52:42
 */
@Mapper
public interface ComplaintWorkLogMapper extends BaseMapper<ComplaintWorkLog> {
    /**
     * 批量新增数据
     *
     * @param entities List<ComplaintWorkLog> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<ComplaintWorkLog> entities);

}

