package com.pxb7.mall.trade.ass.infra.repository.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.pxb7.mall.trade.ass.infra.repository.es.entity.OrderItemIndemnity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 子订单-包赔表(OrderItemIndemnity)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-09-03 15:19:32
 */
@Mapper
public interface OrderItemIndemnityMapper extends BaseMapper<OrderItemIndemnity> {
    /**
     * 批量新增数据
     *
     * @param entities List<OrderItemIndemnity> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<OrderItemIndemnity> entities);

    /**
     * 获取订单行上用户的包赔金额
     *
     * @param orderItemId 订单行id
     * @param userId 用户id
     * @return 包赔费用总计
     */
    Long getOrderItemIndemnityTotal(@Param("orderItemId") String orderItemId, @Param("userId") String userId,
                                    @Param("indemnityStatus") Integer indemnityStatus);

}

