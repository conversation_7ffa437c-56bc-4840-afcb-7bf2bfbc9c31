package com.pxb7.mall.trade.ass.infra.repository.db.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.AssScheduleLog;

/**
 * 售后流程进度日志(AssScheduleLog)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-08-12 10:59:48
 */
@Mapper
public interface AssScheduleLogMapper extends BaseMapper<AssScheduleLog> {

    boolean delByScheduleIdAndNodeIds(@Param("scheduleId") String scheduleId, @Param("nodeIds") List<String> nodeIds);

    @Update(" update ass_schedule_log  set is_deleted = 1   where schedule_id = #{scheduleId} ")
    boolean delByScheduleId(@Param("scheduleId") String scheduleId);

}
