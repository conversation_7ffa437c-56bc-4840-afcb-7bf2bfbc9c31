package com.pxb7.mall.trade.ass.infra.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 买家订单状态
 */
@Getter
@AllArgsConstructor
public enum BuyerOrderStatusEnum {

    /**
     * 待付款
     */
    WAIT_PAY(10, "待付款"),

    /**
     * 交易中
     */
    DEALING(20, "交易中"),

    /**
     * 已成交
     */
    DEALED(30, "已成交"),

    /**
     * 已取消
     */
    CANCELED(40, "已取消"),;

    private final Integer value;

    private final String label;

    public static String getLabel(Integer value){
        return Arrays.stream(BuyerOrderStatusEnum.values())
                .filter(e->e.getValue().equals(value))
                .map(BuyerOrderStatusEnum::getLabel)
                .findAny()
                .orElse("");
    }
}
