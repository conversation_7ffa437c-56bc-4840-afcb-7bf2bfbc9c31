package com.pxb7.mall.trade.ass.infra.model;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>功能描述:</p>
 * 作者：xuexiyang
 * 创建日期：2025/08/06
 * 公司名称：金华博淳网络科技有限公司
 * 域名： www.pxb7.com
 */
public class AssInfoReqPO {


    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class PagePO extends BasePagePO{
        /**
         * 下单时间
         * 周期 1本周内 2 当月 3 三个月内 4半年内 5 一年内
         */
        private Integer cycle;

        /**
         * 售后单申请时间
         * 周期 1本周内 2 当月 3 三个月内 4半年内 5 一年内
         */
        private Integer assCycle;

        /**
         * 商品类型
         */
        private Integer productType;

        /**
         * 游戏id
         */
        private String gameId;

        /**
         * 关键词（模糊查询商品标题/订单编号/商品编号）
         */
        private String keyWords;



        /***********号商**************/

        /**
         * 是否是号商(为true表示号商身份查询)
         */
        private Boolean isMerchant;


        /**
         * 订单编号
         */

        private String orderItemId;

        /**
         * 手机号（子账号）
         */
        private String telephone;

        /**
         * 商品名称
         */
        private String productName;

        /**
         * 商品ID
         */
        private String productId;

        /**
         * 商品编号
         */
        private String productUniqueNo;

        /**
         * 用户ID列表
         */
        private List<String> userIdList;

        /**
         * 商家用户ID
         */
        private String merchantUserId;

        /**
         * 售后单是否已读
         */
        private Integer readFlag;
    }

}
