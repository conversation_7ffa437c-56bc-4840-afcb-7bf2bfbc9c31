package com.pxb7.mall.trade.ass.infra.repository.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.CommandDO;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface CommandMapper extends BaseMapper<CommandDO> {
    @Delete("delete from trade_command where command_status = #{commandStatus} and create_time <= #{endTime}")
    Long deleteCommands(int commandStatus, String endTime);

    @Delete("delete from trade_command where command_id = #{commandId}")
    Long deleteByCommandId(String commandId);
}
