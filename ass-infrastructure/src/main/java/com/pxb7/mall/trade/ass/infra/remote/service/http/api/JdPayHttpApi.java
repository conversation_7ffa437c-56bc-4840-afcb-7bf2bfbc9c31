package com.pxb7.mall.trade.ass.infra.remote.service.http.api;

import okhttp3.ResponseBody;
import retrofit2.Call;
import retrofit2.http.FieldMap;
import retrofit2.http.FormUrlEncoded;
import retrofit2.http.POST;

import java.util.Map;

/**
 * 京东银行卡快捷支付接口
 */
public interface JdPayHttpApi {

    /**
     * 退款申请
     */
    @FormUrlEncoded
    @POST("/api/refund")
    Call<ResponseBody> refund(@FieldMap Map<String, String> reqDTO);

    /**
     * 退款查询
     */
    @FormUrlEncoded
    @POST("/api/refundQuery")
    Call<ResponseBody> refundQuery(@FieldMap Map<String, String> reqDTO);


}
