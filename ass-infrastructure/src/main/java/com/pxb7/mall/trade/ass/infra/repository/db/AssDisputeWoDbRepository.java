package com.pxb7.mall.trade.ass.infra.repository.db;

import java.time.LocalDateTime;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pxb7.mall.trade.ass.client.enums.AssStatus;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.AssDisputeWo;
import com.pxb7.mall.trade.ass.infra.repository.db.mapper.AssDisputeWoMapper;
import com.pxb7.mall.trade.ass.infra.repository.db.model.request.DealDisputeResultReqPO;

/**
 * 售后纠纷工单
 *
 * <AUTHOR>
 * @since: 2024-08-13 16:48
 **/
@Service
public class AssDisputeWoDbRepository extends ServiceImpl<AssDisputeWoMapper, AssDisputeWo>
    implements AssDisputeWoRepository {
    @Override
    public boolean completeDispute(DealDisputeResultReqPO reqPO) {
        LambdaUpdateWrapper<AssDisputeWo> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(AssDisputeWo::getAssDisputeId, reqPO.getAssDisputeId());
        updateWrapper.set(AssDisputeWo::getHandleStatus, reqPO.getDisputeDealResult());
        updateWrapper.set(AssDisputeWo::getStatus, AssStatus.FINISH.getCode());
        updateWrapper.set(AssDisputeWo::getCompleteTime, LocalDateTime.now());
        updateWrapper.set(AssDisputeWo::getClassifyFirst, reqPO.getClassifyFirst());
        updateWrapper.set(AssDisputeWo::getClassifySecond, reqPO.getClassifySecond());
        return update(updateWrapper);
    }

    @Override
    public boolean userCancel(String assDisputeId) {
        LambdaUpdateWrapper<AssDisputeWo> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(AssDisputeWo::getAssDisputeId, assDisputeId);
        updateWrapper.set(AssDisputeWo::getHandleStatus, 1);
        updateWrapper.set(AssDisputeWo::getStatus, AssStatus.FINISH.getCode());
        updateWrapper.set(AssDisputeWo::getCompleteTime, LocalDateTime.now());
        updateWrapper.set(AssDisputeWo::getClassifyFirst, "其它");
        updateWrapper.set(AssDisputeWo::getClassifySecond, "误申请售后");
        return update(updateWrapper);
    }
}
