package com.pxb7.mall.trade.ass.infra.repository.db;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.PayCompanyAccount;
import com.pxb7.mall.trade.ass.infra.repository.db.mapper.PayCompanyAccountMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

/**
 * 公司账户表(PayCompanyAccount)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-09-27 10:27:21
 */
@Slf4j
@Repository
public class PayCompanyAccountDbRepository extends ServiceImpl<PayCompanyAccountMapper, PayCompanyAccount> implements PayCompanyAccountRepository {

    @Override
    public PayCompanyAccount getCompanyAccountById(String payCompanyAccountId) {
        return this.lambdaQuery().eq(PayCompanyAccount::getCompanyAccountId, payCompanyAccountId).last("limit 1").one();
    }

    @Override
    public PayCompanyAccount findCompanyAccountByAccount(String account) {
        return this.lambdaQuery().eq(PayCompanyAccount::getCompanyAccount, account).last("limit 1").one();
    }

}
