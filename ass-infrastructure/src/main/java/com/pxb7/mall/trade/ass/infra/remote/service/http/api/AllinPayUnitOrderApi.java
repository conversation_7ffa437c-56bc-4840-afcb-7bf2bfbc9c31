package com.pxb7.mall.trade.ass.infra.remote.service.http.api;

import okhttp3.RequestBody;
import okhttp3.ResponseBody;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.POST;

public interface AllinPayUnitOrderApi {

    /**
     * 支付统一创单
     *
     * @param
     * @return
     */
    @POST("/apiweb/unitorder/pay")
    Call<ResponseBody> payCreateOrder(@Body RequestBody requestBody);

    /**
     * 统一查询接口
     *
     * @param
     * @return
     */
    @POST("/apiweb/tranx/query")
    Call<ResponseBody> payQuery(@Body RequestBody requestBody);

    /**
     * 银行卡支付
     */
    @POST("/apiweb/gateway/pay")
    Call<ResponseBody> gatewayPay(@Body RequestBody requestBody);

    /**
     * 统一退款接口
     */
    @POST("/apiweb/tranx/refund")
    Call<ResponseBody> tranxRefund(@Body RequestBody requestBody);
}
