package com.pxb7.mall.trade.ass.infra.repository.es.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import co.elastic.clients.elasticsearch._types.FieldValue;
import co.elastic.clients.elasticsearch._types.query_dsl.*;
import co.elastic.clients.json.JsonData;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pxb7.mall.merchant.client.dto.response.account.MerchantAccountIdentityRespDTO;
import com.pxb7.mall.trade.ass.infra.constant.OrderIndexFieldConstant;
import com.pxb7.mall.trade.ass.infra.enums.OrderCycleEnum;
import com.pxb7.mall.trade.ass.infra.model.AssInfoReqPO;
import com.pxb7.mall.trade.ass.infra.remote.dubbo.MerchantAccountGateway;
import com.pxb7.mall.trade.ass.infra.repository.es.entity.AssInfoAggregation;
import com.pxb7.mall.trade.ass.infra.util.OrderSearchKeywordUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.elasticsearch.client.elc.ElasticsearchTemplate;
import org.springframework.data.elasticsearch.client.elc.NativeQuery;
import org.springframework.data.elasticsearch.client.elc.NativeQueryBuilder;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchHitSupport;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.SearchPage;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <p>功能描述:</p>
 * 作者：xuexiyang
 * 创建日期：2025/08/06
 * 公司名称：金华博淳网络科技有限公司
 * 域名： www.pxb7.com
 */
@Slf4j
@Component
public class AssInfoEsRepository {

    @Resource
    private ElasticsearchTemplate elasticsearchTemplate;

    @Resource
    private MerchantAccountGateway merchantAccountGateway;

    public Page<AssInfoAggregation> page(AssInfoReqPO.PagePO param) {
        BoolQuery.Builder builder = buildPageQueryParam(param);
        if (Objects.isNull(builder)) {
            return null;
        }
        Sort sort = Sort.by(
                Sort.Order.desc(AssInfoAggregation.Fields.assApplyTime)
        );

        NativeQuery nativeQuery = NativeQuery.builder()
                .withQuery(builder.build()._toQuery())
                .withPageable(PageRequest.of(param.getPageIndex() - 1, param.getPageSize()))
                .withSort(sort)
                .withTrackTotalHits(true)
                .build();
        log.info("query ass info dsl: {}", nativeQuery.getQuery().toString());
        SearchHits<AssInfoAggregation> searchHits = elasticsearchTemplate.search(nativeQuery, AssInfoAggregation.class);
        SearchPage<AssInfoAggregation> searchHitsPage = SearchHitSupport.searchPageFor(searchHits, nativeQuery.getPageable());

        if (CollUtil.isEmpty(searchHitsPage.getContent())) {
            return new Page<>(searchHitsPage.getNumber(), searchHitsPage.getSize(), searchHitsPage.getTotalElements());
        }
        List<AssInfoAggregation> list = searchHitsPage.getContent().stream().map(SearchHit::getContent).toList();
        Page<AssInfoAggregation> page = new Page<>(searchHitsPage.getNumber(), searchHitsPage.getSize(), searchHitsPage.getTotalElements());
        page.setRecords(list);
        return page;
    }

    private BoolQuery.Builder buildPageQueryParam(AssInfoReqPO.PagePO param) {
        // 存储查询条件列表

        if (BooleanUtil.isTrue(param.getIsMerchant())) {
            return buildMerchantQueryParam(param);
        }
        List<Query> mustQueryList = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(param.getUserIdList())) {
            List<FieldValue> valueList = param.getUserIdList().stream().map(FieldValue::of).toList();
            TermsQuery userIdsTermsQuery = QueryBuilders.terms()
                    .field(OrderIndexFieldConstant.buyer_id)
                    .terms(new TermsQueryField.Builder().value(valueList).build()).build();
            mustQueryList.add(userIdsTermsQuery._toQuery());
        }

        // 时间范围查询
        if (Objects.nonNull(param.getAssCycle())) {
            String dateStr = OrderCycleEnum.getOrderCycleEnum(param.getAssCycle()).getDateStr();
            Query createTimeQuery = QueryBuilders
                    .range(timeQuery -> timeQuery.field(OrderIndexFieldConstant.ass_apply_time).gte(JsonData.of(dateStr)));
            mustQueryList.add(createTimeQuery);
        }
        if (Objects.nonNull(param.getCycle())) {
            String dateStr = OrderCycleEnum.getOrderCycleEnum(param.getAssCycle()).getDateStr();
            Query createTimeQuery = QueryBuilders
                    .range(timeQuery -> timeQuery.field(OrderIndexFieldConstant.create_time).gte(JsonData.of(dateStr)));
            mustQueryList.add(createTimeQuery);
        }
        if (Objects.nonNull(param.getProductType())) {
            TermQuery productTypeQuery = QueryBuilders.term().field(OrderIndexFieldConstant.product_type)
                    .value(param.getProductType()).build();
            mustQueryList.add(productTypeQuery._toQuery());
        }
        if(Objects.nonNull(param.getReadFlag())){
            TermQuery readFlagQuery = QueryBuilders.term()
                    .field(OrderIndexFieldConstant.read_flag)
                    .value(param.getReadFlag())
                    .build();
            mustQueryList.add(readFlagQuery._toQuery());
        }

        // 关键词搜索
        if (StringUtils.isNotBlank(param.getKeyWords())) {
            Query keywordQuery = buildKeywordQuery(param.getKeyWords());
            mustQueryList.add(keywordQuery);
        }
        // gameId
        if (StringUtils.isNotBlank(param.getGameId())) {
            TermQuery gameIdQuery =
                    QueryBuilders.term().field(OrderIndexFieldConstant.game_id).value(param.getGameId()).build();
            mustQueryList.add(gameIdQuery._toQuery());
        }

        return QueryBuilders.bool().must(mustQueryList);
    }

    public Long count(AssInfoReqPO.PagePO param) {
        BoolQuery.Builder builder = buildPageQueryParam(param);
        NativeQuery nativeQuery = NativeQuery.builder()
                .withQuery(builder.build()._toQuery()).build();
        return elasticsearchTemplate.count(nativeQuery, AssInfoAggregation.class);
    }


    private BoolQuery.Builder buildMerchantQueryParam(AssInfoReqPO.PagePO param) {
        // 存储查询条件列表
        List<Query> mustQueryList = new ArrayList<>();

        // 查询商户身份信息
        MerchantAccountIdentityRespDTO data =
                merchantAccountGateway.queryMerchantAccountIdentity(param.getMerchantUserId());
        if (data.isAdmin()) {
            // 是主账号， 查询商家下所有的
            if (StringUtils.isBlank(data.getMerchantId())) {
                // 没有商家ID脏数据
                log.error("buildMerchantQueryParam error, no merchantId, merchantUserId:{}",
                        param.getMerchantUserId());
                return null;
            }
            TermQuery merchantIdQuery = QueryBuilders.term().field(OrderIndexFieldConstant.buyer_merchant_id)
                    .value(data.getMerchantId()).build();
            mustQueryList.add(merchantIdQuery._toQuery());
        } else {
            // 1.0迁移过来的用户，可能没有部门信息，所以统一通过查询该用户下的所有子账号用户
            if (CollectionUtils.isEmpty(data.getUserIds())) {
                log.error("buildMerchantQueryParam error, no userIds, merchantUserId:{}",
                        param.getMerchantUserId());
                return null;
            }

            List<FieldValue> values = data.getUserIds().stream().map(FieldValue::of).toList();
            TermsQuery userIdsTermsQuery = QueryBuilders.terms().field(OrderIndexFieldConstant.buyer_id)
                    .terms(builder -> builder.value(values)).build();
            mustQueryList.add(userIdsTermsQuery._toQuery());

        }
        if (CollectionUtils.isNotEmpty(param.getUserIdList())) {
            List<FieldValue> valueList = param.getUserIdList().stream().map(FieldValue::of).toList();
            TermsQuery userIdsTermsQuery = QueryBuilders.terms()
                    .field(OrderIndexFieldConstant.buyer_id)
                    .terms(new TermsQueryField.Builder().value(valueList).build()).build();
            mustQueryList.add(userIdsTermsQuery._toQuery());
        }
        // 时间范围查询
        if (Objects.nonNull(param.getAssCycle())) {
            String dateStr = OrderCycleEnum.getOrderCycleEnum(param.getAssCycle()).getDateStr();
            Query createTimeQuery = QueryBuilders
                    .range(timeQuery -> timeQuery.field(OrderIndexFieldConstant.ass_apply_time).gte(JsonData.of(dateStr)));
            mustQueryList.add(createTimeQuery);
        }
        if (Objects.nonNull(param.getCycle())) {
            String dateStr = OrderCycleEnum.getOrderCycleEnum(param.getAssCycle()).getDateStr();
            Query createTimeQuery = QueryBuilders
                    .range(timeQuery -> timeQuery.field(OrderIndexFieldConstant.create_time).gte(JsonData.of(dateStr)));
            mustQueryList.add(createTimeQuery);
        }
        if (Objects.nonNull(param.getProductType())) {
            TermQuery productTypeQuery = QueryBuilders.term().field(OrderIndexFieldConstant.product_type)
                    .value(param.getProductType()).build();
            mustQueryList.add(productTypeQuery._toQuery());
        }
        // gameId
        if (StringUtils.isNotBlank(param.getGameId())) {
            TermQuery gameIdQuery =
                    QueryBuilders.term().field(OrderIndexFieldConstant.game_id).value(param.getGameId()).build();
            mustQueryList.add(gameIdQuery._toQuery());
        }

        // 订单编号
        if (StringUtils.isNotBlank(param.getOrderItemId())) {
            TermQuery orderItemIdQuery =
                    QueryBuilders.term().field(OrderIndexFieldConstant.order_item_id).value(param.getOrderItemId()).build();
            mustQueryList.add(orderItemIdQuery._toQuery());
        }

        // 手机号
        if (StringUtils.isNotBlank(param.getTelephone())) {
            TermQuery phoneQuery = QueryBuilders.term().field(OrderIndexFieldConstant.buyer_phone)
                    .value(param.getTelephone()).build();
            mustQueryList.add(phoneQuery._toQuery());
        }

        // 商品id精确查询
        if (StringUtils.isNotBlank(param.getProductId())) {
            TermQuery productIdQuery =
                    QueryBuilders.term().field(OrderIndexFieldConstant.product_id).value(param.getProductId()).build();
            mustQueryList.add(productIdQuery._toQuery());
        }
        // 商品编号精确查询
        if (StringUtils.isNotBlank(param.getProductUniqueNo())) {
            TermQuery productUniqueNoQuery =
                    QueryBuilders.term().field(OrderIndexFieldConstant.product_unique_no).value(param.getProductUniqueNo()).build();
            mustQueryList.add(productUniqueNoQuery._toQuery());
        }

        // 商品标题
        if (StringUtils.isNotBlank(param.getProductName())) {
            MatchQuery productNameMatchQuery = QueryBuilders.match().field(OrderIndexFieldConstant.product_name)
                    .query(param.getProductName()).build();
            mustQueryList.add(productNameMatchQuery._toQuery());
        }
        if(Objects.nonNull(param.getReadFlag())){
            TermQuery readFlagQuery = QueryBuilders.term()
                    .field(OrderIndexFieldConstant.read_flag)
                    .value(param.getReadFlag())
                    .build();
            mustQueryList.add(readFlagQuery._toQuery());
        }
        return QueryBuilders.bool().must(mustQueryList);
    }

    /**
     * 构造关键词搜索条件
     *
     * @param keyword
     * @return query
     */

    public Query buildKeywordQuery(String keyword) {
        String keywordType = OrderSearchKeywordUtil.judgeKeywordType(keyword);
        switch (keywordType) {
            case OrderIndexFieldConstant.order_item_id -> {
                List<Query> orderItemIdShouldQueryList = new ArrayList<>();
                TermQuery oldOrderItemIdQuery =
                        QueryBuilders.term().field(OrderIndexFieldConstant.old_order_item_id).value(keyword).build();
                TermQuery orderItemIdQuery =
                        QueryBuilders.term().field(OrderIndexFieldConstant.order_item_id).value(keyword).build();

                orderItemIdShouldQueryList.add(oldOrderItemIdQuery._toQuery());
                orderItemIdShouldQueryList.add(orderItemIdQuery._toQuery());

                return QueryBuilders.bool().should(orderItemIdShouldQueryList).minimumShouldMatch("1").build()._toQuery();

            }
            case OrderIndexFieldConstant.product_unique_no -> {
                return QueryBuilders.term().field(OrderIndexFieldConstant.product_unique_no).value(keyword).build()
                        ._toQuery();
            }
            case OrderIndexFieldConstant.product_name -> {
                return QueryBuilders.match().field(OrderIndexFieldConstant.product_name).query(keyword).build()
                        ._toQuery();
            }
            case OrderIndexFieldConstant.phone -> {
                // 是手机号 则 匹配买、卖家手机号
                List<Query> phoneShouldQueryList = new ArrayList<>();
                TermQuery buyerPhoneQuery =
                        QueryBuilders.term().field(OrderIndexFieldConstant.buyer_phone).value(keyword).build();
                TermQuery sellerPhoneQuery =
                        QueryBuilders.term().field(OrderIndexFieldConstant.seller_phone).value(keyword).build();

                phoneShouldQueryList.add(buyerPhoneQuery._toQuery());
                phoneShouldQueryList.add(sellerPhoneQuery._toQuery());

                return QueryBuilders.bool().should(phoneShouldQueryList).minimumShouldMatch("1").build()._toQuery();
            }
            default -> {
                return null;
            }
        }
    }


    public AssInfoAggregation findByOrderItemId(String orderItemId) {
        if (StrUtil.isBlank(orderItemId)) {
            return null;
        }
        NativeQueryBuilder nativeQueryBuilder = new NativeQueryBuilder();
        BoolQuery.Builder boolFilter = QueryBuilders.bool();
        boolFilter.filter(QueryBuilders.term(a -> a.field("order_item_id").value(orderItemId)));
        NativeQuery nativeQuery = nativeQueryBuilder.withQuery(boolFilter.build()._toQuery()).build();
        log.info("findByOrderItemId nativeQuery:{}", nativeQuery.getQuery().toString());
        SearchHit<AssInfoAggregation> bargainTicketDocSearchHit = elasticsearchTemplate.searchOne(nativeQuery, AssInfoAggregation.class);
        return Objects.nonNull(bargainTicketDocSearchHit) ? bargainTicketDocSearchHit.getContent() : null;
    }
}
