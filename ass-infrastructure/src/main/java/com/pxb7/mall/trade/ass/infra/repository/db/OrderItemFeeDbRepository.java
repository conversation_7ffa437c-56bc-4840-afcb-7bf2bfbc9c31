package com.pxb7.mall.trade.ass.infra.repository.db;

import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.OrderItemFee;
import com.pxb7.mall.trade.ass.infra.repository.db.mapper.OrderItemFeeMapper;

import lombok.extern.slf4j.Slf4j;

/**
 * 订单行-手续费表(OrderItemFee)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-07-22 20:59:36
 */
@Slf4j
@Repository
public class OrderItemFeeDbRepository extends ServiceImpl<OrderItemFeeMapper, OrderItemFee>
    implements OrderItemFeeRepository {

    @Override
    public OrderItemFee getInfoByOrderItemId(String orderItemId) {
        return this.lambdaQuery().eq(OrderItemFee::getOrderItemId, orderItemId).one();
    }
}
