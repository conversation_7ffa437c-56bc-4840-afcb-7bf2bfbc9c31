package com.pxb7.mall.trade.ass.infra.repository.db.mapper;

import com.pxb7.mall.trade.ass.infra.repository.db.entity.AfcQuestionConf;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 售后问题配置(AfcQuestionConf)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-04-11 13:54:03
 */
@Mapper
public interface AfcQuestionConfMapper extends BaseMapper<AfcQuestionConf> {
    /**
     * 批量新增数据
     *
     * @param entities List<AfcQuestionConf> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<AfcQuestionConf> entities);

}

