package com.pxb7.mall.trade.ass.infra.repository.db;

import com.baomidou.mybatisplus.extension.service.IService;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.OrderItemFee;

/**
 * 订单行-手续费表(OrderItemFee)表服务接口
 *
 * <AUTHOR>
 * @since 2024-07-22 20:59:36
 */
public interface OrderItemFeeRepository extends IService<OrderItemFee> {

    OrderItemFee getInfoByOrderItemId(String orderItemId);
}
