package com.pxb7.mall.trade.ass.infra.repository.db;

import java.util.List;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.RefundVoucher;

/**
 * 退款单(RefundVoucher)表服务接口
 *
 * <AUTHOR>
 * @since 2024-09-25 15:02:33
 */
public interface RefundVoucherRepository extends IService<RefundVoucher> {

    List<RefundVoucher> getSuccessRefund(String orderItemId);

    List<RefundVoucher> getItemRefundList(String orderItemId);

    Page<RefundVoucher> getItemRefundList(List<String> orderItemId, int pageSize, int pageIndex);

    RefundVoucher getByRefundId(String refundId);

    boolean updateByRefundVoucherId(RefundVoucher refundVoucher);

    RefundVoucher getUnderwayByItemId(String orderItemId);

    boolean updateByRefundId(RefundVoucher refundVoucher, Integer fromAuditStatus);

    RefundVoucher lastWholeByOrderItemId(String orderItemId);
}
