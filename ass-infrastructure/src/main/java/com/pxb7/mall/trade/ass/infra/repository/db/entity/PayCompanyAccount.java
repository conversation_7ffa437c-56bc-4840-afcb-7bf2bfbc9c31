package com.pxb7.mall.trade.ass.infra.repository.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 公司账户表(PayCompanyAccount)实体类
 *
 * <AUTHOR>
 * @since 2024-09-27 10:27:21
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
@TableName(value = "pay_company_account")
public class PayCompanyAccount implements Serializable {
    private static final long serialVersionUID = -50185587900266862L;
    /**
     * 主键
     */
    @TableField(value = "id")
    private Long id;
    /**
     * 业务id
     */
    @TableId(value = "company_account_id", type = IdType.INPUT)
    private String companyAccountId;
    /**
     * 支付模式 1线上 2线下, 3挂账
     */
    @TableField(value = "pay_mode")
    private Integer payMode;
    /**
     * 账号
     */
    @TableField(value = "company_account")
    private String companyAccount;

    @TableField(value = "account_name")
    private String accountName;

    /**
     * 支付类型 1支付宝 2微信 3银行卡 4挂账
     */
    @TableField(value = "transfer_type")
    private Integer transferType;
    /**
     * 状态 10 启用 禁用 20
     */
    @TableField(value = "status")
    private Integer status;
    /**
     * 支付信息
     */
    @TableField(value = "qr_code_url")
    private String qrCodeUrl;
    /**
     * 外部支付参数（加密字段，使用需要先解密）
     */
    @TableField(value = "out_payment_param")
    private String outPaymentParam;
    /**
     * 创建用户id
     */
    @TableField(value = "create_user_id")
    private String createUserId;
    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;
    /**
     * 更新用户id
     */
    @TableField(value = "update_user_id")
    private String updateUserId;
    /**
     * 更新时间
     */
    @TableField(value = "update_time", update="now()")
    private LocalDateTime updateTime;
    /**
     * 是否显示：0-不显示，1-显示
     */
    @TableField(value = "displayed")
    private Boolean displayed;

}

