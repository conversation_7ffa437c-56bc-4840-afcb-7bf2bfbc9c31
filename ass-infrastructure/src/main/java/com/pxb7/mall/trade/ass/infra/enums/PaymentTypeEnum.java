package com.pxb7.mall.trade.ass.infra.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 支付方式 1支付宝 2微信
 */
@Getter
@AllArgsConstructor
public enum PaymentTypeEnum {

    /**
     * 支付宝支付
     */
    ALIPAY(1, "支付宝支付", "ALIPAY_NATIVE"),

    /**
     * 微信支付
     */
    WECHAT(2, "微信支付", "WECHAT_NATIVE"),

    /**
     * 银行卡支付
     */
    BANK_CARD(3, "银行卡支付", "EBANK_DEBIT_CARD"),;

    private final Integer value;

    private final String label;

    private final String lianlianCode;

    // public static String getLianlianCode(Integer value){
    // return Arrays.stream(PaymentTypeEnum.values())
    // .filter(e -> e.getValue().equals(value))
    // .map(PaymentTypeEnum::getLianlianCode)
    // .findAny()
    // .orElseThrow(() -> new IllegalArgumentException(ErrorCode.ABNORMAL_OPERATION.getErrDesc()));
    // }

    public static String getLabel(Integer value) {
        return Arrays.stream(PaymentTypeEnum.values()).filter(e -> e.getValue().equals(value))
            .map(PaymentTypeEnum::getLianlianCode).findAny().get();
    }

}
