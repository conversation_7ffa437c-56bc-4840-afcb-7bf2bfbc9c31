package com.pxb7.mall.trade.ass.infra.repository.db;

import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.AssPayment;
import com.pxb7.mall.trade.ass.infra.repository.db.mapper.AssPaymentMapper;

import lombok.extern.slf4j.Slf4j;

/**
 * 售后收款列表(AssPayment)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-10-26 14:49:06
 */
@Slf4j
@Repository
public class AssPaymentDbRepository extends ServiceImpl<AssPaymentMapper, AssPayment> implements AssPaymentRepository {

}
