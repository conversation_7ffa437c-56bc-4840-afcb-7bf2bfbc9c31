package com.pxb7.mall.trade.ass.infra.repository.db;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.ReceiptVoucherDetail;

/**
 * 收款单-资金明细(ReceiptVoucherDetail)表服务接口
 *
 * <AUTHOR>
 * @since 2024-09-25 16:16:37
 */
public interface ReceiptVoucherDetailRepository extends IService<ReceiptVoucherDetail> {
    List<ReceiptVoucherDetail> getListByOrderItemId(String orderItemId);
}
