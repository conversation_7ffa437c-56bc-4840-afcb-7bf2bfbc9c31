package com.pxb7.mall.trade.ass.infra.model.mq;

import com.pxb7.mall.trade.order.client.enums.order.ChannelOrderTypeEnum;
import java.util.Map;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

@Data
@Accessors(chain = true)
public class RefundQueryMessage implements Serializable {
    /**
     * 退款PayLogId
     */
    private String refundLogId;

    /**
     * 支付payLogId
     */
    private String payLogId;
    /**
     * 历史退款 paymentId列表
     */
    private List<String> historyRefundPaymentIds;

    /**
     * ORDER_ITEM_ID
     */
    private String orderItemId;

    /**
     * 退款单编号
     */
    private String refundVoucherId;

    /**
     * 渠道类型
     * {@link ChannelOrderTypeEnum#getValue()}
     */
    private Integer channelType;

    /**
     * 渠道自定义参数
     */
    private Map<String, Object> channelExtMap;

    //当前发送次数
    private Integer times = 1;
    //最大发送次数
    private Integer maxTimes = 20;
    //延迟间隔（分）
    private Integer minutes = 5;
}
