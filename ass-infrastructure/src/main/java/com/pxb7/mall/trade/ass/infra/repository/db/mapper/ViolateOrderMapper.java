package com.pxb7.mall.trade.ass.infra.repository.db.mapper;

import com.pxb7.mall.trade.ass.infra.repository.db.entity.ViolateOrder;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 违约单(ViolateOrder)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-03-27 15:47:15
 */
@Mapper
public interface ViolateOrderMapper extends BaseMapper<ViolateOrder> {
    /**
* 批量新增数据
*
* @param entities List<ViolateOrder> 实例对象列表
* @return 影响行数
*/
int insertBatch(@Param("entities") List<ViolateOrder> entities);

}

