package com.pxb7.mall.trade.ass.infra.repository.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 订单行 -优惠表(OrderItemPromotion)实体类
 *
 * <AUTHOR>
 * @since 2024-07-22 20:59:36
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
@TableName(value = "order_item_promotion")
public class OrderItemPromotion implements Serializable {
    private static final long serialVersionUID = -64269795608312559L;
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 订单行id
     */
    @TableField(value = "order_item_id")
    private String orderItemId;
    /**
     * 1：商品优惠券 2：包赔优惠券 3：红包
     */
    @TableField(value = "promotion_type")
    private Integer promotionType;
    /**
     * 优惠金额
     */
    @TableField(value = "promotion_amount")
    private Long promotionAmount;
    /**
     * 券/红包id
     */
    @TableField(value = "promotion_id")
    private String promotionId;
    /**
     * 优惠归属 1平台 2商家
     */
    @TableField(value = "promotion_belong")
    private Short promotionBelong;
    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    @TableField(value = "update_time", update="now()")
    private LocalDateTime updateTime;
    /**
     * 逻辑删除，0:删除，1:正常
     */
    @TableLogic
    @TableField(value = "is_deleted")
    private Boolean deleted;

}
