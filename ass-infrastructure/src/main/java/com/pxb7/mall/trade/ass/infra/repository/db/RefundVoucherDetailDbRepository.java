package com.pxb7.mall.trade.ass.infra.repository.db;

import java.util.List;

import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.RefundVoucherDetail;
import com.pxb7.mall.trade.ass.infra.repository.db.mapper.RefundVoucherDetailMapper;

import lombok.extern.slf4j.Slf4j;

/**
 * 退款单金额详情(RefundVoucherDetail)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-09-25 15:02:33
 */
@Slf4j
@Repository
public class RefundVoucherDetailDbRepository extends ServiceImpl<RefundVoucherDetailMapper, RefundVoucherDetail>
    implements RefundVoucherDetailRepository {
    @Override
    public RefundVoucherDetail getOneByRefundId(String refundVoucherId) {
        return this.lambdaQuery().eq(RefundVoucherDetail::getRefundVoucherId, refundVoucherId).one();
    }

    @Override
    public boolean updateByRefundId(RefundVoucherDetail refundVoucherDetail) {
        return this.lambdaUpdate().eq(RefundVoucherDetail::getRefundVoucherId, refundVoucherDetail.getRefundVoucherId())
            .update(refundVoucherDetail);
    }

    @Override
    public List<RefundVoucherDetail> getByOrderItemId(String orderItemId) {
        return this.lambdaQuery().eq(RefundVoucherDetail::getOrderItemId, orderItemId).list();
    }

    @Override
    public List<RefundVoucherDetail> getByRefundVoucherIds(List<String> refundVoucherIds) {
        return this.lambdaQuery().in(RefundVoucherDetail::getRefundVoucherId, refundVoucherIds).list();
    }
}
