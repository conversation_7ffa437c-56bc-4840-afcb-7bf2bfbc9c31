package com.pxb7.mall.trade.ass.infra.repository.db.entity;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.*;
import com.pxb7.mall.trade.ass.client.enums.AssPayWay;
import com.pxb7.mall.trade.ass.client.enums.AssPaymentChannel;
import com.pxb7.mall.trade.ass.client.enums.AssPaymentStatus;
import com.pxb7.mall.trade.ass.client.enums.AssSourceType;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 售后收款列表(AssPayment)实体类
 *
 * <AUTHOR>
 * @since 2024-10-26 14:50:31
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
@TableName(value = "ass_payment")
public class AssPayment implements Serializable {
    private static final long serialVersionUID = 684107498439318515L;
    /**
     * 自增id
     */
    @TableField(value = "id")
    private Long id;
    /**
     * 主键ID
     */
    @TableId(value = "ass_payment_id", type = IdType.INPUT)
    private String assPaymentId;
    /**
     * 收款编号
     */
    @TableField(value = "payment_no")
    private String paymentNo;
    /**
     * 收款金额
     */
    @TableField(value = "payment_amount")
    private Long paymentAmount;
    /**
     * 售后类型1找回2纠纷
     */
    @TableField(value = "ass_type")
    private Integer assType;
    /**
     * 商品id
     */
    @TableField(value = "product_id")
    private String productId;

    /**
     * 商品编码
     */
    @TableField(value = "product_unique_no")
    private String productUniqueNo;
    /**
     * 订单id
     */
    @TableField(value = "order_item_id")
    private String orderItemId;
    /**
     * 售后工单ID
     */
    @TableField(value = "work_order_id")
    private String workOrderId;
    /**
     * 售后工单编号
     */
    @TableField(value = "work_order_no")
    private String workOrderNo;
    /**
     * 收款状态 1:待付款 2:已收款 3:已关闭
     *
     * @see AssPaymentStatus
     */
    @TableField(value = "status")
    private Integer status;
    /**
     * 申请人ID
     */
    @TableField(value = "proposer_user_id")
    private String proposerUserId;
    /**
     * 完成时间
     */
    @TableField(value = "complete_time")
    private LocalDateTime completeTime;
    /**
     * 申请原因
     */
    @TableField(value = "proposer_reason")
    private String proposerReason;

    /**
     * 收款账户
     */
    @TableField(value = "payment_account")
    private String paymentAccount;
    /**
     * 收款账户ID
     */
    @TableField(value = "account_id")
    private String accountId;
    /**
     * 收款渠道 1:线上 2:线下
     *
     * @see AssPaymentChannel
     */
    @TableField(value = "payment_channel")
    private Integer paymentChannel;
    /**
     * 收款二维码
     */
    @TableField(value = "qr_code")
    private String qrCode;
    /**
     * 二维码过期时间
     */
    @TableField(value = "expire_time")
    private LocalDateTime expireTime;
    /**
     * 收款凭证
     */
    @TableField(value = "payment_proof")
    private String paymentProof;
    /**
     * 收款方式 1:支付宝 2:微信
     *
     * @see AssPayWay
     */
    @TableField(value = "pay_way")
    private Integer payWay;
    /**
     * 渠道来源 1:螃蟹 2:支付宝小程序
     *
     * @see AssSourceType
     */
    @TableField(value = "source_type")
    private Integer sourceType;
    /**
     * 线下收款备注
     */
    @TableField(value = "notes")
    private String notes;

    /**
     * 财务人员
     */
    @TableField(value = "finance")
    private String finance;
    /**
     * 财务人员编号
     */
    @TableField(value = "finance_id")
    private String financeId;
    /**
     * 创建用户ID
     */
    @TableField(value = "create_user_id", fill = FieldFill.INSERT)
    private String createUserId;
    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;
    /**
     * 更新人ID
     */
    @TableField(value = "update_user_id", fill = FieldFill.INSERT_UPDATE)
    private String updateUserId;
    /**
     * 更新时间
     */
    @TableField(value = "update_time", update="now()")
    private LocalDateTime updateTime;
    /**
     * 删除标记
     */
    @TableLogic
    @TableField(value = "is_deleted")
    private Boolean deleted;

}
