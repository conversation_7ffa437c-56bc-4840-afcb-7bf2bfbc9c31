package com.pxb7.mall.trade.ass.infra.repository.db.mapper;

import com.pxb7.mall.trade.ass.infra.repository.db.entity.ViolatePayRecord;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 违约支付单表(ViolatePayRecord)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-03-31 15:07:26
 */
@Mapper
public interface ViolatePayRecordMapper extends BaseMapper<ViolatePayRecord> {
    /**
* 批量新增数据
*
* @param entities List<ViolatePayRecord> 实例对象列表
* @return 影响行数
*/
int insertBatch(@Param("entities") List<ViolatePayRecord> entities);

}

