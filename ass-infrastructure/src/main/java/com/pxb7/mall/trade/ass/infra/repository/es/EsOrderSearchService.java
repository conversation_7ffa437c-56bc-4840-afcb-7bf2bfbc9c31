package com.pxb7.mall.trade.ass.infra.repository.es;

import java.util.List;

import org.springframework.data.elasticsearch.core.SearchHits;

import com.pxb7.mall.trade.ass.infra.repository.es.entity.MainOrderAggregation;

public interface EsOrderSearchService {

    MainOrderAggregation getOrderDoc(String orderId);

    void syncOrderList(List<MainOrderAggregation> mainOrderAggregationList);

    void deleteOrder(String orderId);

    SearchHits<MainOrderAggregation> listByItemIds(List<String> orderItemIds);

    List<MainOrderAggregation> listByUserId(String userId);

    MainOrderAggregation findOneByRoomId(String roomId);

}
