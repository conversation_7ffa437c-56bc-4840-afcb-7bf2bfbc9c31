package com.pxb7.mall.trade.ass.infra.model.mq;

import com.pxb7.mall.trade.order.client.enums.order.ChannelOrderTypeEnum;
import java.util.Map;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 部分退款申请
 */
@Data
@Accessors(chain = true)
public class PartRefundMessage implements Serializable {
    /**
     * 支付成功的 paymentId
     */
    private String payPaymentId;
    /**
     * 历史退款 paymentId列表
     */
    private List<String> historyRefundPaymentIds;
    /**
     * 退款单据id
     */
    private String refundVoucherId;
    /**
     * 退款金额
     */
    private Long refundAmount;
    /**
     * 支付用户id -- TODO 该字段可以不传，原路退用收款payment.payUserId，避免三方 userId校验失败
     */
    private String userId;
    /**
     * 主订单id
     */
    private String orderId;
    /**
     * 子订单id,业务主键
     */
    private String orderItemId;
    /**
     * 累计成功退款金额
     */
    private Long successRefundAmount;

    /**
     * 渠道订单类型
     * {@link ChannelOrderTypeEnum#getValue()}
     */
    private Integer channelOrderType;

    /**
     * 渠道订单扩展参数
     */
    private Map<String, Object> channelExtMap;
}
