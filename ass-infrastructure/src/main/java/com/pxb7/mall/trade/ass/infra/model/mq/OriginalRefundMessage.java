package com.pxb7.mall.trade.ass.infra.model.mq;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 原路退款申请
 */
@Data
@Accessors(chain = true)
public class OriginalRefundMessage implements Serializable {

    // 退款单号  默认："refund_" + payLog.getPayLogId()
    private String refundTradeNo;

    // 支付PayLog
    private String payLogId;

    // 支付PayLog 三方单号
    private String payOutTradeNo;

    // 支付PayLog的
    private String callbackResult;

    // 支付的
    private String paymentId;
}
