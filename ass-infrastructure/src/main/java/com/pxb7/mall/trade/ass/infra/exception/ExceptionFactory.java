package com.pxb7.mall.trade.ass.infra.exception;

import com.alibaba.cola.exception.BizException;

import org.slf4j.helpers.MessageFormatter;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * 异常工厂
 *
 * <AUTHOR>
 * @date 2024/08/07 15:14
 **/
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class ExceptionFactory {

    public static BizException create(String errMsg) {
        return new BizException(errMsg);
    }

    public static BizException create(String errorCode, String errMsg) {
        return new BizException(errorCode, errMsg);
    }

    public static BizException create(String errorCode, String errMsg, Object... args) {
        return new BizException(errorCode, MessageFormatter.arrayFormat(errMsg, args).getMessage());
    }

}
