package com.pxb7.mall.trade.ass.infra.repository.db;

import com.baomidou.mybatisplus.extension.service.IService;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.ComplaintDepartmentConfig;

import java.util.List;

/**
 * 问题部门-问题类型配置(ComplaintDepartmentConfig)表服务接口
 *
 * <AUTHOR>
 * @since 2024-09-20 16:45:28
 */
public interface ComplaintDepartmentConfigRepository extends IService<ComplaintDepartmentConfig> {

    List<ComplaintDepartmentConfig> queryDepartmentList();
}

