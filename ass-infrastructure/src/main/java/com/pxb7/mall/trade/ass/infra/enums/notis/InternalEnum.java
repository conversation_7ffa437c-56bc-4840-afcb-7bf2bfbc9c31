package com.pxb7.mall.trade.ass.infra.enums.notis;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 功能描述:站内信模版
 * 作者：白春韬
 * 创建日期：2025/08/06 
 * 公司名称：金华博淳网络科技有限公司 
 * 域名： www.pxb7.com
 */
@Getter
@AllArgsConstructor
public enum InternalEnum {
    /**
     * 售后工单创建成功
     */
    INTERNAL_AFTER_SALE_WORK_ORDER_CREATE(0, "售后申请已受理", "after_sale_accept"),
    /**
     * 跟进记录-追回账号，待买家换绑
     */
    INTERNAL_AFTER_SALE_WORK_ORDER_TO_BIND(1, "已为您追回账号", "after_sale_retrieve_account"),
    /**
     * 跟进记录-追回号款，待买家提供收款账号
     */
    INTERNAL_AFTER_SALE_WORK_ORDER_TO_PROVIDE_ACCOUNT(2, "已为您追回号款", "after_sale_retrieve_fund"),
    /**
     * 跟进记录-售后到期，待买家接收赔付
     */
    INTERNAL_AFTER_SALE_WORK_ORDER_EXPIRED(3, "售后到期赔付", "after_sale_payment"),

    ;

    private  final Integer code;
    /**
     * 模版名
     */
    private final  String templateName;
    /**
     * 站内信模版ID
     */
    private final String templateCode;

    public static InternalEnum getInternalEnum(Integer code) {
        for (InternalEnum internalEnum : InternalEnum.values()) {
            if (internalEnum.getCode().equals(code)) {
                return internalEnum;
            }
        }
        return null;
    }

    public static String getInternalEnumTemplateCode(Integer code) {
        for (InternalEnum internalEnum : InternalEnum.values()) {
            if (internalEnum.getCode().equals(code)) {
                return internalEnum.getTemplateCode();
            }
        }
        return null;
    }

}
