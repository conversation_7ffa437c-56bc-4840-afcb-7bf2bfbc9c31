package com.pxb7.mall.trade.ass.infra.model;

import java.util.Date;
import java.time.*;
import java.math.BigDecimal;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.ToString;


/**
 * 售后找回游戏配置(AssRetrieveGameConfig)实体类
 *
 * <AUTHOR>
 * @since 2025-08-01 14:11:29
 */
public class AssRetrieveGameConfigReqPO {

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class AddPO {

        /**
         * 业务主键
         */
        private String gameConfigId;

        /**
         * 找回处理时间单位天
         */
        private Integer retrieveDays;

        /**
         * 是否开启群通知 1:已开启 0:未开启
         */
        private Boolean groupOpen;

        /**
         * 创建人id
         */
        private String createUserId;

        /**
         * 更新人id
         */
        private String updateUserId;

    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class UpdatePO {

        /**
         * 自增id
         */
        private Long id;


        /**
         * 业务主键
         */
        private String gameConfigId;


        /**
         * 找回处理时间单位天
         */
        private Integer retrieveDays;


        /**
         * 是否开启群通知 1:已开启 0:未开启
         */
        private Boolean groupOpen;


        /**
         * 创建人id
         */
        private String createUserId;


        /**
         * 更新人id
         */
        private String updateUserId;


    }


    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class DelPO {
        private Long id;
    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class SearchPO {
        /**
         * 业务主键
         */
        private String gameConfigId;

        /**
         * 找回处理时间单位天
         */
        private Integer retrieveDays;

        /**
         * 是否开启群通知 1:已开启 0:未开启
         */
        private Boolean groupOpen;

        /**
         * 创建人id
         */
        private String createUserId;

        /**
         * 更新人id
         */
        private String updateUserId;

    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class PagePO {

        /**
         * 业务主键
         */
        private String gameConfigId;

        /**
         * 找回处理时间单位天
         */
        private Integer retrieveDays;

        /**
         * 是否开启群通知 1:已开启 0:未开启
         */
        private Boolean groupOpen;

        /**
         * 创建人id
         */
        private String createUserId;

        /**
         * 更新人id
         */
        private String updateUserId;


        /**
         * 页码，从1开始
         */
        private Integer pageIndex;
        /**
         * 每页数量
         */
        private Integer pageSize;

    }

}

