package com.pxb7.mall.trade.ass.infra.repository.db;

import com.baomidou.mybatisplus.extension.service.IService;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.ExtVoucher;

import java.util.List;

/**
 * 放款单(ExtPayment)表服务接口
 *
 * <AUTHOR>
 * @since 2024-08-03 18:35:46
 */
public interface ExtVoucherRepository extends IService<ExtVoucher> {

    Boolean findInProgressReceipt(String orderItemId);

    ExtVoucher getFinishReceipt(String orderId);


    List<ExtVoucher> queryCollectionReceiptList(List<String> item);

    ExtVoucher queryExtPayment(String orderItemId, Integer value);

    /**
     * 根据orderItemId获取ExtPayment
     * @param orderItemId
     * @return
     */
    ExtVoucher getExtPaymentByOrderItemId(String orderItemId);

}

