package com.pxb7.mall.trade.ass.infra.enums;

import java.util.Arrays;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 整单退款类型
 */
@Getter
@AllArgsConstructor
public enum RefundAuditStatusEnum {

    WAIT_SERVICE_AUDIT(1, "待客服审核"),

    WAIT_COLLECT_ACCOUNT_INFO(2, "待提交打款信息(线下打款才有)"),

    WAIT_FINANCE_AUDIT(3, "待财务审核"),

    PASS(4, "审核成功"),

    REJECT(5, "审核失败");

    private final Integer value;

    private final String label;

    public static String getLabel(Integer value) {
        return Arrays.stream(RefundAuditStatusEnum.values()).filter(e -> e.getValue().equals(value))
            .map(RefundAuditStatusEnum::getLabel).findAny().orElse(null);
    }
}
