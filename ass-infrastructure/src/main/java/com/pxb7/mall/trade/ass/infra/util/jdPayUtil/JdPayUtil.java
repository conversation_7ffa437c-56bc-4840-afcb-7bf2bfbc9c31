package com.pxb7.mall.trade.ass.infra.util.jdPayUtil;

import com.alibaba.cola.exception.BizException;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.pxb7.mall.trade.ass.infra.remote.model.outpayparam.request.JdPayOutParamPO;
import com.pxb7.mall.trade.ass.infra.remote.service.http.dto.request.JdPayReqPO;
import com.pxb7.mall.trade.order.client.constants.JdPayConstant;
import com.pxb7.mall.trade.order.client.dto.ErrorCode;
import com.pxb7.mall.trade.ass.infra.util.StringUtil;
import com.wangyin.aks.security.sign.SignEnvelopService;
import com.wangyin.aks.security.sign.SignEnvelopServiceImpl;
import lombok.extern.slf4j.Slf4j;
import okhttp3.ResponseBody;
import org.apache.commons.lang3.StringUtils;
import retrofit2.Response;

import java.nio.charset.StandardCharsets;
import java.util.Map;

@Slf4j
public class JdPayUtil {

    /**
     * 加密并签名
     * @param reqNo 请求唯一标识
     */
    public static JdPayReqPO.BaseParam encryptAndSign(JdPayOutParamPO paramPO, String jsonParam, String reqNo) throws Exception {
        JdPayReqPO.BaseParam dto = new JdPayReqPO.BaseParam();
        dto.setMerchantNo(paramPO.getMerchantNo());
        dto.setReqNo(reqNo);
        //加密请求参数
        byte[] dataBytes = jsonParam.getBytes(StandardCharsets.UTF_8);
        SignEnvelopService se = new SignEnvelopServiceImpl();
        String res = se.signEnvelop(paramPO.getPriCert(), paramPO.getPassword(), paramPO.getPubCert(), dataBytes);
        dto.setEncData(res);
        Map<String, String> signMap = convertToMap(dto);
        String signData = SignUtils.sign(signMap, JdPayConstant.SING_TYPE, paramPO.getSignKey(), JdPayConstant.CHARSET_UTF8, JdPayConstant.TYPE_NPP2);
        dto.setSignData(signData);
        log.info("【京东支付】加签后报文内容，dto={}", JSON.toJSONString(dto));
        return dto;
    }

    public static Map<String, String> convertToMap(JdPayReqPO.BaseParam reqDTO) {
        Map<String, String> map = Maps.newHashMapWithExpectedSize(8);
        map.put("merchantNo", reqDTO.getMerchantNo());
        map.put("reqNo", reqDTO.getReqNo());
        map.put("charset", reqDTO.getCharset());
        map.put("formatType", reqDTO.getFormatType());
        map.put("encType", reqDTO.getEncType());
        map.put("encData", reqDTO.getEncData());
        map.put("signType", reqDTO.getSignType());
        if (StringUtil.isNotBlank(reqDTO.getSignData())) {
            map.put("signData", reqDTO.getSignData());
        }
        log.info("【京东支付】加签参数，map={}", JSON.toJSONString(map));
        return map;
    }

    /**
     * 验签并返回
     */
    public static JSONObject verifySignAndReturn(Response<ResponseBody> response, JdPayOutParamPO paramPO) throws Exception {
        return verifySignAndReturn(response, paramPO.getSignKey());
    }

    /**
     * 验签并返回
     */
    public static JSONObject verifySignAndReturn(Response<ResponseBody> response, String signKey) throws Exception {
        log.info("【京东支付】响应参数:{}", response);
        String responseText;
        if (!response.isSuccessful()) {
            log.error("【京东支付】京东支付响应参数失败");
            throw new BizException(response.message());
        }
        try (ResponseBody body = response.body()) {
            responseText = body.string();
        } catch (Exception e) {
            log.error("【京东支付】解析京东支付结果异常", e);
            throw new BizException(ErrorCode.PAY_ERROR.getErrCode(), "请求响应解析异常");
        }
        return verifySignAndReturn(responseText, signKey);
    }

    /**
     * 验签并返回
     */
    public static JSONObject verifySignAndReturn(String responseText, String signKey) throws Exception {
        log.info("【京东支付】 验签&解码 responseText = {}",  responseText);
        JSONObject jsonObject = null;
        Map<String, String> contentMap = (Map<String, String>) JSON.parse(responseText);
        String code = contentMap.get("code");
        if (!"00000".equals(code)) {
            log.warn("【京东支付】请求异常，responseText = {}", responseText);
            throw new BizException(ErrorCode.PAY_ERROR.getErrCode(), contentMap.get("desc"));
        }
        //验签
        String signType = contentMap.get(JdPayConstant.PARAM_SIGN_TYPE);
        String resSignData = contentMap.remove(JdPayConstant.PARAM_SIGN_DATA);
        String signData = SignUtils.sign(contentMap, signType, signKey, JdPayConstant.CHARSET_UTF8, JdPayConstant.TYPE_NPP2);
        if (!resSignData.equals(signData)) {
            log.error("【京东支付】验签失败！");
            throw new BizException(ErrorCode.BANK_SIGN_ERROR1.getErrCode(),  ErrorCode.BANK_SIGN_ERROR1.getErrDesc());
        }
        String data = contentMap.get(JdPayConstant.RESPONSE_DATA);
        if (null != data) {
            //解码
            String clearData = CodecUtils.decodeBase64(data, false);
            if (StringUtils.isNotEmpty(clearData)) {
                jsonObject = JSONObject.parseObject(clearData);
                log.info("【京东支付】解码结果 jsonObject = {}", jsonObject.toJSONString());
            }
        }
        return jsonObject;
    }
}
