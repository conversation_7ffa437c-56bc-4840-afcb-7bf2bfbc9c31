package com.pxb7.mall.trade.ass.infra.remote.dubbo;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.pxb7.mall.trade.ass.infra.util.DubboCatchUtil;
import com.pxb7.mall.trade.order.client.api.OrderInfoDubboServiceI;
import com.pxb7.mall.trade.order.client.api.OrderItemIndemnityDubboServiceI;
import com.pxb7.mall.trade.order.client.dto.request.order.dubbo.TradeIdentityReqDTO;
import com.pxb7.mall.trade.order.client.dto.request.orderItemIndemnity.OrderItemIndemnityDubboDTO;
import com.pxb7.mall.trade.order.client.dto.response.ass.StartAssInfo;
import com.pxb7.mall.trade.order.client.dto.response.order.dubbo.OrderInfoDubboRespDTO;
import com.pxb7.mall.trade.order.client.dto.response.order.dubbo.TradeIdentityRespDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * Copyright (C), 2002-2025, 螃蟹游戏服务网
 *
 * @fileName: OrderItemRpcGateway.java
 * @description: 订单dubbo接口
 * @author: guominqiang
 * @email: <EMAIL>
 * @date: 2025/4/11 15:47
 * @history: //修改记录
 * <author> <time> <version> <desc>
 * 修改人姓名 修改时间 版本号 描述
 */

@Component
@Slf4j
public class OrderItemRpcGateway {
    @DubboReference(providedBy = "order")
    private OrderInfoDubboServiceI orderInfoDubboServiceI;

    @DubboReference(providedBy = "order")
    private OrderItemIndemnityDubboServiceI orderItemIndemnityDubboServiceI;

    /**
     * 获取订单售后信息
     *
     * @param orderItemId
     * @return
     */
    public StartAssInfo startAss(String orderItemId) {
        SingleResponse<StartAssInfo> response = orderInfoDubboServiceI.startAss(orderItemId);
        return (response.isSuccess() && ObjectUtil.isNotEmpty(response.getData())) ? response.getData() : null;
    }

    /**
     * 获取订单信息
     *
     * @param orderItemId 订单id
     * @return 订单信息
     */
    public OrderInfoDubboRespDTO getOrderInfo(String orderItemId) {
        SingleResponse<OrderInfoDubboRespDTO> response = orderInfoDubboServiceI.getOrderInfo(orderItemId);
        return (response.isSuccess() && ObjectUtil.isNotEmpty(response.getData())) ? response.getData() : null;
    }

    /**
     * 获取订单交易买卖家身份信息
     *
     * @param orderItemId 订单id
     * @return
     */
    public TradeIdentityRespDTO getTradeIdentity(String orderItemId) {
        TradeIdentityReqDTO tradeIdentityReqDTO = new TradeIdentityReqDTO();
        tradeIdentityReqDTO.setOrderItemId(orderItemId);
        SingleResponse<TradeIdentityRespDTO> tradeIdentity = orderInfoDubboServiceI.getTradeIdentity(tradeIdentityReqDTO);
        return (tradeIdentity.isSuccess() && ObjectUtil.isNotEmpty(tradeIdentity.getData()))
            ? tradeIdentity.getData()
            : null;
    }

    /**
     * 查询订单包赔信息
     *
     * @param orderItemId 订单id
     * @return 订单包赔列表
     */
    public List<OrderItemIndemnityDubboDTO> getIndemnityList(String orderItemId) {
        MultiResponse<OrderItemIndemnityDubboDTO> response = orderItemIndemnityDubboServiceI.getIndemnityListByOrderItemId(orderItemId);
        return response.isSuccess() ? response.getData() : Collections.emptyList();
    }


    /**
     * 查询订单包赔信息
     * @param orderItemId
     * @return 生效的包赔列表
     */
    public List<OrderItemIndemnityDubboDTO> getEffectiveIndemnityListByOrderItemId(String orderItemId) {
        MultiResponse<OrderItemIndemnityDubboDTO> multiResponse = DubboCatchUtil.catchException(() -> orderItemIndemnityDubboServiceI.getEffectiveIndemnityListByOrderItemId(orderItemId), "查询订单包赔信息失败");
        if(Objects.isNull(multiResponse) || !multiResponse.isSuccess()){
            return Collections.emptyList();
        }
        return multiResponse.getData();
    }
}
