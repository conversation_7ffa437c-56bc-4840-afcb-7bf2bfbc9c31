package com.pxb7.mall.trade.ass.infra.remote.service.http.api;

import com.pxb7.mall.trade.ass.infra.remote.model.request.*;
import okhttp3.ResponseBody;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.POST;
import retrofit2.http.Path;

public interface TaishanPayHttpApi {

    //@POST("/api/payment/part-refund/{appId}")
    //Call<ResponseBody> partRefund(@Body TaiShanPartRefundReqPO po, @Path("appId") String appId);

    @POST("/api/payment/refund/{appId}")
    Call<ResponseBody> refund(@Body TaiShanRefundReqPO po, @Path("appId") String appId);

    // @POST("/api/payment/refund-query-v2/{appId}")
    // Call<ResponseBody> refundQueryV2(@Body RefundQueryV2 queryV2, @Path("appId") String appId);

    @POST("/api/payment/query/{appId}")
    Call<ResponseBody> payQuery(@Body TaiShanPayQueryReqPO reqPO, @Path("appId") String appId);

    //@POST("/api/payment/refund-query-v3/{appId}")
    //Call<ResponseBody> refundQueryV3(@Body TaiShanRefundQueryV3ReqPO reqPO, @Path("appId") String appId);

    @POST("/api/payment/part-refund-v3/{appId}")
    Call<ResponseBody> partRefundV3(@Body TaiShanPartRefundV3ReqPO po, @Path("appId") String appId);

}
