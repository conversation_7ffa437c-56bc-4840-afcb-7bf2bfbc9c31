package com.pxb7.mall.trade.ass.infra.repository.db.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.pxb7.mall.trade.ass.client.dto.request.AssStatusReqDTO;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.AssSchedule;

/**
 * 售后流程进度(AssSchedule)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-08-12 10:59:09
 */
@Mapper
public interface AssScheduleMapper extends BaseMapper<AssSchedule> {

    boolean updateWorkOrderIdAndAssTypeAndFinishById(@Param("id") Long id, @Param("workOrderId") String workOrderId,
        @Param("assType") Integer assType, @Param("finish") Boolean finish, @Param("updateUserId") String updateUserId);

    boolean updateFinishById(@Param("id") Long id, @Param("finish") Boolean finish,
        @Param("updateUserId") String updateUserId);

    List<AssSchedule> findListByOrderItemIdAndRoomId(@Param("list") List<AssStatusReqDTO> list);
}
