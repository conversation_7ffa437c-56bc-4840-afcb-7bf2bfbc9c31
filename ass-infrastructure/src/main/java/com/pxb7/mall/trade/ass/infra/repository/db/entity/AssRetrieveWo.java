package com.pxb7.mall.trade.ass.infra.repository.db.entity;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.*;
import com.pxb7.mall.trade.ass.client.enums.*;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 售后找回工单
 *
 * <AUTHOR>
 **/
@Getter
@Setter
@ToString
@Accessors(chain = true)
@TableName(value = "ass_retrieve_work")
public class AssRetrieveWo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    /**
     * 自增id
     */
    @TableField(value = "id")
    private Long id;

    /**
     * 主键ID
     */
    @TableId(value = "ass_retrieve_id", type = IdType.INPUT)
    private String assRetrieveId;

    /**
     * 工单编号
     */
    @TableField(value = "work_order_no")
    private String workOrderNo;

    /**
     * 订单ID
     */
    @TableField(value = "order_item_id")
    private String orderItemId;

    /**
     * 商品id
     */
    @TableField(value = "product_id")
    private String productId;

    /**
     * 商品编码
     */
    @TableField(value = "product_unique_no")
    private String productUniqueNo;
    /**
     * 商品类型: 1账号 2充值 3金币 4装备 5初始号 20诚心卖
     */
    @TableField(value = "product_type")
    private Integer productType;

    /**
     * 游戏ID
     */
    @TableField(value = "game_id")
    private String gameId;

    /**
     * 申请人id
     */
    @TableField(value = "proposer_user_id")
    private String proposerUserId;

    /**
     * 工单发起来源 1:c端用户 2:客服 3:admin用户
     *
     * @see AssSponsor
     */
    @TableField(value = "sponsor")
    private Integer sponsor;

    /**
     * 接待客服id
     */
    @TableField(value = "recv_customer_id")
    private String recvCustomerId;

    /**
     * 接待人飞书名称
     */
    @TableField(value = "recv_feishu_name")
    private String recvFeishuName;

    /**
     * 审核客服id
     */
    @TableField(value = "audit_customer_id")
    private String auditCustomerId;

    /**
     * 审核人飞书名称
     */
    @TableField(value = "audit_feishu_name")
    private String auditFeishuName;

    /**
     * 当前处理人员
     */
    @TableField(value = "deal_user_id")
    private String dealUserId;

    /**
     * 状态 0:待石锤 1:处理中 2:处理完成 3:已关闭
     *
     * @see AssStatus
     */
    @TableField(value = "`status`")
    private Integer status;

    /**
     * 处理结果 0:处理中 1:追回账号 2:追回号款 3:无结果
     *
     * @see AssRetrieveDealResult
     */
    @TableField(value = "deal_result")
    private Integer dealResult;

    /**
     * 追回金额
     */
    @TableField(value = "recover_amount")
    private Long recoverAmount;

    /**
     * 追回号款状态 0:无 1:部分追回 2:全追回
     *
     * @see AssRecoverStatus
     */
    @TableField(value = "recover_status")
    private Integer recoverStatus;

    /**
     * 赔付金额
     */
    @TableField(value = "claim_amount")
    private Long claimAmount;

    /**
     * 赔付状态 0:无 1:待审核 2:已赔付
     *
     * @see AssWoClaimStatus
     */
    @TableField(value = "claim_status")
    private Integer claimStatus;

    /**
     * 预计完成时间
     */
    @TableField(value = "expected_time")
    private LocalDateTime expectedTime;

    /**
     * 完成时间
     */
    @TableField(value = "complete_time")
    private LocalDateTime completeTime;

    /**
     * 是否轮转
     */
    @TableField(value = "is_rotate")
    private Boolean rotate;

    /**
     * 问题归类一级目录
     */
    @TableField(value = "classify_first")
    private String classifyFirst;

    /**
     * 问题归类二级目录
     */
    @TableField(value = "classify_second")
    private String classifySecond;

    /**
     * 数据来源 1:IM私聊 2:企业微信 3:投诉转交 4:其他
     *
     * @see AssDataSource
     */
    @TableField(value = "data_source")
    private Integer dataSource;

    /**
     * 渠道来源 1：螃蟹 2：支付宝小程序
     *
     * @see AssSourceType
     */
    @TableField(value = "source_type")
    private Integer sourceType;

    /**
     * 保险 1：购买
     */
    @TableField(value = "insure")
    private Integer insure;

    /**
     * 工单创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * 创建用户ID
     */
    @TableField(value = "create_user_id")
    private String createUserId;

    /**
     * 创建人飞书名称
     */
    @TableField(value = "create_feishu_name")
    private String createFeishuName;

    /**
     * 修改时间
     */
    @TableField(value = "update_time", update = "now()")
    private LocalDateTime updateTime;

    /**
     * 更新人
     */
    @TableField(value = "update_user_id")
    private String updateUserId;

    /**
     * 删除标记
     */
    @TableLogic
    @TableField(value = "is_deleted")
    private Boolean deleted;

    /**
     * 是否为协助单 0:否 1:是
     */
    @TableField(value = "assistance_work_order")
    private Integer assistanceWorkOrder;

    /**
     * 数据创建来源 1:IM客服端 2:后台
     */
    @TableField(value = "data_create_source")
    private Integer dataCreateSource;

    /**
     * 协助类型
     */
    @TableField(value = "assistance_config_desc")
    private  String assistanceConfigDesc;

    @TableField(value = "assistance_user_desc")
    private String assistanceUserDesc;

}
