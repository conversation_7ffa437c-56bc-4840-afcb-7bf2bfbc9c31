package com.pxb7.mall.trade.ass.infra.repository.db;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.AssScheduleLog;
import com.pxb7.mall.trade.ass.infra.repository.db.mapper.AssScheduleLogMapper;

import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 售后流程进度日志(AssScheduleLog)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-08-12 10:59:48
 */
@Slf4j
@Repository
public class AssScheduleLogDbRepository extends ServiceImpl<AssScheduleLogMapper, AssScheduleLog>
    implements AssScheduleLogRepository {

    @Override
    public boolean updateNode(String scheduleId, String nodeId, String data) {
        LambdaUpdateWrapper<AssScheduleLog> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(AssScheduleLog::getScheduleId, scheduleId);
        updateWrapper.eq(AssScheduleLog::getNodeId, nodeId);
        updateWrapper.set(AssScheduleLog::getData, data);
        return this.update(updateWrapper);
    }

    @Override
    public List<AssScheduleLog> findListByScheduleId(String scheduleId) {
        if (StringUtils.isBlank(scheduleId)) {
            return CollUtil.newArrayList();
        }
        return this.lambdaQuery().eq(AssScheduleLog::getScheduleId, scheduleId)
            .eq(AssScheduleLog::getDeleted, Boolean.FALSE).orderByAsc(AssScheduleLog::getId).list();
    }

    @Override
    public boolean delByScheduleIdAndNodeIds(String scheduleId, List<String> nodeIds) {
        if (StringUtils.isBlank(scheduleId) || CollUtil.isEmpty(nodeIds)) {
            return false;
        }
        return baseMapper.delByScheduleIdAndNodeIds(scheduleId, nodeIds);
    }

    @Override
    public boolean delByScheduleId(String scheduleId) {
        if (StringUtils.isBlank(scheduleId)) {
            return false;
        }
        return baseMapper.delByScheduleId(scheduleId);
    }
}
