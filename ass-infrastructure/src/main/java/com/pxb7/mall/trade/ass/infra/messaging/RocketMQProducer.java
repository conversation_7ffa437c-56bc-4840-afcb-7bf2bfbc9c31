package com.pxb7.mall.trade.ass.infra.messaging;

import org.apache.rocketmq.client.apis.ClientException;
import org.apache.rocketmq.client.apis.producer.SendReceipt;
import org.apache.rocketmq.client.apis.producer.Transaction;
import org.apache.rocketmq.client.common.Pair;
import org.apache.rocketmq.client.core.RocketMQClientTemplate;
import org.springframework.stereotype.Repository;

import java.time.Duration;
import java.util.concurrent.CompletableFuture;
import java.util.function.Consumer;

/**
 * @description:
 * @author: heyc
 * @date: 2024/5/14 16:46
 * @Version 1.0
 **/
@Repository
public class RocketMQProducer implements MQProducer {

    private RocketMQClientTemplate rocketMQClientTemplate;

    public RocketMQProducer(RocketMQClientTemplate rocketMQClientTemplate) {
        this.rocketMQClientTemplate = rocketMQClientTemplate;
    }

    @Override
    public String send(String destination, Object payload) {
        SendReceipt sendReceipt = rocketMQClientTemplate.syncSendNormalMessage(destination, payload);
        return sendReceipt.getMessageId().toString();
    }

    @Override
    public String send(String topic, String tag, Object payload) {
        SendReceipt sendReceipt = rocketMQClientTemplate.syncSendNormalMessage(buildDestination(topic, tag), payload);
        return sendReceipt.getMessageId().toString();
    }

    public CompletableFuture<String> asyncSend(String topic, String tag, Object payload) {
        return this.asyncSend(this.buildDestination(topic, tag), payload);
    }

    @Override
    public CompletableFuture<String> asyncSend(String destination, Object payload) {
        CompletableFuture<SendReceipt> completableFuture = rocketMQClientTemplate.asyncSendNormalMessage(destination, payload, null);
        return completableFuture.thenApply(sendReceipt -> sendReceipt.getMessageId().toString());
    }

    @Override
    public String sendDelay(String destination, Object payload, Duration messageDelayTime) {
        SendReceipt sendReceipt = rocketMQClientTemplate.syncSendDelayMessage(destination, payload, messageDelayTime);
        return sendReceipt.getMessageId().toString();
    }

    @Override
    public CompletableFuture<String> asyncSendDelay(String destination, Object payload, Duration messageDelayTime) {
        CompletableFuture<SendReceipt> completableFuture = rocketMQClientTemplate.asyncSendDelayMessage(destination, payload, messageDelayTime, null);
        return completableFuture.thenApply(sendReceipt -> sendReceipt.getMessageId().toString());
    }

    @Override
    public String sendOrder(String destination, Object payload, String group) {
        SendReceipt sendReceipt = rocketMQClientTemplate.syncSendFifoMessage(destination, payload, group);
        return sendReceipt.getMessageId().toString();
    }

    @Override
    public CompletableFuture<String> asyncSendOrder(String destination, Object payload, String group) {
        CompletableFuture<SendReceipt> completableFuture = rocketMQClientTemplate.asyncSendFifoMessage(destination, payload, group, null);
        return completableFuture.thenApply(sendReceipt -> sendReceipt.getMessageId().toString());
    }

    @Override
    public String sendInTransaction(String destination, Object payload, Consumer<String> callback) {
        if (callback == null) {
            throw new RuntimeException("callback is null");
        }
        Pair<SendReceipt, Transaction> pair;
        try {
            pair = rocketMQClientTemplate.sendMessageInTransaction(destination, payload);
        } catch (ClientException ce) {
            throw new RuntimeException(ce);
        }
        try {
            callback.accept(pair.getSendReceipt().toString());
        } catch (Exception e) {
            try {
                pair.getTransaction().rollback();
            } catch (ClientException ex) {
            }
            throw new RuntimeException(e);
        }
        try {
            pair.getTransaction().commit();
        } catch (Exception e) {
            // 提交失败走事务回查
        }
        return pair.getSendReceipt().toString();
    }

    public String buildDestination(String topic, String tag) {
        return topic + ":" + tag;
    }

}
