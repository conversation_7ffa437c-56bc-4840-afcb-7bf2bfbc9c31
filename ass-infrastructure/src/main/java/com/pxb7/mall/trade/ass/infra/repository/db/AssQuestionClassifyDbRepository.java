package com.pxb7.mall.trade.ass.infra.repository.db;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.AssQuestionClassify;
import com.pxb7.mall.trade.ass.infra.repository.db.mapper.AssQuestionClassifyMapper;

import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 售后问题归类配置
 *
 * <AUTHOR>
 * @since: 2024-10-01 19:45
 **/
@Service
public class AssQuestionClassifyDbRepository extends ServiceImpl<AssQuestionClassifyMapper, AssQuestionClassify> implements AssQuestionClassifyRepository {
    @Override
    public List<AssQuestionClassify> selectList(Integer assType) {
        LambdaQueryWrapper<AssQuestionClassify> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AssQuestionClassify::getAssType, assType);
        return this.list(queryWrapper);
    }
}
