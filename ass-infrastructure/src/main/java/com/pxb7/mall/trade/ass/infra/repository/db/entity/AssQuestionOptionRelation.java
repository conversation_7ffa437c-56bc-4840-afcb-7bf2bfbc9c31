package com.pxb7.mall.trade.ass.infra.repository.db.entity;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.*;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 售后问答问题游戏关联
 *
 * <AUTHOR>
 **/
@Getter
@Setter
@ToString
@Accessors(chain = true)
@TableName(value = "ass_question_option_relation")
public class AssQuestionOptionRelation implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    /**
     * 自增id
     */
    @TableField(value = "id")
    private Long id;

    /**
     * 业务主键id
     */
    @TableId(value = "relation_id", type = IdType.INPUT)
    private String relationId;

    /**
     * 配置方案id-ass_question_option
     */
    @TableField(value = "option_id")
    private String optionId;

    /**
     * 游戏id
     */
    @TableField(value = "game_id")
    private String gameId;

    /**
     * 创建用户id
     */
    @TableField(value = "create_user_id")
    private String createUserId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * 更新用户id
     */
    @TableField(value = "update_user_id")
    private String updateUserId;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", update="now()")
    private LocalDateTime updateTime;

    /**
     * 删除标记
     */
    @TableLogic
    @TableField(value = "is_deleted")
    private Boolean deleted;

}