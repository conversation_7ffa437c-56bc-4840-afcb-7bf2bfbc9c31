package com.pxb7.mall.trade.ass.infra.repository.db;

import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.OrderOperate;
import com.pxb7.mall.trade.ass.infra.repository.db.mapper.OrderOperateMapper;

import lombok.extern.slf4j.Slf4j;

/**
 * 订单操作记录表(OrderOperate)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-10-28 18:27:32
 */
@Slf4j
@Repository
public class OrderOperateDbRepository extends ServiceImpl<OrderOperateMapper, OrderOperate>
    implements OrderOperateRepository {

}
