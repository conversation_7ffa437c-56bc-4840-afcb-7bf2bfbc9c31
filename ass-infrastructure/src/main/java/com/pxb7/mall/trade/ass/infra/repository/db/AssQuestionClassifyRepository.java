package com.pxb7.mall.trade.ass.infra.repository.db;

import com.baomidou.mybatisplus.extension.service.IService;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.AssQuestionClassify;

import java.util.List;

/**
 * 售后问题归类配置
 *
 * <AUTHOR>
 * @since: 2024-10-01 19:45
 **/
public interface AssQuestionClassifyRepository extends IService<AssQuestionClassify> {

    List<AssQuestionClassify> selectList(Integer assType);
}
