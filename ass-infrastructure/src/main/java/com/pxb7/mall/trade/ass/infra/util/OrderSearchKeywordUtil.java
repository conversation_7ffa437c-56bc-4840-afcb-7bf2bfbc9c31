package com.pxb7.mall.trade.ass.infra.util;

import com.pxb7.mall.trade.ass.infra.constant.OrderIndexFieldConstant;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2024/11/8 17:47
 */
public class OrderSearchKeywordUtil {


    // 1.0原始订单编号： 字母 + 16个数字
    private static final String orderItemIdRegexV1 = "^[A-Za-z]+\\d{16}$";

    // 2.0产生的订单编号： 字母+ 固定20个数字
    // 1.0迁移过来的订单编号： 字母 + 固定21个数字
    private static final String orderItemIdRegexV2 = "^[a-zA-Z]+\\d{20,}$";

    // 字母开头，长度<15
    private static final String productUniqueNoRegex = "^[a-zA-Z].{0,15}$";


    /**
     * 手机号正则，1开头，第二位是【3-9】之间的数字，之后是9位数字。总共十一位
     */
    private static final String phoneRegex = "^1[3-9]\\d{9}$";


    /**
     * 判断搜索关键字是什么类型
     *
     * @param keyword
     * @return
     */
    public static String judgeKeywordType(String keyword) {
        // 是订单编号
        if (isOrderItemId(keyword)) {
            return OrderIndexFieldConstant.order_item_id;
        }
        // 是商品编号
        if (isProductUniqueNo(keyword)) {
            return OrderIndexFieldConstant.product_unique_no;
        }
        // 子账号：就是手机号
        if (isPhone(keyword)) {
            return OrderIndexFieldConstant.phone;
        }

        // 默认按照商品标题搜索
        return OrderIndexFieldConstant.product_name;
    }

    /**
     * 判断是否是商品编码
     *
     * 1.0 四个字母，四个数字（线上会有脏数据，可能有中文(中文不考虑)，可能有AAD419、AAD679这样的）
     * 2.0 5个字母，四个数字 (固定的)
     *
     *
     * 规则： 字母开头， 长度小于10个
     */
    public static boolean isProductUniqueNo(String keyword) {
        if (StringUtils.isBlank(keyword)) {
            return false;
        }

        if (keyword.matches(productUniqueNoRegex)) {
            return true;
        }

        return false;
    }

    /**
     * 匹配手机号(子账号)
     *
     * @param keyword
     * @return
     */
    public static boolean isPhone(String keyword) {
        if (StringUtils.isBlank(keyword)) {
            return false;
        }

        if (keyword.matches(phoneRegex)) {
            return true;
        }

        return false;
    }


    public static boolean isOrderItemIdV1(String keyword) {
        if (StringUtils.isBlank(keyword)) {
            return false;
        }
        if (keyword.matches(orderItemIdRegexV1)) {
            return true;
        }
        return false;
    }
    /**
     * 是否是订单编号
     *
     * @param keyword
     * @return
     */
    public static boolean isOrderItemId(String keyword) {
        if (StringUtils.isBlank(keyword)) {
            return false;
        }

        // 2.0订单编号
        if (keyword.matches(orderItemIdRegexV2)) {
            return true;
        }

        // 原始1.0订单编号：此种情况会有可能拿1.0订单编号在2.0系统搜索，这时候也兼容一下，号商和卖家没法拼接
        if (keyword.matches(orderItemIdRegexV1)) {
            return true;
        }
        return false;
    }
}
