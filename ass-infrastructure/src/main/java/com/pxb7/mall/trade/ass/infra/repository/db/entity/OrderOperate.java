package com.pxb7.mall.trade.ass.infra.repository.db.entity;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.*;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 订单操作记录表(OrderOperate)实体类
 *
 * <AUTHOR>
 * @since 2024-10-28 18:27:29
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
@TableName(value = "order_operate")
public class OrderOperate implements Serializable {
    private static final long serialVersionUID = -60188279426291665L;
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 主订单id
     */
    @TableField(value = "order_id")
    private String orderId;
    /**
     * 订单行id
     */
    @TableField(value = "order_item_id")
    private String orderItemId;
    /**
     * 操作类型
     */
    @TableField(value = "opt_type")
    private Integer optType;
    /**
     * 操作内容
     */
    @TableField(value = "opt_content")
    private String optContent;
    /**
     * 操作这个订单的用户类型:0系统,1买家,2卖家,3客服
     */
    @TableField(value = "opt_user_type")
    private Integer optUserType;
    /**
     * 操作的用户id
     */
    @TableField(value = "opt_user_id")
    private String optUserId;
    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    @TableField(value = "update_time", update="now()")
    private LocalDateTime updateTime;
    /**
     * 逻辑删除，0:删除，1:正常
     */
    @TableLogic
    @TableField(value = "is_deleted")
    private Boolean deleted;

}
