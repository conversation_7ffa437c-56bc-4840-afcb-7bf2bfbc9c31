package com.pxb7.mall.trade.ass.infra.remote.dubbo;

import com.alibaba.cola.dto.SingleResponse;
import com.pxb7.mall.im.client.api.SendCommonMsgServiceI;
import com.pxb7.mall.im.client.api.SendMsgServiceI;
import com.pxb7.mall.im.client.dto.request.card.SendRichTextMsgReqDTO;
import com.pxb7.mall.im.client.dto.request.card.richtext.BaseRichTextMsgContent;
import com.pxb7.mall.im.client.dto.request.card.richtext.RichTextContent;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.OrderItemExtend;

import com.pxb7.mall.trade.ass.infra.util.DubboCatchUtil;
import com.pxb7.mall.trade.ass.infra.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.curator.shaded.com.google.common.collect.Lists;
import org.apache.dubbo.common.utils.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;


import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class IMRoomMessageGateway {
    @DubboReference
    private SendCommonMsgServiceI sendCommonMsgServiceI;
    @DubboReference(retries = 0, timeout = 2000)
    private SendMsgServiceI sendMsgServiceI;

    /**
     * 发送 订单更新富文本卡片消息（不带：客服通知）
     *
     */
    public void sendIMMessageWithoutNotice(OrderItemExtend orderItemExtend,
                                           String title,
                                           String content,
                                           List<String> mentionedIds,
                                           List<String> targetUserIds
    ) {
        if (Objects.isNull(orderItemExtend)) {
            log.info("sendIMMessageWithoutNotice orderItemExtend is null");
            return;
        }
        SendRichTextMsgReqDTO msgReqDTO = buildRichTextMsgWithoutNotice(orderItemExtend.getOrderItemId(),
                orderItemExtend.getProductUniqueNo(), title, content);
        if (StringUtil.isEffectiveId(orderItemExtend.getDeliveryRoomId())
                && StringUtil.isEffectiveId(orderItemExtend.getDeliveryCustomerId())) {
            msgReqDTO.setTargetId(orderItemExtend.getDeliveryRoomId())
                    .setMentionedIds(mentionedIds)
                    .setFromUserId(orderItemExtend.getDeliveryCustomerId())
                    .setTargetUserIds(targetUserIds);
            log.info("sendIMMessageWithoutNotice 发送交付客服 富文本卡片消息，msgReqDTO = {}", msgReqDTO);
            SingleResponse<String> response = DubboCatchUtil.catchException(() -> sendCommonMsgServiceI.sendRichTextMsg(msgReqDTO), "发送交付文本卡片消息失败");
            log.info("sendIMMessageWithoutNotice 发送交付客服 富文本卡片消息，msgReqDTO:{} , response:{}", msgReqDTO, response);
        }
    }

    private SendRichTextMsgReqDTO buildRichTextMsgWithoutNotice(String orderItemId,
                                                                String productUniqueNo,
                                                                String title,
                                                                String content) {
        ArrayList<BaseRichTextMsgContent> richTextContents = Lists.newArrayList(
                new RichTextContent("订单编号: " + orderItemId),
                new RichTextContent("商品编号: " + productUniqueNo));
        if (StringUtils.isNotBlank(content)) {
            richTextContents.add(new RichTextContent(content));
        }
        return new SendRichTextMsgReqDTO()
                .setTitle(title)
                .setContent(richTextContents);
    }
}
