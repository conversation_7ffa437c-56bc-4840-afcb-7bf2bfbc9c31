package com.pxb7.mall.trade.ass.infra.repository.db.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.OrderOperate;

/**
 * 订单操作记录表(OrderOperate)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-10-28 18:27:31
 */
@Mapper
public interface OrderOperateMapper extends BaseMapper<OrderOperate> {
    /**
     * 批量新增数据
     *
     * @param entities List<OrderOperate> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<OrderOperate> entities);

}
