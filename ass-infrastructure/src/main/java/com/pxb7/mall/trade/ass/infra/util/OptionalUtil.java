package com.pxb7.mall.trade.ass.infra.util;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.SingleResponse;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import com.google.common.collect.Lists;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class OptionalUtil {

    public static <T extends Collection<?>> Optional<T> ofEmpty(T value) {
        return (value == null || value.isEmpty()) ? Optional.empty() : Optional.of(value);
    }

    public static <T extends Map<?, ?>> Optional<T> ofEmpty(T value) {
        return (value == null || value.isEmpty()) ? Optional.empty() : Optional.of(value);
    }

    public static Optional<String> ofBlank(String value) {
        return value == null || "".equals(value.trim()) ? Optional.empty() : Optional.of(value);
    }

    public static <T extends Number> Optional<T> ofPositive(T value) {
        return NumberUtil.isPositive(value) ? Optional.of(value) : Optional.empty();
    }

    public static <T extends List<?>> Optional<List<T>> ofData(MultiResponse<T> result) {
        return Optional.ofNullable(result)
            .filter(MultiResponse::isSuccess)
            .filter(MultiResponse::isNotEmpty)
            .map(MultiResponse::getData);
    }

    public static <T> Optional<T> ofData(SingleResponse<T> result) {
        return Optional.ofNullable(result).filter(SingleResponse::isSuccess).map(SingleResponse::getData);
    }
    public static <T> List<T> of(List<T> source) {
        return Optional.ofNullable(source).orElse(Lists.newArrayListWithCapacity(0));
    }

}
