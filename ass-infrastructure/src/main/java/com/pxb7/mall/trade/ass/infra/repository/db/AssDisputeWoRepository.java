package com.pxb7.mall.trade.ass.infra.repository.db;

import com.baomidou.mybatisplus.extension.service.IService;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.AssDisputeWo;
import com.pxb7.mall.trade.ass.infra.repository.db.model.request.DealDisputeResultReqPO;

/**
 * 售后纠纷工单
 *
 * <AUTHOR>
 * @since: 2024-08-13 16:48
 **/
public interface AssDisputeWoRepository extends IService<AssDisputeWo> {

    boolean completeDispute(DealDisputeResultReqPO reqPO);

    boolean userCancel(String assDisputeId);
}
