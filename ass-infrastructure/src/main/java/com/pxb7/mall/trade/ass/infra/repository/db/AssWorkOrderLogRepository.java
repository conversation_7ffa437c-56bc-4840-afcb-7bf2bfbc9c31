package com.pxb7.mall.trade.ass.infra.repository.db;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pxb7.mall.trade.ass.infra.model.AssWorkOrderLogReqPO;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.AssWorkOrderLog;

/**
 * 售后工单日志明细(AssWorkOrderLog)表服务接口
 *
 * <AUTHOR>
 * @since 2025-07-31 10:19:35
 */
public interface AssWorkOrderLogRepository extends IService<AssWorkOrderLog> {

    boolean insert(AssWorkOrderLogReqPO.AddPO param);

    boolean update(AssWorkOrderLogReqPO.UpdatePO param);

    boolean deleteById(AssWorkOrderLogReqPO.DelPO param);

    AssWorkOrderLog findById(Long id);

    List<AssWorkOrderLog> list(AssWorkOrderLogReqPO.SearchPO param);

    Page<AssWorkOrderLog> page(AssWorkOrderLogReqPO.PagePO param);

}

