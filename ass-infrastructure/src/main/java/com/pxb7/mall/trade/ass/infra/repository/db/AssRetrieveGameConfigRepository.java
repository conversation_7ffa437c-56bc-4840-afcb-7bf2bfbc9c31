package com.pxb7.mall.trade.ass.infra.repository.db;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pxb7.mall.trade.ass.infra.model.AssRetrieveGameConfigReqPO;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.AssRetrieveGameConfig;

/**
 * 售后找回游戏配置(AssRetrieveGameConfig)表服务接口
 *
 * <AUTHOR>
 * @since 2025-08-01 14:11:27
 */
public interface AssRetrieveGameConfigRepository extends IService<AssRetrieveGameConfig> {

    boolean insert(AssRetrieveGameConfigReqPO.AddPO param);

    boolean update(AssRetrieveGameConfigReqPO.UpdatePO param);

    boolean deleteById(AssRetrieveGameConfigReqPO.DelPO param);

    AssRetrieveGameConfig findById(Long id);

    List<AssRetrieveGameConfig> list(AssRetrieveGameConfigReqPO.SearchPO param);

    Page<AssRetrieveGameConfig> page(AssRetrieveGameConfigReqPO.PagePO param);

}

