package com.pxb7.mall.trade.ass.infra.repository.db;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.AssClaim;
import com.pxb7.mall.trade.ass.infra.repository.db.mapper.AssClaimMapper;

import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 售后赔付管理(AssClaim)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-10-26 14:54:02
 */
@Slf4j
@Repository
public class AssClaimDbRepository extends ServiceImpl<AssClaimMapper, AssClaim> implements AssClaimRepository {

    @Override
    public long countByWorkOrderIdAndStatus(String workOrderId, List<Integer> status) {
        if (StringUtils.isBlank(workOrderId) || CollUtil.isEmpty(status)) {
            return 0L;
        }
        LambdaUpdateWrapper<AssClaim> queryWrapper = new LambdaUpdateWrapper<>();
        queryWrapper.eq(AssClaim::getWorkOrderId, workOrderId);
        queryWrapper.in(AssClaim::getStatus, status);
        return this.count(queryWrapper);
    }

}
