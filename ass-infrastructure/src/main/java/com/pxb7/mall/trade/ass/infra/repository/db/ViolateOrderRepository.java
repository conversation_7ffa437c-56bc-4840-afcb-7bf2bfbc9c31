package com.pxb7.mall.trade.ass.infra.repository.db;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pxb7.mall.trade.ass.infra.model.ViolateOrderReqPO;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.ViolateOrder;

/**
 * 违约单(ViolateOrder)表服务接口
 *
 * <AUTHOR>
 * @since 2025-03-27 15:47:15
 */
public interface ViolateOrderRepository extends IService<ViolateOrder> {

    List<ViolateOrder> list(ViolateOrderReqPO.SearchPO param);

    ViolateOrder find(ViolateOrderReqPO.SearchPO param);

    long count(ViolateOrderReqPO.SearchPO param);


}

