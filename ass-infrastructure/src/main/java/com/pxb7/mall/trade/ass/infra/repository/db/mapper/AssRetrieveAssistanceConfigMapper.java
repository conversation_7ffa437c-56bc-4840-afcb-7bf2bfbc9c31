package com.pxb7.mall.trade.ass.infra.repository.db.mapper;

import com.pxb7.mall.trade.ass.infra.repository.db.entity.AssRetrieveAssistanceConfig;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 找回协助类型配置表(AssRetrieveAssistanceConfig)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-07-30 13:45:21
 */
@Mapper
public interface AssRetrieveAssistanceConfigMapper extends BaseMapper<AssRetrieveAssistanceConfig> {
    /**
     * 批量新增数据
     *
     * @param entities List<AssRetrieveAssistanceConfig> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<AssRetrieveAssistanceConfig> entities);

}

