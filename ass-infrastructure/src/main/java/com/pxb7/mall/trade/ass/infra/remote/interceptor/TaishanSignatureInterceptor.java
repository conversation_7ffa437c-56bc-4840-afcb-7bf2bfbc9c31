package com.pxb7.mall.trade.ass.infra.remote.interceptor;

import com.pxb7.mall.trade.ass.infra.util.Md5Util;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Interceptor;
import okhttp3.Request;
import okhttp3.Response;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * 泰山支付请求头添加签名拦截
 */
@Slf4j
@Component
public class TaishanSignatureInterceptor implements Interceptor{

    private String md5Key;

    public void setMd5Key(String md5Key) {
        this.md5Key = md5Key;
    }

    @NotNull
    @Override
    public Response intercept(@NotNull Chain chain) throws IOException {
        Request request = chain.request();
        long secondTimeStamp = System.currentTimeMillis() / 1000;
        String input = md5Key + ":" + secondTimeStamp;
        String digest = Md5Util.digest(input);
        log.info("泰山支付时间戳:{},sign:{}", secondTimeStamp,digest);
        Request.Builder requestBuilder = request.newBuilder();
        Request signedRequest = requestBuilder.addHeader("Content-Type", "application/json")
                .addHeader("timeStamp",String.valueOf(secondTimeStamp) )
                .addHeader("sign",digest).build();
        return chain.proceed(signedRequest);

    }
}
