package com.pxb7.mall.trade.ass.infra.repository.db.entity;

import java.io.Serializable;
import java.util.Date;
import java.time.*;
import java.math.BigDecimal;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * 支付渠道表(PayChannel)实体类
 *
 * <AUTHOR>
 * @since 2024-09-27 10:10:55
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
@TableName(value = "pay_channel")
public class PayChannel implements Serializable {
    private static final long serialVersionUID = 372561075473649943L;
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 业务主键
     */
    @TableField(value = "channel_id")
    private String channelId;
    /**
     * 商户号关联id
     */
    @TableField(value = "pay_company_account_id")
    private String payCompanyAccountId;
    /**
     * 支付方式 1支付宝 2微信 3银行卡',
     */
    @TableField(value = "pay_type")
    private Integer payType;
    /**
     * 支付产品 1 app支付  2手机网站支付 3PC网站支付 4 扫码支付 5 支付宝小程序支付
     */
    @TableField(value = "pay_product")
    private Integer payProduct;
    /**
     * 渠道名称
     */
    @TableField(value = "channel_name")
    private String channelName;
    /**
     * 渠道商 1支付宝 2 连连 3 通联支付 4 泰山
     * @see com.pxb7.mall.trade.order.client.enums.pay.PayChannelEnum
     * @see com.pxb7.mall.trade.ass.client.dto.model.refund.PayChannelEnum
     */
    @TableField(value = "channel")
    private Integer channel;
    /**
     * 渠道备注
     */
    @TableField(value = "channel_remark")
    private String channelRemark;
    /**
     * 状态 1 已激活 0 已冻结
     */
    @TableField(value = "status")
    private Integer status;
    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    @TableField(value = "update_time", update="now()")
    private LocalDateTime updateTime;
    /**
     * 是否显示：0-不显示，1-显示
     */
    @TableField(value = "displayed")
    private Boolean displayed;
    /**
     * 适配客户端，客户端 1PC，2app，3wap 多个以，分割
     */
    @TableField(value = "adaptation_client")
    private String adaptationClient;

    /**
     * 跳转方式 1-拉起支付宝/微信原生app,2-跳转H5页面之后拉起原生app,3-iframe渲染出二维码-扫码,4-链接渲染成二维码-扫码,5-链接直接打开跳转-泰山h5
     * @see com.pxb7.mall.trade.order.client.enums.pay.PayMethodEnum
     */
    @TableField(value = "pay_method")
    private Integer payMethod;
}

