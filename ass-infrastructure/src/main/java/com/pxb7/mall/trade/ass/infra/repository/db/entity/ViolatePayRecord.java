package com.pxb7.mall.trade.ass.infra.repository.db.entity;
import java.io.Serializable;
import java.time.*;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import com.baomidou.mybatisplus.annotation.*;

/**
 * 违约支付单表(ViolatePayRecord)实体类
 *
 * <AUTHOR>
 * @since 2025-04-16 15:21:49
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "violate_pay_record")
public class ViolatePayRecord implements Serializable {
    private static final long serialVersionUID = 574597858156725620L;
     @TableField(value="id")
     private Integer  id;
     /**
     * 业务id
     */
     @TableId(value = "record_id", type = IdType.INPUT)
     private String  recordId;
     /**
     * 违约单id
     */
     @TableField(value = "violate_id")
     private String  violateId;
     /**
     * 被违约订单id
     */
     @TableField(value = "order_item_id")
     private String  orderItemId;
     /**
     * 用户id
     */
     @TableField(value = "user_id")
     private String  userId;
     /**
     * 1收违约金、2打守约金
     */
     @TableField(value = "type")
     private Integer  type;
     /**
     * 资金渠道:1在线支付、2钱包
     */
     @TableField(value = "channel")
     private Integer  channel;
     /**
     * 金额
     */
     @TableField(value = "amount")
     private Long  amount;
     /**
     * 外部流水号
     */
     @TableField(value = "out_trade_no")
     private String  outTradeNo;
     /**
     * 备注,若失败/取消需要备注
     */
     @TableField(value = "remark")
     private String  remark;
     /**
     * 1待处理、2处理中、3处理成功、4处理失败、5取消
     */
     @TableField(value = "status")
     private Integer  status;
     /**
     * 处理完成时间
     */
     @TableField(value = "completed_time")
     private LocalDateTime  completedTime;
     /**
     * 取消时间
     */
     @TableField(value = "cancel_time")
     private LocalDateTime  cancelTime;
     @TableField(value = "create_time")
     private LocalDateTime  createTime;
     @TableField(value = "update_time", update = "now()")
     private LocalDateTime  updateTime;
     /**
     * 制单客服id
     */
     @TableField(value = "create_user_id")
     private String  createUserId;
     /**
     * 制单客服
     */
     @TableField(value = "create_username")
     private String  createUsername;
     /**
     * 0正常,1已删除
     */
     @TableLogic
     @TableField(value = "is_deleted")
     private Integer  isDeleted;


}

