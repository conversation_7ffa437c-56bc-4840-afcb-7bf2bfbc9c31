package com.pxb7.mall.trade.ass.infra.repository.db;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.RefundVoucherDetail;

/**
 * 退款单金额详情(RefundVoucherDetail)表服务接口
 *
 * <AUTHOR>
 * @since 2024-09-25 15:02:33
 */
public interface RefundVoucherDetailRepository extends IService<RefundVoucherDetail> {
    RefundVoucherDetail getOneByRefundId(String refundId);

    boolean updateByRefundId(RefundVoucherDetail RefundVoucherDetail);

    List<RefundVoucherDetail> getByOrderItemId(String orderItemId);

    List<RefundVoucherDetail> getByRefundVoucherIds(List<String> refundVoucherIds);
}
