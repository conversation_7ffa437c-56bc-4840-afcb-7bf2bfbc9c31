package com.pxb7.mall.trade.ass.infra.enums.notis;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 功能描述:短信模版 
 * 作者：白春韬
 * 创建日期：2025/08/06 
 * 公司名称：金华博淳网络科技有限公司 
 * 域名： www.pxb7.com
 */
@Getter
@AllArgsConstructor
public enum SmsEnum {
    /**
     * 售后工单创建成功
     */
    SMS_AFTER_SALE_WORK_ORDER_CREATE(0, "售后", "SMS_491310438", "售后申请已受理", "您的订单${code}售后申请已受理，请登录螃蟹在“我的-售后”中查看进度"),
    /**
     * 跟进记录-追回账号，待买家换绑
     */
    SMS_AFTER_SALE_WORK_ORDER_TO_BIND(1, "售后", "SMS_491535450", "售后-追回账号", "您的订单${code}售后已为您找回账号，请登录螃蟹在“我的-售后”中处理"),
    /**
     * 跟进记录-追回号款，待买家提供收款账号
     */
    SMS_AFTER_SALE_WORK_ORDER_TO_PROVIDE_ACCOUNT(2, "售后", "SMS_491365440", "售后-追回号款", "您的订单${code}售后已为您追回损失，请登录螃蟹在“我的-售后”中处理"),
    /**
     * 跟进记录-售后到期，待买家接收赔付
     */
    SMS_AFTER_SALE_WORK_ORDER_EXPIRED(3, "售后", "SMS_491455432", "售后到期", "您的订单${code}售后已到期，请登录螃蟹在“我的-售后”中进群处理"),

    ;

    private final Integer code;
    private final String businessType;
    private final String smsCode;
    private final String desc;
    private final String messageTemplate;

    public static SmsEnum getSmsEnum(Integer code) {
        for (SmsEnum smsEnum : SmsEnum.values()) {
            if (smsEnum.getCode().equals(code)) {
                return smsEnum;
            }
        }
        return null;
    }
}
