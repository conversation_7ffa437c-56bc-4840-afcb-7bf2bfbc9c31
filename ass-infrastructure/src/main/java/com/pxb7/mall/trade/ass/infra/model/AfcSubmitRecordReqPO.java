package com.pxb7.mall.trade.ass.infra.model;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 售后提交记录(AfcSubmitRecord)实体类
 *
 * <AUTHOR>
 * @since 2025-04-11 13:54:04
 */
public class AfcSubmitRecordReqPO {

    @Data
    @Accessors(chain = true)
    public static class AddPO implements Serializable {

        private String submitRecordId;

        private String orderItemId;

        private String roomId;

        private String gameId;

        private String gameName;

        private String productId;

        private String productUniqueNo;

        private Integer afcType;

        private String questionId;

        private String remark;

        /**
         * 用户昵称
         */
        private String nickname;
        /**
         * 手机号
         */
        private String phone;

        private String createUserId;

        private String updateUserId;

    }

    @Data
    @Accessors(chain = true)
    public static class UpdatePO implements Serializable {

        private String submitRecordId;

        private String orderItemId;

        private String roomId;

        private String gameId;

        private String gameName;

        private String productId;

        private String productUniqueNo;

        private Integer afcType;

        private String questionId;

        private String remark;

        /**
         * 用户昵称
         */
        private String nickname;
        /**
         * 手机号
         */
        private String phone;

        private String updateUserId;

    }

    @Data
    @Accessors(chain = true)
    public static class DelPO implements Serializable {
        private String submitRecordId;
    }

    @Data
    @Accessors(chain = true)
    public static class SearchPO implements Serializable {

        private String submitRecordId;

        private String orderItemId;

        private String roomId;

        private String gameId;

        private String gameName;

        private String productId;

        private String productUniqueNo;

        private Integer afcType;

        private String questionId;

        private String remark;

        /**
         * 用户昵称
         */
        private String nickname;
        /**
         * 手机号
         */
        private String phone;

        private String createUserId;

        private String updateUserId;

    }

    @Data
    @Accessors(chain = true)
    public static class PagePO implements Serializable {

        private String submitRecordId;

        private String orderItemId;

        private String roomId;

        private String gameId;

        private String gameName;

        private String productId;

        private String productUniqueNo;

        private Integer afcType;

        private String questionId;

        private String remark;

        /**
         * 用户昵称
         */
        private String nickname;
        /**
         * 手机号
         */
        private String phone;

        private String createUserId;

        private String updateUserId;

        /**
         * 页码，从1开始
         */
        private Integer pageIndex;
        /**
         * 每页数量
         */
        private Integer pageSize;
    }
}

