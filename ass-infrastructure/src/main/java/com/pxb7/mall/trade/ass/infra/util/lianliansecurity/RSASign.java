package com.pxb7.mall.trade.ass.infra.util.lianliansecurity;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.bouncycastle.jce.provider.BouncyCastleProvider;

import java.nio.charset.StandardCharsets;
import java.security.*;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;

/**
 * RSA方式签名
 */
@Slf4j
public class RSASign {

    static {
        Security.addProvider(new BouncyCastleProvider());// 转换密钥格式
    }

    /**
     * 签名处理
     *
     * @param privateKey：私钥
     * @param sign_str：签名源内容
     * @return
     */
    public static String sign(String privateKey, String sign_str) {
        try {
            PKCS8EncodedKeySpec priPKCS8 = new PKCS8EncodedKeySpec(getBytesBASE64(privateKey));
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            PrivateKey myPrivateKey = keyFactory.generatePrivate(priPKCS8);
            // 用私钥对信息生成数字签名
            Signature signet = Signature.getInstance("MD5withRSA");
            signet.initSign(myPrivateKey);
            signet.update(sign_str.getBytes(StandardCharsets.UTF_8));
            byte[] signed = signet.sign(); // 对信息的数字签名
            return new String(Base64.encodeBase64(signed));
        } catch (Exception e) {
            log.error("签名失败", e);
            return null;
        }
    }

    /**
     * @param s
     * @return
     */
    public static byte[] getBytesBASE64(String s) {
        if (s == null) {
            return null;
        }
        return Base64.decodeBase64(s.getBytes(StandardCharsets.UTF_8));
    }

    /**
     * 签名验证
     *
     * @param publicKey：公钥
     * @param oid_str：源串
     * @param signed_str：签名结果串
     * @return
     */
    public static boolean checkSign(String publicKey, String oid_str, String signed_str) {
        try {
            X509EncodedKeySpec bobPubKeySpec = new X509EncodedKeySpec(getBytesBASE64(publicKey));
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            PublicKey pubKey = keyFactory.generatePublic(bobPubKeySpec);
            byte[] signed = getBytesBASE64(signed_str); // 这是SignatureData输出的数字签名
            Signature signature = Signature.getInstance("MD5withRSA");
            signature.initVerify(pubKey);
            signature.update(oid_str.getBytes(StandardCharsets.UTF_8));
            return signature.verify(signed);
        } catch (Exception e) {
            log.error("签名验证异常", e);
            return false;
        }
    }
}
