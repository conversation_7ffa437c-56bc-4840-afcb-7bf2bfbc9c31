package com.pxb7.mall.trade.ass.infra.config.nacos;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;

@Slf4j
@Component
@Data
public class ViolateRiskBlackConfig implements NacosYamlConfigListener {

    /**
     * riskblack.switch.add:false
     */
    private Boolean addRiskBlackSwitch = false;
    /**
     * riskblack.switch.remove:true
     */
    private Boolean removeRiskBlackSwitch = true;

    @Override
    public void onRefresh(Object newConfig) {

        if (newConfig instanceof Map) {
            Map<String, Object> configMap = (Map<String, Object>)newConfig;
            Object obj = configMap.get("switch");
            if (obj instanceof Map) {
                Map<String, Object> switchMap = (Map<String, Object>)obj;
                switchMap.forEach((k, v) -> {
                    switch (k) {
                        case "add":
                            if (v instanceof Boolean) {
                                addRiskBlackSwitch = (Boolean)v;
                            }
                            break;
                        case "remove":
                            if (v instanceof Boolean) {
                                removeRiskBlackSwitch = (Boolean)v;
                            }
                            break;
                    }
                });
            }
        }

    }

    @Override
    public String configPath() {
        return "riskblack";
    }
}
