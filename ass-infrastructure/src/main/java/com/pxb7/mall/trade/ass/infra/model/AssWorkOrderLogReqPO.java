package com.pxb7.mall.trade.ass.infra.model;

import java.util.Date;
import java.time.*;
import java.math.BigDecimal;
import java.util.List;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.ToString;


/**
 * 售后工单日志明细(AssWorkOrderLog)实体类
 *
 * <AUTHOR>
 * @since 2025-07-31 10:19:37
 */
public class AssWorkOrderLogReqPO {

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class AddPO {

        /**
         * 售后工单号关联ass_work_order.work_order_id
         */
        private String workOrderId;

        /**
         * 日志id
         */
        private String workOrderLogId;

        /**
         * 订单ID
         */
        private String orderItemId;

        /**
         * 标题
         */
        private String title;

        /**
         * 售后类型1找回2纠纷
         */
        private Integer assType;

        /**
         * 0:全展示 1:用户端展示 2:后台展示
         */
        private Integer showType;

        /**
         * 用户端展示操作内容
         */
        private String content;

        /**
         * admin展示操作内容
         */
        private String adminContent;

        /**
         * 日志添加方式1系统自动产生的日志 2手动添加的日志
         */
        private Integer addWay = 1;

        /**
         * 节点id
         */
        private String nodeId;

        /**
         * 当前节点状态描述
         */
        private String nodeDesc;

        /**
         * 创建人id
         */
        private String createUserId;

        /**
         * 更新人id
         */
        private String updateUserId;

        /**
         * 通知类型 0:不通知 1:追回账号,待卖家换绑2:追回号款,待卖家提供收款账号3:售后到期,待买家接受赔付
         */
        private Integer noticeType;

        /**
         * 进群提醒文案
         */
        private String joinGroupMsg;

    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class UpdatePO {

        /**
         * 自增id
         */
        private Long id;


        /**
         * 售后工单号关联ass_work_order.work_order_id
         */
        private String workOrderId;


        /**
         * 日志id
         */
        private String workOrderLogId;


        /**
         * 订单ID
         */
        private String orderItemId;


        /**
         * 标题
         */
        private String title;


        /**
         * 售后类型1找回2纠纷
         */
        private Integer assType;


        /**
         * 0:全展示 1:用户端展示 2:后台展示
         */
        private Integer showType;


        /**
         * 用户端展示操作内容
         */
        private String content;


        /**
         * admin展示操作内容
         */
        private String adminContent;


        /**
         * 日志添加方式1系统自动产生的日志 2手动添加的日志
         */
        private Integer addWay;


        /**
         * 节点id
         */
        private String nodeId;


        /**
         * 当前节点状态描述
         */
        private String nodeDesc;


        /**
         * 创建人id
         */
        private String createUserId;


        /**
         * 更新人id
         */
        private String updateUserId;

        /**
         * 通知类型 0:不通知 1:追回账号,待卖家换绑2:追回号款,待卖家提供收款账号3:售后到期,待买家接受赔付
         */
        private Integer noticeType;

        /**
         * 进群提醒文案
         */
        private String joinGroupMsg;


    }


    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class DelPO {
        private Long id;
    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class SearchPO {
        /**
         * 售后工单号关联ass_work_order.work_order_id
         */
        private String workOrderId;

        /**
         * 日志id
         */
        private String workOrderLogId;

        /**
         * 订单ID
         */
        private String orderItemId;

        /**
         * 标题
         */
        private String title;

        /**
         * 售后类型1找回2纠纷
         */
        private Integer assType;

        /**
         * 0:全展示 1:用户端展示 2:后台展示
         */
        private Integer showType;

        /**
         * 用户端展示操作内容
         */
        private String content;

        /**
         * admin展示操作内容
         */
        private String adminContent;

        /**
         * 日志添加方式1系统自动产生的日志 2手动添加的日志
         */
        private Integer addWay;

        /**
         * 节点id
         */
        private String nodeId;

        /**
         * 当前节点状态描述
         */
        private String nodeDesc;

        /**
         * 创建人id
         */
        private String createUserId;

        /**
         * 更新人id
         */
        private String updateUserId;

        /**
         * 通知类型 0:不通知 1:追回账号,待卖家换绑2:追回号款,待卖家提供收款账号3:售后到期,待买家接受赔付
         */
        private Integer noticeType;

        /**
         * 进群提醒文案
         */
        private String joinGroupMsg;

        /**
         * 列表查询
         * 0:全展示 1:用户端展示 2:后台展示
         */
        private List<Integer> showTypeList;

    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class PagePO {

        /**
         * 售后工单号关联ass_work_order.work_order_id
         */
        private String workOrderId;

        /**
         * 日志id
         */
        private String workOrderLogId;

        /**
         * 订单ID
         */
        private String orderItemId;

        /**
         * 标题
         */
        private String title;

        /**
         * 售后类型1找回2纠纷
         */
        private Integer assType;

        /**
         * 0:全展示 1:用户端展示 2:后台展示
         */
        private Integer showType;

        /**
         * 用户端展示操作内容
         */
        private String content;

        /**
         * admin展示操作内容
         */
        private String adminContent;

        /**
         * 日志添加方式1系统自动产生的日志 2手动添加的日志
         */
        private Integer addWay;

        /**
         * 节点id
         */
        private String nodeId;

        /**
         * 当前节点状态描述
         */
        private String nodeDesc;

        /**
         * 创建人id
         */
        private String createUserId;

        /**
         * 更新人id
         */
        private String updateUserId;


        /**
         * 页码，从1开始
         */
        private Integer pageIndex;
        /**
         * 每页数量
         */
        private Integer pageSize;


        /**
         * 通知类型 0:不通知 1:追回账号,待卖家换绑2:追回号款,待卖家提供收款账号3:售后到期,待买家接受赔付
         */
        private Integer noticeType;

        /**
         * 进群提醒文案
         */
        private String joinGroupMsg;

    }

}

