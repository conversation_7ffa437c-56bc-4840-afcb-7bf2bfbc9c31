package com.pxb7.mall.trade.ass.infra.repository.db;

import java.util.List;
import java.util.Set;

import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pxb7.mall.trade.ass.infra.model.AssRetrieveGameConfigDetailReqPO;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.AssRetrieveGameConfigDetail;

/**
 * 售后找回游戏配置明细表(AssRetrieveGameConfigDetail)表服务接口
 *
 * <AUTHOR>
 * @since 2025-07-30 13:45:38
 */
public interface AssRetrieveGameConfigDetailRepository extends IService<AssRetrieveGameConfigDetail> {

    boolean insert(AssRetrieveGameConfigDetailReqPO.AddPO param);

    boolean update(AssRetrieveGameConfigDetailReqPO.UpdatePO param);

    boolean deleteById(AssRetrieveGameConfigDetailReqPO.DelPO param);

    AssRetrieveGameConfigDetail findById(Long id);

    List<AssRetrieveGameConfigDetail> list(AssRetrieveGameConfigDetailReqPO.SearchPO param);

    Page<AssRetrieveGameConfigDetail> page(AssRetrieveGameConfigDetailReqPO.PagePO param);


    List<AssRetrieveGameConfigDetail> getListBy(Set<String> gameIdSet);


}

