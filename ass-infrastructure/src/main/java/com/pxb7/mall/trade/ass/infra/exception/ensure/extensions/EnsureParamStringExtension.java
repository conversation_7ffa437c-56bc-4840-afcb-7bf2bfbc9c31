package com.pxb7.mall.trade.ass.infra.exception.ensure.extensions;

import com.pxb7.mall.trade.ass.infra.enums.ErrorCode;
import com.pxb7.mall.trade.ass.infra.exception.ExceptionFactory;

import org.apache.commons.lang3.StringUtils;

/**
 * 断言工具类
 *
 * <AUTHOR>
 * @date 2024/08/07 15:12
 **/
public class EnsureParamStringExtension extends EnsureParamObjectExtension<String> {
    private final String string;

    public EnsureParamStringExtension(String string) {
        super(string);
        this.string = string;
    }

    public EnsureParamStringExtension isNotBlank(ErrorCode errorCode) {
        if (StringUtils.isBlank(this.string)) {
            throw ExceptionFactory.create(errorCode.getErrCode(), errorCode.getErrDesc());
        } else {
            return this;
        }
    }

    public EnsureParamStringExtension isBlank(ErrorCode errorCode) {
        if (StringUtils.isNotBlank(this.string)) {
            throw ExceptionFactory.create(errorCode.getErrCode(), errorCode.getErrDesc());
        } else {
            return this;
        }
    }
}
