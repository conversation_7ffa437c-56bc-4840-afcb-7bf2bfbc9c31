package com.pxb7.mall.trade.ass.infra.constant;

/**
 * <AUTHOR>
 * @date 2024/9/27 12:56 PM
 */
public class RMQConstant {
    /**
     * 退款查询 延时消息 topic
     */
    public static final String REFUND_QUERY_DELAY_GROUP = "ass_refund_query_delay_group";
    public static final String REFUND_QUERY_DELAY_TOPIC = "ass_refund_query_delay_topic";
    public static final String REFUND_QUERY_DELAY_TAG = "ass_refund_query";

    /**
     * 退款成功/失败消息 topic
     */
    public static final String ASS_ORIGINAL_REFUND_GROUP = "ass_original_refund_group";
    public static final String ASS_REFUND_CENTER_TOPIC = "ass_refund_center_topic";
    public static final String ASS_REFUND_CALLBACK_TAG = "ass_refund_callback";
    public static final String PROMISE_REFUND_CALLBACK_TAG = "promise_refund_callback";
    //原路退
    public static final String ASS_ORIGINAL_REFUND_TAG = "ass_original_refund";
    //部分退
    public static final String ASS_REFUND_PARTLY_GROUP = "ass_refund_partly_group";
    public static final String ASS_REFUND_PARTLY_TAG = "ass_refund_partly";
    //延迟原路退
    public static final String ASS_REFUND_CENTER_DELAY_TOPIC = "ass_refund_center_delay_topic";
    public static final String ASS_ORIGINAL_REFUND_DELAY_GROUP = "ass_original_refund_delay_group";
    //退款三方回调
    public static final String REFUND_CALLBACK_GROUP = "ass_refund_callback_group";
    public static final String REFUND_CALLBACK_TAG = "ass_refund_callback_tag";

    /**
     * 违约金 topic
     */
    public static final String VIOLATE_TOPIC = "violate_topic";
    public static final String VIOLATE_PAY_RECORD_TOPIC = "violate_pay_record_topic";
    // 违约金延迟队列topic
    public static final String VIOLATE_RECEIPT_DELAY_TOPIC = "violate_receipt_delay_topic";
    // 发起退款-创建违约单
    public static final String VIOLATE_CREATE_TAG = "violate_create_tag";
    public static final String VIOLATE_CREATE_GROUP = "violate_create_group";
    // 订单退款完成-处理违约单
    public static final String VIOLATE_DEAL_GROUP = "violate_deal_group";
    // 发起处理违约打款
    public static final String VIOLATE_TRANSFER_TAG = "violate_transfer_tag";
    public static final String VIOLATE_TRANSFER_GROUP = "violate_transfer_group";
    // 发起处理违约扣款
    public static final String VIOLATE_DEDUCTION_TAG = "violate_deduction_tag";
    public static final String VIOLATE_DEDUCTION_GROUP = "violate_deduction_group";
    // 发起处理违约收款
    public static final String VIOLATE_RECEIPT_TAG = "violate_receipt_tag";

    // 发起处理违约收款中断 24h
    public static final String VIOLATE_RECEIPT_STOP_TAG = "violate_stop_tag";
    public static final String VIOLATE_RECEIPT_STOP_GROUP = "violate_stop_group";

    // 违约金-钱包
    // 钱包违约打款结果回调
    public static final String VIOLATE_ACCOUNT_GROUP = "violate_account_group";

    // 违约金-拉黑/拉白
    public static final String VIOLATE_RISK_BLACK_TAG = "violate_risk_black_tag";
    public static final String VIOLATE_RISK_BLACK_GROUP = "violate_risk_black_group";

    /**
     * 售后工单状态变更
     */
    public static final String AFC_WORK_ORDER_STATUS_CHANGE_TOPIC = "T_afc_work_order_status_change";

    /**
     * 找回工单
     */
    public static final String AFC_RETRIEVE_WORK_ORDER_STATUS_CHANGE_TAG = "retrieve";

    /**
     * 纠纷工单
     */
    public static final String AFC_DISPUTE_WORK_ORDER_STATUS_CHANGE_TAG = "dispute";
    /**
     * 投诉工单
     */
    public static final String AFC_COMPLAINT_WORK_ORDER_STATUS_CHANGE_TAG = "complaint";

    // 违约金-取消
    public static final String VIOLATE_CANCEL_GROUP = "violate_cancel_group";


    // 订单单据状态变更 topic
    public static final String ORDER_VOUCHER_STATUS_CHANGE_TOPIC = "order_voucher_status_change_topic";
}
