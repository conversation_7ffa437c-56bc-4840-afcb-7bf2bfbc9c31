package com.pxb7.mall.trade.ass.infra.repository.db;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.CommandDO;
import com.pxb7.mall.trade.ass.infra.repository.db.mapper.CommandMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

@Service
public class CommandRepositoryImpl extends ServiceImpl<CommandMapper, CommandDO> implements CommandRepository {

    @Resource
    private CommandMapper commandMapper;

    @Override
    public Long queryCount(int commandStatus, String begin, String end, List<String> typeList, String env) {
        return lambdaQuery()
                .eq(CommandDO::getCommandStatus, commandStatus)
                .ge(StrUtil.isNotBlank(begin), CommandDO::getCreateTime, begin)
                .le(StrUtil.isNotBlank(end), CommandDO::getCreateTime, end)
                .in(CollUtil.isNotEmpty(typeList), CommandDO::getCommandType, typeList)
                .and(StrUtil.isNotBlank(env), q -> q.eq(CommandDO::getEnv, env).or().eq(CommandDO::getEnv, ""))
                .and(q -> q.lt(CommandDO::getExecuteTime, LocalDateTime.now()).or().isNull(CommandDO::getExecuteTime))
                .count();
    }

    @Override
    public List<CommandDO> listCommands(Long startId, int commandStatus, String begin, String end, Integer limit, List<String> typeList, String env) {
        return lambdaQuery()
                .gt(CommandDO::getId, startId)
                .eq(CommandDO::getCommandStatus, commandStatus)
                .ge(StrUtil.isNotBlank(begin), CommandDO::getCreateTime, begin)
                .le(StrUtil.isNotBlank(end), CommandDO::getCreateTime, end)
                .in(CollUtil.isNotEmpty(typeList), CommandDO::getCommandType, typeList)
                .and(StrUtil.isNotBlank(env), q -> q.eq(CommandDO::getEnv, env).or().eq(CommandDO::getEnv, ""))
                .and(q -> q.lt(CommandDO::getExecuteTime, LocalDateTime.now()).or().isNull(CommandDO::getExecuteTime))
                .orderByAsc(CommandDO::getId)
                .last(String.format("limit %d", limit))
                .list();
    }

    @Override
    public Long deleteCommands(int commandStatus, String endTime) {
        return commandMapper.deleteCommands(commandStatus, endTime);
    }

    @Override
    public Long deleteByCommandId(String commandId) {
        return commandMapper.deleteByCommandId(commandId);
    }
}
