package com.pxb7.mall.trade.ass.infra.remote.dubbo;

import cn.hutool.json.JSONUtil;
import com.alibaba.cola.dto.SingleResponse;
import com.google.common.collect.Maps;
import com.pxb7.mall.common.client.api.message.WechatMessageServiceI;
import com.pxb7.mall.common.client.request.message.WechatMessageTemplateSendReqDTO;
import com.pxb7.mall.trade.ass.infra.enums.notis.WechatEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Objects;

/**
 * 功能描述:微信消息推送
 * 作者：白春韬
 * 创建日期：2025/08/05
 * 公司名称：金华博淳网络科技有限公司
 * 域名： www.pxb7.com
 */
@Service
@Slf4j
public class WechatMessageGateway {

    @DubboReference(providedBy = "common-support")
    private WechatMessageServiceI wechatMessageServiceI;

    /**
     * 发微信消息
     */
    public void sendWechatMessage(String businessId, String userId, WechatEnum wechatEnum) {
        if (StringUtils.isEmpty(userId)) {
            return;
        }
        WechatMessageTemplateSendReqDTO reqDTO = new WechatMessageTemplateSendReqDTO();
        reqDTO.setBusinessId(businessId);
        reqDTO.setUserId(userId);
        reqDTO.setTemplateName(wechatEnum.getWechatTemplateName());
        Map<String, String> params = Maps.newHashMap();
        params.put("character_string1", businessId);
        params.put("const7", wechatEnum.getWechatTemplateRemark());
        reqDTO.setParams(JSONUtil.toJsonStr(params));
        try {
            SingleResponse<Void> response = wechatMessageServiceI.sendWechatMessageTemplate(reqDTO);
            if (Objects.isNull(response) || !response.isSuccess()) {
                log.info("WechatMessageGateway.setWechatMessage failed, param---{}, response---{}", reqDTO, response);
            }
        } catch (Exception e) {
            log.error("WechatMessageGateway.setWechatMessage error, param---{}", reqDTO, e);
        }
    }
}
