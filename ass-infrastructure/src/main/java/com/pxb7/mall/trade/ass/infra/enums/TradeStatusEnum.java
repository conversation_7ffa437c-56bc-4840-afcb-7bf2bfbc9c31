package com.pxb7.mall.trade.ass.infra.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum TradeStatusEnum {

    /**
     * 待执行
     */
    UNEXECUTED(1, "待执行"),

    /**
     * 执行中
     */
    EXECUTING(2, "执行中"),

    /**
     * 执行成功
     */
    SUCCESS(3, "执行成功"),

    /**
     * 执行失败
     */
    FAIL(4, "执行失败"),

    /**
     * 取消执行
     */
    CANCEL(5, "取消执行"),
    ;

    private final Integer value;

    private final String label;

    public static String getLabel(Integer value) {
        return Arrays.stream(PaymentTypeEnum.values()).filter(e -> e.getValue().equals(value))
            .map(PaymentTypeEnum::getLabel).findAny().get();
    }

}
