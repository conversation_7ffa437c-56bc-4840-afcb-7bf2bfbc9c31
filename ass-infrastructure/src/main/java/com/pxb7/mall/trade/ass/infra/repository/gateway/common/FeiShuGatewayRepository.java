package com.pxb7.mall.trade.ass.infra.repository.gateway.common;

import com.pxb7.mall.common.client.api.message.FeishuService;
import com.pxb7.mall.common.client.request.message.BotMessageRichTextContentReqDTO;
import com.pxb7.mall.common.client.request.message.BotMessageRichTextReqDTO;
import com.pxb7.mall.common.client.request.message.BotMessageTextReqDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;

import static org.apache.dubbo.common.constants.ClusterRules.FAIL_SAFE;

@Slf4j
@Service
public class FeiShuGatewayRepository {

    @DubboReference(timeout = 2000, cluster = FAIL_SAFE)
    private FeishuService feishuServiceSafe;

    /**
     * 支付接口预警机器人
     */
    @Value("${feishu.payBot.hook.url}")
    private String hookUrl;
    @Value("${feishu.payBot.hook.secret.key}")
    private String secretKey;
    @Value("${spring.profiles.active}")
    private String profile;

    /**
     * 发送普通文本消息
     *
     * @param template 消息模板，参考：FeiShuWarningConstants
     */
    public void sendTextMessage(String template, String... values) {
        sendTextMessage(String.format(template, (Object[]) values));
    }

    /**
     * 发送普通文本消息
     */
    public void sendTextMessage(String message) {
        BotMessageTextReqDTO reqDTO = new BotMessageTextReqDTO();
        reqDTO.setSecret(secretKey);
        reqDTO.setWebhook(hookUrl);
        reqDTO.setText("【" + profile + "】" + message);
        feishuServiceSafe.botTextMessage(reqDTO);
    }

    /**
     * 发送富文本消息
     */
    public void sendRichTextMessage(String title, List<List<BotMessageRichTextContentReqDTO>> content) {
        BotMessageRichTextReqDTO reqDTO = new BotMessageRichTextReqDTO();
        reqDTO.setSecret(secretKey);
        reqDTO.setWebhook(hookUrl);
        reqDTO.setTitle("【" + profile + "】" + title);
        reqDTO.setContent(content);
        feishuServiceSafe.botRichTextMessage(reqDTO);
    }

}
