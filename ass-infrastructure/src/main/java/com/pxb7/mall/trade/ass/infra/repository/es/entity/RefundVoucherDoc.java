package com.pxb7.mall.trade.ass.infra.repository.es.entity;

import java.time.LocalDateTime;

import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.*;

import lombok.Data;

@Data
@Document(indexName = "refund_voucher_doc", dynamic = Dynamic.STRICT)
@Setting(shards = 3)
public class RefundVoucherDoc {

    /**
     * 主键
     */
    @Field(value = "id", type = FieldType.Long)
    private Long id;
    /**
     * 订单行id
     */
    @Field(value = "order_item_id", type = FieldType.Keyword)
    private String orderItemId;
    /**
     * 退款单id
     */
    @Id
    @Field(value = "refund_voucher_id", type = FieldType.Keyword)
    private String refundVoucherId;
    /**
     * 商品类型: 1账号 2充值 3金币 4装备 5初始号 20诚心卖曝光劵 21诚心卖服务
     */
    @Field(value = "product_type", type = FieldType.Integer)
    private Integer productType;
    /**
     * 退款状态 1待审核, 2退款中, 3退款成功, 4退款失败, 5退款关闭
     */
    @Field(value = "refund_status", type = FieldType.Integer)
    private Integer refundStatus;
    /**
     * 退款原因id
     */
    @Field(value = "refund_reason_id", type = FieldType.Keyword)
    private String refundReasonId;
    /**
     * 退款原因
     */
    @Field(value = "refund_reason", type = FieldType.Keyword)
    private String refundReason;
    /**
     * 退款类型 1 整单退款 2.退商品差价
     */
    @Field(value = "is_whole_refund", type = FieldType.Integer)
    private Integer wholeRefund;
    /**
     * 退款类型 1线上原路返回 2线下打款
     */
    @Field(value = "refund_type", type = FieldType.Integer)
    private Integer refundType;
    /**
     * 使用的对应收款单id,线上退款时绑定
     */
    @Field(value = "receipt_voucher_id", type = FieldType.Keyword)
    private String receiptVoucherId;
    /**
     * 对应支付单id，审核通过后绑定
     */
    @Field(value = "payment_id", type = FieldType.Keyword)
    private String paymentId;
    /**
     * 退款金额
     */
    @Field(value = "refund_amount", type = FieldType.Long)
    private Long refundAmount;
    /**
     * 实际退款金额，退款完成后写入
     */
    @Field(value = "actual_refund_amount", type = FieldType.Long)
    private Long actualRefundAmount;
    /**
     * 提交人
     */
    @Field(value = "submitter", type = FieldType.Keyword)
    private String submitter;
    /**
     * 1-用户,2-客服
     */
    @Field(value = "submitter_type", type = FieldType.Integer)
    private Integer submitterType;
    /**
     * 1-用户,2-客服,3-财务
     */
    @Field(value = "close_type", type = FieldType.Integer)
    private Integer closeType;
    /**
     * 客服拒绝原因
     */
    @Field(value = "reject_reason", type = FieldType.Keyword)
    private String rejectReason;
    /**
     * 申请时间
     */
    @Field(value = "apply_time", type = FieldType.Date, format = DateFormat.date_hour_minute_second_fraction)
    private LocalDateTime applyTime;
    /**
     * 退款完成时间
     */
    @Field(value = "finish_time", type = FieldType.Date, format = DateFormat.date_hour_minute_second_fraction)
    private LocalDateTime finishTime;
    /**
     * 1支付宝 2微信 3银行卡
     */
    @Field(value = "refund_channel", type = FieldType.Integer)
    private Integer refundChannel;
    /**
     * 买家收款账户
     */
    @Field(value = "refund_account", type = FieldType.Keyword)
    private String refundAccount;
    /**
     * 买家姓名
     */
    @Field(value = "buyer_name", type = FieldType.Keyword)
    private String buyerName;
    /**
     * 审核状态 1待客服审核,2待提交打款信息(线下打款才有),3待财务审核,4审核成功,5审核失败
     */
    @Field(value = "audit_status", type = FieldType.Integer)
    private Integer auditStatus;
    /**
     * 财务审核人用户id
     */
    @Field(value = "audit_user", type = FieldType.Keyword)
    private String auditUser;
    /**
     * 财务审核人审核备注
     */
    @Field(value = "audit_remark", type = FieldType.Keyword)
    private String auditRemark;
    /**
     * 财务审核截图
     */
    @Field(value = "audit_img", type = FieldType.Keyword)
    private String auditImg;
    /**
     * 打款人用户id
     */
    @Field(value = "execute_user", type = FieldType.Keyword)
    private String executeUser;
    /**
     * 打款账号名称
     */
    @Field(value = "account_name", type = FieldType.Keyword)
    private String accountName;
    /**
     * 打款账号
     */
    @Field(value = "company_account", type = FieldType.Keyword)
    private String companyAccount;
    /**
     * 扩展信息
     */
    @Field(value = "ext_info", type = FieldType.Keyword)
    private String extInfo;
    /**
     * 创建人id
     */
    @Field(value = "create_user_id", type = FieldType.Keyword)
    private String createUserId;
    /**
     * 更新人id
     */
    @Field(value = "update_user_id", type = FieldType.Keyword)
    private String updateUserId;
    /**
     * 创建时间
     */
    @Field(value = "create_time", type = FieldType.Date, format = DateFormat.date_hour_minute_second_fraction)
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    @Field(value = "update_time", type = FieldType.Date, format = DateFormat.date_hour_minute_second_fraction)
    private LocalDateTime updateTime;
    /**
     * 是否删除
     */
    @Field(value = "is_deleted", type = FieldType.Boolean)
    private Boolean deleted;

    // 以下字段来自order_item_extend
    /**
     * 商品编号
     */
    @Field(name = "product_unique_no", type = FieldType.Keyword)
    private String productUniqueNo;
    /**
     * 买家手机号
     */
    @Field(name = "buyer_phone", type = FieldType.Keyword)
    private String buyerPhone;
    /**
     * 卖家手机号
     */
    @Field(name = "seller_phone", type = FieldType.Keyword)
    private String sellerPhone;
    /**
     * 商品名称
     */
    @Field(value = "product_name", type = FieldType.Keyword)
    private String productName;

    // 以下字段来自order_item
    /**
     * 游戏
     */
    @Field(name = "game_id", type = FieldType.Keyword)
    private String gameId;
    /**
     * 商品id
     */
    @Field(name = "product_id", type = FieldType.Keyword)
    private String productId;

    /**
     * 买家id
     */
    @Field(name = "buyer_id", type = FieldType.Keyword)
    private String buyerId;

    /**
     * 卖家id
     */
    @Field(name = "seller_id", type = FieldType.Keyword)
    private String sellerId;

    /**
     * 订单时间
     */
    @Field(value = "order_create_time", type = FieldType.Date, format = DateFormat.date_hour_minute_second_fraction)
    private LocalDateTime orderCreateTime;

    // 以下字段来自main_order
    /**
     * 订单来源，1:主站；2:im创建；3:闲鱼；4:支付宝；5主站IM线下
     */
    @Field(name = "order_source", type = FieldType.Integer)
    private Integer orderSource;
    /**
     * 交易模式 1代售 2中介 3诚心卖 4金币回收 5金币出售，6充值
     */
    @Field(name = "service_mode", type = FieldType.Integer)
    private Integer serviceMode;

}
