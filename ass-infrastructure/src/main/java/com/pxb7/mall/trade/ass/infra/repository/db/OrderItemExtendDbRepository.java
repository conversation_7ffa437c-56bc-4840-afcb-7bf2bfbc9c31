package com.pxb7.mall.trade.ass.infra.repository.db;

import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.OrderItemExtend;
import com.pxb7.mall.trade.ass.infra.repository.db.mapper.OrderItemExtendMapper;

import lombok.extern.slf4j.Slf4j;

/**
 * 订单行扩展(OrderItemExtend)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-07-22 20:59:36
 */
@Slf4j
@Repository
public class OrderItemExtendDbRepository extends ServiceImpl<OrderItemExtendMapper, OrderItemExtend>
    implements OrderItemExtendRepository {
    @Override
    public OrderItemExtend getOneByItemId(String orderItemId) {
        return this.lambdaQuery().eq(OrderItemExtend::getOrderItemId, orderItemId).one();
    }
}
