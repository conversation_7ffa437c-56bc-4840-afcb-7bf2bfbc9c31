package com.pxb7.mall.trade.ass.infra.repository.db;


import java.util.List;
import java.util.Objects;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.pxb7.mall.trade.ass.infra.model.AssFollowupConfigReqPO;
import com.pxb7.mall.trade.ass.infra.repository.db.mapper.AssFollowupConfigMapper;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.AssFollowupConfig;
import com.pxb7.mall.trade.ass.infra.repository.db.AssFollowupConfigRepository;

/**
 * 跟进结果类型配置表(AssFollowupConfig)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-30 11:51:58
 */
@Slf4j
@Repository
public class AssFollowupConfigDbRepository extends ServiceImpl<AssFollowupConfigMapper, AssFollowupConfig> implements AssFollowupConfigRepository {

    @Override
    public boolean insert(AssFollowupConfigReqPO.AddPO param) {
        AssFollowupConfig entity = new AssFollowupConfig();
        entity.setFollowupConfigId(param.getFollowupConfigId());
        entity.setAssType(param.getAssType());
        entity.setResultDesc(param.getResultDesc());
        entity.setProgressDesc(param.getProgressDesc());
        entity.setJoinGroupDesc(param.getJoinGroupDesc());
        entity.setNoticeType(param.getNoticeType());
        entity.setSort(param.getSort());
        entity.setCreateUserId(param.getCreateUserId());
        entity.setUpdateUserId(param.getUpdateUserId());
        return this.save(entity);
    }

    @Override
    public boolean update(AssFollowupConfigReqPO.UpdatePO param) {
        LambdaUpdateWrapper<AssFollowupConfig> updateWrapper = new LambdaUpdateWrapper<>();
        //where
        updateWrapper.eq(AssFollowupConfig::getId, param.getId());
        //set
        if (StringUtils.isNotBlank(param.getFollowupConfigId())) {
            updateWrapper.set(AssFollowupConfig::getFollowupConfigId, param.getFollowupConfigId());
        }
        if (Objects.nonNull(param.getAssType())) {
            updateWrapper.set(AssFollowupConfig::getAssType, param.getAssType());
        }
        if (StringUtils.isNotBlank(param.getResultDesc())) {
            updateWrapper.set(AssFollowupConfig::getResultDesc, param.getResultDesc());
        }
        if (StringUtils.isNotBlank(param.getProgressDesc())) {
            updateWrapper.set(AssFollowupConfig::getProgressDesc, param.getProgressDesc());
        }
        if (StringUtils.isNotBlank(param.getJoinGroupDesc())) {
            updateWrapper.set(AssFollowupConfig::getJoinGroupDesc, param.getJoinGroupDesc());
        }
        if (Objects.nonNull(param.getNoticeType())) {
            updateWrapper.set(AssFollowupConfig::getNoticeType, param.getNoticeType());
        }
        if (Objects.nonNull(param.getSort())) {
            updateWrapper.set(AssFollowupConfig::getSort, param.getSort());
        }
        if (StringUtils.isNotBlank(param.getCreateUserId())) {
            updateWrapper.set(AssFollowupConfig::getCreateUserId, param.getCreateUserId());
        }
        if (StringUtils.isNotBlank(param.getUpdateUserId())) {
            updateWrapper.set(AssFollowupConfig::getUpdateUserId, param.getUpdateUserId());
        }
        return this.update(updateWrapper);
    }

    @Override
    public boolean deleteById(AssFollowupConfigReqPO.DelPO param) {
        return this.removeById(param.getId());
    }

    @Override
    public AssFollowupConfig findById(Long id) {
        return this.getById(id);
    }

    @Override
    public List<AssFollowupConfig> list(AssFollowupConfigReqPO.SearchPO param) {
        LambdaQueryWrapper<AssFollowupConfig> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(param.getFollowupConfigId())) {
            queryWrapper.eq(AssFollowupConfig::getFollowupConfigId, param.getFollowupConfigId());
        }
        if (Objects.nonNull(param.getAssType())) {
            queryWrapper.eq(AssFollowupConfig::getAssType, param.getAssType());
        }
        if (StringUtils.isNotBlank(param.getResultDesc())) {
            queryWrapper.eq(AssFollowupConfig::getResultDesc, param.getResultDesc());
        }
        if (StringUtils.isNotBlank(param.getProgressDesc())) {
            queryWrapper.eq(AssFollowupConfig::getProgressDesc, param.getProgressDesc());
        }
        if (StringUtils.isNotBlank(param.getJoinGroupDesc())) {
            queryWrapper.eq(AssFollowupConfig::getJoinGroupDesc, param.getJoinGroupDesc());
        }
        if (Objects.nonNull(param.getNoticeType())) {
            queryWrapper.eq(AssFollowupConfig::getNoticeType, param.getNoticeType());
        }
        if (Objects.nonNull(param.getSort())) {
            queryWrapper.eq(AssFollowupConfig::getSort, param.getSort());
        }
        if (StringUtils.isNotBlank(param.getCreateUserId())) {
            queryWrapper.eq(AssFollowupConfig::getCreateUserId, param.getCreateUserId());
        }
        if (StringUtils.isNotBlank(param.getUpdateUserId())) {
            queryWrapper.eq(AssFollowupConfig::getUpdateUserId, param.getUpdateUserId());
        }
        return this.list(queryWrapper);
    }

    @Override
    public Page<AssFollowupConfig> page(AssFollowupConfigReqPO.PagePO param) {
        LambdaQueryWrapper<AssFollowupConfig> queryWrapper = new LambdaQueryWrapper<>();
        // 返回分页查询结果
        if (StringUtils.isNotBlank(param.getFollowupConfigId())) {
            queryWrapper.eq(AssFollowupConfig::getFollowupConfigId, param.getFollowupConfigId());
        }
        if (Objects.nonNull(param.getAssType())) {
            queryWrapper.eq(AssFollowupConfig::getAssType, param.getAssType());
        }
        if (StringUtils.isNotBlank(param.getResultDesc())) {
            queryWrapper.eq(AssFollowupConfig::getResultDesc, param.getResultDesc());
        }
        if (StringUtils.isNotBlank(param.getProgressDesc())) {
            queryWrapper.eq(AssFollowupConfig::getProgressDesc, param.getProgressDesc());
        }
        if (StringUtils.isNotBlank(param.getJoinGroupDesc())) {
            queryWrapper.eq(AssFollowupConfig::getJoinGroupDesc, param.getJoinGroupDesc());
        }
        if (Objects.nonNull(param.getNoticeType())) {
            queryWrapper.eq(AssFollowupConfig::getNoticeType, param.getNoticeType());
        }
        if (Objects.nonNull(param.getSort())) {
            queryWrapper.eq(AssFollowupConfig::getSort, param.getSort());
        }
        if (StringUtils.isNotBlank(param.getCreateUserId())) {
            queryWrapper.eq(AssFollowupConfig::getCreateUserId, param.getCreateUserId());
        }
        if (StringUtils.isNotBlank(param.getUpdateUserId())) {
            queryWrapper.eq(AssFollowupConfig::getUpdateUserId, param.getUpdateUserId());
        }
        return this.page(new Page<>(param.getPageIndex(), param.getPageSize()), queryWrapper);
    }
}
