package com.pxb7.mall.trade.ass.infra.repository.db;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.ComplaintWork;
import com.pxb7.mall.trade.ass.infra.repository.db.mapper.ComplaintWorkMapper;

import org.springframework.stereotype.Repository;

import java.util.List;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 客诉工单(ComplaintWork)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-09-20 17:55:44
 */
@Slf4j
@Repository
public class ComplaintWorkDbRepository extends ServiceImpl<ComplaintWorkMapper, ComplaintWork> implements ComplaintWorkRepository {

    @Override
    public boolean finishWorkOrder(ComplaintWork complaintWork) {
        return this.lambdaUpdate()
            .eq(ComplaintWork::getOrderItemId, complaintWork.getOrderItemId())
            .eq(ComplaintWork::getComplaintWorkId, complaintWork.getComplaintWorkId())
            .set(ComplaintWork::getWorkOrderStatus, complaintWork.getWorkOrderStatus())
            .set(ComplaintWork::getDealResult, complaintWork.getDealResult())
            .set(ComplaintWork::getNote, StrUtil.isNotBlank(complaintWork.getNote()) ? complaintWork.getNote() : "")
            .set(ComplaintWork::getDepartmentName, complaintWork.getDepartmentName())
            .set(ComplaintWork::getQuestionType, complaintWork.getQuestionType())
            .set(ComplaintWork::getQuestionLevel, complaintWork.getQuestionLevel())
            .set(ComplaintWork::getResponsiblePerson, complaintWork.getResponsiblePerson())
            .set(ComplaintWork::getResponsibility, complaintWork.getResponsibility())
            .set(ComplaintWork::getFinisherId, complaintWork.getFinisherId())
            .set(ComplaintWork::getFinishTime, complaintWork.getFinishTime())
            .set(ComplaintWork::getUpdateUserId, complaintWork.getUpdateUserId())
            .update();
    }

    @Override
    public boolean transferWorkOrder(ComplaintWork complaintWork) {
        return this.lambdaUpdate()
            .eq(ComplaintWork::getOrderItemId, complaintWork.getOrderItemId())
            .eq(ComplaintWork::getComplaintWorkId, complaintWork.getComplaintWorkId())
            .set(ComplaintWork::getTransfereeId, complaintWork.getTransfereeId())
            .set(ComplaintWork::getCurrentProcessorId, complaintWork.getCurrentProcessorId())
            .update();
    }

    @Override
    public ComplaintWork queryById(String complaintWorkId) {
        return this.lambdaQuery()
            .eq(ComplaintWork::getComplaintWorkId, complaintWorkId)
            .eq(ComplaintWork::getDeleted, false)
            .last("limit 1")
            .one();
    }

    @Override
    public List<ComplaintWork> listByUserId(String customerId, String userId) {

        return this.lambdaQuery()
                .eq(ComplaintWork::getUserId, userId)
                .orderByAsc(ComplaintWork::getWorkOrderStatus)
                .orderByDesc(ComplaintWork::getCreateTime)
                .list();
/*                    .or(item -> item.eq(ComplaintWork::getTransfereeId, customerId)
                .or()
                .eq(ComplaintWork::getCurrentProcessorId, customerId)
                .or()
                .eq(ComplaintWork::getFinisherId, customerId))*/
    }
}
