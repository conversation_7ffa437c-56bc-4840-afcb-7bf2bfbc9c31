package com.pxb7.mall.trade.ass.infra.config;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.pxb7.mall.components.env.profile.PxProfileEnum;
import com.pxb7.mall.components.env.profile.PxProfileHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class AssMetaObjectHandler implements MetaObjectHandler {

    @Override
    public void insertFill(MetaObject metaObject) {
        PxProfileEnum profile = PxProfileHelper.getProfile();
        if (metaObject.hasSetter("envProfile")) {
            this.strictInsertFill(metaObject, "envProfile", Integer.class, profile.getProfileValue());
        }
    }

    @Override
    public void updateFill(MetaObject metaObject) {

    }
}
