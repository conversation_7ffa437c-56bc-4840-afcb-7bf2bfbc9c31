package com.pxb7.mall.trade.ass.infra.model;

import java.time.*;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.ToString;


/**
 * 售后工单(AssWorkOrder)实体类
 *
 * <AUTHOR>
 * @since 2025-07-31 11:52:43
 */
public class AssWorkOrderReqPO {

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class AddPO {

        /**
         * 售后工单号
         */
        private String workOrderId;

        /**
         * ass_schedule.id
         */
        private String scheduleId;

        /**
         * 订单ID
         */
        private String orderItemId;

        /**
         * 订单ID
         */
        private String roomId;

        /**
         * 售后类型1找回2纠纷
         */
        private Integer assType;

        /**
         * 关联找回/纠纷工单号
         */
        private String relOrderId;

        /**
         * 售后工单状态 0:处理中 1:已完成 2:已取消
         */
        private Integer assStatus;

        /**
         * 售后工单状态备注
         */
        private String assStatusMemo;

        /**
         * 发起售后申请时间
         */
        private LocalDateTime applyTime;

        /**
         * 预计完成时间
         */
        private LocalDateTime expectedTime;

        /**
         * 完成时间
         */
        private LocalDateTime completeTime;

        /**
         * 是否已读 1:已读 0:未读
         */
        private Boolean readFlag;

        /**
         * 创建人id
         */
        private String createUserId;

        /**
         * 更新人id
         */
        private String updateUserId;

    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class UpdatePO {

        /**
         * 自增id
         */
        private Long id;


        /**
         * 售后工单号
         */
        private String workOrderId;


        /**
         * ass_schdule.id
         */
        private String scheduleId;


        /**
         * 订单ID
         */
        private String orderItemId;


        /**
         * 订单ID
         */
        private String roomId;


        /**
         * 售后类型1找回2纠纷
         */
        private Integer assType;


        /**
         * 关联找回/纠纷工单号
         */
        private String relOrderId;


        /**
         * 售后工单状态 0:处理中 1:已完成 2:已取消
         */
        private Integer assStatus;


        /**
         * 售后工单状态备注
         */
        private String assStatusMemo;


        /**
         * 发起售后申请时间
         */
        private LocalDateTime applyTime;


        /**
         * 预计完成时间
         */
        private LocalDateTime expectedTime;


        /**
         * 完成时间
         */
        private LocalDateTime completeTime;


        /**
         * 是否已读 1:已读 0:未读
         */
        private Boolean readFlag;


        /**
         * 创建人id
         */
        private String createUserId;


        /**
         * 更新人id
         */
        private String updateUserId;


    }


    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class DelPO {
        private Long id;
    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class SearchPO {
        /**
         * 售后工单号
         */
        private String workOrderId;

        /**
         * ass_schdule.id
         */
        private String scheduleId;

        /**
         * 订单ID
         */
        private String orderItemId;

        /**
         * 订单ID
         */
        private String roomId;

        /**
         * 售后类型1找回2纠纷
         */
        private Integer assType;

        /**
         * 关联找回/纠纷工单号
         */
        private String relOrderId;

        /**
         * 售后工单状态 0:处理中 1:已完成 2:已取消
         */
        private Integer assStatus;

        /**
         * 售后工单状态备注
         */
        private String assStatusMemo;

        /**
         * 发起售后申请时间
         */
        private LocalDateTime applyTime;

        /**
         * 预计完成时间
         */
        private LocalDateTime expectedTime;

        /**
         * 完成时间
         */
        private LocalDateTime completeTime;

        /**
         * 是否已读 1:已读 0:未读
         */
        private Boolean readFlag;

        /**
         * 创建人id
         */
        private String createUserId;

        /**
         * 更新人id
         */
        private String updateUserId;

    }

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class PagePO {

        /**
         * 售后工单号
         */
        private String workOrderId;

        /**
         * ass_schdule.id
         */
        private String scheduleId;

        /**
         * 订单ID
         */
        private String orderItemId;

        /**
         * 订单ID
         */
        private String roomId;

        /**
         * 售后类型1找回2纠纷
         */
        private Integer assType;

        /**
         * 关联找回/纠纷工单号
         */
        private String relOrderId;

        /**
         * 售后工单状态 0:处理中 1:已完成 2:已取消
         */
        private Integer assStatus;

        /**
         * 售后工单状态备注
         */
        private String assStatusMemo;

        /**
         * 发起售后申请时间
         */
        private LocalDateTime applyTime;

        /**
         * 预计完成时间
         */
        private LocalDateTime expectedTime;

        /**
         * 完成时间
         */
        private LocalDateTime completeTime;

        /**
         * 是否已读 1:已读 0:未读
         */
        private Boolean readFlag;

        /**
         * 创建人id
         */
        private String createUserId;

        /**
         * 更新人id
         */
        private String updateUserId;


        /**
         * 页码，从1开始
         */
        private Integer pageIndex;
        /**
         * 每页数量
         */
        private Integer pageSize;

    }

}

