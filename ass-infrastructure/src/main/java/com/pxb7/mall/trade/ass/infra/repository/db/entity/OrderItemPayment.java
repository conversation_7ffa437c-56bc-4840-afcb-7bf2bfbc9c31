package com.pxb7.mall.trade.ass.infra.repository.db.entity;

import java.io.Serializable;
import java.util.Date;
import java.time.*;
import java.math.BigDecimal;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * 子订单支付记录表(OrderItemPayment)实体类
 *
 * <AUTHOR>
 * @since 2024-09-06 19:40:32
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
@TableName(value = "order_item_payment")
public class OrderItemPayment implements Serializable {
    private static final long serialVersionUID = 384005861756927914L;
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 主订单id
     */
    @TableField(value = "order_id")
    private String orderId;
    /**
     * 订单行id
     */
    @TableField(value = "order_item_id")
    private String orderItemId;
    /**
     * 支付域业务类型 关联枚举: 支付/原路退/打款/挂账
     */
    @TableField(value = "business_type")
    private Integer businessType;
    /**
     * 收款单业务id
     */
    @TableField(value = "receipt_voucher_id")
    private String receiptVoucherId;
    /**
     * 退款单业务id
     */
    @TableField(value = "refund_voucher_id")
    private String refundVoucherId;
    /**
     * 放款单业务id
     */
    @TableField(value = "payout_voucher_id")
    private String payoutVoucherId;
    /**
     * 内部交易凭证
     */
    @TableField(value = "payment_id")
    private String paymentId;
    /**
     * 处理状态: 1处理中 2处理成功 3处理失败
     */
    @TableField(value = "status")
    private Integer status;
    /**
     * 交易金额
     */
    @TableField(value = "trade_amount")
    private Long tradeAmount;
    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDate createTime;
    /**
     * 更新时间
     */
    @TableField(value = "update_time", update="now()")
    private LocalDate updateTime;
    /**
     * 逻辑删除，0:删除，1:正常
     */
    @TableField(value = "is_deleted")
    private Boolean deleted;

}

