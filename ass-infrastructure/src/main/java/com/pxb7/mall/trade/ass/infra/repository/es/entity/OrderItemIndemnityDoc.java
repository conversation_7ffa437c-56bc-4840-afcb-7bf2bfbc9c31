package com.pxb7.mall.trade.ass.infra.repository.es.entity;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.DateFormat;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 子订单-包赔表(OrderItemIndemnityDoc)es
 *
 * <AUTHOR>
 * @since 2024-09-03 15:19:32
 */
@Data
@Document(indexName = "order_item_indemnity_doc")
public class OrderItemIndemnityDoc implements Serializable {

    private static final long serialVersionUID = 524646317665002036L;

    @Id()
    @Field(value = "order_item_indemnity_id")
    private String orderItemIndemnityId;
    /**
     * 订单包赔业务id
     */
    @Field(type = FieldType.Keyword, value = "order_item_id")
    private String orderItemId;
    /**
     * 关联包赔id
     */
    @Field(type = FieldType.Keyword,value = "indemnity_id")
    private String indemnityId;
    /**
     * 包赔名称
     */
    @Field(value = "indemnity_name")
    private String indemnityName;
    /**
     * 一级包赔类型 10 普通  20 增值 --indemnity_type.type_code
     */
    @Field(value = "indemnity_type_lev1")
    private Short indemnityTypeLev1;
    /**
     * 二级包赔类型 11 免费 12 单倍 13 双倍 14 三倍 15 新增包 21 实名 22 人脸 23 充值 24 装备 --indemnity_type.type_code
     */
    @Field(value = "indemnity_type_lev2")
    private Short indemnityTypeLev2;
    /**
     * 赔付比例
     */
    @Field(value = "percent_compensation")
    private Short percentCompensation;
    /**
     * 商品id
     */
    @Field(type = FieldType.Keyword,value = "product_id")
    private String productId;
    /**
     * 保单id, 只有成品号有并且只有单倍包赔才走保险(还要看配置)
     */
    @Field(type = FieldType.Keyword,value = "policy_id")
    private String policyId;
    /**
     * 包赔承担方 0买家 1卖家
     */
    @Field(value = "responsible_user")
    private Integer responsibleUser;
    /**
     * 承担的用户id
     */
    @Field(type = FieldType.Keyword,value = "responsible_user_id")
    private String responsibleUserId;
    /**
     * 1新购待生效 2生效中 3退订待生效 4已退订
     */
    @Field(value = "indemnity_status")
    private Integer indemnityStatus;
    /**
     * 包赔购买费率
     */
    @Field(value = "indemnity_buy_ratio")
    private Integer indemnityBuyRatio;
    /**
     * 包赔售价, 未使用优惠的号价乘以包赔费率
     */
    @Field(value = "indemnity_amount")
    private Long indemnityAmount;
    /**
     * 包赔优惠金额, 优惠劵或者红包方面的金额
     */
    @Field(value = "indemnity_coupon")
    private Long indemnityCoupon;
    /**
     * 包赔号商折扣金额, 有号商折扣就不能用优惠
     */
    @Field(value = "indemnity_merchant_coupon")
    private Long indemnityMerchantCoupon;
    /**
     * 包赔实付金额
     */
    @Field(value = "indemnity_real_amount")
    private Long indemnityRealAmount;

    /**
     * 号商折扣比例
     */
    @Field(value = "indemnity_merchant_discount")
    private Long indemnityMerchantDiscount;
    /**
     * 商品价格大于此值，才允许投保 0代表无限制
     */
    @Field(value = "price_min")
    private Long priceMin;
    /**
     * 商品价格大于此值，按该价格投保 0代表无限制
     */
    @Field(value = "price_max")
    private Long priceMax;
    /**
     * 保险平台名
     */
    @Field(value = "insurance_name")
    private String insuranceName;
    /**
     * 保险平台sku
     */
    @Field(value = "insurance_sku")
    private String insuranceSku;
    /**
     * 包赔说明
     */
    @Field(value = "indemnity_desc")
    private String indemnityDesc;
    /**
     * 创建时间
     */
    @Field(type = FieldType.Date,format = DateFormat.date_hour_minute_second_fraction,value = "create_time")
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    @Field(type = FieldType.Date,format = DateFormat.date_hour_minute_second_fraction,value = "update_time")
    private LocalDateTime updateTime;

}
