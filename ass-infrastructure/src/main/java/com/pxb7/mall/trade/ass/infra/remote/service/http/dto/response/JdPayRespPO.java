package com.pxb7.mall.trade.ass.infra.remote.service.http.dto.response;

import com.alibaba.cola.dto.DTO;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 京东支付  三方请求出参
 */
public class JdPayRespPO {

    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class BaseRespPO extends DTO {
        /**
         * 请求结果：0000-成功，其他为失败
         */
        private String resultCode;
        /**
         * 请求消息
         */
        private String resultDesc;
    }

    /**
     * 京东退款 响应参数
     */
    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString
    public static class RefundRespPO extends BaseRespPO {
        /**
         * 商户退款订单号
         */
        private String outTradeNo;
        /**
         * 商户原订单号
         */
        private String originalOutTradeNo;
        /**
         * 京东退款订单号
         */
        private String tradeNo;
        /**
         * 退款完成时间，格式为 yyyyMMddHHmmss
         */
        private String finishDate;
        /**
         * 退款金额，单位：分
         */
        private String tradeAmount;
        /**
         * 交易币种,固定值：CNY
         */
        private String currency;
        /**
         * 退款状态：
         * BUID 交易建立（交易处理中）
         * WPAR 等待支付结果（交易处理中）
         * ACSU 受理成功（交易处理中）
         * FINI 交易成功（交易成功）
         * REFU 交易退款（交易成功后发起了退款，原交易的交易状态会变成交易退款）
         * CLOS 交易关闭，失败、交易失败
         *
         * @see com.pxb7.mall.trade.order.client.enums.pay.JdPayStatusEnum
         */
        private String tradeStatus;
        /**
         * 回传信息
         */
        private String returnParams;
    }

}
