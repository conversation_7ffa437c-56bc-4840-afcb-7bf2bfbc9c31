package com.pxb7.mall.trade.ass.infra.repository.db;

import com.baomidou.mybatisplus.extension.service.IService;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.CommandDO;

import java.util.List;

public interface CommandRepository extends IService<CommandDO> {

    /**
     * 查询数量
     *
     * @param commandStatus 指令状态
     * @param begin         开始时间
     * @param end           结束时间
     * @return 数量
     */
    Long queryCount(int commandStatus, String begin, String end, List<String> typeList, String env);

    List<CommandDO> listCommands(Long startId, int commandStatus, String begin, String end, Integer limit, List<String> typeList, String env);

    Long deleteCommands(int commandStatus, String end);

    Long deleteByCommandId(String commandId);
}
