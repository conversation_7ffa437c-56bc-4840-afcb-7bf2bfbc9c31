package com.pxb7.mall.trade.ass.infra.repository.db;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.AssClaim;

/**
 * 售后赔付管理(AssClaim)表服务接口
 *
 * <AUTHOR>
 * @since 2024-10-26 14:54:02
 */
public interface AssClaimRepository extends IService<AssClaim> {

    long countByWorkOrderIdAndStatus(String workOrderId, List<Integer> status);

}
