package com.pxb7.mall.trade.ass.infra.repository.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.OrderItem;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 订单行(OrderItem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-07-22 20:59:35
 */
@Mapper
public interface OrderItemMapper extends BaseMapper<OrderItem> {
    /**
     * 批量新增数据
     *
     * @param entities List<OrderItem> 实例对象列表
     * @return 影响行数
     */
//    int insertBatch(@Param("entities") List<OrderItem> entities);
//
//    OrderItem getOrderItem(@Param("orderItemId") String orderItemId);
//
//
//    List<GameSalesVolumeDubboRespDTO> getGameSalesVolume(@Param("po") GameSalesVolumeReqPO po,@Param("startTime") LocalDateTime startTime,@Param("endTime") LocalDateTime endTime);
}
