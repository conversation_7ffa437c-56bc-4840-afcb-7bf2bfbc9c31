package com.pxb7.mall.trade.ass.infra.model;

import lombok.Data;

@Data
public class RefundFeeDetailPO {

    /**
     * 变更的金额 （正数：新增；负数：减少）
     */
    private Long changeFeeAmount;


    /**
     * 订单费用-变更的实收金额 （正数：新增；负数：减少）
     */
    private Long changeFeeRealAmount;


    /**
     * 订单费用-变更的优惠金额 （正数：新增；负数：减少）
     */
    private Long changeFeeDiscount;


    /**
     *  订单手续费 没有 业务id，和orderItem 共用业务id，所以此处使用orderItemId
     */
    private String orderItemId;


    /**
     * 承担方 0买家 1卖家
     */
    private Integer responsibleUser;
}
