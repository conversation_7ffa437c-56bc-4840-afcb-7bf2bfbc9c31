package com.pxb7.mall.trade.ass.infra.repository.db.mapper;

import com.pxb7.mall.trade.ass.infra.repository.db.entity.AssRetrieveGameConfigDetail;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 售后找回游戏配置明细表(AssRetrieveGameConfigDetail)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-07-30 13:45:36
 */
@Mapper
public interface AssRetrieveGameConfigDetailMapper extends BaseMapper<AssRetrieveGameConfigDetail> {
    /**
     * 批量新增数据
     *
     * @param entities List<AssRetrieveGameConfigDetail> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<AssRetrieveGameConfigDetail> entities);

}

