package com.pxb7.mall.trade.ass.infra.repository.db;


import java.util.List;
import java.util.Objects;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.pxb7.mall.trade.ass.infra.model.AssWorkOrderLogReqPO;
import com.pxb7.mall.trade.ass.infra.repository.db.mapper.AssWorkOrderLogMapper;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.AssWorkOrderLog;
import com.pxb7.mall.trade.ass.infra.repository.db.AssWorkOrderLogRepository;

/**
 * 售后工单日志明细(AssWorkOrderLog)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-31 10:19:36
 */
@Slf4j
@Repository
public class AssWorkOrderLogDbRepository extends ServiceImpl<AssWorkOrderLogMapper, AssWorkOrderLog> implements AssWorkOrderLogRepository {

    @Override
    public boolean insert(AssWorkOrderLogReqPO.AddPO param) {
        AssWorkOrderLog entity = new AssWorkOrderLog();
        entity.setWorkOrderId(param.getWorkOrderId());
        entity.setWorkOrderLogId(param.getWorkOrderLogId());
        entity.setOrderItemId(param.getOrderItemId());
        entity.setTitle(param.getTitle());
        entity.setAssType(param.getAssType());
        entity.setShowType(param.getShowType());
        entity.setContent(param.getContent());
        entity.setAdminContent(param.getAdminContent());
        entity.setAddWay(param.getAddWay());
        entity.setNodeId(param.getNodeId());
        entity.setNodeDesc(param.getNodeDesc());
        entity.setCreateUserId(param.getCreateUserId());
        entity.setUpdateUserId(param.getUpdateUserId());
        entity.setNoticeType(param.getNoticeType());
        entity.setJoinGroupMsg(param.getJoinGroupMsg());
        return this.save(entity);
    }

    @Override
    public boolean update(AssWorkOrderLogReqPO.UpdatePO param) {
        LambdaUpdateWrapper<AssWorkOrderLog> updateWrapper = new LambdaUpdateWrapper<>();
        //where
        updateWrapper.eq(AssWorkOrderLog::getId, param.getId());
        //set
        if (StringUtils.isNotBlank(param.getWorkOrderId())) {
            updateWrapper.set(AssWorkOrderLog::getWorkOrderId, param.getWorkOrderId());
        }
        if (StringUtils.isNotBlank(param.getWorkOrderLogId())) {
            updateWrapper.set(AssWorkOrderLog::getWorkOrderLogId, param.getWorkOrderLogId());
        }
        if (StringUtils.isNotBlank(param.getOrderItemId())) {
            updateWrapper.set(AssWorkOrderLog::getOrderItemId, param.getOrderItemId());
        }
        if (StringUtils.isNotBlank(param.getTitle())) {
            updateWrapper.set(AssWorkOrderLog::getTitle, param.getTitle());
        }
        if (Objects.nonNull(param.getAssType())) {
            updateWrapper.set(AssWorkOrderLog::getAssType, param.getAssType());
        }
        if (Objects.nonNull(param.getShowType())) {
            updateWrapper.set(AssWorkOrderLog::getShowType, param.getShowType());
        }
        if (StringUtils.isNotBlank(param.getContent())) {
            updateWrapper.set(AssWorkOrderLog::getContent, param.getContent());
        }
        if (StringUtils.isNotBlank(param.getAdminContent())) {
            updateWrapper.set(AssWorkOrderLog::getAdminContent, param.getAdminContent());
        }
        if (Objects.nonNull(param.getAddWay())) {
            updateWrapper.set(AssWorkOrderLog::getAddWay, param.getAddWay());
        }
        if (StringUtils.isNotBlank(param.getNodeId())) {
            updateWrapper.set(AssWorkOrderLog::getNodeId, param.getNodeId());
        }
        if (StringUtils.isNotBlank(param.getNodeDesc())) {
            updateWrapper.set(AssWorkOrderLog::getNodeDesc, param.getNodeDesc());
        }
        if (StringUtils.isNotBlank(param.getCreateUserId())) {
            updateWrapper.set(AssWorkOrderLog::getCreateUserId, param.getCreateUserId());
        }
        if (StringUtils.isNotBlank(param.getUpdateUserId())) {
            updateWrapper.set(AssWorkOrderLog::getUpdateUserId, param.getUpdateUserId());
        }
        return this.update(updateWrapper);
    }

    @Override
    public boolean deleteById(AssWorkOrderLogReqPO.DelPO param) {
        return this.removeById(param.getId());
    }

    @Override
    public AssWorkOrderLog findById(Long id) {
        return this.getById(id);
    }

    @Override
    public List<AssWorkOrderLog> list(AssWorkOrderLogReqPO.SearchPO param) {
        LambdaQueryWrapper<AssWorkOrderLog> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(param.getWorkOrderId())) {
            queryWrapper.eq(AssWorkOrderLog::getWorkOrderId, param.getWorkOrderId());
        }
        if (StringUtils.isNotBlank(param.getWorkOrderLogId())) {
            queryWrapper.eq(AssWorkOrderLog::getWorkOrderLogId, param.getWorkOrderLogId());
        }
        if (StringUtils.isNotBlank(param.getOrderItemId())) {
            queryWrapper.eq(AssWorkOrderLog::getOrderItemId, param.getOrderItemId());
        }
        if (StringUtils.isNotBlank(param.getTitle())) {
            queryWrapper.eq(AssWorkOrderLog::getTitle, param.getTitle());
        }
        if (Objects.nonNull(param.getAssType())) {
            queryWrapper.eq(AssWorkOrderLog::getAssType, param.getAssType());
        }
        if (Objects.nonNull(param.getShowType())) {
            queryWrapper.eq(AssWorkOrderLog::getShowType, param.getShowType());
        }
        if (StringUtils.isNotBlank(param.getContent())) {
            queryWrapper.eq(AssWorkOrderLog::getContent, param.getContent());
        }
        if (StringUtils.isNotBlank(param.getAdminContent())) {
            queryWrapper.eq(AssWorkOrderLog::getAdminContent, param.getAdminContent());
        }
        if (Objects.nonNull(param.getAddWay())) {
            queryWrapper.eq(AssWorkOrderLog::getAddWay, param.getAddWay());
        }
        if (StringUtils.isNotBlank(param.getNodeId())) {
            queryWrapper.eq(AssWorkOrderLog::getNodeId, param.getNodeId());
        }
        if (StringUtils.isNotBlank(param.getNodeDesc())) {
            queryWrapper.eq(AssWorkOrderLog::getNodeDesc, param.getNodeDesc());
        }
        if (StringUtils.isNotBlank(param.getCreateUserId())) {
            queryWrapper.eq(AssWorkOrderLog::getCreateUserId, param.getCreateUserId());
        }
        if (StringUtils.isNotBlank(param.getUpdateUserId())) {
            queryWrapper.eq(AssWorkOrderLog::getUpdateUserId, param.getUpdateUserId());
        }
        return this.list(queryWrapper);
    }

    @Override
    public Page<AssWorkOrderLog> page(AssWorkOrderLogReqPO.PagePO param) {
        LambdaQueryWrapper<AssWorkOrderLog> queryWrapper = new LambdaQueryWrapper<>();
        // 返回分页查询结果
        if (StringUtils.isNotBlank(param.getWorkOrderId())) {
            queryWrapper.eq(AssWorkOrderLog::getWorkOrderId, param.getWorkOrderId());
        }
        if (StringUtils.isNotBlank(param.getWorkOrderLogId())) {
            queryWrapper.eq(AssWorkOrderLog::getWorkOrderLogId, param.getWorkOrderLogId());
        }
        if (StringUtils.isNotBlank(param.getOrderItemId())) {
            queryWrapper.eq(AssWorkOrderLog::getOrderItemId, param.getOrderItemId());
        }
        if (StringUtils.isNotBlank(param.getTitle())) {
            queryWrapper.eq(AssWorkOrderLog::getTitle, param.getTitle());
        }
        if (Objects.nonNull(param.getAssType())) {
            queryWrapper.eq(AssWorkOrderLog::getAssType, param.getAssType());
        }
        if (Objects.nonNull(param.getShowType())) {
            queryWrapper.eq(AssWorkOrderLog::getShowType, param.getShowType());
        }
        if (StringUtils.isNotBlank(param.getContent())) {
            queryWrapper.eq(AssWorkOrderLog::getContent, param.getContent());
        }
        if (StringUtils.isNotBlank(param.getAdminContent())) {
            queryWrapper.eq(AssWorkOrderLog::getAdminContent, param.getAdminContent());
        }
        if (Objects.nonNull(param.getAddWay())) {
            queryWrapper.eq(AssWorkOrderLog::getAddWay, param.getAddWay());
        }
        if (StringUtils.isNotBlank(param.getNodeId())) {
            queryWrapper.eq(AssWorkOrderLog::getNodeId, param.getNodeId());
        }
        if (StringUtils.isNotBlank(param.getNodeDesc())) {
            queryWrapper.eq(AssWorkOrderLog::getNodeDesc, param.getNodeDesc());
        }
        if (StringUtils.isNotBlank(param.getCreateUserId())) {
            queryWrapper.eq(AssWorkOrderLog::getCreateUserId, param.getCreateUserId());
        }
        if (StringUtils.isNotBlank(param.getUpdateUserId())) {
            queryWrapper.eq(AssWorkOrderLog::getUpdateUserId, param.getUpdateUserId());
        }
        return this.page(new Page<>(param.getPageIndex(), param.getPageSize()), queryWrapper);
    }
}
