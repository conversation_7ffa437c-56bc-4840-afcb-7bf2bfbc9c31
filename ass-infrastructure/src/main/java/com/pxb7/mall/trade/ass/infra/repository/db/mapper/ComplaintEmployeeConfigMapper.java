package com.pxb7.mall.trade.ass.infra.repository.db.mapper;

import com.pxb7.mall.trade.ass.infra.repository.db.entity.ComplaintEmployeeConfig;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 工单处理人员信息维护配置(ComplaintEmployeeConfig)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-09-20 16:45:28
 */
@Mapper
public interface ComplaintEmployeeConfigMapper extends BaseMapper<ComplaintEmployeeConfig> {
    /**
     * 批量新增数据
     *
     * @param entities List<ComplaintEmployeeConfig> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<ComplaintEmployeeConfig> entities);

}

