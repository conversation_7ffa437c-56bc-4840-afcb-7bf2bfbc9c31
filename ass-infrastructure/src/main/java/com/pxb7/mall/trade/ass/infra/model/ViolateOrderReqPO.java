package com.pxb7.mall.trade.ass.infra.model;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;


/**
 * 违约单(ViolateOrder)实体类
 *
 * <AUTHOR>
 * @since 2025-03-27 15:47:15
 */
public class ViolateOrderReqPO {
     
    @Getter
    @Setter
    @Accessors(chain = true)
    public static class AddPO  {
    
       
         private String  violateId;

       
         private String  orderItemId;

       
         private String  refundVoucherId;

       
         private String  violateUserId;

       
         private String  promiseUserId;

       
         private Integer  violateUserType;

       
         private Long  violateAmount;

       
         private Long  promiseAmount;

       
         private Long  platformAmount;

       
         private Integer  violateStatus;

       
         private Integer  receiptStatus;

       
         private Integer  transferStatus;

    }

    @Getter
    @Setter
    @Accessors(chain = true)
    public static class UpdatePO {
     
      
         private Integer  id;
         
     
      
         private String  violateId;
         
     
      
         private String  orderItemId;
         
     
      
         private String  refundVoucherId;
         
     
      
         private String  violateUserId;
         
     
      
         private String  promiseUserId;
         
     
      
         private Integer  violateUserType;
         
     
      
         private Long  violateAmount;
         
     
      
         private Long  promiseAmount;
         
     
      
         private Long  platformAmount;
         
     
      
         private Integer  violateStatus;
         
     
      
         private Integer  receiptStatus;
         
     
      
         private Integer  transferStatus;
         
     
     
     
    }


    @Getter
    @Setter
    @Accessors(chain = true)
    public  static class  DelPO{
        private  Integer  id;
    }

    @Getter
    @Setter
    @Accessors(chain = true)
    public static class  SearchPO{
       
         private String  violateId;

       
         private String  orderItemId;

       
         private String  refundVoucherId;

       
         private String  violateUserId;

       
         private String  promiseUserId;

       
         private Integer  violateUserType;

       
         private Long  violateAmount;

       
         private Long  promiseAmount;

       
         private Long  platformAmount;

       
         private Integer  violateStatus;


         private List<Integer> violateStatuses;

       
         private Integer  receiptStatus;

       
         private Integer  transferStatus;

    }

    @Getter
    @Setter
    @Accessors(chain = true)
    public static class PagePO{
    
       
         private String  violateId;

       
         private String  orderItemId;

       
         private String  refundVoucherId;

       
         private String  violateUserId;

       
         private String  promiseUserId;

       
         private Integer  violateUserType;

       
         private Long  violateAmount;

       
         private Long  promiseAmount;

       
         private Long  platformAmount;

       
         private Integer  violateStatus;

       
         private Integer  receiptStatus;

       
         private Integer  transferStatus;


        /**
         * 页码，从1开始
         */
        private Integer pageIndex;
        /**
         * 每页数量
         */
        private Integer pageSize;

    }

}

