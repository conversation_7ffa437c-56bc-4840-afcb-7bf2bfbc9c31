package com.pxb7.mall.trade.ass.infra.remote.service.http.api;

import com.pxb7.mall.trade.ass.infra.remote.model.outpayparam.request.LianLianRefundPO;
import com.pxb7.mall.trade.ass.infra.remote.model.request.LianLianRefundQueryReqPO;

import okhttp3.ResponseBody;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.POST;

public interface LLianPayAccpApi {

    /**
     * 退款申请
     */
    @POST("/v1/txn/more-payee-refund")
    Call<ResponseBody> morePayeeRefund(@Body LianLianRefundPO refundPO);

    /**
     * 退款申请
     */
    @POST("/v1/txn/query-refund")
    Call<ResponseBody> refundQuery(@Body LianLianRefundQueryReqPO refundQueryReqPO);
}
