package com.pxb7.mall.trade.ass.infra.enums;

import java.util.Arrays;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 整单退款类型
 */
@Getter
@AllArgsConstructor
public enum RefundActionTypeEnum {

    USER(1, "用户"),

    SERVICE(2, "客服"),

    FINANCE(3, "财务"),

    MERCHANT(4, "商家");

    private final Integer value;

    private final String label;

    public static String getLabel(Integer value) {
        return Arrays.stream(RefundActionTypeEnum.values()).filter(e -> e.getValue().equals(value))
            .map(RefundActionTypeEnum::getLabel).findAny().orElse(null);
    }
}
