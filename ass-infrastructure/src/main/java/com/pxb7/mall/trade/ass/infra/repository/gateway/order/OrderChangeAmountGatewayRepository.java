package com.pxb7.mall.trade.ass.infra.repository.gateway.order;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Lists;
import com.pxb7.mall.trade.ass.infra.model.RefundFeeDetailPO;
import com.pxb7.mall.trade.ass.infra.model.RefundIndemnityDetailPO;
import com.pxb7.mall.trade.order.client.api.OrderChangeAmountServiceI;
import com.pxb7.mall.trade.order.client.dto.request.order.dubbo.CalcFeeChangeAmountReqDTO;
import com.pxb7.mall.trade.order.client.dto.request.order.dubbo.CalcIndemnityChangeAmountReqDTO;
import com.pxb7.mall.trade.order.client.dto.request.order.dubbo.FeeChangeAmountDTO;
import com.pxb7.mall.trade.order.client.dto.request.order.dubbo.IndemnityChangeAmountDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
public class OrderChangeAmountGatewayRepository {

    @DubboReference(check = false)
    private OrderChangeAmountServiceI orderChangeAmountServiceI;


    public List<RefundIndemnityDetailPO> calculateRefundIndemnityDetails(String orderItemId, Long changeIndemnityAmount, Long newProductPrice) {
        CalcIndemnityChangeAmountReqDTO calcIndemnityChangeAmountReqDTO = new CalcIndemnityChangeAmountReqDTO();
        calcIndemnityChangeAmountReqDTO.setOrderItemId(orderItemId);
        calcIndemnityChangeAmountReqDTO.setChangeIndemnityAmount(changeIndemnityAmount);
        calcIndemnityChangeAmountReqDTO.setNewProductSalePrice(newProductPrice);
        log.info("calculateIndemnityChangeAmounts input:{}", calcIndemnityChangeAmountReqDTO);
        MultiResponse<IndemnityChangeAmountDTO> response = orderChangeAmountServiceI.calculateIndemnityChangeAmounts(calcIndemnityChangeAmountReqDTO);
        log.info("calculateIndemnityChangeAmounts response:{}", JSON.toJSONString(response));
        if (response.isSuccess()) {
            return response.getData().stream().map(this::convert).toList();
        }
        return Lists.newArrayList();
    }


    private RefundIndemnityDetailPO convert(IndemnityChangeAmountDTO item) {
        if (null == item) {
            return null;
        }
        RefundIndemnityDetailPO refundIndemnityDetailPO = new RefundIndemnityDetailPO();
        refundIndemnityDetailPO.setOrderItemIndemnityId(item.getOrderItemIndemnityId());
        refundIndemnityDetailPO.setIndemnityRealAmount(Math.abs(item.getChangeIndemnityRealAmount()));
        refundIndemnityDetailPO.setIndemnityMerchantCoupon(Math.abs(item.getChangeIndemnityMerchantCoupon()));
        refundIndemnityDetailPO.setIndemnityAmount(Math.abs(item.getChangeIndemnityAmount()));
        refundIndemnityDetailPO.setResponsibleUser(item.getResponsibleUser());
        return refundIndemnityDetailPO;
    }

    public RefundFeeDetailPO calculateRefundFeeChangeAmount(String orderItemId, Long changeFeeAmount, Long newProductPrice) {
        CalcFeeChangeAmountReqDTO calcFeeChangeAmountReqDTO = new CalcFeeChangeAmountReqDTO();
        calcFeeChangeAmountReqDTO.setOrderItemId(orderItemId);
        calcFeeChangeAmountReqDTO.setChangeFeeAmount(changeFeeAmount);
        calcFeeChangeAmountReqDTO.setNewProductSalePrice(newProductPrice);
        log.info("calculateFeeChangeAmount input:{}", calcFeeChangeAmountReqDTO);
        SingleResponse<FeeChangeAmountDTO> response = orderChangeAmountServiceI.calculateFeeChangeAmount(calcFeeChangeAmountReqDTO);
        log.info("calculateFeeChangeAmount response:{}", JSON.toJSONString(response));
        if (response.isSuccess()) {
            FeeChangeAmountDTO data = response.getData();
            return this.convert(data);
        }
        return null;
    }

    private RefundFeeDetailPO convert(FeeChangeAmountDTO item) {
        if (null == item) {
            return null;
        }
        RefundFeeDetailPO refundFeeDetailPO = new RefundFeeDetailPO();
        refundFeeDetailPO.setChangeFeeAmount(Math.abs(item.getChangeFeeAmount()));
        refundFeeDetailPO.setChangeFeeRealAmount(Math.abs(item.getChangeFeeRealAmount()));
        refundFeeDetailPO.setChangeFeeDiscount(Math.abs(item.getChangeFeeDiscount()));
        refundFeeDetailPO.setOrderItemId(item.getOrderItemId());
        refundFeeDetailPO.setResponsibleUser(Math.abs(item.getResponsibleUser()));
        return refundFeeDetailPO;
    }
}
