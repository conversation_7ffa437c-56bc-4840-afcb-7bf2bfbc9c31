package com.pxb7.mall.trade.ass.infra.model.mq;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@Accessors(chain = true)
public class RefundNotifyMessage implements Serializable {
    /**
     * 退款payment，线上退款需要
     */
    private String refundPaymentId;
    /**
     * 退款单据id
     */
    private String refundVoucherId;
    /**
     * 退款单对应的收款单，线上退款需要
     */
    private String receiptVoucherId;
    /**
     * 退款金额
     */
    private Long refundAmount;
    /**
     * 退款类型，1线上原路返回 2线下打款 3, 挂账
     */
    private Integer refundType;
    /**
     * 交易状态
     */
    private Integer tradeStatus;
}
