package com.pxb7.mall.trade.ass.infra.remote.model.refund;

import com.alibaba.cola.dto.DTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 京东退款
 * <AUTHOR>
 * @since 2025/5/6
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class JdRefundPO extends DTO {

    /**
     * 京东订单编号
     */
    private Long jdOrderId;

    /**
     * 京东买家ID
     */
    private String jdBuyerId;

    /**
     * 退款唯一标识
     */
    private String refundUuid;

    /**
     * 是否部分退款
     */
    private Boolean partRefund = true;

    /**
     * 退款金额(分)
     */
    private Long refundAmount;

}
