package com.pxb7.mall.trade.ass.infra.repository.db.entity;
import java.io.Serializable;
import java.time.*;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import com.baomidou.mybatisplus.annotation.*;

/**
 * 违约单(ViolateOrder)实体类
 *
 * <AUTHOR>
 * @since 2025-04-16 15:19:52
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "violate_order")
public class ViolateOrder implements Serializable {
    private static final long serialVersionUID = -48618061610636394L;
     @TableField(value="id")
     private Integer  id;
     /**
     * 违约单id
     */
     @TableId(value = "violate_id", type = IdType.INPUT)
     private String  violateId;
     /**
     * 订单id
     */
     @TableField(value = "order_item_id")
     private String  orderItemId;
     /**
     * 退款单id
     */
     @TableField(value = "refund_voucher_id")
     private String  refundVoucherId;
     /**
     * 违约方userID
     */
     @TableField(value = "violate_user_id")
     private String  violateUserId;
     /**
     * 守约方userID
     */
     @TableField(value = "promise_user_id")
     private String  promiseUserId;
     /**
     * 违约方:1买家、2卖家
     */
     @TableField(value = "violate_user_type")
     private Integer  violateUserType;
     /**
     * 违约金,分
     */
     @TableField(value = "violate_amount")
     private Long  violateAmount;
     /**
     * 守约金,分
     */
     @TableField(value = "promise_amount")
     private Long  promiseAmount;
     /**
     * 平台金额,分
     */
     @TableField(value = "platform_amount")
     private Long  platformAmount;
     /**
     * 违约单状态:1待执行、2执行中、3执行成功、4执行失败、5取消
     */
     @TableField(value = "violate_status")
     private Integer  violateStatus;
     /**
     * 收款状态:1待缴款、2缴款成功、3缴款失败、4缴款中断、5缴款取消
     */
     @TableField(value = "receipt_status")
     private Integer  receiptStatus;
     /**
     * 打款状态:1打款中、2打款成功、3打款失败
     */
     @TableField(value = "transfer_status")
     private Integer  transferStatus;
     /**
     * 开始处理时间(退款完成)
     */
     @TableField(value = "deal_time")
     private LocalDateTime  dealTime;
     /**
     * 收款中断时间
     */
     @TableField(value = "receipt_stop_time")
     private LocalDateTime  receiptStopTime;
     /**
     * 完结时间
     */
     @TableField(value = "completed_time")
     private LocalDateTime  completedTime;
     @TableField(value = "create_time")
     private LocalDateTime  createTime;
     /**
     * 制单客服userid
     */
     @TableField(value = "create_user_id")
     private String  createUserId;
     /**
     * 制单客服
     */
     @TableField(value = "create_username")
     private String  createUsername;
     @TableField(value = "update_time", update = "now()")
     private LocalDateTime  updateTime;
     /**
     * 编辑客服userid
     */
     @TableField(value = "update_user_id")
     private String  updateUserId;
     /**
     * 编辑客服
     */
     @TableField(value = "update_username")
     private String  updateUsername;
     /**
     * 0正常、1已删除
     */
     @TableLogic
     @TableField(value = "is_deleted")
     private Integer  isDeleted;


}

