package com.pxb7.mall.trade.ass.infra.repository.db;

import com.baomidou.mybatisplus.extension.service.IService;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.ComplaintEmployeeConfig;

import java.util.List;

/**
 * 工单处理人员信息维护配置(ComplaintEmployeeConfig)表服务接口
 *
 * <AUTHOR>
 * @since 2024-09-20 16:45:28
 */
public interface ComplaintEmployeeConfigRepository extends IService<ComplaintEmployeeConfig> {

    List<ComplaintEmployeeConfig> queryEmployeeList(List<String> departmentIdList);

    ComplaintEmployeeConfig queryEmployeeByUserId(String transfereeId);
}

