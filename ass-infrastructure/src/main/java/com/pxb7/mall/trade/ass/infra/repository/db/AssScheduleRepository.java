package com.pxb7.mall.trade.ass.infra.repository.db;

import java.time.LocalDateTime;
import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.pxb7.mall.trade.ass.client.dto.request.AssStatusReqDTO;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.AssSchedule;

/**
 * 售后流程进度(AssSchedule)表服务接口
 *
 * <AUTHOR>
 * @since 2024-08-12 10:59:09
 */
public interface AssScheduleRepository extends IService<AssSchedule> {

    AssSchedule getByRoomId(String roomId);

    boolean updateWorkOrderIdAndAssTypeAndFinishById(Long id, String workOrderId, Integer assType, Boolean finish,
        String updateUserId);

    boolean updateFinishById(Long id, Boolean finish, String updateUserId);

    AssSchedule findOneByOrderItemIdAndRoomId(String orderItemId, String roomId);

    List<AssSchedule> findListByOrderItemIdAndRoomId(List<AssStatusReqDTO> list);


    List<AssSchedule> listNoFinishAndMaxId(Long minId, Long maxId);

}
