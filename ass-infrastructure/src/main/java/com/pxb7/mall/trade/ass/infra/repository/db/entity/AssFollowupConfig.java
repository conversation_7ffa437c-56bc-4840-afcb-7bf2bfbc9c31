package com.pxb7.mall.trade.ass.infra.repository.db.entity;

import java.io.Serializable;
import java.util.Date;
import java.time.*;
import java.math.BigDecimal;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import com.baomidou.mybatisplus.annotation.*;

/**
 * 跟进结果类型配置表(AssFollowupConfig)实体类
 *
 * <AUTHOR>
 * @since 2025-07-30 11:51:50
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "ass_followup_config")
@ToString
public class AssFollowupConfig implements Serializable {
    private static final long serialVersionUID = -74624658204438595L;
    /**
     * 自增id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 业务主键
     */
    @TableField(value = "followup_config_id")
    private String followupConfigId;
    /**
     * 售后类型1找回2纠纷
     */
    @TableField(value = "ass_type")
    private Integer assType;
    /**
     * 跟进结果类型
     */
    @TableField(value = "result_desc")
    private String resultDesc;
    /**
     * 售后进度展示文案
     */
    @TableField(value = "progress_desc")
    private String progressDesc;
    /**
     * 提醒用户进群文案
     */
    @TableField(value = "join_group_desc")
    private String joinGroupDesc;
    /**
     * 发送通知类型1:追回账号，待买家换绑2:追回号款，待买家提供收款账号3:售后到期，待买家接收赔付
     */
    @TableField(value = "notice_type")
    private Integer noticeType;
    /**
     * 排序
     */
    @TableField(value = "sort")
    private Integer sort;
    /**
     * 创建人id
     */
    @TableField(value = "create_user_id")
    private String createUserId;
    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;
    /**
     * 更新人id
     */
    @TableField(value = "update_user_id")
    private String updateUserId;
    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;
    /**
     * 是否删除 1:已删除 0:未删除
     */
    @TableLogic
    @TableField(value = "is_deleted")
    private Boolean deleted;


}

