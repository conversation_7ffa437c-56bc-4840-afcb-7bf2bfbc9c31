package com.pxb7.mall.trade.ass.infra.repository.db.mapper;

import com.pxb7.mall.trade.ass.infra.repository.db.entity.OrderItemPayment;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 子订单支付记录表(OrderItemPayment)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-09-06 19:40:32
 */
@Mapper
public interface OrderItemPaymentMapper extends BaseMapper<OrderItemPayment> {
    /**
     * 批量新增数据
     *
     * @param entities List<OrderItemPayment> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<OrderItemPayment> entities);

}

