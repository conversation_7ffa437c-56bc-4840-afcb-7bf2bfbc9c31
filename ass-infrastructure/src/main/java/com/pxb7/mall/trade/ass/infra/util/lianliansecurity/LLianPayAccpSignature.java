package com.pxb7.mall.trade.ass.infra.util.lianliansecurity;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;

/**
 * 连连账户+签名
 */
@Slf4j
public class LLianPayAccpSignature {
    /**
     * 签名处理
     *
     * @param merchantPrivateKey ：商户私钥
     * @param sign_str ：签名源内容
     * @return
     */
    public static String sign(String merchantPrivateKey, String sign_str) {
        try {
            String hash = DigestUtils.md5Hex(sign_str);
            log.info("签名处理中，签名源内容：{}，对应MD5值：{}", sign_str, hash);
            return RSASign.sign(merchantPrivateKey, hash);
        } catch (Exception e) {
            log.error("签名失败", e);
            return null;
        }
    }

    /**
     * 签名验证
     *
     * @param publicKey ：公钥
     * @param sign_str ：源串
     * @param signed_str ：签名结果串
     * @return
     */
    public static boolean checkSign(String publicKey, String sign_str, String signed_str) {
        try {
            String hash = DigestUtils.md5Hex(sign_str);
            log.info(String.format("签名验证中，源串：%s，对应MD5值：%s", sign_str, hash));
            return RSASign.checkSign(publicKey, hash, signed_str);
        } catch (Exception e) {
            log.error("签名验证异常", e);
            return false;
        }
    }
}
