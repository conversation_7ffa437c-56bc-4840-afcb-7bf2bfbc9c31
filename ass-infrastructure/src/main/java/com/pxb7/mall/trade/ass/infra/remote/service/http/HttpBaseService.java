package com.pxb7.mall.trade.ass.infra.remote.service.http;

import com.pxb7.mall.trade.ass.infra.remote.model.request.TaiShanPayQueryReqPO;
import com.pxb7.mall.trade.ass.infra.remote.service.http.api.JdPayHttpApi;
import org.springframework.stereotype.Service;

import com.pxb7.mall.trade.ass.infra.config.AllinPayConfig;
import com.pxb7.mall.trade.ass.infra.constant.PayConstant;
import com.pxb7.mall.trade.ass.infra.remote.interceptor.LLianSignatureInterceptor;
import com.pxb7.mall.trade.ass.infra.remote.interceptor.TaishanSignatureInterceptor;
import com.pxb7.mall.trade.ass.infra.remote.service.http.api.AllinPayUnitOrderApi;
import com.pxb7.mall.trade.ass.infra.remote.service.http.api.LLianPayAccpApi;
import com.pxb7.mall.trade.ass.infra.remote.service.http.api.TaishanPayHttpApi;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.logging.HttpLoggingInterceptor;
import retrofit2.Retrofit;
import retrofit2.converter.jackson.JacksonConverterFactory;

import java.io.IOException;

@Slf4j
@Service
public class HttpBaseService {

    public static final HttpLoggingInterceptor interceptor;

    static {
        interceptor = new HttpLoggingInterceptor(log::info);
        interceptor.level(HttpLoggingInterceptor.Level.BASIC);
        if (log.isDebugEnabled()) {
            interceptor.level(HttpLoggingInterceptor.Level.BODY);
        }
    }

    protected LLianPayAccpApi lianPayAccpApi;

    protected AllinPayUnitOrderApi allinPayUnitOrderApi;

    protected TaishanPayHttpApi taishanPayHttpApi;

    public JdPayHttpApi jdPayHttpApi;

    /**
     * 连连支付验签拦截器
     */
    @Resource
    protected LLianSignatureInterceptor lLianSignatureInterceptor;

    /**
     * 泰山支付验签拦截器
     */
    @Resource
    protected TaishanSignatureInterceptor taishanSignatureInterceptor;

    @PostConstruct
    public void initAllinPayUnitOrderApi() {
        var retrofit = new Retrofit.Builder().client(new OkHttpClient.Builder().addInterceptor(interceptor).build())
            .baseUrl(AllinPayConfig.unitOrderHost).addConverterFactory(JacksonConverterFactory.create()).build();
        allinPayUnitOrderApi = retrofit.create(AllinPayUnitOrderApi.class);
    }

    @PostConstruct
    public void initLLianAccpClient() {

        var retrofit =
            new Retrofit.Builder()
                .client(new OkHttpClient.Builder().addInterceptor(interceptor).addInterceptor(lLianSignatureInterceptor)
                    .build())
                .baseUrl(PayConstant.ACCP_HOST).addConverterFactory(JacksonConverterFactory.create()).build();
        lianPayAccpApi = retrofit.create(LLianPayAccpApi.class);
    }

    @PostConstruct
    public void taishanPayHttpApi() {
        var retrofit = new Retrofit.Builder()
            .client(new OkHttpClient.Builder().addInterceptor(interceptor).addInterceptor(taishanSignatureInterceptor)
                .build())
            .baseUrl(PayConstant.TS_SERVER_URL).addConverterFactory(JacksonConverterFactory.create()).build();
        taishanPayHttpApi = retrofit.create(TaishanPayHttpApi.class);
    }

    @PostConstruct
    public void jdPayHttpApi(){
        var retrofit = new Retrofit.Builder()
                .client(new OkHttpClient.Builder().addInterceptor(interceptor).build())
                .baseUrl(PayConstant.JD_SERVER_URL)
                .addConverterFactory(JacksonConverterFactory.create())
                .build();
        jdPayHttpApi = retrofit.create(JdPayHttpApi.class);
    }

    public static void main(String[] args) throws IOException {
        TaishanSignatureInterceptor interceptor1 = new TaishanSignatureInterceptor();
        interceptor1.setMd5Key("q3kzs8owyo");
        var retrofit = new Retrofit.Builder()
                .client(new OkHttpClient.Builder().addInterceptor(interceptor).addInterceptor(interceptor1)
                        .build())
                .baseUrl(PayConstant.TS_SERVER_URL).addConverterFactory(JacksonConverterFactory.create()).build();
        TaishanPayHttpApi api = retrofit.create(TaishanPayHttpApi.class);
        TaiShanPayQueryReqPO queryReqPO = new TaiShanPayQueryReqPO();
        queryReqPO.setMerchantTradeNo("12437460437408827015");
        //System.out.println(api.payQuery(queryReqPO, "2088340441371760").execute().body().string());
    }
}
