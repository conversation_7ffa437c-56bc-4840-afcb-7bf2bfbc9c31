package com.pxb7.mall.trade.ass.infra.repository.es.impl;

import org.springframework.data.elasticsearch.core.SearchPage;

import com.pxb7.mall.trade.ass.infra.model.RefundVoucherSearchPO;
import com.pxb7.mall.trade.ass.infra.repository.es.entity.RefundVoucherDoc;

public interface EsRefundSearchService {

    SearchPage<RefundVoucherDoc> searchRefundPage(RefundVoucherSearchPO refundVoucherSearchPO);
}
