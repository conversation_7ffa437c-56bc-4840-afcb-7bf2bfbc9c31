package com.pxb7.mall.trade.ass.infra.repository.es.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 子订单-包赔表(OrderItemIndemnity)实体类
 *
 * <AUTHOR>
 * @since 2024-09-03 15:19:32
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "order_item_indemnity")
public class OrderItemIndemnity implements Serializable {
    private static final long serialVersionUID = -78039963910634634L;
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField(value = "order_item_indemnity_id")
    private String orderItemIndemnityId;
    /**
     * 关联包赔id
     */
    @TableField(value = "indemnity_id")
    private String indemnityId;
    /**
     * 包赔名称
     */
    @TableField(value = "indemnity_name")
    private String indemnityName;
    /**
     * 一级包赔类型 10 普通  20 增值 --indemnity_type.type_code
     */
    @TableField(value = "indemnity_type_lev1")
    private Short indemnityTypeLev1;
    /**
     * 二级包赔类型 11 免费 12 单倍 13 双倍 14 三倍 15 新增包 21 实名 22 人脸 23 充值 24 装备 --indemnity_type.type_code
     */
    @TableField(value = "indemnity_type_lev2")
    private Short indemnityTypeLev2;
    /**
     * 赔付比例
     */
    @TableField(value = "percent_compensation")
    private Short percentCompensation;
    /**
     * 商品id
     */
    @TableField(value = "product_id")
    private String productId;
    /**
     * 子订单id
     */
    @TableField(value = "order_item_id")
    private String orderItemId;
    /**
     * 保单id, 只有成品号有并且只有单倍包赔才走保险(还要看配置)
     */
    @TableField(value = "policy_id")
    private String policyId;
    /**
     * 包赔承担方 0买家 1卖家
     */
    @TableField(value = "responsible_user")
    private Integer responsibleUser;
    /**
     * 承担的用户id
     */
    @TableField(value = "responsible_user_id")
    private String responsibleUserId;
    /**
     * 1新购待生效 2生效中 3退订待生效 4已退订
     */
    @TableField(value = "indemnity_status")
    private Integer indemnityStatus;
    /**
     * 包赔购买费率
     */
    @TableField(value = "indemnity_buy_ratio")
    private Integer indemnityBuyRatio;
    /**
     * 包赔售价, 未使用优惠的号价乘以包赔费率
     */
    @TableField(value = "indemnity_amount")
    private Long indemnityAmount;
    /**
     * 包赔优惠金额, 优惠劵或者红包方面的金额
     */
    @TableField(value = "indemnity_coupon")
    private Long indemnityCoupon;
    /**
     * 包赔号商折扣金额, 有号商折扣就不能用优惠
     */
    @TableField(value = "indemnity_merchant_coupon")
    private Long indemnityMerchantCoupon;
    /**
     * 包赔实付金额
     */
    @TableField(value = "indemnity_real_amount")
    private Long indemnityRealAmount;

    /**
     * 号商折扣比例
     */
    @TableField(value = "indemnity_merchant_discount")
    private Long indemnityMerchantDiscount;
    /**
     * 商品价格大于此值，才允许投保 0代表无限制
     */
    @TableField(value = "price_min")
    private Long priceMin;
    /**
     * 商品价格大于此值，按该价格投保 0代表无限制
     */
    @TableField(value = "price_max")
    private Long priceMax;
    /**
     * 保险平台名
     */
    @TableField(value = "insurance_name")
    private String insuranceName;
    /**
     * 保险平台sku
     */
    @TableField(value = "insurance_sku")
    private String insuranceSku;
    /**
     * 包赔说明
     */
    @TableField(value = "indemnity_desc")
    private String indemnityDesc;
    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    @TableField(value = "update_time", update="now()")
    private LocalDateTime updateTime;
    /**
     * 逻辑删除，0:删除，1:正常
     */
    @TableLogic
    @TableField(value = "is_deleted")
    private Boolean deleted;


}

