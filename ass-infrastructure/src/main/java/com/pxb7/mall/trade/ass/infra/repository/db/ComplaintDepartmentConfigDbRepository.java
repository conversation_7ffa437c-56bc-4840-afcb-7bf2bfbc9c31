package com.pxb7.mall.trade.ass.infra.repository.db;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.ComplaintDepartmentConfig;
import com.pxb7.mall.trade.ass.infra.repository.db.mapper.ComplaintDepartmentConfigMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 问题部门-问题类型配置(ComplaintDepartmentConfig)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-09-20 16:45:28
 */
@Slf4j
@Repository
public class ComplaintDepartmentConfigDbRepository extends ServiceImpl<ComplaintDepartmentConfigMapper, ComplaintDepartmentConfig> implements ComplaintDepartmentConfigRepository {

    @Override
    public List<ComplaintDepartmentConfig> queryDepartmentList() {
        LambdaQueryWrapper<ComplaintDepartmentConfig> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ComplaintDepartmentConfig::getDeleted, false);
        wrapper.orderByAsc(ComplaintDepartmentConfig::getQuestionSort);
        return baseMapper.selectList(wrapper);
    }
}
