package com.pxb7.mall.trade.ass.infra.repository.db;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pxb7.mall.trade.ass.infra.repository.db.mapper.ComplaintEmployeeConfigMapper;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.ComplaintEmployeeConfig;

import java.util.List;

/**
 * 工单处理人员信息维护配置(ComplaintEmployeeConfig)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-09-20 16:45:29
 */
@Slf4j
@Repository
public class ComplaintEmployeeConfigDbRepository extends ServiceImpl<ComplaintEmployeeConfigMapper, ComplaintEmployeeConfig> implements ComplaintEmployeeConfigRepository {

    @Override
    public List<ComplaintEmployeeConfig> queryEmployeeList(List<String> departmentIdList) {
        LambdaQueryWrapper<ComplaintEmployeeConfig> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(ComplaintEmployeeConfig::getComplaintDepartmentId, departmentIdList);
        wrapper.eq(ComplaintEmployeeConfig::getDeleted, false);
        return baseMapper.selectList(wrapper);
    }

    @Override
    public ComplaintEmployeeConfig queryEmployeeByUserId(String transfereeId) {
        LambdaQueryWrapper<ComplaintEmployeeConfig> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ComplaintEmployeeConfig::getUserId, transfereeId);
        wrapper.eq(ComplaintEmployeeConfig::getDeleted, false);
        return baseMapper.selectOne(wrapper);
    }
}
