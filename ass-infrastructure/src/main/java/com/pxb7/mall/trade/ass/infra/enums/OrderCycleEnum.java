package com.pxb7.mall.trade.ass.infra.enums;

import java.time.DayOfWeek;
import java.time.LocalDateTime;
import java.time.temporal.TemporalAdjusters;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum OrderCycleEnum {
    /**
     * 本周
     */
    THIS_WEEK(1,"now/w"),

    /**
     * 本月
     */
    THIS_MONTH(2,"now/M"),

    /**
     * 三月内
     */
    THREE_MONTHS_AGO(3,"now-3M"),

    /**
     * 半年内
     */
    SIX_MONTHS_AGO(4,"now-6M"),

    /**
     * 一年内
     */
    ONE_YEAR_AGO(5,"now-1y");

    private final Integer value;
    private String dateStr;

    public static OrderCycleEnum getOrderCycleEnum(Integer value) {
        for (OrderCycleEnum orderCycleEnum : OrderCycleEnum.values()) {
            if (orderCycleEnum.value.equals(value)) {
                return orderCycleEnum;
            }
        }
        return null;
    }

    public static LocalDateTime getCycleStart(OrderCycleEnum orderCycleEnum, LocalDateTime endTime) {

        LocalDateTime startTime = endTime.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
        if (orderCycleEnum != null) {
            switch (orderCycleEnum) {
                // 默认本周本周
                case THIS_WEEK -> startTime = endTime.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
                // 本月
                case THIS_MONTH -> startTime = endTime.withDayOfMonth(1);
                // 3月前
                case THREE_MONTHS_AGO -> startTime = endTime.minusMonths(3);
                // 半年前
                case SIX_MONTHS_AGO -> startTime = endTime.minusMonths(6);
                // 一年前
                case ONE_YEAR_AGO -> startTime = endTime.minusMonths(12);
            }
        }

        return startTime;
    }

}
