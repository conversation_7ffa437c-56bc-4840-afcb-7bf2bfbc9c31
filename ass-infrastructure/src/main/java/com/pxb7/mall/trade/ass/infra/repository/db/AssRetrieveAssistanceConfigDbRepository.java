package com.pxb7.mall.trade.ass.infra.repository.db;


import java.util.List;
import java.util.Objects;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.pxb7.mall.trade.ass.infra.model.AssRetrieveAssistanceConfigReqPO;
import com.pxb7.mall.trade.ass.infra.repository.db.mapper.AssRetrieveAssistanceConfigMapper;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.AssRetrieveAssistanceConfig;
import com.pxb7.mall.trade.ass.infra.repository.db.AssRetrieveAssistanceConfigRepository;

/**
 * 找回协助类型配置表(AssRetrieveAssistanceConfig)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-30 13:45:25
 */
@Slf4j
@Repository
public class AssRetrieveAssistanceConfigDbRepository extends ServiceImpl<AssRetrieveAssistanceConfigMapper, AssRetrieveAssistanceConfig> implements AssRetrieveAssistanceConfigRepository {

    @Override
    public boolean insert(AssRetrieveAssistanceConfigReqPO.AddPO param) {
        AssRetrieveAssistanceConfig entity = new AssRetrieveAssistanceConfig();
        entity.setAssistanceConfigId(param.getAssistanceConfigId());
        entity.setAssistanceDesc(param.getAssistanceDesc());
        entity.setUserDesc(param.getUserDesc());
        entity.setSort(param.getSort());
        entity.setCreateUserId(param.getCreateUserId());
        entity.setUpdateUserId(param.getUpdateUserId());
        return this.save(entity);
    }

    @Override
    public boolean update(AssRetrieveAssistanceConfigReqPO.UpdatePO param) {
        LambdaUpdateWrapper<AssRetrieveAssistanceConfig> updateWrapper = new LambdaUpdateWrapper<>();
        //where
        updateWrapper.eq(AssRetrieveAssistanceConfig::getId, param.getId());
        //set
        if (StringUtils.isNotBlank(param.getAssistanceConfigId())) {
            updateWrapper.set(AssRetrieveAssistanceConfig::getAssistanceConfigId, param.getAssistanceConfigId());
        }
        if (StringUtils.isNotBlank(param.getAssistanceDesc())) {
            updateWrapper.set(AssRetrieveAssistanceConfig::getAssistanceDesc, param.getAssistanceDesc());
        }
        if (StringUtils.isNotBlank(param.getUserDesc())) {
            updateWrapper.set(AssRetrieveAssistanceConfig::getUserDesc, param.getUserDesc());
        }
        if (Objects.nonNull(param.getSort())) {
            updateWrapper.set(AssRetrieveAssistanceConfig::getSort, param.getSort());
        }
        if (StringUtils.isNotBlank(param.getCreateUserId())) {
            updateWrapper.set(AssRetrieveAssistanceConfig::getCreateUserId, param.getCreateUserId());
        }
        if (StringUtils.isNotBlank(param.getUpdateUserId())) {
            updateWrapper.set(AssRetrieveAssistanceConfig::getUpdateUserId, param.getUpdateUserId());
        }
        return this.update(updateWrapper);
    }

    @Override
    public boolean deleteById(AssRetrieveAssistanceConfigReqPO.DelPO param) {
        return this.removeById(param.getId());
    }

    @Override
    public AssRetrieveAssistanceConfig findById(Long id) {
        return this.getById(id);
    }

    @Override
    public List<AssRetrieveAssistanceConfig> list(AssRetrieveAssistanceConfigReqPO.SearchPO param) {
        LambdaQueryWrapper<AssRetrieveAssistanceConfig> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(param.getAssistanceConfigId())) {
            queryWrapper.eq(AssRetrieveAssistanceConfig::getAssistanceConfigId, param.getAssistanceConfigId());
        }
        if (StringUtils.isNotBlank(param.getAssistanceDesc())) {
            queryWrapper.eq(AssRetrieveAssistanceConfig::getAssistanceDesc, param.getAssistanceDesc());
        }
        if (StringUtils.isNotBlank(param.getUserDesc())) {
            queryWrapper.eq(AssRetrieveAssistanceConfig::getUserDesc, param.getUserDesc());
        }
        if (Objects.nonNull(param.getSort())) {
            queryWrapper.eq(AssRetrieveAssistanceConfig::getSort, param.getSort());
        }
        if (StringUtils.isNotBlank(param.getCreateUserId())) {
            queryWrapper.eq(AssRetrieveAssistanceConfig::getCreateUserId, param.getCreateUserId());
        }
        if (StringUtils.isNotBlank(param.getUpdateUserId())) {
            queryWrapper.eq(AssRetrieveAssistanceConfig::getUpdateUserId, param.getUpdateUserId());
        }
        // 排序
        queryWrapper.orderByAsc(AssRetrieveAssistanceConfig::getSort);
        return this.list(queryWrapper);
    }

    @Override
    public Page<AssRetrieveAssistanceConfig> page(AssRetrieveAssistanceConfigReqPO.PagePO param) {
        LambdaQueryWrapper<AssRetrieveAssistanceConfig> queryWrapper = new LambdaQueryWrapper<>();
        // 返回分页查询结果
        if (StringUtils.isNotBlank(param.getAssistanceConfigId())) {
            queryWrapper.eq(AssRetrieveAssistanceConfig::getAssistanceConfigId, param.getAssistanceConfigId());
        }
        if (StringUtils.isNotBlank(param.getAssistanceDesc())) {
            queryWrapper.eq(AssRetrieveAssistanceConfig::getAssistanceDesc, param.getAssistanceDesc());
        }
        if (StringUtils.isNotBlank(param.getUserDesc())) {
            queryWrapper.eq(AssRetrieveAssistanceConfig::getUserDesc, param.getUserDesc());
        }
        if (Objects.nonNull(param.getSort())) {
            queryWrapper.eq(AssRetrieveAssistanceConfig::getSort, param.getSort());
        }
        if (StringUtils.isNotBlank(param.getCreateUserId())) {
            queryWrapper.eq(AssRetrieveAssistanceConfig::getCreateUserId, param.getCreateUserId());
        }
        if (StringUtils.isNotBlank(param.getUpdateUserId())) {
            queryWrapper.eq(AssRetrieveAssistanceConfig::getUpdateUserId, param.getUpdateUserId());
        }
        return this.page(new Page<>(param.getPageIndex(), param.getPageSize()), queryWrapper);
    }
}
