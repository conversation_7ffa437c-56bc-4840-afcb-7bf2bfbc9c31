package com.pxb7.mall.trade.ass.infra.repository.db.entity;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.*;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 售后证据材料
 *
 * <AUTHOR>
 **/
@Getter
@Setter
@ToString
@Accessors(chain = true)
@TableName(value = "ass_proof_material")
public class AssProofMaterial implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 自增id
     */
    @TableField(value = "id")
    private Long id;

    /**
     * 主键id
     */
    @TableId(value = "proof_id", type = IdType.INPUT)
    private String proofId;

    /**
     * 说明
     */
    @TableField(value = "`name`")
    private String name;

    /**
     * 链接
     */
    @TableField(value = "link_url")
    private String linkUrl;

    /**
     * 类型1找回2纠纷
     */
    @TableField(value = "ass_type")
    private Integer assType;

    /**
     * 售后工单id
     */
    @TableField(value = "work_order_id")
    private String workOrderId;

    /**
     * 上传到阿里后的文件id
     */
    @TableField(value = "file_id")
    private String fileId;

    /**
     * 创建用户ID
     */
    @TableField(value = "create_user_id")
    private String createUserId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * 更新人ID
     */
    @TableField(value = "update_user_id")
    private String updateUserId;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", update="now()")
    private LocalDateTime updateTime;

    /**
     * 删除标记
     */
    @TableLogic
    @TableField(value = "is_deleted")
    private Boolean deleted;

}