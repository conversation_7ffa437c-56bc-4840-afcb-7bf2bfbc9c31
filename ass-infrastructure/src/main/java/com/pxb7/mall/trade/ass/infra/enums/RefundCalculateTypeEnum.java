package com.pxb7.mall.trade.ass.infra.enums;

import java.util.Arrays;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 整单退款类型
 */
@Getter
@AllArgsConstructor
public enum RefundCalculateTypeEnum {

    BY_PRODUCT_AMOUNT(0, "退商品差价"),

    BY_INPUT_AMOUNT(1, "手动输入金额");

    private final Integer value;

    private final String label;

    public static String getLabel(Integer value) {
        return Arrays.stream(RefundCalculateTypeEnum.values()).filter(e -> e.getValue().equals(value))
            .map(RefundCalculateTypeEnum::getLabel).findAny().orElse(null);
    }
}
