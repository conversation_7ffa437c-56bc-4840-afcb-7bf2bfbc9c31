package com.pxb7.mall.trade.ass.infra.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 功能描述:站内信跳转链接
 * 作者：白春韬
 * 创建日期：2025/08/05
 * 公司名称：金华博淳网络科技有限公司
 * 域名： www.pxb7.com
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class InternalMsgRedirectReqPO {
    /**
     * 跳转链接
     */
    private String redirectUrl;
    /**
     * 客户端类型 0pc,1Android,2h5,3hormone
     */
    private int clientType;

}
