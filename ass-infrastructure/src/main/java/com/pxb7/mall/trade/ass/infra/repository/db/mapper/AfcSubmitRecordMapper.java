package com.pxb7.mall.trade.ass.infra.repository.db.mapper;

import com.pxb7.mall.trade.ass.infra.repository.db.entity.AfcSubmitRecord;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 售后提交记录(AfcSubmitRecord)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-04-11 13:54:04
 */
@Mapper
public interface AfcSubmitRecordMapper extends BaseMapper<AfcSubmitRecord> {
    /**
     * 批量新增数据
     *
     * @param entities List<AfcSubmitRecord> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<AfcSubmitRecord> entities);

}

