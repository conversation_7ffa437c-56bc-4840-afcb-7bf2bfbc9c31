package com.pxb7.mall.trade.ass.infra.config.dubbo;

import com.alibaba.cola.exception.BaseException;
import org.apache.dubbo.common.constants.CommonConstants;
import org.apache.dubbo.common.extension.Activate;
import org.apache.dubbo.rpc.*;
import org.apache.dubbo.rpc.service.GenericService;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.cola.exception.BizException;
import com.alibaba.cola.exception.SysException;
import com.pxb7.mall.response.PxPageResponse;
import com.pxb7.mall.response.PxResponse;

import lombok.extern.slf4j.Slf4j;

/**
 * 自定义异常过滤器，让dubbo放行自定义异常
 */
@Slf4j
@Activate(group = {CommonConstants.PROVIDER}, order = 99)
public class CustomExceptionFilter implements Filter {

    @Override
    public Result invoke(Invoker<?> invoker, Invocation invocation) throws RpcException {
        // 获取rpcResult
        Result result = invoker.invoke(invocation);
        // 发生异常
        if (result.hasException() && invoker.getInterface() != GenericService.class) {
            // 获取抛出的异常
            Throwable exception = result.getException();
            Class<?> returnType = null;
            try {
                returnType = invoker.getInterface()
                    .getMethod(invocation.getMethodName(), invocation.getParameterTypes()).getReturnType();
            } catch (NoSuchMethodException e) {
                log.error("[ass dubbo error] CustomExceptionFilter error", e);
                throw new RuntimeException(e);
            }
            // 如果是自定义的异常，则返回异常code和message
            if (exception instanceof BizException || exception instanceof SysException) {
                log.error("[ass dubbo error] dubbo business exception occurred", exception);
                return AsyncRpcResult
                    .newDefaultAsyncResult(buildFailureResponse(returnType, ((BaseException) exception).getErrCode(), exception.getMessage()), invocation);
            }
            // 未知异常，返回系统异常code和message
            log.error("[ass dubbo error]: unknown exception occurred", exception);
            return AsyncRpcResult.newDefaultAsyncResult(buildFailureResponse(returnType, "500", "系统异常"), invocation);
        }
        return result;
    }

    /**
     * 根据方法返回类型构建不同的失败响应对象
     *
     * @param returnType 方法返回类型
     * @param code 错误代码
     * @param message 错误消息
     * @return 返回对应的失败响应对象
     */
    private Response buildFailureResponse(Class<?> returnType, String code, String message) {
        if (PageResponse.class.isAssignableFrom(returnType)) {
            return PageResponse.buildFailure(code, message);

        } else if (SingleResponse.class.isAssignableFrom(returnType)) {
            return SingleResponse.buildFailure(code, message);

        } else if (MultiResponse.class.isAssignableFrom(returnType)) {
            return MultiResponse.buildFailure(code, message);

        } else if (PxResponse.class.isAssignableFrom(returnType)) {
            return PxResponse.buildFailure(code, message);

        } else if (PxPageResponse.class.isAssignableFrom(returnType)) {
            return PxPageResponse.buildFailure(code, message);
        }
        // 默认返回 Response 类型
        return Response.buildFailure(code, message);
    }
}
