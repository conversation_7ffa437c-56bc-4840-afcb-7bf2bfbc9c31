package com.pxb7.mall.trade.ass.infra.util;

import com.alibaba.cola.exception.SysException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;


@Slf4j
public class PxTransactionUtils {

    public static Boolean inTransaction() {

        return TransactionSynchronizationManager.isActualTransactionActive();
    }

    // 先开锁再开事务, 不然会出问题
    public static void shouldNotInTransaction() {
        if (inTransaction()) {

            String transactionName = TransactionSynchronizationManager.getCurrentTransactionName();

            log.error("不能先开事务再加锁, 当前事务名称: {}", transactionName);

            throw new SysException("tra522", "异常操作流程");
        }

    }

    public static void execAfterCommit(Runnable runnable) {
        if (PxTransactionUtils.inTransaction()) {
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    runnable.run();
                }
            });
        } else {
            runnable.run();
        }
    }

}
