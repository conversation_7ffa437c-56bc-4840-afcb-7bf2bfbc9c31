package com.pxb7.mall.trade.ass.infra.repository.db;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import com.pxb7.mall.trade.order.client.dto.ErrorCode;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.OrderItem;
import com.pxb7.mall.trade.ass.infra.repository.db.mapper.OrderItemMapper;
import com.pxb7.mall.trade.order.client.enums.extPayment.ExtVoucherStatusEnum;
import com.pxb7.mall.trade.order.client.enums.order.OrderItemStatusEnum;
import com.pxb7.mall.trade.order.client.enums.order.ReceiptVoucherStatusEnum;
import com.pxb7.mall.trade.order.client.enums.order.RefundStatusEnum;

import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;

/**
 * 订单行(OrderItem)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-07-22 20:59:36
 */
@Slf4j
@Repository
public class OrderItemDbRepository extends ServiceImpl<OrderItemMapper, OrderItem> implements OrderItemRepository {

    @Override
    public boolean startRefund(String orderItemId) {
        return this.lambdaUpdate().eq(OrderItem::getOrderItemId, orderItemId)
            .notIn(OrderItem::getRefundStatus,
                Arrays.asList(RefundStatusEnum.WAIT_APPLY.getValue(), RefundStatusEnum.APPLYING.getValue()))
            .notIn(OrderItem::getPayoutStatus,
                Arrays.asList(ExtVoucherStatusEnum.CREATED.getValue(), ExtVoucherStatusEnum.PROCESSING.getValue()))
            .notIn(OrderItem::getReceiptStatus,
                Arrays.asList(ReceiptVoucherStatusEnum.PENDING.getValue(),
                    ReceiptVoucherStatusEnum.IN_PROGRESS.getValue()))
            .set(OrderItem::getRefundStatus, RefundStatusEnum.WAIT_APPLY.getValue()).update();
    }

    @Override
    public boolean updateRefundStatus(String orderItemId, Integer fromStatus, Integer toStatus) {
        return this.lambdaUpdate().eq(OrderItem::getOrderItemId, orderItemId).eq(OrderItem::getRefundStatus, fromStatus)
            .set(OrderItem::getRefundStatus, toStatus).update();
    }

    /**
     * 买家待办
     * 
     * @param orderItemId
     * @return
     */
    @Override
    public OrderItem getOrderItem(String orderItemId) {
        return this.lambdaQuery().eq(OrderItem::getOrderItemId, orderItemId).one();
    }

    @Override
    public boolean sinceritySellStartRefund(String orderItemId) {
        return this.lambdaUpdate().eq(OrderItem::getOrderItemId, orderItemId)
                .notIn(OrderItem::getRefundStatus,
                        Arrays.asList(RefundStatusEnum.WAIT_APPLY.getValue(), RefundStatusEnum.APPLYING.getValue()))
                .notIn(OrderItem::getReceiptStatus,
                        Arrays.asList(ReceiptVoucherStatusEnum.PENDING.getValue(),
                                ReceiptVoucherStatusEnum.IN_PROGRESS.getValue()))
                .set(OrderItem::getRefundStatus, RefundStatusEnum.APPLYING.getValue()).update();
    }

    @Override
    public boolean updateRefundStatusToRefunding(String orderItemId) {
        return this.lambdaUpdate()
                .eq(OrderItem::getOrderItemId, orderItemId)
                .set(OrderItem::getRefundStatus, RefundStatusEnum.APPLYING.getValue())
                .update();
    }

    @Override
    public List<OrderItem> getOrderItemList(List<String> orderItemIds) {
        if (CollectionUtils.isEmpty(orderItemIds)) {
            return Collections.EMPTY_LIST;
        }
        return this.lambdaQuery().in(OrderItem::getOrderItemId, orderItemIds).list();
    }
    // @Override
    // public OrderItem checkOrderItem(String orderItemId) {
    // OrderItem orderItem = this.getOrderItem(orderItemId);
    // if (orderItem == null) {
    // throw new BusinessException(ErrorCode.ORDER_INFO_ERROR);
    // }
    // return orderItem;
    // }

    @Override
    public List<OrderItem> listByBuyerIdAndIds(String buyerId, List<String> itemIds) {
        return this.lambdaQuery().eq(OrderItem::getBuyerId, buyerId)
            .eq(OrderItem::getOrderItemStatus, OrderItemStatusEnum.DEAL_SUCCESS.getValue())
            .in(OrderItem::getOrderItemId, itemIds).list();
    }

    /**
     * 根据主订单号查询子订单列表
     * 
     * @param orderId
     * @return
     */
    @Override
    public List<OrderItem> getOrderItemListByOrderId(String orderId, String orderItemId) {
        return this.lambdaQuery().eq(OrderItem::getDeleted, false).eq(OrderItem::getOrderId, orderId)
            .eq(OrderItem::getOrderItemId, orderItemId)
            .ne(OrderItem::getOrderItemStatus, OrderItemStatusEnum.DEAL_CANCEL.getValue()).list();
    }

    /**
     * 根据主订单号查询子订单列表
     * 
     * @param orderId
     * @return
     */
    @Override
    public List<OrderItem> getOrderItemListByOrderId(String orderId) {
        return this.lambdaQuery().eq(OrderItem::getDeleted, false).eq(OrderItem::getOrderId, orderId).list();
    }


    /**
     * 判断单据是否存在进行中的收/退/放
     * @param orderItemId 单据ID
     * @return 判断结果
     */
    @Override
    public boolean existsProcessingTransfer(String orderItemId) {
        OrderItem orderItem = getOrderItem(orderItemId);
        Assert.isTrue(orderItem != null, ErrorCode.ORDER_NOT_EXIST_ERROR.getErrDesc());

        Integer receiptStatus = orderItem.getReceiptStatus();
        Integer refundStatus = orderItem.getRefundStatus();
        Integer payoutStatus = orderItem.getPayoutStatus();

        return ReceiptVoucherStatusEnum.PENDING.eq(receiptStatus)
                || ReceiptVoucherStatusEnum.IN_PROGRESS.eq(receiptStatus)
                || RefundStatusEnum.WAIT_APPLY.getValue().equals(refundStatus)
                || RefundStatusEnum.APPLYING.getValue().equals(refundStatus)
                || ExtVoucherStatusEnum.CREATED.getValue().equals(payoutStatus)
                || ExtVoucherStatusEnum.PROCESSING.getValue().equals(payoutStatus);
    }
    @Override
    public Boolean updateReceiptVoucherStatus(String orderItemId, Integer receiptVoucherStatus) {
        return this.lambdaUpdate().eq(OrderItem::getOrderItemId, orderItemId)
                .set(OrderItem::getReceiptStatus, receiptVoucherStatus).update();
    }

}
