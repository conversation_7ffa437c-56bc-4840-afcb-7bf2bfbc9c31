package com.pxb7.mall.trade.ass.infra.repository.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 退款单(RefundVoucher)实体类
 *
 * <AUTHOR>
 * @since 2024-11-20 16:24:06
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
@TableName(value = "refund_voucher")
public class RefundVoucher implements Serializable {
    private static final long serialVersionUID = 844059202978150510L;
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 订单行id
     */
    @TableField(value = "order_item_id")
    private String orderItemId;
    /**
     * 退款单id
     */
    @TableField(value = "refund_voucher_id")
    private String refundVoucherId;
    /**
     * 商品类型: 1账号 2充值 3金币 4装备 5初始号 20诚心卖曝光劵 21诚心卖服务
     */
    @TableField(value = "product_type")
    private Integer productType;
    /**
     * 退款状态 1待审核, 2退款中, 3退款成功, 4退款失败, 5退款关闭
     */
    @TableField(value = "refund_status")
    private Integer refundStatus;
    /**
     * 退款原因id
     */
    @TableField(value = "refund_reason_id")
    private String refundReasonId;
    /**
     * 退款原因
     */
    @TableField(value = "refund_reason")
    private String refundReason;
    /**
     * 退款类型 1 整单退款 2.退商品差价
     */
    @TableField(value = "is_whole_refund")
    private Integer wholeRefund;
    /**
     * 退款类型 1线上原路返回 2线下打款 3 挂账
     */
    @TableField(value = "refund_type")
    private Integer refundType;
    /**
     * 使用的对应收款单id,线上退款时绑定
     */
    @TableField(value = "receipt_voucher_id")
    private String receiptVoucherId;
    /**
     * 对应支付单id，审核通过后绑定
     */
    @TableField(value = "payment_id")
    private String paymentId;
    /**
     * 退款金额
     */
    @TableField(value = "refund_amount")
    private Long refundAmount;
    /**
     * 实际退款金额，退款完成后写入
     */
    @TableField(value = "actual_refund_amount")
    private Long actualRefundAmount;
    /**
     * 提交人
     */
    @TableField(value = "submitter")
    private String submitter;
    /**
     * 1-用户,2-客服
     */
    @TableField(value = "submitter_type")
    private Integer submitterType;
    /**
     * 1-用户,2-客服,3-财务
     */
    @TableField(value = "close_type")
    private Integer closeType;
    /**
     * 客服拒绝原因
     */
    @TableField(value = "reject_reason")
    private String rejectReason;
    /**
     * 申请时间
     */
    @TableField(value = "apply_time")
    private LocalDateTime applyTime;
    /**
     * 退款完成时间
     */
    @TableField(value = "finish_time")
    private LocalDateTime finishTime;
    /**
     * 1支付宝 2微信 3银行卡
     */
    @TableField(value = "refund_channel")
    private Integer refundChannel;
    /**
     * 买家收款账户
     */
    @TableField(value = "refund_account")
    private String refundAccount;
    /**
     * 买家姓名
     */
    @TableField(value = "buyer_name")
    private String buyerName;
    /**
     * 审核状态 1待客服审核,2待提交打款信息(线下打款才有),3待财务审核,4审核成功,5审核失败
     */
    @TableField(value = "audit_status")
    private Integer auditStatus;
    /**
     * 财务审核人用户id
     */
    @TableField(value = "audit_user")
    private String auditUser;
    /**
     * 财务审核人审核备注
     */
    @TableField(value = "audit_remark")
    private String auditRemark;
    /**
     * 财务审核截图
     */
    @TableField(value = "audit_img")
    private String auditImg;
    /**
     * 打款人用户id
     */
    @TableField(value = "execute_user")
    private String executeUser;
    /**
     * 打款账号名称
     */
    @TableField(value = "account_name")
    private String accountName;
    /**
     * 打款账号
     */
    @TableField(value = "company_account")
    private String companyAccount;
    /**
     * 扩展信息
     */
    @TableField(value = "ext_info")
    private String extInfo;
    /**
     * 创建人id
     */
    @TableField(value = "create_user_id")
    private String createUserId;
    /**
     * 更新人id
     */
    @TableField(value = "update_user_id")
    private String updateUserId;
    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    @TableField(value = "update_time", update = "now()")
    private LocalDateTime updateTime;
    /**
     * 是否删除
     */
    @TableLogic
    @TableField(value = "is_deleted")
    private Boolean deleted;

    /**
     * profile
     */
    @TableField(value = "env_profile", fill = FieldFill.INSERT)
    private Integer envProfile;
}
