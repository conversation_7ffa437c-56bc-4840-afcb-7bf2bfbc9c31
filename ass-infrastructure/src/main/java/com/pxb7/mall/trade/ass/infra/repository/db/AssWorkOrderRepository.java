package com.pxb7.mall.trade.ass.infra.repository.db;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pxb7.mall.trade.ass.infra.model.AssWorkOrderReqPO;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.AssWorkOrder;

/**
 * 售后工单(AssWorkOrder)表服务接口
 *
 * <AUTHOR>
 * @since 2025-07-31 11:52:41
 */
public interface AssWorkOrderRepository extends IService<AssWorkOrder> {

    boolean insert(AssWorkOrderReqPO.AddPO param);

    boolean update(AssWorkOrderReqPO.UpdatePO param);

    boolean deleteById(AssWorkOrderReqPO.DelPO param);

    AssWorkOrder findById(Long id);

    List<AssWorkOrder> list(AssWorkOrderReqPO.SearchPO param);

    Page<AssWorkOrder> page(AssWorkOrderReqPO.PagePO param);

    AssWorkOrder findByOrderItemId(String orderItemId);

    AssWorkOrder findByScheduleId(String scheduleId);


    boolean updateWithOpt(Long id, Integer originStatus, Integer targetStatus, String assStatusMemo,Integer readFlag);

    boolean updateReadFlag(String workOrderId, Integer readFlag);


    boolean updateRelOrderIdAndFlag(String workOrderId, Integer readFlag,String relOrderId);

}

