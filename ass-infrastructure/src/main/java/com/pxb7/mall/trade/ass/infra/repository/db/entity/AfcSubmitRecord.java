package com.pxb7.mall.trade.ass.infra.repository.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 售后提交记录(AfcSubmitRecord)实体类
 *
 * <AUTHOR>
 * @since 2025-04-11 13:54:04
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "afc_submit_record")
public class AfcSubmitRecord implements Serializable {
    private static final long serialVersionUID = -97570488744244677L;
    /**
     * 自增id
     */
    @TableField(value = "id")
    private Long id;
    /**
     * 提交记录id
     */
    @TableId(value = "submit_record_id", type = IdType.INPUT)
    private String submitRecordId;
    /**
     * 订单id
     */
    @TableField(value = "order_item_id")
    private String orderItemId;
    /**
     * 房间id
     */
    @TableField(value = "room_id")
    private String roomId;
    /**
     * 游戏ID
     */
    @TableField(value = "game_id")
    private String gameId;
    /**
     * 游戏名称
     */
    @TableField(value = "game_name")
    private String gameName;
    /**
     * 商品id
     */
    @TableField(value = "product_id")
    private String productId;
    /**
     * 商品编码
     */
    @TableField(value = "product_unique_no")
    private String productUniqueNo;
    /**
     * 售后类型 1找回 2：纠纷
     */
    @TableField(value = "afc_type")
    private Integer afcType;
    /**
     * 售后问题id
     */
    @TableField(value = "question_id")
    private String questionId;
    /**
     * 备注(其他问题，用户输入的内容)
     */
    @TableField(value = "remark")
    private String remark;
    /**
     * 用户昵称
     */
    @TableField(value = "nickname")
    private String nickname;
    /**
     * 手机号
     */
    @TableField(value = "phone")
    private String phone;
    /**
     * 创建人ID
     */
    @TableField(value = "create_user_id")
    private String createUserId;
    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;
    /**
     * 更新人id
     */
    @TableField(value = "update_user_id")
    private String updateUserId;
    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;
    /**
     * 是否删除 1:已删除 0:未删除
     */
    @TableLogic
    @TableField(value = "is_deleted")
    private Boolean deleted;

}

