package com.pxb7.mall.trade.ass.infra.repository.es.impl;

import java.util.ArrayList;
import java.util.List;

import org.apache.dubbo.common.utils.CollectionUtils;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.elasticsearch.client.elc.ElasticsearchTemplate;
import org.springframework.data.elasticsearch.client.elc.NativeQuery;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.stereotype.Service;

import com.pxb7.mall.trade.ass.infra.repository.es.entity.OrderItemAggregation;

import co.elastic.clients.elasticsearch._types.FieldValue;
import co.elastic.clients.elasticsearch._types.query_dsl.Query;
import co.elastic.clients.elasticsearch._types.query_dsl.QueryBuilders;
import co.elastic.clients.elasticsearch._types.query_dsl.TermQuery;
import co.elastic.clients.elasticsearch._types.query_dsl.TermsQuery;
import jakarta.annotation.Resource;

@Service
public class EsOrderItemSearchServiceI implements EsOrderItemSearchService {
    @Resource
    private ElasticsearchTemplate elasticsearchTemplate;

    /**
     * 根据房间id查询订单
     * 
     * @param roomId
     */
    @Override
    public OrderItemAggregation findOneByRoomId(String roomId) {
        TermQuery groupRoomTerm = QueryBuilders.term().field("delivery_room_id").value(roomId).build();

        NativeQuery nativeQuery = NativeQuery.builder()
            .withQuery(QueryBuilders.bool().must(groupRoomTerm._toQuery()).build()._toQuery())
                // 一个房间可能会有多个订单，取最新的一个
                .withSort(Sort.by(Sort.Order.desc("create_time")))
                .build();

        SearchHit<OrderItemAggregation> searchHit =
            elasticsearchTemplate.searchOne(nativeQuery, OrderItemAggregation.class);
        if (searchHit != null) {
            return searchHit.getContent();
        } else {
            return new OrderItemAggregation();
        }
    }

    @Override
    public List<SearchHit<OrderItemAggregation>> orderItemSearchByOrderItemIds(List<String> orderItemIds) {
        List<Query> queryList = new ArrayList<>();

        // 订单编号
        if (CollectionUtils.isNotEmpty(orderItemIds)) {
            List<FieldValue> values = orderItemIds.stream().map(FieldValue::of).toList();
            TermsQuery refundStatus =
                QueryBuilders.terms().field("order_item_id").terms(builder -> builder.value(values)).build();
            queryList.add(refundStatus._toQuery());
        }

        // 排序
        Sort sort = Sort.by(Sort.Order.desc("create_time"));

        NativeQuery nativeQuery =
            NativeQuery.builder().withQuery(QueryBuilders.bool().must(queryList).build()._toQuery())
                .withPageable(PageRequest.of(0, orderItemIds.size())).withSort(sort).build();

        SearchHits<OrderItemAggregation> search = elasticsearchTemplate.search(nativeQuery, OrderItemAggregation.class);
        return search.getSearchHits();
    }
}
