package com.pxb7.mall.trade.ass.infra.config.nacos;

import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;

import javax.annotation.PostConstruct;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.beans.factory.config.YamlMapFactoryBean;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import com.alibaba.cloud.nacos.NacosConfigManager;
import com.alibaba.nacos.api.config.listener.AbstractListener;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class NacosYamlConfigManager {

    @Value("${spring.profiles.active}")
    private String springActive;

    @Value("${spring.application.name}")
    private String applicationName;

    @Value("${spring.cloud.nacos.config.file-extension}")
    private String extension;

    public String getDataId() {
        return applicationName + "-" + springActive + "." + extension;
    }

    protected String getGroupId() {
        return "DEFAULT_GROUP";
    }

    @Resource
    private NacosConfigManager nacosConfigManager;

    @Autowired(required = false)
    private List<NacosYamlConfigListener> nacosYamlConfigListeners;

    @PostConstruct
    public void init() throws Exception {
        String dataId = getDataId();
        String groupId = getGroupId();
        // 启动时先拉取
        boolean flag =
            refreshConfig(dataId, groupId, nacosConfigManager.getConfigService().getConfig(dataId, groupId, 5000));
        if (!flag) {
            throw new RuntimeException(String.format("拉取nacos配置失败,dataId:%s,groupId:%s", dataId, groupId));
        }
        // 然后注册监听器变化时拉取
        nacosConfigManager.getConfigService().addListener(dataId, groupId, new AbstractListener() {
            @Override
            public void receiveConfigInfo(String configInfo) {
                refreshConfig(dataId, groupId, configInfo);
            }
        });
    }

    private boolean refreshConfig(String dataId, String groupId, String configInfo) {
        try {
            YamlMapFactoryBean mapFactoryBean = new YamlMapFactoryBean();
            mapFactoryBean.setResources(new ByteArrayResource(configInfo.getBytes(StandardCharsets.UTF_8)));
            notifyConfigListener(mapFactoryBean.getObject());
            return true;
        } catch (Exception e) {
            log.warn("获取nacos配置异常,dataId:{},groupId:{}", dataId, groupId, e);
            return false;
        }
    }

    private void notifyConfigListener(Map<String, Object> yamlMap) {
        if (!CollectionUtils.isEmpty(nacosYamlConfigListeners)) {
            nacosYamlConfigListeners.forEach(listener -> {
                String path = listener.configPath();
                if (StringUtils.hasLength(path)) {
                    Object configObject = yamlMap.get(listener.configPath());
                    if (configObject != null) {
                        log.info("refresh yaml config {}:{}", path, configObject);
                        try {
                            listener.onRefresh(configObject);
                        } catch (Exception e) {
                            log.error("refresh yaml config failed {}:{}", path, configObject, e);
                        }
                    }
                }
            });
        } else {
            log.info("nacosYamlConfigListeners is empty");
        }
    }
}
