package com.pxb7.mall.trade.ass.infra.repository.db.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.RefundVoucherDetail;

/**
 * 退款单金额详情(RefundVoucherDetail)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-09-25 15:02:33
 */
@Mapper
public interface RefundVoucherDetailMapper extends BaseMapper<RefundVoucherDetail> {
    /**
     * 批量新增数据
     *
     * @param entities List<RefundVoucherDetail> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<RefundVoucherDetail> entities);

}
