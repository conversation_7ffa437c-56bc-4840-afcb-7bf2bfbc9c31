package com.pxb7.mall.trade.ass.infra.remote.model.outpayparam.request;

import java.util.List;

import lombok.Data;
import lombok.ToString;

@Data
@ToString
public class LianLianRefundPO {
    /**
     * 时间戳
     */
    private String timestamp;

    /**
     * 商户号
     */
    private String oid_partner;

    /**
     * 原交易付款方user_id
     */
    private String user_id;

    /**
     * 交易结果异步通知接收地址，建议HTTPS协议
     */
    private String notify_url;

    /**
     * 原商户订单信息originalOrderInfo
     */
    private RefundPaymentInfoPO originalOrderInfo;

    /**
     * 退款订单信息refundOrderInfo
     */
    private RefundInfoPO refundOrderInfo;

    /**
     * 原收款方退款信息pyeeRefundInfos
     */
    private PayeeRefundInfosPO pyeeRefundInfos;

    /**
     * 原付款方式退款规则信息refundMethods
     */
    private List<WxAlipayPayMethodPO> refundMethods;

    @Data
    public static class RefundInfoPO {

        /**
         * 退款订单号
         */
        private String refund_seqno;

        /**
         * orderId
         */
        private String order_info;

        /**
         * 退款订单时间
         */
        private String refund_time;

        /**
         * 退款总金额
         */
        private String refund_amount;
    }

    @Data
    public static class RefundPaymentInfoPO {

        /**
         * 原支付交易商户系统唯一交易流水号
         */
        private String txn_seqno;

        /**
         * 订单总金额，单位为元，精确到小数点后两位
         */
        private String total_amount;

    }

    @Data
    public static class WxAlipayPayMethodPO {

        private String method;

        private String amount;
    }

    @Data
    public static class PayeeRefundInfosPO {

        /**
         * 收款方id
         */
        private String payee_id;

        /**
         * 原收款方类型。 用户：USER 平台商户：MERCHANT
         */
        private String payee_type;

        /**
         * 原收款方账户类型。 用户账户：USEROWN 平台商户自有资金账户：MCHOWN 平台商户担保账户：MCHASSURE 平台商户优惠券账户：MCHCOUPON 平台商户手续费账户：MCHFEE
         */
        private String payee_accttype;

        /**
         * 退款金额
         */
        private String payee_refund_amount;

    }
}
