package com.pxb7.mall.trade.ass.infra.repository.db.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.RefundReason;

/**
 * 退款原因管理(RefundReason)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-09-25 15:02:32
 */
@Mapper
public interface RefundReasonMapper extends BaseMapper<RefundReason> {

    /**
     * 根据业务ID 获取退款原因（包括删除的）
     */
    RefundReason getOneByReasonIdIncludeDeleted(@Param("reasonId") String reasonId);

}
