package com.pxb7.mall.trade.ass.infra.repository.db.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.RefundReason;

/**
 * 退款原因管理(RefundReason)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-09-25 15:02:32
 */
@Mapper
public interface RefundReasonMapper extends BaseMapper<RefundReason> {
    /**
     * 批量新增数据
     *
     * @param entities List<RefundReason> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<RefundReason> entities);

}
