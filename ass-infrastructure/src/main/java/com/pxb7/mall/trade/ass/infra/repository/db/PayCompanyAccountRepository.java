package com.pxb7.mall.trade.ass.infra.repository.db;


import com.baomidou.mybatisplus.extension.service.IService;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.PayCompanyAccount;

/**
 * 公司账户表(PayCompanyAccount)表服务接口
 *
 * <AUTHOR>
 * @since 2024-09-27 10:27:21
 */
public interface PayCompanyAccountRepository extends IService<PayCompanyAccount> {

    PayCompanyAccount getCompanyAccountById(String payCompanyAccountId);

    PayCompanyAccount findCompanyAccountByAccount(String account);
}

