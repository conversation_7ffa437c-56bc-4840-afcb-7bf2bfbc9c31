package com.pxb7.mall.trade.ass.infra.remote.dubbo;

import cn.hutool.core.util.StrUtil;
import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.fastjson2.JSON;
import com.pxb7.mall.im.client.api.ImGroupMemberServiceI;
import com.pxb7.mall.im.client.api.ImGroupServiceI;
import com.pxb7.mall.im.client.dto.request.AddAfterSaleCustomerCareReqDTO;
import com.pxb7.mall.im.client.dto.request.GetOneAfterSaleCustomerCareReqDTO;
import com.pxb7.mall.im.client.dto.request.OderJumpGroupReqDTO;
import com.pxb7.mall.im.client.dto.response.AddGroupMemberRespDTO;
import com.pxb7.mall.trade.ass.infra.enums.ErrorCode;
import com.pxb7.mall.trade.ass.infra.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

/**
 * Copyright (C), 2002-2025, 螃蟹游戏服务网
 *
 * @fileName: ImRpcGateway.java
 * @description: im dubbo接口
 * @author: guominqiang
 * @email: <EMAIL>
 * @date: 2025/4/11 16:07
 * @history: //修改记录
 * <author> <time> <version> <desc>
 * 修改人姓名 修改时间 版本号 描述
 */

@Component
@Slf4j
public class ImRpcGateway {
    @DubboReference
    private ImGroupMemberServiceI imGroupMemberServiceI;

    @DubboReference
    private ImGroupServiceI imGroupServiceI;

    /**
     * 添加客服到群聊
     *
     * @param deliveryRoomId 交付房间id
     * @param gameId         游戏id
     * @param orderItemId    订单id
     * @param assType        售后类型 1:找回 2:纠纷
     * @return {@link AddGroupMemberRespDTO}
     */
    public AddGroupMemberRespDTO addGroupMember(String deliveryRoomId,
                                                String gameId,
                                                String orderItemId,
                                                Integer assType) {
        AddAfterSaleCustomerCareReqDTO reqDTO = new AddAfterSaleCustomerCareReqDTO();
        reqDTO.setOrderId(orderItemId);
        reqDTO.setGroupId(deliveryRoomId);
        reqDTO.setGameId(gameId);
        reqDTO.setAfterSaleType(assType);
        SingleResponse<AddGroupMemberRespDTO> singleResponse = imGroupMemberServiceI.addAfterSaleCustomerCare(reqDTO);
        if (!singleResponse.isSuccess()) {
            log.warn("Failed to add the customer service to the group，reqDTO:{}，singleResponse:{}", JSON.toJSONString(reqDTO), JSON.toJSONString(singleResponse));
            throw new BusinessException(ErrorCode.PULL_CUSTOMER_ERROR);
        }
        return singleResponse.getData();
    }

    /**
     * 跳转群聊
     *
     * @param deliveryRoomId 交付房间号
     * @param userId         登录用户id
     */
    public void orderJumpGroup(String deliveryRoomId, String userId) {
        OderJumpGroupReqDTO renewGroup = new OderJumpGroupReqDTO();
        renewGroup.setGroupId(deliveryRoomId);
        renewGroup.setLoginUserId(userId);
        try {
            imGroupServiceI.orderJumpGroup(renewGroup);
        } catch (Exception ex) {
            log.error("恢复群异常:{}", JSON.toJSONString(renewGroup), ex);
        }
    }

    /**
     * 查售后群房间客服
     *
     * @param groupId 房间ID
     * @param assType 售后类型 1-账号找回 2-交易纠纷
     * @return 客服ID
     */
    public String getOneAfterSaleCustomerCare(String groupId, Integer assType) {
        if (StrUtil.isBlank(groupId)) {
            return "";
        }
        GetOneAfterSaleCustomerCareReqDTO reqDTO = new GetOneAfterSaleCustomerCareReqDTO();
        reqDTO.setGroupId(groupId);
        reqDTO.setAfterSaleType(assType);
        SingleResponse<String> singleResponse = imGroupMemberServiceI.getOneAfterSaleCustomerCare(reqDTO);
        if (!singleResponse.isSuccess()) {
            log.warn("getOneAfterSaleCustomerCare fail，reqDTO:{}，singleResponse:{}", JSON.toJSONString(reqDTO), JSON.toJSONString(singleResponse));
            throw new BusinessException(ErrorCode.ASS_SCHEDULE_CREATE_ROOM);
        }
        return singleResponse.getData();
    }
}
