package com.pxb7.mall.trade.ass.infra.repository.db;

import java.util.List;

import com.pxb7.mall.trade.ass.infra.enums.RefundWholeEnum;
import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.RefundVoucher;
import com.pxb7.mall.trade.ass.infra.repository.db.mapper.RefundVoucherMapper;
import com.pxb7.mall.trade.order.client.enums.order.RefundStatusEnum;

import lombok.extern.slf4j.Slf4j;

/**
 * 退款单(RefundVoucher)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-09-25 15:02:33
 */
@Slf4j
@Repository
public class RefundVoucherDbRepository extends ServiceImpl<RefundVoucherMapper, RefundVoucher>
    implements RefundVoucherRepository {

    @Override
    public List<RefundVoucher> getSuccessRefund(String orderItemId) {
        return this.lambdaQuery().eq(RefundVoucher::getOrderItemId, orderItemId)
            .eq(RefundVoucher::getRefundStatus, RefundStatusEnum.SUCCESS.getValue()).list();
    }

    @Override
    public List<RefundVoucher> getItemRefundList(String orderItemId) {
        return this.lambdaQuery().eq(RefundVoucher::getOrderItemId, orderItemId)
            .orderBy(true, false, RefundVoucher::getApplyTime).list();
    }

    @Override
    public Page<RefundVoucher> getItemRefundList(List<String> orderItemId, int pageSize, int pageIndex) {
        LambdaQueryWrapper<RefundVoucher> query = new LambdaQueryWrapper<RefundVoucher>()
            .in(RefundVoucher::getOrderItemId, orderItemId).orderBy(true, false, RefundVoucher::getApplyTime);
        return this.page(new Page<>(pageIndex, pageSize), query);
    }

    @Override
    public RefundVoucher getByRefundId(String refundId) {
        return this.lambdaQuery().eq(RefundVoucher::getRefundVoucherId, refundId).one();
    }

    @Override
    public boolean updateByRefundVoucherId(RefundVoucher refundVoucher) {
        return this.lambdaUpdate().eq(RefundVoucher::getRefundVoucherId, refundVoucher.getRefundVoucherId())
            .update(refundVoucher);
    }

    @Override
    public RefundVoucher getUnderwayByItemId(String orderItemId) {
        return this.lambdaQuery().eq(RefundVoucher::getOrderItemId, orderItemId)
            .eq(RefundVoucher::getRefundStatus, RefundStatusEnum.WAIT_APPLY.getValue()).one();
    }

    @Override
    public boolean updateByRefundId(RefundVoucher refundVoucher, Integer fromAuditStatus) {
        return this.lambdaUpdate().eq(RefundVoucher::getRefundVoucherId, refundVoucher.getRefundVoucherId())
            .eq(fromAuditStatus != null, RefundVoucher::getAuditStatus, fromAuditStatus).update(refundVoucher);
    }

    @Override
    public RefundVoucher lastWholeByOrderItemId(String orderItemId) {
        return this.lambdaQuery()
                .eq(RefundVoucher::getOrderItemId, orderItemId)
                .eq(RefundVoucher::getWholeRefund, RefundWholeEnum.WHOLE.getValue())
                .eq(RefundVoucher::getRefundStatus, RefundStatusEnum.SUCCESS.getValue())
                .orderByDesc(RefundVoucher::getId)
                .last("limit 1").one();
    }
}
