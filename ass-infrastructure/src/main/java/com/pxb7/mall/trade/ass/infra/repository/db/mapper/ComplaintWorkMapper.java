package com.pxb7.mall.trade.ass.infra.repository.db.mapper;

import com.pxb7.mall.trade.ass.infra.repository.db.entity.ComplaintWork;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 客诉工单(ComplaintWork)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-09-20 17:55:40
 */
@Mapper
public interface ComplaintWorkMapper extends BaseMapper<ComplaintWork> {
    /**
     * 批量新增数据
     *
     * @param entities List<ComplaintWork> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<ComplaintWork> entities);

}

