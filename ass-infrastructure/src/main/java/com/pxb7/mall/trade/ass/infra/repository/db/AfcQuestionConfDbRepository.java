package com.pxb7.mall.trade.ass.infra.repository.db;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.AfcQuestionConf;
import com.pxb7.mall.trade.ass.infra.repository.db.mapper.AfcQuestionConfMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 售后问题配置(AfcQuestionConf)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-04-11 13:54:03
 */
@Slf4j
@Repository
public class AfcQuestionConfDbRepository extends ServiceImpl<AfcQuestionConfMapper, AfcQuestionConf> implements AfcQuestionConfRepository {

    @Override
    public List<AfcQuestionConf> findAfcQuestionConfList(String userId) {
        return lambdaQuery().orderByDesc(AfcQuestionConf::getSort)
            .orderByDesc(AfcQuestionConf::getUpdateTime)
            .list();
    }
}
