package com.pxb7.mall.trade.ass.infra.repository.db;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.AssFollowupLog;
import com.pxb7.mall.trade.ass.infra.repository.db.mapper.AssFollowupLogMapper;

import org.springframework.stereotype.Service;

/**
 * 售后工单跟进记录
 *
 * <AUTHOR>
 * @since: 2024-10-14 13:46
 **/
@Service
public class AssFollowupLogDbRepository extends ServiceImpl<AssFollowupLogMapper, AssFollowupLog> implements AssFollowupLogRepository {

}
