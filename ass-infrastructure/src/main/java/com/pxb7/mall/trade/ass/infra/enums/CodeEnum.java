package com.pxb7.mall.trade.ass.infra.enums;

/**
 * 通过code获取枚举
 */
public interface CodeEnum {
    Integer getCode();

    static <E extends Enum<E> & CodeEnum> E fromCode(Class<E> enumClass, Integer code) {
        for (E enumConstant : enumClass.getEnumConstants()) {
            if (enumConstant.getCode().equals(code)) {
                return enumConstant;
            }
        }
        throw new IllegalArgumentException("错误的枚举值: " + code);
    }
}
