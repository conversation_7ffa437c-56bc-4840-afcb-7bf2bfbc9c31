package com.pxb7.mall.trade.ass.infra.repository.db;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pxb7.mall.trade.ass.infra.model.AfcSubmitRecordReqPO;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.AfcSubmitRecord;
import com.pxb7.mall.trade.ass.infra.repository.db.mapper.AfcSubmitRecordMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.Objects;

/**
 * 售后提交记录(AfcSubmitRecord)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-04-11 13:54:04
 */
@Slf4j
@Repository
public class AfcSubmitRecordDbRepository extends ServiceImpl<AfcSubmitRecordMapper, AfcSubmitRecord> implements AfcSubmitRecordRepository {

    @Override
    public boolean insert(AfcSubmitRecordReqPO.AddPO param) {
        AfcSubmitRecord entity = new AfcSubmitRecord();
        entity.setSubmitRecordId(param.getSubmitRecordId());
        entity.setOrderItemId(param.getOrderItemId());
        entity.setRoomId(param.getRoomId());
        entity.setGameId(param.getGameId());
        entity.setGameName(param.getGameName());
        entity.setProductId(param.getProductId());
        entity.setProductUniqueNo(param.getProductUniqueNo());
        entity.setAfcType(param.getAfcType());
        entity.setQuestionId(param.getQuestionId());
        entity.setRemark(param.getRemark());
        entity.setNickname(param.getNickname());
        entity.setPhone(param.getPhone());
        entity.setCreateUserId(param.getCreateUserId());
        entity.setUpdateUserId(param.getUpdateUserId());
        return this.save(entity);
    }

    @Override
    public boolean update(AfcSubmitRecordReqPO.UpdatePO param) {
        LambdaUpdateWrapper<AfcSubmitRecord> updateWrapper = new LambdaUpdateWrapper<>();
        //where
        updateWrapper.eq(AfcSubmitRecord::getSubmitRecordId, param.getSubmitRecordId());
        //set
        if (StringUtils.isNotBlank(param.getSubmitRecordId())) {
            updateWrapper.set(AfcSubmitRecord::getSubmitRecordId, param.getSubmitRecordId());
        }
        if (StringUtils.isNotBlank(param.getOrderItemId())) {
            updateWrapper.set(AfcSubmitRecord::getOrderItemId, param.getOrderItemId());
        }
        if (StringUtils.isNotBlank(param.getRoomId())) {
            updateWrapper.set(AfcSubmitRecord::getRoomId, param.getRoomId());
        }
        if (StringUtils.isNotBlank(param.getGameId())) {
            updateWrapper.set(AfcSubmitRecord::getGameId, param.getGameId());
        }
        if (StringUtils.isNotBlank(param.getGameName())) {
            updateWrapper.set(AfcSubmitRecord::getGameName, param.getGameName());
        }
        if (StringUtils.isNotBlank(param.getProductId())) {
            updateWrapper.set(AfcSubmitRecord::getProductId, param.getProductId());
        }
        if (StringUtils.isNotBlank(param.getProductUniqueNo())) {
            updateWrapper.set(AfcSubmitRecord::getProductUniqueNo, param.getProductUniqueNo());
        }
        if (Objects.nonNull(param.getAfcType())) {
            updateWrapper.set(AfcSubmitRecord::getAfcType, param.getAfcType());
        }
        if (StringUtils.isNotBlank(param.getQuestionId())) {
            updateWrapper.set(AfcSubmitRecord::getQuestionId, param.getQuestionId());
        }
        if (StringUtils.isNotBlank(param.getRemark())) {
            updateWrapper.set(AfcSubmitRecord::getRemark, param.getRemark());
        }
        if (StringUtils.isNotBlank(param.getPhone())) {
            updateWrapper.set(AfcSubmitRecord::getPhone, param.getPhone());
        }
        if (StringUtils.isNotBlank(param.getNickname())) {
            updateWrapper.set(AfcSubmitRecord::getNickname, param.getNickname());
        }
        if (StringUtils.isNotBlank(param.getUpdateUserId())) {
            updateWrapper.set(AfcSubmitRecord::getUpdateUserId, param.getUpdateUserId());
        }
        return this.update(updateWrapper);
    }

    @Override
    public boolean deleteById(AfcSubmitRecordReqPO.DelPO param) {
        return this.removeById(param.getSubmitRecordId());
    }

    @Override
    public AfcSubmitRecord findById(Long id) {
        return this.getById(id);
    }
}
