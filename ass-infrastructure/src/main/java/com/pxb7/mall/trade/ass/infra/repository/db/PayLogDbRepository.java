package com.pxb7.mall.trade.ass.infra.repository.db;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.PayLog;
import com.pxb7.mall.trade.ass.infra.repository.db.mapper.PayLogMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 支付记录(包括线上原路退款)(PayLog)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-09-06 19:58:42
 */
@Slf4j
@Repository
public class PayLogDbRepository extends ServiceImpl<PayLogMapper, PayLog> implements PayLogRepository {

    @Override
    public List<PayLog> getListById(List<String> paymentIds) {
        return this.lambdaQuery()
            .in(PayLog::getPaymentId, paymentIds)
            .eq(PayLog::getPayStatus,3)
            .eq(PayLog::getDeleted,0)
            .list();
    }

    @Override
    public PayLog getPayLog(String refundPaymentId, Integer payStatus) {
        return this.lambdaQuery().eq(PayLog::getPaymentId, refundPaymentId)
                .eq(PayLog::getPayStatus, payStatus)
                .one();
    }

    @Override
    public PayLog getPayLogById(String payLogId) {
        return this.lambdaQuery().eq(PayLog::getPayLogId, payLogId).one();
    }
}
