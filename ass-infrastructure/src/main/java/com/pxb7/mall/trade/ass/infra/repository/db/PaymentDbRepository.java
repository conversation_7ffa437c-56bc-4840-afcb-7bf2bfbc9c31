package com.pxb7.mall.trade.ass.infra.repository.db;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pxb7.mall.trade.ass.infra.enums.PayBusinessTypeEnum;
import com.pxb7.mall.trade.ass.infra.enums.TradeStatusEnum;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.Payment;
import com.pxb7.mall.trade.ass.infra.repository.db.mapper.PaymentMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 交易中心表(Payment)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-09-03 13:43:22
 */
@Slf4j
@Repository
public class PaymentDbRepository extends ServiceImpl<PaymentMapper, Payment> implements PaymentRepository {

    @Override
    public List<Payment> getPaymentListByOrderId(String orderId) {
        return this.lambdaQuery()
                .eq(Payment::getOrderId,orderId)
                .in(Payment::getBusinessType, PayBusinessTypeEnum.PAY.getValue(),PayBusinessTypeEnum.REFUND.getValue())
                .list();
    }

    @Override
    public List<Payment> getOrderItemPaymentList(String orderItemId) {
        return this.lambdaQuery()
                .eq(Payment::getOrderItemId,orderItemId)
                .in(Payment::getBusinessType, PayBusinessTypeEnum.PAY.getValue(),PayBusinessTypeEnum.REFUND.getValue())
                .list();
    }

    @Override
    public List<Payment> getListById(List<String> paymentIds) {
        return this.lambdaQuery().in(Payment::getPaymentId,paymentIds)
            .in(Payment::getBusinessType, PayBusinessTypeEnum.PAY.getValue(),PayBusinessTypeEnum.REFUND.getValue())
            .eq(Payment::getDeleted,0)
            .list();
    }

    @Override
    public Boolean updatePaymentStatus(Payment payment) {
        return this.lambdaUpdate().eq(Payment::getPaymentId,payment.getPaymentId())
                .update(payment);
    }

    @Override
    public List<Payment> listPayment(List<String> historyPaymentIdList) {
        return this.lambdaQuery()
                .in(Payment::getPaymentId,historyPaymentIdList)
                .in(Payment::getTradeStatus, TradeStatusEnum.EXECUTING.getValue(),TradeStatusEnum.SUCCESS.getValue())
                .list();
    }

    @Override
    public Payment getByPaymentId(String paymentId) {
        return this.lambdaQuery().eq(Payment::getPaymentId, paymentId).one();
    }

    @Override
    public Payment getByVoucherId(String receiptVoucherId) {
        return this.lambdaQuery().eq(Payment::getVoucherId, receiptVoucherId).one();
    }

    @Override
    public List<Payment> getListByPaymentIds(List<String> paymentIds) {
        return this.lambdaQuery().in(Payment::getPaymentId, paymentIds).list();
    }
}
