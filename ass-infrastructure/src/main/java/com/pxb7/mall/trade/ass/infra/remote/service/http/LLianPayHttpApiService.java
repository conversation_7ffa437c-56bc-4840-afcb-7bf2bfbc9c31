package com.pxb7.mall.trade.ass.infra.remote.service.http;

import com.alibaba.cola.exception.BizException;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.ImmutableSet;
import com.pxb7.mall.trade.ass.client.dto.model.refund.RefundErrorCode;
import com.pxb7.mall.trade.ass.infra.remote.model.outpayparam.request.LianLianPayCreationReqPO;
import com.pxb7.mall.trade.ass.infra.remote.model.outpayparam.request.LianLianRefundPO;
import com.pxb7.mall.trade.ass.infra.remote.model.request.LianLianRefundQueryReqPO;
import com.pxb7.mall.trade.ass.infra.util.LLianSignUtil;
import com.pxb7.mall.trade.ass.infra.util.lianliansecurity.LLianPayAccpSignature;
import lombok.extern.slf4j.Slf4j;
import okhttp3.ResponseBody;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import retrofit2.Response;

import java.util.Map;
import java.util.Set;

@Service
@Slf4j
public class LLianPayHttpApiService extends HttpBaseService {

    private static final Set<String> DIRECT_FAIL_CODE = ImmutableSet.<String>builder()
            .add("3005")
            .build();

    /**
     * 退款
     */
    public JSONObject refund(LianLianRefundPO refundPO, LianLianPayCreationReqPO payCreationReqPO, boolean retBody) {
        // 设置过滤器中的私钥
        super.lLianSignatureInterceptor.setPrivateKey(payCreationReqPO.getRsa_private_key());
        Response<ResponseBody> response;
        try {
            response = lianPayAccpApi.morePayeeRefund(refundPO).execute();
            log.info("调用连连退款接口，refundPO:{}, response:{}", refundPO,
                    JSONObject.toJSONString(response));
        } catch (Exception e) {
            log.error("调用连连支付退款接口异常, refundPO:{}, payCreationReqPO:{}", JSONObject.toJSONString(refundPO),
                    JSONObject.toJSONString(payCreationReqPO), e);
            throw new BizException(RefundErrorCode.REFUND_THIRD_FAIL.getErrCode(), e.getMessage());
        }
//        if (!retBody) {
//            return null;
//        }

        return this.parseResponse(response);
    }

    /**
     * 退款查询
     */
    public JSONObject refundQuery(LianLianRefundQueryReqPO po, LianLianPayCreationReqPO payCreationReqPO) {
        // 设置过滤器中的私钥
        super.lLianSignatureInterceptor.setPrivateKey(payCreationReqPO.getRsa_private_key());
        Response<ResponseBody> response;
        try {
            response = lianPayAccpApi.refundQuery(po).execute();
        } catch (Exception e) {
            log.error("调用连连支付退款查询接口异常, LianLianRefundQueryReqPO:{}, LianLianPayCreationReqPO:{}",
                    JSONObject.toJSONString(po), JSONObject.toJSONString(payCreationReqPO), e);
            throw new BizException(RefundErrorCode.REFUND_THIRD_FAIL.getErrCode(), e.getMessage());
        }

        return this.parseResponse(response);
    }

    /**
     * 处理响应结果
     */
    private JSONObject parseResponse(Response<ResponseBody> response) {

        // List<String> codeList = Arrays.asList("3014", "3015", "3013");

        String result;
        if (response.isSuccessful()) {
            try (ResponseBody body = response.body()) {
                result = body.string();
            } catch (Exception e) {
                log.error("解析连连支付结果异常:{}", response, e);
                throw new BizException(RefundErrorCode.REFUND_THIRD_FAIL.getErrCode(),
                        RefundErrorCode.REFUND_THIRD_FAIL.getErrDesc());
            }
        } else {
            log.error("解析连连支付结果失败:{}", JSONObject.toJSONString(response));
            throw new BizException(response.message());
        }

        //result = "{\"ret_code\":\"3005\",\"ret_msg\":\"账户余额不足[112001]\"}";

        JSONObject paramMap = JSON.parseObject(result);
        String code = paramMap.getString("ret_code");

        if (!"0000".equals(code)) {
            log.error("解析连连支付结果, 非成功:{}", JSONObject.toJSONString(paramMap));
            if (StringUtils.isNotEmpty(code) && DIRECT_FAIL_CODE.contains(code)) {
                throw new BizException(RefundErrorCode.REFUND_FAIL_ERROR.getErrCode(), paramMap.getString("ret_msg"));
            }
            throw new BizException(RefundErrorCode.REFUND_THIRD_FAIL.getErrCode(), paramMap.getString("ret_msg"));
        }
        return paramMap;
    }

    /**
     * 连连响应统一RSA签名验证
     *
     * @param paramMap:   The obtained asynchronous notification body
     * @param rsa_public: The public key provided by LianLian
     */
    private boolean checkSignRSA(Map<String, String> paramMap, String rsa_public) {
        if (CollectionUtils.isEmpty(paramMap)) {
            return false;
        }
        String sign = paramMap.get("sign");
        // 没有sign字段，无需验签
        if (StringUtils.isBlank(sign)) {
            return true;
        }
        try {
            String sign_src = LLianSignUtil.toSignStr(JSON.toJSONString(paramMap));
            return LLianPayAccpSignature.checkSign(rsa_public, sign_src, sign);
        } catch (Exception e) {
            return false;
        }
    }
}
