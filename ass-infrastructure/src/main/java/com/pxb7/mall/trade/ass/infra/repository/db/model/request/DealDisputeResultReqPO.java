package com.pxb7.mall.trade.ass.infra.repository.db.model.request;

import java.io.Serial;
import java.io.Serializable;

import lombok.Data;

/**
 * 完结纠纷工单
 *
 * <AUTHOR>
 * @since: 2024-08-13 20:21
 **/
@Data
public class DealDisputeResultReqPO implements Serializable {

    @Serial
    private static final long serialVersionUID = -754062281366985219L;
    /**
     * 纠纷工单id
     */
    private String assDisputeId;
    /**
     * 订单ID
     */
    private String orderItemId;
    /**
     * 1正常完结2平台规则完结3赔付安抚完结
     */
    private Integer disputeDealResult;
    /**
     * 问题归类一级目录
     */
    private String classifyFirst;

    /**
     * 问题归类二级目录
     */
    private String classifySecond;
}
