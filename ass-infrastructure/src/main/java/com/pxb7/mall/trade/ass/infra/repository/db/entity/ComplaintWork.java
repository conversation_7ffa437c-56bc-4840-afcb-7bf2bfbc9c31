package com.pxb7.mall.trade.ass.infra.repository.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 客诉工单(ComplaintWork)实体类
 *
 * <AUTHOR>
 * @since 2024-09-20 17:55:35
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
@TableName(value = "complaint_work")
public class ComplaintWork implements Serializable {
    private static final long serialVersionUID = -84444791012729917L;
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 工单编号(业务id)
     */
    @TableField(value = "complaint_work_id")
    private String complaintWorkId;
    /**
     * 房间id
     */
    @TableField(value = "room_id")
    private String roomId;
    /**
     * 订单行id
     */
    @TableField(value = "order_item_id")
    private String orderItemId;
    /**
     * 投诉标题
     */
    @TableField(value = "complaint_title")
    private String complaintTitle;
    /**
     * 投诉渠道1:IM 2支付宝 3闲鱼 4:12315 5消费宝 6连连支付 7电话 8反诈邮箱 9外部门升级 10黑猫投诉 11工商局 12工信部
     */
    @TableField(value = "complaint_channel")
    private Integer complaintChannel;
    /**
     * 投诉手机号
     */
    @TableField(value = "complaint_phone")
    private String complaintPhone;
    /**
     * 用户id
     */
    @TableField(value = "user_id")
    private String userId;
    /**
     * 投诉人身份1买家 2卖家 3其他
     */
    @TableField(value = "complaint_role")
    private Integer complaintRole;
    /**
     * 投诉级别:1-6
     */
    @TableField(value = "complaint_level")
    private Integer complaintLevel;
    /**
     * 投诉文字内容
     */
    @TableField(value = "complaint_content")
    private String complaintContent;
    /**
     * 投诉图片地址多图,隔开
     */
    @TableField(value = "complaint_img")
    private String complaintImg;
    /**
     * 工单状态1待处理2已完成
     */
    @TableField(value = "work_order_status")
    private Integer workOrderStatus;
    /**
     * 当前处理人ID
     */
    @TableField(value = "current_processor_id")
    private String currentProcessorId;
    /**
     * 处理结果
     */
    @TableField(value = "deal_result")
    private String dealResult;
    /**
     * 备注
     */
    @TableField(value = "note")
    private String note;
    /**
     * 被转交人id
     */
    @TableField(value = "transferee_id")
    private String transfereeId;
    /**
     * 完结人id
     */
    @TableField(value = "finisher_id")
    private String finisherId;
    /**
     * 完结时间
     */
    @TableField(value = "finish_time")
    private LocalDateTime finishTime;
    /**
     * 责任人
     */
    @TableField(value = "responsible_person")
    private String responsiblePerson;
    /**
     * 是否有责 0:否 1是
     */
    @TableField(value = "is_responsibility")
    private Boolean responsibility;
    /**
     * 部门名称
     */
    @TableField(value = "department_name")
    private String departmentName;
    /**
     * 问题类型
     */
    @TableField(value = "question_type")
    private String questionType;
    /**
     * 是否有效 0:否 1是
     */
    @TableField(value = "is_valid")
    private Integer isValid;

    /**
     * 问题级别:1-6
     */
    @TableField(value = "question_level")
    private Integer questionLevel;
    /**
     * 创建人id
     */
    @TableField(value = "create_user_id")
    private String createUserId;
    /**
     * 更新人id
     */
    @TableField(value = "update_user_id")
    private String updateUserId;
    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    @TableField(value = "update_time", update="now()")
    private LocalDateTime updateTime;
    /**
     * 是否删除
     */
    @TableLogic
    @TableField(value = "is_deleted")
    private Boolean deleted;

    /**
     * 创建来源(1:系统,2:IM)
     */
    @TableField(value = "complaint_source")
    private Integer complaintSource;

}

