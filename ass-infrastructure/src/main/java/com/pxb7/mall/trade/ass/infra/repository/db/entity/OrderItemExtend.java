package com.pxb7.mall.trade.ass.infra.repository.db.entity;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.*;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 订单行扩展表(OrderItemExtend)实体类
 *
 * <AUTHOR>
 * @since 2024-10-21 17:05:27
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
@TableName(value = "order_item_extend")
public class OrderItemExtend implements Serializable {
    private static final long serialVersionUID = -29784739292249957L;
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 子订单id
     */
    @TableField(value = "order_item_id")
    private String orderItemId;
    /**
     * 交易客服ID
     */
    @TableField(value = "trade_customer_id")
    private String tradeCustomerId;
    /**
     * 交付客服id
     */
    @TableField(value = "delivery_customer_id")
    private String deliveryCustomerId;
    /**
     * 交付客服名称
     */
    @TableField(value = "delivery_customer")
    private String deliveryCustomer;
    /**
     * 交易客服名称
     */
    @TableField(value = "trade_customer")
    private String tradeCustomer;
    /**
     * 商品名称
     */
    @TableField(value = "product_name")
    private String productName;
    /**
     * 商品图片
     */
    @TableField(value = "product_pic")
    private String productPic;
    /**
     * 商品属性
     */
    @TableField(value = "product_attr")
    private String productAttr;
    /**
     * 账号信息
     */
    @TableField(value = "game_account")
    private String gameAccount;
    /**
     * 商品亮点
     */
    @TableField(value = "product_highlight")
    private String productHighlight;
    /**
     * 游戏名称
     */
    @TableField(value = "game_name")
    private String gameName;
    /**
     * 游戏属性
     */
    @TableField(value = "game_attr")
    private String gameAttr;
    /**
     * 是否顺手买商品 0否 1是
     */
    @TableField(value = "is_easy_buy")
    private Boolean easyBuy;
    /**
     * 咨询房间id, 私聊房间
     */
    @TableField(value = "room_id")
    private String roomId;
    /**
     * 交付群聊房间id, 智能交付是新群交付, 中介订单是原群交付
     */
    @TableField(value = "delivery_room_id")
    private String deliveryRoomId;
    /**
     * 买家手机号
     */
    @TableField(value = "buyer_phone")
    private String buyerPhone;
    /**
     * 买家身份 1散户 2号商
     */
    @TableField(value = "buyer_user_type")
    private Integer buyerUserType;
    /**
     * 卖家手机号
     */
    @TableField(value = "seller_phone")
    private String sellerPhone;
    /**
     * 卖家身份 0系统 1散户 2号商
     */
    @TableField(value = "seller_user_type")
    private Integer sellerUserType;
    /**
     * 买商品的时候,它是否有生效的诚心卖服务, 0没有 1有
     */
    @TableField(value = "is_sincerity_sell")
    private Boolean sinceritySell;
    /**
     * 买家的商家id
     */
    @TableField(value = "buyer_merchant_id")
    private String buyerMerchantId;
    /**
     * 买家的商家部门id
     */
    @TableField(value = "buyer_merchant_dept_id")
    private String buyerMerchantDeptId;
    /**
     * 卖家的商家id
     */
    @TableField(value = "seller_merchant_id")
    private String sellerMerchantId;
    /**
     * 卖家的商家部门id
     */
    @TableField(value = "seller_merchant_dept_id")
    private String sellerMerchantDeptId;
    /**
     * 是否签署合同了 0否 1是
     */
    @TableField(value = "is_add_contract")
    private Boolean addContract;
    /**
     * 买家确认收货时间
     */
    @TableField(value = "buyer_receipt_time")
    private LocalDateTime buyerReceiptTime;
    /**
     * 自动收货最晚时间
     */
    @TableField(value = "auto_receipt_time")
    private LocalDateTime autoReceiptTime;
    /**
     * 买家订单备注
     */
    @TableField(value = "buyer_remark")
    private String buyerRemark;
    /**
     * 卖家订单备注
     */
    @TableField(value = "seller_remark")
    private String sellerRemark;
    /**
     * 客服订单备注
     */
    @TableField(value = "customer_remark")
    private String customerRemark;
    /**
     * 是否续包
     */
    @TableField(value = "is_guarantee")
    private Boolean guarantee;
    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    @TableField(value = "update_time", update="now()")
    private LocalDateTime updateTime;
    /**
     * 逻辑删除，0:删除，1:正常
     */
    @TableLogic
    @TableField(value = "is_deleted")
    private Boolean deleted;
    /**
     * 商品编码
     */
    @TableField(value = "product_unique_no")
    private String productUniqueNo;
    /**
     * 充值卖家状态 1 待发货 2 准备发货 3 已发货
     */
    @TableField(value = "recharge_seller_status")
    private Integer rechargeSellerStatus;

}
