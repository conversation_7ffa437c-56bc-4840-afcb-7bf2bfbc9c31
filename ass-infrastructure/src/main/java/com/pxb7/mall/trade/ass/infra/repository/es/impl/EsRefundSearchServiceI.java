package com.pxb7.mall.trade.ass.infra.repository.es.impl;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

import com.pxb7.mall.trade.order.client.enums.order.ProductTypeEnum;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.elasticsearch.annotations.DateFormat;
import org.springframework.data.elasticsearch.client.elc.ElasticsearchTemplate;
import org.springframework.data.elasticsearch.client.elc.NativeQuery;
import org.springframework.data.elasticsearch.core.SearchHitSupport;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.SearchPage;
import org.springframework.stereotype.Service;

import com.pxb7.mall.trade.ass.infra.enums.OrderCycleEnum;
import com.pxb7.mall.trade.ass.infra.model.RefundVoucherSearchPO;
import com.pxb7.mall.trade.ass.infra.repository.es.entity.RefundVoucherDoc;

import co.elastic.clients.elasticsearch._types.FieldValue;
import co.elastic.clients.elasticsearch._types.query_dsl.*;
import co.elastic.clients.json.JsonData;
import jakarta.annotation.Resource;

@Service
public class EsRefundSearchServiceI implements EsRefundSearchService {

    @Resource
    private ElasticsearchTemplate elasticsearchTemplate;

    @Override
    public SearchPage<RefundVoucherDoc> searchRefundPage(RefundVoucherSearchPO refundVoucherSearchPO) {
        List<Query> queryList = new ArrayList<>();
        List<Query> mustNotQueryList = new ArrayList<>();
        if (StringUtils.isNotEmpty(refundVoucherSearchPO.getUserId())) {
            TermQuery userTerm =
                QueryBuilders.term().field(refundVoucherSearchPO.getIdentity() == 0 ? "buyer_id" : "seller_id")
                    .value(refundVoucherSearchPO.getUserId()).build();

            queryList.add(userTerm._toQuery());
        }

        // 过滤掉服务订单类型
        List<Integer> notProductTypeList = List.of(ProductTypeEnum.SINCERITY_SELL_PACKAGE.getValue(),
                ProductTypeEnum.SINCERITY_SELL_SERVICE.getValue());
        List<FieldValue> valueList = notProductTypeList.stream().map(FieldValue::of).toList();
        TermsQuery notProductTypesTermsQuery = QueryBuilders.terms().field("product_type")
                .terms(new TermsQueryField.Builder().value(valueList).build()).build();
        mustNotQueryList.add(notProductTypesTermsQuery._toQuery());

        if (refundVoucherSearchPO.getCycle() != null) {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS");
            OrderCycleEnum orderCycleEnum = OrderCycleEnum.getOrderCycleEnum(refundVoucherSearchPO.getCycle());

            LocalDateTime endTime = LocalDateTime.now();
            LocalDateTime startTime = OrderCycleEnum.getCycleStart(orderCycleEnum, endTime);

            // 时间周期
            Query cycleQuery = QueryBuilders.range(timeQuery -> timeQuery.field("create_time")
                .format(String.valueOf(DateFormat.date_hour_minute_second_fraction))
                .gte(JsonData.of(startTime.format(formatter))).lte(JsonData.of(endTime.format(formatter))));

            queryList.add(cycleQuery);
        }

        // 游戏id
        if (StringUtils.isNotBlank(refundVoucherSearchPO.getGameId())) {
            TermQuery gameTerm = QueryBuilders.term().field("game_id").value(refundVoucherSearchPO.getGameId()).build();
            queryList.add(gameTerm._toQuery());
        }

        // 商品类型
        if (refundVoucherSearchPO.getProductType() != null) {
            TermQuery productTypeQuery =
                QueryBuilders.term().field("product_type").value(refundVoucherSearchPO.getProductType()).build();
            queryList.add(productTypeQuery._toQuery());
        }

        // 关键字
        if (StringUtils.isNotBlank(refundVoucherSearchPO.getKeyWords())) {
            QueryStringQuery stringQuery = QueryBuilders.queryString().fields("product_name", "order_item_id")
                .query(refundVoucherSearchPO.getKeyWords()).build();
            queryList.add(stringQuery._toQuery());
        }
        // 订单编号
        if (StringUtils.isNotBlank(refundVoucherSearchPO.getOrderItemId())) {
            TermQuery query =
                QueryBuilders.term().field("order_item_id").value(refundVoucherSearchPO.getOrderItemId()).build();
            queryList.add(query._toQuery());
        }
        // 手机号（子账号）
        if (StringUtils.isNotBlank(refundVoucherSearchPO.getTelephone())) {
            TermQuery query =
                QueryBuilders.term().field("buyer_phone").value(refundVoucherSearchPO.getTelephone()).build();
            queryList.add(query._toQuery());
        }
        // 商品名称
        if (StringUtils.isNotBlank(refundVoucherSearchPO.getProductName())) {
            QueryStringQuery query = QueryBuilders.queryString().fields("product_name")
                .query(refundVoucherSearchPO.getProductName()).build();
            queryList.add(query._toQuery());
        }
        // 商品id
        if (StringUtils.isNotBlank(refundVoucherSearchPO.getProductId())) {
            TermQuery query =
                QueryBuilders.term().field("product_id").value(refundVoucherSearchPO.getProductId()).build();
            queryList.add(query._toQuery());
        }
        // 商品编号
        if (StringUtils.isNotBlank(refundVoucherSearchPO.getProductUniqueNo())) {
            TermQuery query = QueryBuilders.term().field("product_unique_no")
                .value(refundVoucherSearchPO.getProductUniqueNo()).build();
            queryList.add(query._toQuery());
        }
        // 退款状态
        if (CollectionUtils.isNotEmpty(refundVoucherSearchPO.getRefundStatusList())) {
            List<FieldValue> values = refundVoucherSearchPO.getRefundStatusList().stream().map(FieldValue::of).toList();
            TermsQuery refundStatus =
                QueryBuilders.terms().field("refund_status").terms(builder -> builder.value(values)).build();
            queryList.add(refundStatus._toQuery());
        }

        if (StringUtils.isNotBlank(refundVoucherSearchPO.getStartTime())
            || StringUtils.isNotBlank(refundVoucherSearchPO.getEndTime())) {
            // 时间周期
            Query cycleQuery = QueryBuilders.range(timeQuery -> {
                timeQuery.field("apply_time").format(String.valueOf(DateFormat.date_hour_minute_second_fraction));
                if (StringUtils.isNotBlank(refundVoucherSearchPO.getStartTime())) {
                    timeQuery.gte(JsonData.of(refundVoucherSearchPO.getStartTime()));
                }
                if (StringUtils.isNotBlank(refundVoucherSearchPO.getEndTime())) {
                    timeQuery.lte(JsonData.of(refundVoucherSearchPO.getEndTime()));
                }
                return timeQuery;
            });
            queryList.add(cycleQuery);
        }

        // 排序
        Sort sort = Sort.by(Sort.Order.desc("apply_time"));

        NativeQuery nativeQuery = NativeQuery.builder()
            .withQuery(QueryBuilders.bool().must(queryList).mustNot(mustNotQueryList).build()._toQuery())
            .withPageable(PageRequest.of(refundVoucherSearchPO.getPageIndex() - 1, refundVoucherSearchPO.getPageSize()))
            .withSort(sort).build();

        SearchHits<RefundVoucherDoc> searchHits = elasticsearchTemplate.search(nativeQuery, RefundVoucherDoc.class);
        return SearchHitSupport.searchPageFor(searchHits,
            PageRequest.of(refundVoucherSearchPO.getPageIndex() - 1, refundVoucherSearchPO.getPageSize()));
    }
}
