package com.pxb7.mall.trade.ass.infra.repository.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.pxb7.mall.trade.ass.client.enums.AssDataSource;
import com.pxb7.mall.trade.ass.client.enums.AssDisputeDealResult;
import com.pxb7.mall.trade.ass.client.enums.AssRecoverStatus;
import com.pxb7.mall.trade.ass.client.enums.AssSourceType;
import com.pxb7.mall.trade.ass.client.enums.AssSponsor;
import com.pxb7.mall.trade.ass.client.enums.AssStatus;
import com.pxb7.mall.trade.ass.client.enums.AssWoClaimStatus;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 售后纠纷工单
 *
 * <AUTHOR>
 **/
@Getter
@Setter
@ToString
@Accessors(chain = true)
@TableName(value = "ass_dispute_work")
public class AssDisputeWo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 自增id
     */
    @TableField(value = "id")
    private Long id;

    /**
     * 主键id
     */
    @TableId(value = "ass_dispute_id", type = IdType.INPUT)
    private String assDisputeId;

    /**
     * 工单编号
     */
    @TableField(value = "work_order_no")
    private String workOrderNo;

    /**
     * 订单ID
     */
    @TableField(value = "order_item_id")
    private String orderItemId;

    /**
     * 商品id
     */
    @TableField(value = "product_id")
    private String productId;

    /**
     * 商品编码
     */
    @TableField(value = "product_unique_no")
    private String productUniqueNo;

    /**
     * 商品类型: 1账号 2充值 3金币 4装备 5初始号 20诚心卖
     */
    @TableField(value = "product_type")
    private Integer productType;

    /**
     * 游戏ID
     */
    @TableField(value = "game_id")
    private String gameId;

    /**
     * 工单状态 1:处理中 2:处理完成 3:已关闭
     *
     * @see AssStatus
     */
    @TableField(value = "`status`")
    private Integer status;

    /**
     * 申请人id
     */
    @TableField(value = "proposer_user_id")
    private String proposerUserId;

    /**
     * 工单发起来源 1:c端用户 2:客服 3:admin用户
     *
     * @see AssSponsor
     */
    @TableField(value = "sponsor")
    private Integer sponsor;

    // /**
    // * 申请人 1:admin用户发起 2:客服发起
    // *
    // * @see AssProposerType
    // */
    // @TableField(value = "proposer_type")
    // private Integer proposerType;

    /**
     * 纠纷售后人员
     */
    @TableField(value = "dispute_user_id")
    private String disputeUserId;

    /**
     * 处理状态 1:正常完结 2:平台规则完结 3:赔付安抚完结
     *
     * @see AssDisputeDealResult
     */
    @TableField(value = "handle_status")
    private Integer handleStatus;

    /**
     * 追回金额
     */
    @TableField(value = "recover_amount")
    private Long recoverAmount;

    /**
     * 追回号款状态 0:无 1:部分追回 2:全追回
     *
     * @see AssRecoverStatus
     */
    @TableField(value = "recover_status")
    private Integer recoverStatus;

    /**
     * 赔付金额
     */
    @TableField(value = "claim_amount")
    private Long claimAmount;

    /**
     * 赔付状态 0:无 1:待审核 2:已赔付
     *
     * @see AssWoClaimStatus
     */
    @TableField(value = "claim_status")
    private Integer claimStatus;

    /**
     * 完成时间
     */
    @TableField(value = "complete_time")
    private LocalDateTime completeTime;

    /**
     * 问题归类一级目录
     */
    @TableField(value = "classify_first")
    private String classifyFirst;

    /**
     * 问题归类二级目录
     */
    @TableField(value = "classify_second")
    private String classifySecond;

    /**
     * 数据来源 1:IM私聊 2:企业微信 3:投诉转交 4:其他
     *
     * @see AssDataSource
     */
    @TableField(value = "data_source")
    private Integer dataSource;

    /**
     * 渠道来源 1:螃蟹 2:支付宝小程序
     *
     * @see AssSourceType
     */
    @TableField(value = "source_type")
    private Integer sourceType;

    /**
     * 创建用户ID
     */
    @TableField(value = "create_user_id")
    private String createUserId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    @TableField(value = "update_user_id")
    private String updateUserId;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", update="now()")
    private LocalDateTime updateTime;

    /**
     * 删除标记
     */
    @TableLogic
    @TableField(value = "is_deleted")
    private Boolean deleted;

    /**
     * 纠纷售后飞书名称
     */
    @TableField(value = "feishu_name")
    private String feishuName;

    /**
     * 数据创建来源 1:IM客服端 2:后台
     */
    @TableField(value = "data_create_source")
    private Integer dataCreateSource;
}