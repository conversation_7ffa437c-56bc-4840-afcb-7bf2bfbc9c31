package com.pxb7.mall.trade.ass.infra.enums.complaint;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Copyright (C), 2002-2024, 螃蟹游戏服务网
 *
 * @fileName: WorkOrderStatus.java
 * @description: 客诉工单状态
 * @author: g<PERSON><PERSON><PERSON><PERSON>
 * @email: <EMAIL>
 * @date: 2024/9/21 11:49
 * @history: //修改记录
 * <author> <time> <version> <desc>
 * 修改人姓名 修改时间 版本号 描述
 */
@AllArgsConstructor
@Getter
public enum WorkOrderStatus {

    WORK_STATUS_PROCESSING(1, "待处理"), WORK_STATUS_FINISH(2, "已完成"),
    ;

    private final Integer code;
    private final String desc;
}
