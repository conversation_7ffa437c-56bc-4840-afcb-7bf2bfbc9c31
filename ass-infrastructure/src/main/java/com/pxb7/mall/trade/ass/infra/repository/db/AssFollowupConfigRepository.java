package com.pxb7.mall.trade.ass.infra.repository.db;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pxb7.mall.trade.ass.infra.model.AssFollowupConfigReqPO;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.AssFollowupConfig;

/**
 * 跟进结果类型配置表(AssFollowupConfig)表服务接口
 *
 * <AUTHOR>
 * @since 2025-07-30 11:51:56
 */
public interface AssFollowupConfigRepository extends IService<AssFollowupConfig> {

    boolean insert(AssFollowupConfigReqPO.AddPO param);

    boolean update(AssFollowupConfigReqPO.UpdatePO param);

    boolean deleteById(AssFollowupConfigReqPO.DelPO param);

    AssFollowupConfig findById(Long id);

    List<AssFollowupConfig> list(AssFollowupConfigReqPO.SearchPO param);

    Page<AssFollowupConfig> page(AssFollowupConfigReqPO.PagePO param);

}

