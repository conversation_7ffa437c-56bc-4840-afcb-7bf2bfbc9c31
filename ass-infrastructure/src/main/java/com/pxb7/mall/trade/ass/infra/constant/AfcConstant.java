package com.pxb7.mall.trade.ass.infra.constant;

import cn.hutool.core.util.StrUtil;

/**
 * Copyright (C), 2002-2025, 螃蟹游戏服务网
 *
 * @fileName: AfcConstant.java
 * @description: 售后常量
 * @author: g<PERSON><PERSON><PERSON><PERSON>
 * @email: <EMAIL>
 * @date: 2025/4/14 16:40
 * @history: //修改记录
 * <author> <time> <version> <desc>
 * 修改人姓名 修改时间 版本号 描述
 */
public class AfcConstant {

    public static String AFC_APPLY_CARD = "售后申请: 售后类型: %s,商品编号:%s,订单编号:%s,售后问题:%s";

    public static String OTHER_QUESTION_MARK = "其他";

    public final static String ASS_WORK_ORDER_LOCK = "ass:order:lock:{}";




    public static String getAfcOrderLockKey(String orderItemId) {
        return StrUtil.format(ASS_WORK_ORDER_LOCK, orderItemId);
    }

}
