package com.pxb7.mall.trade.ass.infra.repository.db;

import com.baomidou.mybatisplus.extension.service.IService;
import com.pxb7.mall.trade.ass.infra.model.AfcSubmitRecordReqPO;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.AfcSubmitRecord;

/**
 * 售后提交记录(AfcSubmitRecord)表服务接口
 *
 * <AUTHOR>
 * @since 2025-04-11 13:54:04
 */
public interface AfcSubmitRecordRepository extends IService<AfcSubmitRecord> {

    boolean insert(AfcSubmitRecordReqPO.AddPO param);

    boolean update(AfcSubmitRecordReqPO.UpdatePO param);

    boolean deleteById(AfcSubmitRecordReqPO.DelPO param);

    AfcSubmitRecord findById(Long id);

}

