package com.pxb7.mall.trade.ass.infra.repository.db;

import java.util.List;

import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.ExtVoucher;
import com.pxb7.mall.trade.ass.infra.repository.db.mapper.ExtVoucherMapper;
import com.pxb7.mall.trade.order.client.enums.extPayment.ExtVoucherStatusEnum;

import lombok.extern.slf4j.Slf4j;

/**
 * 放款单(ExtPayment)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-08-03 18:35:46
 */
@Slf4j
@Repository
public class ExtVoucherDbRepository extends ServiceImpl<ExtVoucherMapper, ExtVoucher> implements ExtVoucherRepository {

    @Override
    public Boolean findInProgressReceipt(String orderItemId) {
        Long count = this.lambdaQuery().eq(ExtVoucher::getOrderItemId, orderItemId)
            .eq(ExtVoucher::getExtPaymentStatus, ExtVoucherStatusEnum.CREATED.getValue()).count();
        return count > 0;
    }

    @Override
    public ExtVoucher getFinishReceipt(String orderId) {
        return this.lambdaQuery().eq(ExtVoucher::getOrderId, orderId)
            .eq(ExtVoucher::getExtPaymentStatus, ExtVoucherStatusEnum.SUCCESS.getValue()).one();
    }

    @Override
    public List<ExtVoucher> queryCollectionReceiptList(List<String> item) {
        LambdaQueryWrapper<ExtVoucher> eq = Wrappers.lambdaQuery(ExtVoucher.class).in(ExtVoucher::getOrderItemId, item)
            .eq(ExtVoucher::getIsDeleted, false);
        return this.list(eq);
    }

    @Override
    public ExtVoucher queryExtPayment(String orderItemId, Integer ltPaymentStatus) {
        LambdaQueryWrapper<ExtVoucher> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(ExtVoucher::getId);
        wrapper.eq(ExtVoucher::getOrderItemId, orderItemId);
        wrapper.lt(ExtVoucher::getExtPaymentStatus, ltPaymentStatus);// 小于
        return getOne(wrapper, Boolean.FALSE);
    }

    /**
     * 根据orderItemId获取ExtPayment
     *
     * @param orderItemId
     * @return
     */
    @Override
    public ExtVoucher getExtPaymentByOrderItemId(String orderItemId) {
        return this.lambdaQuery().eq(ExtVoucher::getOrderItemId, orderItemId).one();
    }

}
