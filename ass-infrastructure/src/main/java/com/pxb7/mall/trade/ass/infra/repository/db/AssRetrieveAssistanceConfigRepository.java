package com.pxb7.mall.trade.ass.infra.repository.db;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pxb7.mall.trade.ass.infra.model.AssRetrieveAssistanceConfigReqPO;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.AssRetrieveAssistanceConfig;

/**
 * 找回协助类型配置表(AssRetrieveAssistanceConfig)表服务接口
 *
 * <AUTHOR>
 * @since 2025-07-30 13:45:23
 */
public interface AssRetrieveAssistanceConfigRepository extends IService<AssRetrieveAssistanceConfig> {

    boolean insert(AssRetrieveAssistanceConfigReqPO.AddPO param);

    boolean update(AssRetrieveAssistanceConfigReqPO.UpdatePO param);

    boolean deleteById(AssRetrieveAssistanceConfigReqPO.DelPO param);

    AssRetrieveAssistanceConfig findById(Long id);

    List<AssRetrieveAssistanceConfig> list(AssRetrieveAssistanceConfigReqPO.SearchPO param);

    Page<AssRetrieveAssistanceConfig> page(AssRetrieveAssistanceConfigReqPO.PagePO param);

}

