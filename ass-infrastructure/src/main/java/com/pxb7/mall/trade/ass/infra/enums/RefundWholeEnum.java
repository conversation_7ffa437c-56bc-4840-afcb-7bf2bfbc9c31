package com.pxb7.mall.trade.ass.infra.enums;

import java.util.Arrays;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 整单退款类型
 */
@Getter
@AllArgsConstructor
public enum RefundWholeEnum {

    WHOLE(1, "整单"),

    PART(2, "部分"),
    INDEMNITY(3, "变更包赔");

    private final Integer value;

    private final String label;

    public static String getLabel(Integer value) {
        return Arrays.stream(RefundWholeEnum.values()).filter(e -> e.getValue().equals(value))
            .map(RefundWholeEnum::getLabel).findAny().orElse(null);
    }
}
