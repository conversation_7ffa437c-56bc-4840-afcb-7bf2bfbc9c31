package com.pxb7.mall.trade.ass.infra.util;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.Objects;

public class DateUtil {
    public static LocalDateTime from(Date date) {
        if (Objects.isNull(date)) {
            return null;
        }
        return LocalDateTime.ofInstant(date.toInstant(), ZoneId.systemDefault());
    }

    /**
     * 计算超时时间
     */
    public static LocalDateTime getExpireTime(LocalDateTime beginTime, long delayTime) {
        if (Objects.isNull(beginTime)) {
            return LocalDateTime.now();
        }
        return beginTime.plusSeconds(delayTime);
    }
}
