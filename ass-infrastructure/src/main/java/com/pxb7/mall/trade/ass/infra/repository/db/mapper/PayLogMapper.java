package com.pxb7.mall.trade.ass.infra.repository.db.mapper;

import com.pxb7.mall.trade.ass.infra.repository.db.entity.PayLog;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 支付记录(包括线上原路退款)(PayLog)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-09-06 19:58:42
 */
@Mapper
public interface PayLogMapper extends BaseMapper<PayLog> {
    /**
     * 批量新增数据
     *
     * @param entities List<PayLog> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<PayLog> entities);

}

