package com.pxb7.mall.trade.ass.infra.repository.db;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.AssAnswer;
import com.pxb7.mall.trade.ass.infra.repository.db.mapper.AssAnswerMapper;

import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

/**
 * 售后问题回答记录
 *
 * <AUTHOR>
 * @since: 2024-08-13 17:06
 **/
@Service
public class AssAnswerDbRepository extends ServiceImpl<AssAnswerMapper, AssAnswer> implements AssAnswerRepository {
    @Override
    public Boolean relationAssAnswer(String assRetrieveId, String orderItemId) {
        LambdaQueryWrapper<AssAnswer> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AssAnswer::getOrderItemId, orderItemId);
        queryWrapper.orderByDesc(AssAnswer::getCreateTime);
        queryWrapper.last("limit 1");
        AssAnswer answer = this.getOne(queryWrapper);
        if (ObjectUtils.isEmpty(answer)) {
            //这里处理的是未回答问题就创建工单的情况
            return true;
        }
        answer.setWorkOrderId(assRetrieveId);
        return this.updateById(answer);
    }

}
