package com.pxb7.mall.trade.ass.infra.model;
import java.time.*;
import java.util.List;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;


/**
 * 违约支付单表(ViolatePayRecord)实体类
 *
 * <AUTHOR>
 * @since 2025-03-31 15:07:26
 */
public class ViolatePayRecordReqPO {
     
    @Getter
    @Setter
    @Accessors(chain = true)
    public static class AddPO  {
    
       
         private String  recordId;

       
         private String  violateId;

       
         private String  orderItemId;

       
         private String  userId;

       
         private Integer  type;

       
         private Integer  channel;

       
         private Long  amount;

       
         private String  outTradeNo;

       
         private String  remark;

       
         private Integer  status;

       
         private LocalDateTime  completedTime;

       
         private LocalDateTime  cancelTime;

    }

    @Getter
    @Setter
    @Accessors(chain = true)
    public static class UpdatePO {
     
      
         private Integer  id;
         
     
      
         private String  recordId;
         
     
      
         private String  violateId;
         
     
      
         private String  orderItemId;
         
     
      
         private String  userId;
         
     
      
         private Integer  type;
         
     
      
         private Integer  channel;
         
     
      
         private Long  amount;
         
     
      
         private String  outTradeNo;
         
     
      
         private String  remark;
         
     
      
         private Integer  status;
         
     
      
         private LocalDateTime  completedTime;
         
     
      
         private LocalDateTime  cancelTime;
         
     
     
     
    }


    @Getter
    @Setter
    @Accessors(chain = true)
    public  static class  DelPO{
        private  Integer  id;
    }

    @Getter
    @Setter
    @Accessors(chain = true)
    public static class  SearchPO{
       
         private String  recordId;

       
         private String  violateId;

       
         private String  orderItemId;

       
         private String  userId;

       
         private Integer  type;

       
         private Integer  channel;

       
         private Long  amount;

       
         private String  outTradeNo;

       
         private String  remark;

       
         private Integer  status;

         private List<Integer> statusList;

       
         private LocalDateTime  completedTime;

       
         private LocalDateTime  cancelTime;

    }

    @Getter
    @Setter
    @Accessors(chain = true)
    public static class PagePO{
    
       
         private String  recordId;

       
         private String  violateId;

       
         private String  orderItemId;

       
         private String  userId;

       
         private Integer  type;

       
         private Integer  channel;

       
         private Long  amount;

       
         private String  outTradeNo;

       
         private String  remark;

       
         private Integer  status;

       
         private LocalDateTime  completedTime;

       
         private LocalDateTime  cancelTime;


        /**
         * 页码，从1开始
         */
        private Integer pageIndex;
        /**
         * 每页数量
         */
        private Integer pageSize;

    }

}

