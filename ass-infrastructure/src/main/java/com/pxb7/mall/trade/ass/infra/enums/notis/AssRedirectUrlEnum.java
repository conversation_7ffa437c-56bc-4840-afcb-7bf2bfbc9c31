package com.pxb7.mall.trade.ass.infra.enums.notis;

import com.pxb7.mall.trade.ass.infra.model.InternalMsgRedirectReqPO;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 功能描述:售后工单通知 
 * 作者：白春韬
 * 创建日期：2025/08/08 
 * 公司名称：金华博淳网络科技有限公司 
 * 域名： www.pxb7.com
 */
@Getter
@AllArgsConstructor
public enum AssRedirectUrlEnum {
    //pc  号商 order/buyer/afterSale
    PC_AFTER_SALE_WORK_ORDER_USER(0, "/user/order/afterSale/{orderItemId}"),
    // android,
    ANDROID_AFTER_SALE_WORK_ORDER(1, "pxapp://pxb7.com/order/after_sale"),
    // h5
    H5_AFTER_SALE_WORK_ORDER(2, "/pages-my/AfterSalesDetails/index?orderItemId={orderItemId}"),
    // hormone
    HORMONE_AFTER_SALE_WORK_ORDER(7, "hmAppRouter://order/orderDetailPage")
    ;

    /**
     * 0pc,1Android,2h5
     */
    private final Integer clientType;
    /**
     * 跳转URL
     */
    private final String urlTemplate;

    public static List<InternalMsgRedirectReqPO> getRedirectUrls(Map<String, String> params) {
        return Arrays.stream(AssRedirectUrlEnum.values())
            .map(e -> new InternalMsgRedirectReqPO(e.getUrl(params), e.getClientType())).toList();
    }

    public String getUrl(Map<String, String> params) {
        String url = urlTemplate;
        for (Map.Entry<String, String> entry : params.entrySet()) {
            url = url.replace("{" + entry.getKey() + "}", entry.getValue());
        }
        return url;
    }
}
