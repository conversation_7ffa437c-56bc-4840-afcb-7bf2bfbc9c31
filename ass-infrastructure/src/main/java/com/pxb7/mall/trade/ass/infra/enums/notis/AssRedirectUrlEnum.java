package com.pxb7.mall.trade.ass.infra.enums.notis;

import com.pxb7.mall.trade.ass.infra.model.InternalMsgRedirectReqPO;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;

/**
 * 功能描述:售后工单通知 
 * 作者：白春韬
 * 创建日期：2025/08/08 
 * 公司名称：金华博淳网络科技有限公司 
 * 域名： www.pxb7.com
 */
@Getter
@AllArgsConstructor
public enum AssRedirectUrlEnum {
    //pc  号商 order/buyer/afterSale
    PC_AFTER_SALE_WORK_ORDER(0, "/order/afterSale"),
    // android,
    ANDROID_AFTER_SALE_WORK_ORDER(1, "/order/after_sale"),
    // h5
    H5_AFTER_SALE_WORK_ORDER(2, "/order/afterSale"),
    // hormone
    HORMONE_AFTER_SALE_WORK_ORDER(3, "hmAppRouter://order/orderDetailPage")
    ;

    /**
     * 0pc,1Android,2h5
     */
    private final Integer clientType;
    private final String urlTemplate;

    public static List<InternalMsgRedirectReqPO> getRedirectUrls() {
        for (AssRedirectUrlEnum value : AssRedirectUrlEnum.values()) {
            return List.of(new InternalMsgRedirectReqPO(value.getUrlTemplate(), value.getClientType()));
        }
        return null;
    }
}
