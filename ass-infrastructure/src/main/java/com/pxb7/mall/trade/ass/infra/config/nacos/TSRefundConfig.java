package com.pxb7.mall.trade.ass.infra.config.nacos;

import cn.hutool.core.collection.CollectionUtil;
import com.pxb7.mall.trade.ass.infra.util.StringUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

@Slf4j
@Component
@Data
public class TSRefundConfig  implements NacosYamlConfigListener {

    private List<String> tsIdList;

    @Override
    public void onRefresh(Object newConfig) {
        String idStr = newConfig.toString();
        if (StringUtil.isNotBlank(idStr)) {
            List<String> list = Arrays.stream(idStr.split(",")).toList();
            if (CollectionUtil.isNotEmpty(list)) {
                this.tsIdList = list;
                log.info("nacos tsId: {}", tsIdList);
            }
        }
    }

    @Override
    public String configPath() {
        return "refund_ts";
    }
}
