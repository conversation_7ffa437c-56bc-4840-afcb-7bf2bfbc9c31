package com.pxb7.mall.trade.ass.infra.repository.db;


import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Set;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.pxb7.mall.trade.ass.infra.model.AssRetrieveGameConfigDetailReqPO;
import com.pxb7.mall.trade.ass.infra.repository.db.mapper.AssRetrieveGameConfigDetailMapper;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.AssRetrieveGameConfigDetail;
import com.pxb7.mall.trade.ass.infra.repository.db.AssRetrieveGameConfigDetailRepository;

/**
 * 售后找回游戏配置明细表(AssRetrieveGameConfigDetail)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-30 13:45:39
 */
@Slf4j
@Repository
public class AssRetrieveGameConfigDetailDbRepository extends ServiceImpl<AssRetrieveGameConfigDetailMapper, AssRetrieveGameConfigDetail> implements AssRetrieveGameConfigDetailRepository {

    @Override
    public boolean insert(AssRetrieveGameConfigDetailReqPO.AddPO param) {
        AssRetrieveGameConfigDetail entity = new AssRetrieveGameConfigDetail();
        entity.setDetailId(param.getDetailId());
        entity.setGameConfigId(param.getGameConfigId());
        entity.setRetrieveDays(param.getRetrieveDays());
        entity.setGameId(param.getGameId());
        entity.setGroupOpen(param.getGroupOpen());
        entity.setCreateUserId(param.getCreateUserId());
        entity.setUpdateUserId(param.getUpdateUserId());
        return this.save(entity);
    }

    @Override
    public boolean update(AssRetrieveGameConfigDetailReqPO.UpdatePO param) {
        LambdaUpdateWrapper<AssRetrieveGameConfigDetail> updateWrapper = new LambdaUpdateWrapper<>();
        //where
        updateWrapper.eq(AssRetrieveGameConfigDetail::getId, param.getId());
        //set
        if (StringUtils.isNotBlank(param.getDetailId())) {
            updateWrapper.set(AssRetrieveGameConfigDetail::getDetailId, param.getDetailId());
        }
        if (StringUtils.isNotBlank(param.getGameConfigId())) {
            updateWrapper.set(AssRetrieveGameConfigDetail::getGameConfigId, param.getGameConfigId());
        }
        if (Objects.nonNull(param.getRetrieveDays())) {
            updateWrapper.set(AssRetrieveGameConfigDetail::getRetrieveDays, param.getRetrieveDays());
        }
        if (StringUtils.isNotBlank(param.getGameId())) {
            updateWrapper.set(AssRetrieveGameConfigDetail::getGameId, param.getGameId());
        }
        if (Objects.nonNull(param.getGroupOpen())) {
            updateWrapper.set(AssRetrieveGameConfigDetail::getGroupOpen, param.getGroupOpen());
        }
        if (StringUtils.isNotBlank(param.getCreateUserId())) {
            updateWrapper.set(AssRetrieveGameConfigDetail::getCreateUserId, param.getCreateUserId());
        }
        if (StringUtils.isNotBlank(param.getUpdateUserId())) {
            updateWrapper.set(AssRetrieveGameConfigDetail::getUpdateUserId, param.getUpdateUserId());
        }
        return this.update(updateWrapper);
    }

    @Override
    public boolean deleteById(AssRetrieveGameConfigDetailReqPO.DelPO param) {
        return this.removeById(param.getId());
    }

    @Override
    public AssRetrieveGameConfigDetail findById(Long id) {
        return this.getById(id);
    }

    @Override
    public List<AssRetrieveGameConfigDetail> list(AssRetrieveGameConfigDetailReqPO.SearchPO param) {
        LambdaQueryWrapper<AssRetrieveGameConfigDetail> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(param.getDetailId())) {
            queryWrapper.eq(AssRetrieveGameConfigDetail::getDetailId, param.getDetailId());
        }
        if (StringUtils.isNotBlank(param.getGameConfigId())) {
            queryWrapper.eq(AssRetrieveGameConfigDetail::getGameConfigId, param.getGameConfigId());
        }
        if (Objects.nonNull(param.getRetrieveDays())) {
            queryWrapper.eq(AssRetrieveGameConfigDetail::getRetrieveDays, param.getRetrieveDays());
        }
        if (StringUtils.isNotBlank(param.getGameId())) {
            queryWrapper.eq(AssRetrieveGameConfigDetail::getGameId, param.getGameId());
        }
        if (Objects.nonNull(param.getGroupOpen())) {
            queryWrapper.eq(AssRetrieveGameConfigDetail::getGroupOpen, param.getGroupOpen());
        }
        if (StringUtils.isNotBlank(param.getCreateUserId())) {
            queryWrapper.eq(AssRetrieveGameConfigDetail::getCreateUserId, param.getCreateUserId());
        }
        if (StringUtils.isNotBlank(param.getUpdateUserId())) {
            queryWrapper.eq(AssRetrieveGameConfigDetail::getUpdateUserId, param.getUpdateUserId());
        }
        return this.list(queryWrapper);
    }

    @Override
    public Page<AssRetrieveGameConfigDetail> page(AssRetrieveGameConfigDetailReqPO.PagePO param) {
        LambdaQueryWrapper<AssRetrieveGameConfigDetail> queryWrapper = new LambdaQueryWrapper<>();
        // 返回分页查询结果
        if (StringUtils.isNotBlank(param.getDetailId())) {
            queryWrapper.eq(AssRetrieveGameConfigDetail::getDetailId, param.getDetailId());
        }
        if (StringUtils.isNotBlank(param.getGameConfigId())) {
            queryWrapper.eq(AssRetrieveGameConfigDetail::getGameConfigId, param.getGameConfigId());
        }
        if (Objects.nonNull(param.getRetrieveDays())) {
            queryWrapper.eq(AssRetrieveGameConfigDetail::getRetrieveDays, param.getRetrieveDays());
        }
        if (StringUtils.isNotBlank(param.getGameId())) {
            queryWrapper.eq(AssRetrieveGameConfigDetail::getGameId, param.getGameId());
        }
        if (Objects.nonNull(param.getGroupOpen())) {
            queryWrapper.eq(AssRetrieveGameConfigDetail::getGroupOpen, param.getGroupOpen());
        }
        if (StringUtils.isNotBlank(param.getCreateUserId())) {
            queryWrapper.eq(AssRetrieveGameConfigDetail::getCreateUserId, param.getCreateUserId());
        }
        if (StringUtils.isNotBlank(param.getUpdateUserId())) {
            queryWrapper.eq(AssRetrieveGameConfigDetail::getUpdateUserId, param.getUpdateUserId());
        }
        return this.page(new Page<>(param.getPageIndex(), param.getPageSize()), queryWrapper);
    }

    @Override
    public AssRetrieveGameConfigDetail getByGameId(String gameId) {
        if(StrUtil.isBlank(gameId)){
            return null;
        }
        LambdaQueryWrapper<AssRetrieveGameConfigDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AssRetrieveGameConfigDetail::getGameId, gameId);
        return this.baseMapper.selectOne(queryWrapper);
    }

    @Override
    public List<AssRetrieveGameConfigDetail> getListBy(Set<String> gameIdSet) {
        if(CollUtil.isEmpty(gameIdSet)){
            return Collections.emptyList();
        }
        LambdaQueryWrapper<AssRetrieveGameConfigDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(AssRetrieveGameConfigDetail::getGameId, gameIdSet);
        return this.list(queryWrapper);
    }
}
