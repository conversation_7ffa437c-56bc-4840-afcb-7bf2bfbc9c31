package com.pxb7.mall.trade.ass.infra.repository.db;

import com.baomidou.mybatisplus.extension.service.IService;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.RefundReason;

import java.util.List;

/**
 * 退款原因管理(RefundReason)表服务接口
 *
 * <AUTHOR>
 * @since 2024-09-25 15:02:33
 */
public interface RefundReasonRepository extends IService<RefundReason> {

    /**
     * 通过 id 查询退款原因（未删除的）
     */
    RefundReason getOneByReasonId(String refundReasonId);

    /**
     * 获取 退款原因Content（未删除的）
     */
    String getReasonContent(String refundReasonId);

    /**
     * 买家获取 退款原因列表
     */
    List<RefundReason> getListForBuyer();

    /**
     * 客服获取 退款原因列表
     */
    List<RefundReason> getListForCustomer(String reasonRefundId,Integer refundType);

    /**
     * 根据 业务ID 获取 退款原因 （包括已经删除的）
     */
    RefundReason getOneByReasonIdIncludeDeleted(String reasonId);
    /**
     * 根据 业务ID 获取 退款原因Content （包括已经删除的）
     */
    String getReasonContentIncludeDeleted(String reasonId);
}
