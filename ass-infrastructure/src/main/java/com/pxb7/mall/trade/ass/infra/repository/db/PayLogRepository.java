package com.pxb7.mall.trade.ass.infra.repository.db;

import com.baomidou.mybatisplus.extension.service.IService;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.PayLog;

import java.util.List;

/**
 * 支付记录(包括线上原路退款)(PayLog)表服务接口
 *
 * <AUTHOR>
 * @since 2024-09-06 19:58:42
 */
public interface PayLogRepository extends IService<PayLog> {

    List<PayLog> getListById(List<String> paymentIds);

    /**
     * 退款用 因为退款payment和退款pay_log是一对一
     * @param refundPaymentId
     * @param payStatus
     * @return
     */
    PayLog getPayLog(String refundPaymentId, Integer payStatus);

    PayLog getPayLogById(String payLogId);
}

