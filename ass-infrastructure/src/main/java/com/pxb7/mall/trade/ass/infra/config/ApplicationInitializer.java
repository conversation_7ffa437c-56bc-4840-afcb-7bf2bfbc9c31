package com.pxb7.mall.trade.ass.infra.config;

import java.sql.Connection;
import java.sql.SQLException;
import javax.sql.DataSource;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.core.Ordered;
import org.springframework.stereotype.Component;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class ApplicationInitializer implements ApplicationListener<ContextRefreshedEvent>, Ordered {

    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        DataSource dataSource = event.getApplicationContext().getBean(DataSource.class);

        try (Connection ignored = dataSource.getConnection()) {
            log.info("[ContextRefreshedEvent] 已经初始化连接池");
        } catch (SQLException e) {
            log.error("[ContextRefreshedEvent] 初始化连接失败", e);
        }
    }

    @Override
    public int getOrder() {
        return LOWEST_PRECEDENCE - 10;
    }

}
