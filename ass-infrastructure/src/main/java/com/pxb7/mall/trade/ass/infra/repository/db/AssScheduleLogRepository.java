package com.pxb7.mall.trade.ass.infra.repository.db;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.AssScheduleLog;

/**
 * 售后流程进度日志(AssScheduleLog)表服务接口
 *
 * <AUTHOR>
 * @since 2024-08-12 10:59:48
 */
public interface AssScheduleLogRepository extends IService<AssScheduleLog> {
    boolean updateNode(String scheduleId, String nodeId, String data);

    List<AssScheduleLog> findListByScheduleId(String scheduleId);

    boolean delByScheduleIdAndNodeIds(String scheduleId, List<String> nodeIds);

    boolean delByScheduleId(String scheduleId);
}
