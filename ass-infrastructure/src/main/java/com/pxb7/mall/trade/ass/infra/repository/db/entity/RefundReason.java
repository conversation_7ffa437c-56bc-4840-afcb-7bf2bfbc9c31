package com.pxb7.mall.trade.ass.infra.repository.db.entity;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.*;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 退款原因管理(RefundReason)实体类
 *
 * <AUTHOR>
 * @since 2024-09-25 15:02:32
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
@TableName(value = "refund_reason")
public class RefundReason implements Serializable {
    private static final long serialVersionUID = -76737894839879305L;
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 退款原因id
     */
    @TableField(value = "reason_refund_id")
    private String reasonRefundId;
    /**
     * 退款原因
     */
    @TableField(value = "reason_refund_content")
    private String reasonRefundContent;
    /**
     * 挽留策略，0:无 1: 自动推荐替换商品，2员工撮合卖家降价，3不挽留
     */
    @TableField(value = "reason_intercept")
    private Integer reasonIntercept;
    /**
     * 商品自动上架1:是,0:否
     */
    @TableField(value = "auto_grounding")
    private Boolean autoGrounding;
    /**
     * 适用范围:1用户端申请退款,2客户端申请退款,3全部
     */
    @TableField(value = "reason_range")
    private Integer reasonRange;
    /**
     * 层级
     */
    @TableField(value = "reason_level")
    private Integer reasonLevel;
    /**
     * 父原因id
     */
    @TableField(value = "pid")
    private String pid;
    /**
     * 退款类型，1，全额退款，2部分退款
     */
    @TableField(value = "refund_type")
    private Integer refundType;
    /**
     * 创建用户id(客服)
     */
    @TableField(value = "create_user_id")
    private String createUserId;
    /**
     * 更新用户id
     */
    @TableField(value = "update_user_id")
    private String updateUserId;
    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    @TableField(value = "update_time", update="now()")
    private LocalDateTime updateTime;
    /**
     * 是否删除
     */
    @TableLogic
    @TableField(value = "is_deleted")
    private Boolean deleted;

}
