package com.pxb7.mall.trade.ass.infra.remote.model.request;

import lombok.Data;

/**
 * 泰山查询支付结果
 */
@Data
public class TaiShanPayQueryReqPO {

    /**
     * 商户订单号
     */
    private String merchantTradeNo;

    /**
     * 平台订单号
     */
    private String platformOutTradeNo;

    /**
     * 支付宝订单号
     */
    private String tradeNo;

    // 三个订单号有一个不为空就可以，
    // 如果都不为空， 优先级为merchantTradeNo->platformOutTradeNo->tradeNo
}
