package com.pxb7.mall.trade.ass.infra.model;

import com.pxb7.mall.common.client.request.message.SpecialRedirectUrlDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Map;

/**
 * 功能描述:发站内信参数
 * 作者：白春韬
 * 创建日期：2025/08/05
 * 公司名称：金华博淳网络科技有限公司
 * 域名： www.pxb7.com
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class InternalMsgReqPO {

    /**
     * 用户ID-接收消息的人
     */
    private String userId;
    /**
     * 站内信模板id
     */
    private String templateCode;
    /**
     * 消息体参数
     */
    private Map<String, String> params;
    /**
     * 跳转链接参数
     */
    private Map<String, String> urlParams;
    /**
     * 跳转类型：1正常页面传值，2前端区分商家和散户路由不同
     */
    private Integer redirectType;
    /**
     * 跳转路由 只有redirectType为2时才存在
     */
    private List<SpecialRedirectUrlDTO> redirectUrlList;
    /**
     * 业务流水号
     */
    private String businessId;
    /**
     * 业务方识别码
     *
     * @see com.pxb7.mall.common.client.enums.BusinessTypeEnum
     */
    private String businessType;

}
