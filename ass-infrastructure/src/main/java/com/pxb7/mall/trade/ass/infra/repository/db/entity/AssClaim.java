package com.pxb7.mall.trade.ass.infra.repository.db.entity;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.*;
import com.pxb7.mall.trade.ass.client.enums.*;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 售后赔付管理(AssClaim)实体类
 *
 * <AUTHOR>
 * @since 2024-10-26 14:53:53
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
@TableName(value = "ass_claim")
public class AssClaim implements Serializable {

    @Serial
    private static final long serialVersionUID = -31051709039461579L;
    /**
     * 自增id
     */
    @TableField(value = "id")
    private Long id;

    /**
     * 主键ID
     */
    @TableId(value = "ass_claim_id", type = IdType.INPUT)
    private String assClaimId;

    /**
     * 赔付编号
     */
    @TableField(value = "claim_no")
    private String claimNo;

    /**
     * 赔付金额
     */
    @TableField(value = "claim_amount")
    private Long claimAmount;

    /**
     * 售后类型1找回2纠纷
     */
    @TableField(value = "ass_type")
    private Integer assType;

    /**
     * 商品id
     */
    @TableField(value = "product_id")
    private String productId;

    /**
     * 商品编码
     */
    @TableField(value = "product_unique_no")
    private String productUniqueNo;

    /**
     * 订单id
     */
    @TableField(value = "order_item_id")
    private String orderItemId;

    /**
     * 工单ID
     */
    @TableField(value = "work_order_id")
    private String workOrderId;

    /**
     * 工单编号
     */
    @TableField(value = "work_order_no")
    private String workOrderNo;

    /**
     * 申请人ID
     */
    @TableField(value = "proposer_user_id")
    private String proposerUserId;

    /**
     * 申请原因
     */
    @TableField(value = "proposer_reason")
    private String proposerReason;

    /**
     * 放款状态 1:待赔付审核 2:待财务审核 3:线上打款中 4：打款成功 5：已关闭
     *
     * @see AssClaimStatus
     */
    @TableField(value = "`status`")
    private Integer status;

    /**
     * 收款账号类型 1:个人 2:公司
     *
     * @see AssAccountType
     */
    @TableField(value = "account_type")
    private Integer accountType;

    /**
     * 银行code
     */
    @TableField(value = "bank_code")
    private String bankCode;

    /**
     * 支付渠道 1:线上 2:线下
     *
     * @see AssPaymentChannel
     */
    @TableField(value = "payment_channel")
    private Integer paymentChannel;

    /**
     * 收款姓名
     */
    @TableField(value = "payer_name")
    private String payerName;

    /**
     * 收款账号
     */
    @TableField(value = "account")
    private String account;

    /**
     * 收款方式 1:支付宝 2:微信 3:银行卡
     *
     * @see AssPayWay
     */
    @TableField(value = "pay_way")
    private Integer payWay;

    /**
     * 用户的微信二维码
     */
    @TableField(value = "qr_code")
    private String qrCode;

    /**
     * 付款账户ID
     */
    @TableField(value = "payment_account_id")
    private String paymentAccountId;

    /**
     * 业务审核人ID
     */
    @TableField(value = "examine_user_id")
    private String examineUserId;

    /**
     * 业务审核时间
     */
    @TableField(value = "examine_time")
    private LocalDateTime examineTime;

    /**
     * 业务审核驳回原因
     */
    @TableField(value = "examine_reason")
    private String examineReason;

    /**
     * 财务ID
     */
    @TableField(value = "finance_account_id")
    private String financeAccountId;

    /**
     * 财务审核时间
     */
    @TableField(value = "finance_time")
    private LocalDateTime financeTime;

    /**
     * 财务驳回原因
     */
    @TableField(value = "reject_reason")
    private String rejectReason;

    /**
     * 完成时间
     */
    @TableField(value = "complete_time")
    private LocalDateTime completeTime;

    /**
     * 放款凭证列表
     */
    @TableField(value = "proof_list")
    private String proofList;

    // /**
    // * 出纳ID
    // */
    // @TableField(value = "cashier_account_id")
    // private String cashierAccountId;
    //
    // /**
    // * 出纳审核时间
    // */
    // @TableField(value = "cashier_time")
    // private LocalDateTime cashierTime;
    //
    // /**
    // * 出纳驳回原因
    // */
    // @TableField(value = "cashier_reason")
    // private String cashierReason;

    /**
     * 运营渠道 1:螃蟹 2:支付宝小程序
     *
     * @see AssSourceType
     */
    @TableField(value = "source_type")
    private Integer sourceType;

    /**
     * 创建用户ID
     */
    @TableField(value = "create_user_id")
    private String createUserId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    @TableField(value = "update_user_id")
    private String updateUserId;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", update="now()")
    private LocalDateTime updateTime;

    /**
     * 删除标记
     */
    @TableLogic
    @TableField(value = "is_deleted")
    private Boolean deleted;

}
