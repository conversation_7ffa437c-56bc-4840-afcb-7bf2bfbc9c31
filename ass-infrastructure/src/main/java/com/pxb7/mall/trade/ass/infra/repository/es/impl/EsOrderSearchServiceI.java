package com.pxb7.mall.trade.ass.infra.repository.es.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import org.apache.dubbo.common.utils.CollectionUtils;
import org.springframework.data.elasticsearch.client.elc.ElasticsearchTemplate;
import org.springframework.data.elasticsearch.client.elc.NativeQuery;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.query.Criteria;
import org.springframework.data.elasticsearch.core.query.CriteriaQuery;
import org.springframework.stereotype.Service;

import com.pxb7.mall.trade.ass.infra.repository.es.EsOrderSearchService;
import com.pxb7.mall.trade.ass.infra.repository.es.entity.MainOrderAggregation;

import co.elastic.clients.elasticsearch._types.query_dsl.*;
import jakarta.annotation.Resource;

@Service
public class EsOrderSearchServiceI implements EsOrderSearchService {

    @Resource
    private ElasticsearchTemplate elasticsearchTemplate;

    @Override
    public MainOrderAggregation getOrderDoc(String orderId) {

        Criteria criteria = new Criteria();
        criteria.and(Criteria.where("order_id").is(orderId));
        CriteriaQuery criteriaQuery = CriteriaQuery.builder(criteria).build();

        SearchHit<MainOrderAggregation> mainOrderAggregationSearchHit =
            elasticsearchTemplate.searchOne(criteriaQuery, MainOrderAggregation.class);

        return Optional.ofNullable(mainOrderAggregationSearchHit).map(SearchHit::getContent).orElse(null);
    }

    @Override
    public void syncOrderList(List<MainOrderAggregation> mainOrderAggregationList) {
        elasticsearchTemplate.save(mainOrderAggregationList);
    }

    @Override
    public void deleteOrder(String orderId) {

        Criteria criteria = new Criteria();
        criteria.and(Criteria.where("order_id").is(orderId));
        CriteriaQuery criteriaQuery = CriteriaQuery.builder(criteria).build();
        elasticsearchTemplate.delete(criteriaQuery, MainOrderAggregation.class);
    }

    // @Override
    // public SearchHits<OrderItemAggregation> getGameSalesVolume(GameSalesVolumeReqPO po) {
    // List<Query> queryList = new ArrayList<>();
    // DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS");
    // String nestPath = "order_aggregation_list";
    //
    // LocalDateTime endTime = LocalDateTime.now();
    // LocalDateTime startTime = endTime.plusDays(-60);
    //
    // Query cycleQuery = QueryBuilders.range(timeQuery -> timeQuery
    // .field("create_time")
    // .format("uuuu-MM-dd'T'HH:mm:ss.SSS")
    // .gte(JsonData.of(startTime.format(formatter)))
    // .lte(JsonData.of(endTime.format(formatter))));
    // queryList.add(cycleQuery);
    //
    //
    // Query orderItemStatusQuery = QueryBuilders.range(timeQuery -> timeQuery
    // .field("order_item_status").from(OrderItemStatusEnum.DEAL_SUCCESS.getValue().toString())
    // .gte(JsonData.of(startTime.format(formatter)))
    // .lte(JsonData.of(endTime.format(formatter))));
    // queryList.add(orderItemStatusQuery);
    //
    //
    // var nativeQuery = NativeQuery
    // .builder()
    // .withQuery(QueryBuilders.bool().must(queryList).build()._toQuery())
    // .withAggregation("game_id",AggregationBuilders.terms().field("game_id").build()._toAggregation())
    // .build();
    // SearchHits<OrderItemAggregation> search = elasticsearchTemplate.search(nativeQuery, OrderItemAggregation.class);
    // return search;
    // }

    @Override
    public SearchHits<MainOrderAggregation> listByItemIds(List<String> orderItemIds) {

        if (CollectionUtils.isEmpty(orderItemIds)) {
            return null;
        }

        List<Query> nestShouldQueryList = new ArrayList<>();
        for (String orderItemId : orderItemIds) {
            MatchQuery matchQuery =
                QueryBuilders.match().field("order_aggregation_list.order_item_id").query(orderItemId).build();
            nestShouldQueryList.add(matchQuery._toQuery());
        }

        NestedQuery nestedQuery = QueryBuilders.nested().path("order_aggregation_list")
            .query(QueryBuilders.bool().should(nestShouldQueryList).build()._toQuery()).scoreMode(ChildScoreMode.None)
            .build();

        NativeQuery nativeQuery = NativeQuery.builder().withQuery(nestedQuery._toQuery()).build();

        return elasticsearchTemplate.search(nativeQuery, MainOrderAggregation.class);
    }

    @Override
    public List<MainOrderAggregation> listByUserId(String userId) {

        TermQuery termQuery = QueryBuilders.term().field("order_aggregation_list.buyer_id").value(userId).build();

        NestedQuery nestedQuery = QueryBuilders.nested().path("order_aggregation_list").scoreMode(ChildScoreMode.None)
            .query(termQuery._toQuery()).build();

        NativeQuery nativeQuery = NativeQuery.builder()
            .withQuery(QueryBuilders.bool().must(nestedQuery._toQuery()).build()._toQuery()).build();

        SearchHits<MainOrderAggregation> searchHits =
            elasticsearchTemplate.search(nativeQuery, MainOrderAggregation.class);

        return searchHits.stream().map(SearchHit::getContent).collect(Collectors.toList());
    }

    /**
     * 根据房间id查询订单
     * 
     * @param roomId
     */
    @Override
    public MainOrderAggregation findOneByRoomId(String roomId) {

        TermQuery groupRoomTerm =
            QueryBuilders.term().field("order_aggregation_list.group_room_id").value(roomId).build();

        List<Query> queryList = new ArrayList<>();
        queryList.add(groupRoomTerm._toQuery());

        NestedQuery nestedQuery = QueryBuilders.nested().path("order_aggregation_list")
            .query(
                QueryBuilders.bool().must(QueryBuilders.bool().must(queryList).build()._toQuery()).build()._toQuery())
            .build();

        NativeQuery nativeQuery = NativeQuery.builder()
            .withQuery(QueryBuilders.bool().must(nestedQuery._toQuery()).build()._toQuery()).build();

        SearchHit<MainOrderAggregation> searchHit =
            elasticsearchTemplate.searchOne(nativeQuery, MainOrderAggregation.class);
        if (searchHit != null) {
            return searchHit.getContent();
        } else {
            return new MainOrderAggregation();
        }
    }
}
