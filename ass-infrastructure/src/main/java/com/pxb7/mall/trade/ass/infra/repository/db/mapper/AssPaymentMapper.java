package com.pxb7.mall.trade.ass.infra.repository.db.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.AssPayment;

/**
 * 售后收款列表(AssPayment)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-10-26 14:49:06
 */
@Mapper
public interface AssPaymentMapper extends BaseMapper<AssPayment> {
    /**
     * 批量新增数据
     *
     * @param entities List<AssPayment> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<AssPayment> entities);

}
