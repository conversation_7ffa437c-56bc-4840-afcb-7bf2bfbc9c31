package com.pxb7.mall.trade.ass.infra.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum OrderServiceModeEnum {

    /**
     * 代售
     */
    AGENT(1, "代售"),

    /**
     * 中介
     */
    MEDIATOR(2, "中介"),

    /**
     * 诚心卖
     */
    SINCERITY_SELL(3,"诚心卖"),
    ;

    private final Integer value;

    private final String label;


    public static String getLabel(Integer value){
        return Arrays.stream(OrderServiceModeEnum.values())
                .filter(e->e.getValue().equals(value))
                .map(OrderServiceModeEnum::getLabel)
                .findAny()
                .orElse(null);
    }
}
