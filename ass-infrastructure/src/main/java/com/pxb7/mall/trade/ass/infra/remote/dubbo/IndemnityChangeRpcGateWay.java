package com.pxb7.mall.trade.ass.infra.remote.dubbo;

import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.fastjson2.JSON;
import com.pxb7.mall.trade.ass.infra.enums.ErrorCode;
import com.pxb7.mall.trade.ass.infra.exception.BusinessException;
import com.pxb7.mall.trade.order.client.api.OrderItemIndemnityDubboServiceI;
import com.pxb7.mall.trade.order.client.dto.response.orderitemindemnity.IndemnityChangeRespDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

/**
 * 包赔变更远程服务
 *
 * <AUTHOR>
 * @date 2025/5/14 13:51
 */
@Component
@Slf4j
public class IndemnityChangeRpcGateWay {

    @DubboReference(providedBy = "order")
    private OrderItemIndemnityDubboServiceI orderItemIndemnityDubboServiceI;

    /**
     * 根据变更ID查询变更详情
     *
     * @param indemnityChangeId 变更ID
     * @return 变更详情
     */
    public IndemnityChangeRespDTO getIndemnityChangeDetailsByChangeId(String indemnityChangeId) {
        SingleResponse<IndemnityChangeRespDTO> response =
                orderItemIndemnityDubboServiceI.getIndemnityChangeDetailsByChangeId(indemnityChangeId);

        if (!response.isSuccess()) {
            log.error("Failed to getIndemnityChangeDetailsByChangeId，indemnityChangeId:{}，singleResponse:{}", indemnityChangeId, JSON.toJSONString(response));
            throw new BusinessException(ErrorCode.INDEMNITY_CHANGE_QUERY_FAIL);
        }
        return response.getData();
    }

}
