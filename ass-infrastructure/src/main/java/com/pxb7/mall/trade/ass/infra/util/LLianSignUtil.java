package com.pxb7.mall.trade.ass.infra.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.lang3.StringUtils;

import javax.crypto.Cipher;
import java.nio.charset.StandardCharsets;
import java.security.*;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;

public class LLianSignUtil {

    /**
     * MD5 摘要计算(byte[]).
     * 
     * @param src byte[]
     * @throws Exception
     * @return String
     */
    public static String md5Digest(byte[] src) throws NoSuchAlgorithmException {
        // MD5 is 32 bit message digest
        MessageDigest alg = MessageDigest.getInstance("MD5");
        return byteArrayToHexString(alg.digest(src));
    }

    private static String byteArrayToHexString(byte[] digest) {
        StringBuilder hexString = new StringBuilder();
        for (byte b : digest) {
            String hex = Integer.toHexString(b & 0xFF);
            if (hex.length() == 1) {
                hexString.append('0');
            }
            hexString.append(hex);
        }
        return hexString.toString();
    }

    /**
     * 签名处理
     * 
     * @param prikeyvalue：私钥
     * @param sign_str：签名源内容
     * @return
     */
    public static String sign(String prikeyvalue, String sign_str) throws Exception {

        PKCS8EncodedKeySpec priPKCS8 = new PKCS8EncodedKeySpec(getBytesBASE64(prikeyvalue));
        KeyFactory keyf = KeyFactory.getInstance("RSA");
        PrivateKey myprikey = keyf.generatePrivate(priPKCS8);
        // 用私钥对信息生成数字签名
        Signature signet = Signature.getInstance("MD5withRSA");
        signet.initSign(myprikey);
        signet.update(sign_str.getBytes("UTF-8"));
        byte[] signed = signet.sign(); // 对信息的数字签名
        return new String(org.apache.commons.codec.binary.Base64.encodeBase64(signed));

    }

    /**
     * RSA加密
     */
    public static String encrypt(String pubKeyValue, String content) throws Exception {
        PublicKey publicKey = KeyFactory.getInstance("RSA")
            .generatePublic(new X509EncodedKeySpec(Base64.getDecoder().decode(pubKeyValue)));

        Cipher cipher = Cipher.getInstance("RSA");
        cipher.init(Cipher.ENCRYPT_MODE, publicKey);
        // 分段加密
        byte[] contentBytes = content.getBytes();
        byte[] split = new byte[64];
        int j = 0;
        StringBuilder stringBuilder = new StringBuilder();
        for (int i = 0; i < contentBytes.length; i++) {
            split[j] = contentBytes[i];
            if (i % 64 == 0 && i != 0) {
                byte[] subEncryptedBytes = cipher.doFinal(split);
                stringBuilder.append(new String(subEncryptedBytes));
                split = new byte[64];
                j = 0;
            }

            if (i == contentBytes.length - 1) {
                byte[] subEncryptedBytes = cipher.doFinal(split);
                stringBuilder.append(new String(subEncryptedBytes));
                j = 0;
            }
        }
        return Base64.getEncoder().encodeToString(stringBuilder.toString().getBytes());
    }

    /**
     * @param s
     * @return
     */
    public static byte[] getBytesBASE64(String s) {
        if (s == null) {
            return null;
        }
        byte[] b = org.apache.commons.codec.binary.Base64.decodeBase64(s.getBytes(StandardCharsets.UTF_8));
        return b;
    }

    /**
     * 将对象的属性和织按照key_1=value_1&key_2=value2的形式进行排列
     * 
     * @param jsonString
     * @return
     */
    public static String toSignStr(String jsonString) throws JsonProcessingException {
        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode jsonNode = objectMapper.readTree(jsonString);
        if (jsonNode.isObject()) {
            return convert(jsonNode);
        }
        return null;
    }

    public static String convert(JsonNode jsonNode) {
        StringBuilder sb = new StringBuilder();
        Iterator<String> fieldNames = jsonNode.fieldNames();

        // 排序
        List<String> fieldList = new ArrayList<>();
        while (fieldNames.hasNext()) {
            fieldList.add(fieldNames.next());
        }
        List<String> fields = fieldList.stream().sorted().collect(Collectors.toList());

        // 拼接字符串
        int lastIndex = fields.size() - 1;
        for (String fieldName : fields) {

            String fieldValue = jsonNode.get(fieldName).asText();

            // 不参与签名字段
            if (StringUtils.isBlank(fieldValue) || "sign".equals(fieldName) || "card_no".equals(fieldName)) {
                continue;
            }

            sb.append(fieldName).append("=").append(fieldValue);
            if (lastIndex != fields.lastIndexOf(fieldName)) {
                sb.append("&");
            }
        }
        return sb.toString();
    }
}
