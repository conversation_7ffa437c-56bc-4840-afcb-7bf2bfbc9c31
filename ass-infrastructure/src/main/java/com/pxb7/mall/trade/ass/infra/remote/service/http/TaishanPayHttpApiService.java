package com.pxb7.mall.trade.ass.infra.remote.service.http;

import cn.hutool.core.util.StrUtil;
import com.alibaba.cola.exception.BizException;
import com.alibaba.cola.exception.SysException;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.pxb7.mall.trade.ass.infra.remote.model.outpayparam.request.TspayCreationReqPO;
import com.pxb7.mall.trade.ass.infra.remote.model.request.*;
import com.pxb7.mall.trade.order.client.dto.ErrorCode;
import lombok.extern.slf4j.Slf4j;
import okhttp3.ResponseBody;
import org.springframework.stereotype.Service;
import retrofit2.Response;

import java.io.IOException;

@Slf4j
@Service
public class TaishanPayHttpApiService extends HttpBaseService {
    public JSONObject partRefundV3(TaiShanPartRefundV3ReqPO reqPO, TspayCreationReqPO po) {
        super.taishanSignatureInterceptor.setMd5Key(po.getMd5Key());

        Response<ResponseBody> response;
        try {
            log.info("invoke Ts 部分退款 入参:{}, appId:{}", reqPO, po.getApp_id());
            response = taishanPayHttpApi.partRefundV3(reqPO, po.getApp_id()).execute();
            log.info("invoke Ts 部分退款 响应:{}", response.body());
        } catch (Exception e) {
            log.error("invoke Ts 部分退款 异常  req:{}", reqPO, e);
            throw new SysException(e.getMessage());
        }

        return parseResponse(response);
    }

    public JSONObject refund(TaiShanRefundReqPO reqPO, TspayCreationReqPO po) {
        super.taishanSignatureInterceptor.setMd5Key(po.getMd5Key());

        Response<ResponseBody> response;
        try {
            log.info("invoke Ts 全额退款 入参:{}, appId:{}", reqPO, po.getApp_id());
            response = taishanPayHttpApi.refund(reqPO, po.getApp_id()).execute();
            log.info("invoke Ts 全额退款 响应:{}", response.body());
        } catch (Exception e) {
            log.error("invoke Ts 全额退款 异常  req:{}", reqPO, e);
            throw new SysException(e.getMessage());
        }

        return parseResponse(response);
    }

    public JSONObject refundPayQuery(TaiShanPayQueryReqPO reqPO, TspayCreationReqPO po) {
        super.taishanSignatureInterceptor.setMd5Key(po.getMd5Key());

        Response<ResponseBody> response;
        try {
            log.info("invoke Ts 退款查询 入参:{}, appId:{}", reqPO, po);
            response = taishanPayHttpApi.payQuery(reqPO, po.getApp_id()).execute();
            log.info("invoke Ts 退款查询 出参:{}", JSON.toJSONString(response));
            return parseResponse(response);
        } catch (IOException e) {
            log.error("invoke Ts 退款查询 异常 {}", JSONObject.toJSONString(reqPO), e);
            throw new SysException(e.getMessage());
        }
    }

    /**
     * 处理响应结果
     */
    private JSONObject parseResponse(Response<ResponseBody> response) {
        String result = "";

        if (!response.isSuccessful()) {
            log.error("泰山请求失败, response:{}", JSONObject.toJSONString(response));
            throw new BizException(response.message());
        }

        try (ResponseBody body = response.body()) {
            if (body != null) {
                result = body.string();
            }
        } catch (IOException e) {
            log.error("解析泰山返回结果异常, response:{}", JSONObject.toJSONString(response), e);
            throw new BizException(ErrorCode.PAY_ERROR.getErrCode(), "泰山支付http请求异常");
        }

        log.info("泰山返回结果:{}", result);

        if (StrUtil.isBlank(result)) {
            log.error("泰山返回结果为空, response:{}", JSONObject.toJSONString(response));
            throw new BizException(ErrorCode.PAY_ERROR.getErrCode(), "泰山返回结果为空");
        }

        return JSON.parseObject(result);
    }
}
