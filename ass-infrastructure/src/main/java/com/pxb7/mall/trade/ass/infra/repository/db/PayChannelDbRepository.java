package com.pxb7.mall.trade.ass.infra.repository.db;


import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pxb7.mall.trade.ass.infra.repository.db.mapper.PayChannelMapper;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.PayChannel;
import com.pxb7.mall.trade.ass.infra.repository.db.PayChannelRepository;

/**
 * 支付渠道表(PayChannel)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-09-27 10:10:55
 */
@Slf4j
@Repository
public class PayChannelDbRepository extends ServiceImpl<PayChannelMapper, PayChannel> implements PayChannelRepository {

    @Override
    public PayChannel getPayChannelById(String payChannelId) {
        return this.lambdaQuery().eq(PayChannel::getChannelId, payChannelId).last("limit 1").one();
    }
}
