package com.pxb7.mall.trade.ass.infra.repository.db.entity;

import java.io.Serializable;
import java.util.Date;
import java.time.*;
import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.*;
import com.pxb7.mall.trade.order.client.enums.pay.PayStatusEnum;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 支付记录(包括线上原路退款)(PayLog)实体类
 *
 * <AUTHOR>
 * @since 2024-09-06 19:58:42
 */
@Data
@Accessors(chain = true)
@TableName(value = "pay_log")
public class PayLog implements Serializable {
    private static final long serialVersionUID = -29627332490918071L;
    /**
     * 主键
     */
    @TableField(value = "id")
    private Long id;
    /**
     * 支付记录id
     */
    @TableId(value = "pay_log_id", type = IdType.INPUT)
    private String payLogId;
    /**
     * 渠道id
     */
    @TableField(value = "pay_channel_id")
    private String payChannelId;
    /**
     * 支付商户id
     */
    @TableField(value = "pay_merchant_id")
    private String payMerchantId;
    /**
     * 内部交易单号
     */
    @TableField(value = "payment_id")
    private String paymentId;
    /**
     * 交易状态: 1待执行 2执行中 3执行成功 4执行失败
     * @see PayStatusEnum
     */
    @TableField(value = "pay_status")
    private Integer payStatus;

    /**
     * 交易模式 1:线上支付 2:线下支付 3:线上原路退款  4：系统触发退款
     *  @see com.pxb7.mall.trade.order.client.enums.pay.TradeModeEnum
     */
    @TableField(value = "trade_mode")
    private Integer tradeMode;

    @TableField(value = "user_pay_account")
    private String userPayAccount;

    /**
     * 线下支付图片url, 多个的话逗号分割
     */
    @TableField(value = "pay_offline_image_url")
    private String payOfflineImageUrl;
    /**
     * 三方外部支付单号
     */
    @TableField(value = "out_trade_no")
    private String outTradeNo;
    /**
     * 原路退三方外部单号
     */
    @TableField(value = "refund_out_trade_no")
    private String refundOutTradeNo;
    /**
     * 三方创单请求参数
     */
    @TableField(value = "request_param")
    private String requestParam;
    /**
     * 三方返回链接
     */
    @TableField(value = "response_url")
    private String responseUrl;
    /**
     * 支付回调结果
     */
    @TableField(value = "callback_result")
    private String callbackResult;
    /**
     * 交易金额
     */
    @TableField(value = "trade_amount")
    private Long tradeAmount;
    /**
     * 业务类型：1, "诚心卖" 2, "代售" 3, "中介" 4, "保证金" 5, "充值、金币" 6, "售后"
     * @see com.pxb7.mall.trade.order.client.enums.pay.BusinessTypeEnum
     */
    @TableField(value = "biz_type")
    private Integer bizType;
    /**
     * 客户端类型 1, "pc网页支付" 2, "手机app支付" 3, "手机网站"
     */
    @TableField(value = "pay_client_type")
    private Integer payClientType;
    /**
     * 支付类型 1支付宝 2微信 3银行卡
     * @see com.pxb7.mall.trade.order.client.enums.pay.PaymentTypeEnum
     */
    @TableField(value = "payment_type")
    private Integer paymentType;
    /**
     * 扩展字段
     */
    @TableField(value = "ext_info")
    private String extInfo;
    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    @TableField(value = "update_time", update="now()")
    private LocalDateTime updateTime;
    /**
     * 逻辑删除，0:删除，1:正常
     */
    @TableField(value = "is_deleted")
    @TableLogic
    private Boolean deleted;

}

