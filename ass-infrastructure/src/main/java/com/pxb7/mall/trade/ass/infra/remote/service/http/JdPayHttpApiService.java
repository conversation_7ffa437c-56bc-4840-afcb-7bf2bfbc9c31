package com.pxb7.mall.trade.ass.infra.remote.service.http;

import com.alibaba.cola.exception.BizException;
import com.alibaba.fastjson.JSONObject;
import com.pxb7.mall.trade.ass.infra.exception.BusinessException;
import com.pxb7.mall.trade.ass.infra.remote.model.outpayparam.request.JdPayOutParamPO;
import com.pxb7.mall.trade.ass.infra.remote.service.http.dto.request.JdPayReqPO;
import com.pxb7.mall.trade.ass.infra.remote.service.http.dto.response.JdPayRespPO;
import com.pxb7.mall.trade.ass.infra.util.jdPayUtil.JdPayUtil;
import lombok.extern.slf4j.Slf4j;
import okhttp3.ResponseBody;
import org.springframework.stereotype.Service;
import retrofit2.Response;

/**
 * 京东支付
 */
@Service
@Slf4j
public class JdPayHttpApiService extends HttpBaseService {

    /**
     * 发起退款
     */
    public JdPayRespPO.RefundRespPO refund(JdPayReqPO.RefundParam po, JdPayOutParamPO paramPO) {
        log.info("【京东支付】发起退款，po = {}", po);
        try {
            //加密并签名
            JdPayReqPO.BaseParam param = JdPayUtil.encryptAndSign(paramPO, JSONObject.toJSONString(po), po.getOutTradeNo());
            //发起请求
            Response<ResponseBody> response = jdPayHttpApi.refund(JdPayUtil.convertToMap(param)).execute();
            //验签并返回
            JSONObject jsonObject = JdPayUtil.verifySignAndReturn(response, paramPO);
            //解析拿到结果
            return jsonObject.toJavaObject(JdPayRespPO.RefundRespPO.class);
        } catch (Exception e) {
            throw new BizException(e.getMessage());
        }
    }

    /**
     * 退款查询
     */
    public JdPayRespPO.RefundRespPO refundQuery(JdPayReqPO.RefundQueryParam po, JdPayOutParamPO paramPO) {
        log.info("【京东支付】退款查询，po = {}", po);
        try {
            //加密并签名
            JdPayReqPO.BaseParam param = JdPayUtil.encryptAndSign(paramPO, JSONObject.toJSONString(po), po.getOutTradeNo());
            //发起请求
            Response<ResponseBody> response = jdPayHttpApi.refundQuery(JdPayUtil.convertToMap(param)).execute();
            //验签并返回
            JSONObject jsonObject = JdPayUtil.verifySignAndReturn(response, paramPO);
            //解析拿到结果
            JdPayRespPO.RefundRespPO resultPO = jsonObject.toJavaObject(JdPayRespPO.RefundRespPO.class);
            if (!"0000".equals(resultPO.getResultCode())) {
                log.warn("【京东支付】退款查询失败，resultPO = {}", resultPO);
                throw new BizException(resultPO.getResultCode(),resultPO.getResultDesc());
            }
            return resultPO;
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw new BizException(e.getMessage());
        }
    }

}
