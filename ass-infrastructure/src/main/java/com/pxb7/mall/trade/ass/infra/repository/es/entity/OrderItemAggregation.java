package com.pxb7.mall.trade.ass.infra.repository.es.entity;

import java.time.LocalDateTime;
import java.util.List;

import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.DateFormat;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import lombok.Data;

/**
 * 订单聚合数据
 */
@Data
@Document(indexName = "order_item_aggregation")
public class OrderItemAggregation {
    /************************************************* 订单行信息 ****************************************************/
    /**
     * 订单id
     */
    @Id
    @Field(name = "order_item_id", type = FieldType.Text)
    private String orderItemId;

    /**
     * 主订单id
     */
    @Field(type = FieldType.Keyword, value = "order_id")
    private String orderId;

    /**
     * 商品id
     */
    @Field(name = "product_id", type = FieldType.Text)
    private String productId;
    /**
     * 游戏ID
     */
    @Field(type = FieldType.Keyword, value = "game_id")
    private String gameId;
    /**
     * 买家id
     */
    @Field(type = FieldType.Keyword, value = "buyer_id")
    private String buyerId;
    /**
     * 卖家id,诚心卖的卖家是系统
     */
    @Field(type = FieldType.Keyword, value = "seller_id")
    private String sellerId;
    /**
     * 商品类型: 1账号 2充值 3金币 4装备 5初始号 20诚心卖
     */
    @Field(type = FieldType.Integer, value = "product_type")
    private Integer productType;
    /**
     * 1待付款 2交易中 3待结算 4已成交 5已取消 (待支付是买家, 其他都是卖家状态)
     */
    @Field(type = FieldType.Integer, value = "order_item_status")
    private Integer orderItemStatus;
    /**
     * 买家订单状态: 1待付款 2交易中 3已成交 4已取消
     */
    @Field(type = FieldType.Integer, value = "buyer_status")
    private Integer buyerStatus;
    /**
     * 卖家订单状态: 1交易中 2待结算 3已成交 4已取消
     */
    @Field(type = FieldType.Integer, value = "seller_status")
    private Integer sellerStatus;
    /**
     * 订单行金额
     */
    @Field(type = FieldType.Long, value = "order_item_amount")
    private Long orderItemAmount;
    /**
     * 订单行应付款金额
     */
    @Field(type = FieldType.Long, value = "order_item_pay_amount")
    private Long orderItemPayAmount;
    /**
     * 订单行实付金额
     */
    @Field(type = FieldType.Long, value = "order_item_actual_pay_amount")
    private Long orderItemActualPayAmount;
    /**
     * 商品原价(不包含包赔)
     */
    @Field(type = FieldType.Long, value = "product_original_price")
    private Long productOriginalPrice;
    /**
     * 是否议价: 0否 1是
     */
    @Field(type = FieldType.Boolean, value = "is_bargain")
    private Boolean bargain;
    /**
     * 商品销售价格(单价)(求降价之后的价格)
     */
    @Field(type = FieldType.Long, value = "product_sale_price")
    private Long productSalePrice;
    /**
     * 商品优惠金额, 是号价优惠了多少钱
     */
    @Field(type = FieldType.Long, value = "product_coupon_amount")
    private Long productCouponAmount;
    /**
     * 商品应付价格(实际支付的号价)(是商品不包含包赔)
     */
    @Field(type = FieldType.Long, value = "product_pay_amount")
    private Long productPayAmount;
    /**
     * 购买的商品数量(诚心卖服务订单以天为单位)
     */
    @Field(value = "product_quantity")
    private Integer productQuantity;
    /**
     * 当前可放款金额
     */
    @Field(type = FieldType.Long, value = "payout_amount")
    private Long payoutAmount;
    /**
     * 订单完结时间
     */
    @Field(type = FieldType.Date, format = DateFormat.date_hour_minute_second_fraction, value = "complete_time")
    private LocalDateTime completeTime;
    /**
     * 订单取消时间
     */
    @Field(type = FieldType.Date, format = DateFormat.date_hour_minute_second_fraction, value = "cancel_time")
    private LocalDateTime cancelTime;
    /**
     * 最新收款单状态
     */
    @Field(type = FieldType.Integer, value = "receipt_status")
    private Integer receiptStatus;
    /**
     * 最新退款单状态
     */
    @Field(type = FieldType.Integer, value = "refund_status")
    private Integer refundStatus;
    /**
     * 最新放款单状态
     */
    @Field(type = FieldType.Integer, value = "payout_status")
    private Integer payoutStatus;

    @Field(type = FieldType.Keyword, value = "buyer_merchant_id")
    private String buyerMerchantId;

    @Field(type = FieldType.Keyword, value = "buyer_merchant_dept_id")
    private String buyerMerchantDeptId;

    @Field(type = FieldType.Keyword, value = "seller_merchant_id")
    private String sellerMerchantId;

    @Field(type = FieldType.Keyword, value = "seller_merchant_dept_id")
    private String sellerMerchantDeptId;

    /*************************************************** 订单行扩展信息 **************************************************/

    /**
     * 交易客服ID
     */
    @Field(type = FieldType.Keyword, value = "trade_customer_id")
    private String tradeCustomerId;
    /**
     * 交付客服id
     */
    @Field(type = FieldType.Keyword, value = "delivery_customer_id")
    private String deliveryCustomerId;
    /**
     * 交付客服名称
     */
    @Field(value = "delivery_customer")
    private String deliveryCustomer;
    /**
     * 交易客服名称
     */
    @Field(value = "trade_customer")
    private String tradeCustomer;
    /**
     * 商品名称
     */
    @Field(value = "product_name")
    private String productName;
    /**
     * 商品图片
     */
    @Field(value = "product_pic")
    private String productPic;
    /**
     * 商品属性
     */
    @Field(value = "product_attr")
    private String productAttr;
    /**
     * 账号信息
     */
    @Field(value = "game_account")
    private String gameAccount;
    /**
     * 商品亮点
     */
    @Field(value = "product_highlight")
    private String productHighlight;
    /**
     * 游戏名称
     */
    @Field(value = "game_name")
    private String gameName;
    /**
     * 游戏名称
     */
    @Field(value = "game_attr")
    private String gameAttr;
    /**
     * 是否顺手买商品 0否 1是
     */
    @Field(type = FieldType.Boolean, value = "is_easy_buy")
    private Boolean easyBuy;
    /**
     * 咨询房间id, 私聊房间
     */
    @Field(type = FieldType.Keyword, value = "room_id")
    private String roomId;
    /**
     * 交付群聊房间id, 智能交付是新群交付, 中介订单是原群交付
     */
    @Field(type = FieldType.Keyword, value = "delivery_room_id")
    private String deliveryRoomId;
    /**
     * 买家手机号
     */
    @Field(value = "buyer_phone")
    private String buyerPhone;
    /**
     * 买家身份 1散户 2号商
     */
    @Field(value = "buyer_user_type")
    private Integer buyerUserType;
    /**
     * 卖家手机号
     */
    @Field(value = "seller_phone")
    private String sellerPhone;
    /**
     * 卖家身份 0系统 1散户 2号商
     */
    @Field(value = "seller_identity")
    private Integer sellerIdentity;
    /**
     * 买商品的时候,它是否有生效的诚心卖服务, 0没有 1有
     */
    @Field(type = FieldType.Boolean, value = "is_sincerity_sell")
    private Boolean sinceritySell;

    /**
     * 买商品的时候,它是否有生效的诚心卖履约服务, 0没有 1有
     */
    @Field(type = FieldType.Boolean,value = "is_promise_sell")
    private Boolean promiseSell;

    /**
     * 是否添加合同了 0是 1否
     */
    @Field(type = FieldType.Boolean, value = "is_add_contract")
    private Boolean addContract;
    /**
     * 买家确认收货时间
     */
    @Field(type = FieldType.Date, format = DateFormat.date_hour_minute_second_fraction, value = "buyer_receipt_time")
    private LocalDateTime buyerReceiptTime;
    /**
     * 自动收货最晚时间
     */
    @Field(type = FieldType.Date, format = DateFormat.date_hour_minute_second_fraction, value = "auto_receipt_time")
    private LocalDateTime autoReceiptTime;
    /**
     * 买家订单备注
     */
    @Field(value = "buyer_remark")
    private String buyerRemark;
    /**
     * 卖家订单备注
     */
    @Field(value = "seller_remark")
    private String sellerRemark;
    /**
     * 商品编号
     */
    @Field(value = "product_unique_no")
    private String productUniqueNo;

    /**
     * 充值套餐名称
     */
    @Field(value = "recharge_package")
    private String rechargePackage;

    /** ---------------------------------------订单行，手续费表----------------------------------------------- */

    /**
     * 承担方 0买家 1卖家
     */
    @Field(value = "responsible_user")
    private Integer responsibleUser;
    /**
     * 承担方用户id
     */
    @Field(type = FieldType.Keyword, value = "responsible_user_id")
    private String responsibleUserId;
    /**
     * 手续费比例
     */
    @Field(value = "fee_ratio")
    private Integer feeRatio;
    /**
     * 1 按比例收费 2 固定金额收费
     */
    @Field(value = "fee_type")
    private Integer feeType;
    /**
     * 手续费号商折扣比例
     */
    @Field(value = "fee_merchant_discount_ratio")
    private Integer feeMerchantDiscountRatio;
    /**
     * 手续费金额
     */
    @Field(value = "fee_amount")
    private Long feeAmount;
    /**
     * 手续费上限
     */
    @Field(value = "fee_amount_max")
    private Long feeAmountMax;
    /**
     * 手续费下限
     */
    @Field(value = "fee_amount_min")
    private Long feeAmountMin;
    /**
     * 手续费折扣
     */
    @Field(value = "fee_discount")
    private Long feeDiscount;
    /**
     * 真实手续费金额
     */
    @Field(value = "fee_real_amount")
    private Long feeRealAmount;

    /**
     * ---------------------------------------子订单-包赔表(OrderItemIndemnityDoc)-----------------------------------------------
     */

    // 将1对多关系表的主键id，转成jsonarray字符串，
    // 通过模糊匹配id反查orderItem
    @Field(type = FieldType.Keyword, name = "order_item_indemnity_ids")
    private String orderItemIndemnityIds;

    @Field(name = "order_item_indemnity_doc_List", type = FieldType.Nested)
    private List<OrderItemIndemnityDoc> orderItemIndemnityDocList;

    /**---------------------------------------子订单-包赔优惠表(OrderItemIndemnityPromotionDoc)-----------------------------------------------*/

    @Field(name = "order_item_indemnity_promotion_doc_List", type = FieldType.Nested)
    private List<OrderItemIndemnityPromotionDoc> orderItemIndemnityPromotionDocList;

    /**
     * ---------------------------------------子订单-支付记录表(OrderItemPaymentDoc)-----------------------------------------------
     */

    // 通过模糊匹配id反查orderItem
    @Field(type = FieldType.Keyword, name = "payment_ids")
    private String paymentIds;

    @Field(name = "order_item_payment_doc_list", type = FieldType.Nested)
    private List<OrderItemPaymentDoc> orderItemPaymentDocList;

    /**
     * ---------------------------------------子订单-优惠表(OrderItemPromotionDoc)-----------------------------------------------
     */

    // 通过模糊匹配id反查orderItem
    @Field(type = FieldType.Keyword, name = "promotion_ids")
    private String promotionIds;

    @Field(name = "order_item_promotion_doc_list", type = FieldType.Nested)
    private List<OrderItemPromotionDoc> orderItemPromotionDocList;


    /**
     * 违约单号
     */
    @Field(name = "violate_id", type = FieldType.Keyword)
    private String violateId;
}
