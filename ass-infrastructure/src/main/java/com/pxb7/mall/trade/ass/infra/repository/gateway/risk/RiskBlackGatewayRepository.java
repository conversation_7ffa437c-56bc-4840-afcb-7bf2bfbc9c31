package com.pxb7.mall.trade.ass.infra.repository.gateway.risk;

import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.fastjson.JSON;
import com.pxb7.mall.common.client.api.risk.BlackServiceI;
import com.pxb7.mall.common.client.enums.RiskLevelEnum;
import com.pxb7.mall.common.client.request.risk.action.BlackAddReqDTO;
import com.pxb7.mall.common.client.request.risk.action.BlackRemoveReqDTO;
import com.pxb7.mall.trade.ass.infra.model.RiskAddBlackRecordPO;
import com.pxb7.mall.trade.ass.infra.model.RiskRemoveBlackRecordPO;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class RiskBlackGatewayRepository {


    @DubboReference(check = false)
    private BlackServiceI blackServiceI;

    public void addBlackRecord(RiskAddBlackRecordPO addBlackRecordPO) {
        try {
            BlackAddReqDTO request = new BlackAddReqDTO();
            request.setGameAccount(addBlackRecordPO.getGameAccount());
            request.setRegisterPhone(addBlackRecordPO.getPhone());
            request.setIdNumber(addBlackRecordPO.getCertNo());
            request.setBlackType("跑单");
            request.setRiskLevel(RiskLevelEnum.MIDDLE.getValue());
            request.setSerialNo(addBlackRecordPO.getVoucherId());
            log.info("addBlackRecord request:{}",request);
            SingleResponse<Boolean> booleanSingleResponse = blackServiceI.addBlack(request);
            log.info("addBlackRecord response:{}", JSON.toJSONString(booleanSingleResponse));
        }catch (Exception e){
            log.error("addBlackRecord error",e);
        }
    }


    public void removeBlackRecord(RiskRemoveBlackRecordPO removeBlackRecordPO) {
        try {
            BlackRemoveReqDTO request = new BlackRemoveReqDTO();
            request.setBlackType("跑单");
            request.setSerialNo(removeBlackRecordPO.getVoucherId());
            log.info("removeBlackRecord request:{}",request);
            SingleResponse<Boolean> response = blackServiceI.removeBlack(request);
            log.info("removeBlackRecord response:{}",JSON.toJSONString(response));
        }catch (Exception e){
            log.error("removeBlackRecord error",e);
        }
    }
}
