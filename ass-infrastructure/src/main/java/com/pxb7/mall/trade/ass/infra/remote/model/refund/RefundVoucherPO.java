package com.pxb7.mall.trade.ass.infra.remote.model.refund;

import java.io.Serializable;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 退款单(RefundVoucher)实体类
 *
 * <AUTHOR>
 * @since 2024-09-25 16:35:52
 */
@Getter
@Setter
@Accessors(chain = true)
public class RefundVoucherPO implements Serializable {
    /**
     * 退款单id
     */
    private String refundVoucherId;
    /**
     * 退款类型 1 整单退款 2.退商品差价
     */
    private Integer wholeRefund;
    /**
     * 退款类型 1线上原路返回 2线下打款
     */
    private Integer refundType;
    /**
     * 使用的对应收款单id,线上退款时绑定
     */
    private String receiptVoucherId;
    /**
     * 对应支付单id，审核通过后绑定
     */
    private String paymentId;
    /**
     * 实际退款金额，退款完成后写入
     */
    private Long actualRefundAmount;
}
