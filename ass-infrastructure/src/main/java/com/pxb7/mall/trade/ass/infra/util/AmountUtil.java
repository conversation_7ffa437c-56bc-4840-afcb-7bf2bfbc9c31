package com.pxb7.mall.trade.ass.infra.util;

import java.math.BigDecimal;
import java.math.RoundingMode;

import org.apache.commons.lang3.StringUtils;

import com.alibaba.cola.exception.BizException;
import com.pxb7.mall.trade.ass.client.dto.model.refund.RefundErrorCode;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class AmountUtil {

    private static final BigDecimal EXCHANGE_RATE = BigDecimal.valueOf(100);

    /**
     * 分转换为元
     * 
     */
    public static String convertYuan(Long longAmount) {
        if (longAmount == null) {
            log.error("分转换为元失败，longAmount为null");
            throw new BizException(RefundErrorCode.REFUND_ERROR.getErrCode(),
                RefundErrorCode.REFUND_ERROR.getErrDesc());
        }

        return BigDecimal.valueOf(longAmount).divide(EXCHANGE_RATE, 2, RoundingMode.UP).toString();
    }

    /**
     * 元转换为分
     * 
     */
    public static Long convertFen(String stringAmount) {
        if (StringUtils.isBlank(stringAmount)) {
            log.error("元转换为分失败，stringAmount为空:{}", stringAmount);
            throw new BizException(RefundErrorCode.REFUND_ERROR.getErrCode(),
                RefundErrorCode.REFUND_ERROR.getErrDesc());
        }
        return new BigDecimal(stringAmount).multiply(EXCHANGE_RATE).longValue();
    }
}
