package com.pxb7.mall.trade.ass.infra.repository.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.PayCompanyAccount;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 公司账户表(PayCompanyAccount)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-09-27 10:27:21
 */
@Mapper
public interface PayCompanyAccountMapper extends BaseMapper<PayCompanyAccount> {
    /**
     * 批量新增数据
     *
     * @param entities List<PayCompanyAccount> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<PayCompanyAccount> entities);

}

