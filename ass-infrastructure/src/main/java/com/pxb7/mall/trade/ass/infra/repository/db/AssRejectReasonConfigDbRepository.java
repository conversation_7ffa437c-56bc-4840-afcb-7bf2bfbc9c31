package com.pxb7.mall.trade.ass.infra.repository.db;


import java.util.Collections;
import java.util.List;
import java.util.Objects;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.pxb7.mall.trade.ass.infra.model.AssRejectReasonConfigReqPO;
import com.pxb7.mall.trade.ass.infra.repository.db.mapper.AssRejectReasonConfigMapper;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.AssRejectReasonConfig;
import com.pxb7.mall.trade.ass.infra.repository.db.AssRejectReasonConfigRepository;

/**
 * 驳回原因配置表(AssRejectReasonConfig)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-30 13:45:17
 */
@Slf4j
@Repository
public class AssRejectReasonConfigDbRepository extends ServiceImpl<AssRejectReasonConfigMapper, AssRejectReasonConfig> implements AssRejectReasonConfigRepository {

    @Override
    public boolean insert(AssRejectReasonConfigReqPO.AddPO param) {
        AssRejectReasonConfig entity = new AssRejectReasonConfig();
        entity.setReasonConfigId(param.getReasonConfigId());
        entity.setAssType(param.getAssType());
        entity.setReason(param.getReason());
        entity.setUserDesc(param.getUserDesc());
        entity.setSort(param.getSort());
        entity.setCreateUserId(param.getCreateUserId());
        entity.setUpdateUserId(param.getUpdateUserId());
        return this.save(entity);
    }

    @Override
    public boolean update(AssRejectReasonConfigReqPO.UpdatePO param) {
        LambdaUpdateWrapper<AssRejectReasonConfig> updateWrapper = new LambdaUpdateWrapper<>();
        //where
        updateWrapper.eq(AssRejectReasonConfig::getId, param.getId());
        //set
        if (StringUtils.isNotBlank(param.getReasonConfigId())) {
            updateWrapper.set(AssRejectReasonConfig::getReasonConfigId, param.getReasonConfigId());
        }
        if (Objects.nonNull(param.getAssType())) {
            updateWrapper.set(AssRejectReasonConfig::getAssType, param.getAssType());
        }
        if (StringUtils.isNotBlank(param.getReason())) {
            updateWrapper.set(AssRejectReasonConfig::getReason, param.getReason());
        }
        if (StringUtils.isNotBlank(param.getUserDesc())) {
            updateWrapper.set(AssRejectReasonConfig::getUserDesc, param.getUserDesc());
        }
        if (Objects.nonNull(param.getSort())) {
            updateWrapper.set(AssRejectReasonConfig::getSort, param.getSort());
        }
        if (StringUtils.isNotBlank(param.getCreateUserId())) {
            updateWrapper.set(AssRejectReasonConfig::getCreateUserId, param.getCreateUserId());
        }
        if (StringUtils.isNotBlank(param.getUpdateUserId())) {
            updateWrapper.set(AssRejectReasonConfig::getUpdateUserId, param.getUpdateUserId());
        }
        return this.update(updateWrapper);
    }

    @Override
    public boolean deleteById(AssRejectReasonConfigReqPO.DelPO param) {
        return this.removeById(param.getId());
    }

    @Override
    public AssRejectReasonConfig findById(Long id) {
        return this.getById(id);
    }

    @Override
    public List<AssRejectReasonConfig> list(AssRejectReasonConfigReqPO.SearchPO param) {
        LambdaQueryWrapper<AssRejectReasonConfig> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(param.getReasonConfigId())) {
            queryWrapper.eq(AssRejectReasonConfig::getReasonConfigId, param.getReasonConfigId());
        }
        if (Objects.nonNull(param.getAssType())) {
            queryWrapper.eq(AssRejectReasonConfig::getAssType, param.getAssType());
        }
        if (StringUtils.isNotBlank(param.getReason())) {
            queryWrapper.eq(AssRejectReasonConfig::getReason, param.getReason());
        }
        if (StringUtils.isNotBlank(param.getUserDesc())) {
            queryWrapper.eq(AssRejectReasonConfig::getUserDesc, param.getUserDesc());
        }
        if (Objects.nonNull(param.getSort())) {
            queryWrapper.eq(AssRejectReasonConfig::getSort, param.getSort());
        }
        if (StringUtils.isNotBlank(param.getCreateUserId())) {
            queryWrapper.eq(AssRejectReasonConfig::getCreateUserId, param.getCreateUserId());
        }
        if (StringUtils.isNotBlank(param.getUpdateUserId())) {
            queryWrapper.eq(AssRejectReasonConfig::getUpdateUserId, param.getUpdateUserId());
        }
        return this.list(queryWrapper);
    }

    @Override
    public Page<AssRejectReasonConfig> page(AssRejectReasonConfigReqPO.PagePO param) {
        LambdaQueryWrapper<AssRejectReasonConfig> queryWrapper = new LambdaQueryWrapper<>();
        // 返回分页查询结果
        if (StringUtils.isNotBlank(param.getReasonConfigId())) {
            queryWrapper.eq(AssRejectReasonConfig::getReasonConfigId, param.getReasonConfigId());
        }
        if (Objects.nonNull(param.getAssType())) {
            queryWrapper.eq(AssRejectReasonConfig::getAssType, param.getAssType());
        }
        if (StringUtils.isNotBlank(param.getReason())) {
            queryWrapper.eq(AssRejectReasonConfig::getReason, param.getReason());
        }
        if (StringUtils.isNotBlank(param.getUserDesc())) {
            queryWrapper.eq(AssRejectReasonConfig::getUserDesc, param.getUserDesc());
        }
        if (Objects.nonNull(param.getSort())) {
            queryWrapper.eq(AssRejectReasonConfig::getSort, param.getSort());
        }
        if (StringUtils.isNotBlank(param.getCreateUserId())) {
            queryWrapper.eq(AssRejectReasonConfig::getCreateUserId, param.getCreateUserId());
        }
        if (StringUtils.isNotBlank(param.getUpdateUserId())) {
            queryWrapper.eq(AssRejectReasonConfig::getUpdateUserId, param.getUpdateUserId());
        }
        return this.page(new Page<>(param.getPageIndex(), param.getPageSize()), queryWrapper);
    }

    @Override
    public List<AssRejectReasonConfig> getListByType(Integer assType) {
        if(Objects.isNull(assType)){
            return Collections.EMPTY_LIST;
        }
        LambdaQueryWrapper<AssRejectReasonConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AssRejectReasonConfig::getAssType, assType);
        queryWrapper.orderByAsc(AssRejectReasonConfig::getSort);
        return this.list(queryWrapper);
    }
}
