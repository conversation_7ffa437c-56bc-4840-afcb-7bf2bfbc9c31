package com.pxb7.mall.trade.ass.infra.repository.db;

import com.baomidou.mybatisplus.extension.service.IService;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.ComplaintWorkLog;

import java.util.List;

/**
 * 客诉工单操作日志表(ComplaintWorkLog)表服务接口
 *
 * <AUTHOR>
 * @since 2024-09-20 16:52:42
 */
public interface ComplaintWorkLogRepository extends IService<ComplaintWorkLog> {

    List<ComplaintWorkLog> queryByWorkId(String complaintWorkId);
}

