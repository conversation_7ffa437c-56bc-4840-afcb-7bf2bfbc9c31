package com.pxb7.mall.trade.ass.infra.repository.db;

import java.util.List;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.OrderItem;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.OrderItemExtend;

/**
 * 订单行(OrderItem)表服务接口
 *
 * <AUTHOR>
 * @since 2024-07-22 20:59:36
 */
public interface OrderItemRepository extends IService<OrderItem> {

    boolean startRefund(String orderItemId);

    boolean updateRefundStatus(String orderItemId, Integer fromStatus, Integer toStatus);

    OrderItem getOrderItem(String orderItemId);

    boolean sinceritySellStartRefund(String orderItemId);

    /**
     * 更新状态到退款中
     * @param orderItemId
     * @return
     */
    boolean updateRefundStatusToRefunding(String orderItemId);

    /**
     * 查询订单行列表
     * 
     * @return
     */
    List<OrderItem> getOrderItemList(List<String> orderItemIds);

    List<OrderItem> listByBuyerIdAndIds(String buyerId, List<String> itemIds);

    /**
     * 根据主订单号查询子订单列表
     * 
     * @param orderId
     * @return
     */
    List<OrderItem> getOrderItemListByOrderId(String orderId, String orderItemId);

    /**
     * 根据主订单号查询子订单列表
     * 
     * @param orderId
     * @return
     */
    List<OrderItem> getOrderItemListByOrderId(String orderId);



    /**
     * 判断单据是否存在进行中的收/退/放
     * @param orderItemId 单据ID
     * @return 判断结果
     */
    boolean existsProcessingTransfer(String orderItemId);

    Boolean updateReceiptVoucherStatus(String orderItemId, Integer receiptVoucherStatus);


}
