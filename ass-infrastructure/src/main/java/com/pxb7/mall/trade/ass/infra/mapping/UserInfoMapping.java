package com.pxb7.mall.trade.ass.infra.mapping;

import com.pxb7.mall.trade.ass.infra.model.UserCertInfoRespPO;
import com.pxb7.mall.user.dto.response.user.UserCertInfoRespDTO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.List;

@Mapper(componentModel = "Spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface UserInfoMapping {


    List<UserCertInfoRespPO> toUserCertInfoRespPOList(List<UserCertInfoRespDTO> list);
}
