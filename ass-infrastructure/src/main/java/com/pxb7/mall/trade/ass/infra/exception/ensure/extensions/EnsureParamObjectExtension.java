package com.pxb7.mall.trade.ass.infra.exception.ensure.extensions;

import com.pxb7.mall.trade.ass.infra.enums.ErrorCode;
import com.pxb7.mall.trade.ass.infra.exception.ExceptionFactory;
import com.pxb7.mall.trade.ass.infra.exception.ensure.EnsureParam;

import java.util.Objects;

/**
 * 断言工具类
 *
 * <AUTHOR>
 * @date 2024/08/07 15:11
 **/
public class EnsureParamObjectExtension<T> extends EnsureParam<T> {

    public EnsureParamObjectExtension(T o) {
        super(o);
    }

    public EnsureParamObjectExtension<T> isNull(ErrorCode errorCode) {
        if (Objects.nonNull(this.t)) {
            throw ExceptionFactory.create(errorCode.getErrCode(), errorCode.getErrDesc());
        } else {
            return this;
        }
    }

    public EnsureParamObjectExtension<T> isNotNull(ErrorCode errorCode) {
        if (Objects.isNull(this.t)) {
            throw ExceptionFactory.create(errorCode.getErrCode(), errorCode.getErrDesc());
        } else {
            return this;
        }
    }

}
