package com.pxb7.mall.trade.ass.infra.repository.db.entity;

import java.io.Serializable;
import java.time.*;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import com.baomidou.mybatisplus.annotation.*;

/**
 * 售后工单(AssWorkOrder)实体类
 *
 * <AUTHOR>
 * @since 2025-07-31 11:52:35
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "ass_work_order")
@ToString
public class AssWorkOrder implements Serializable {
    private static final long serialVersionUID = -21275026274709219L;
    /**
     * 自增id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 售后工单号
     */
    @TableField(value = "work_order_id")
    private String workOrderId;
    /**
     * ass_schdule.id
     */
    @TableField(value = "schedule_id")
    private String scheduleId;
    /**
     * 订单ID
     */
    @TableField(value = "order_item_id")
    private String orderItemId;
    /**
     * 订单ID
     */
    @TableField(value = "room_id")
    private String roomId;
    /**
     * 售后类型1找回2纠纷
     */
    @TableField(value = "ass_type")
    private Integer assType;
    /**
     * 关联找回/纠纷工单号
     */
    @TableField(value = "rel_order_id")
    private String relOrderId;
    /**
     * 申请人类型 1:买家 2:卖家
     */
    @TableField(value = "proposer_type")
    private Integer proposerType;
    /**
     * 售后工单状态 0:处理中 1:已完成 2:已取消
     */
    @TableField(value = "ass_status")
    private Integer assStatus;
    /**
     * 售后工单状态备注
     */
    @TableField(value = "ass_status_memo")
    private String assStatusMemo;
    /**
     * 发起售后申请时间
     */
    @TableField(value = "apply_time")
    private LocalDateTime applyTime;
    /**
     * 预计完成时间
     */
    @TableField(value = "expected_time")
    private LocalDateTime expectedTime;
    /**
     * 完成时间
     */
    @TableField(value = "complete_time")
    private LocalDateTime completeTime;
    /**
     * 是否已读 1:已读 0:未读
     */
    @TableField(value = "read_flag")
    private Boolean readFlag;
    /**
     * 创建人id
     */
    @TableField(value = "create_user_id")
    private String createUserId;
    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;
    /**
     * 更新人id
     */
    @TableField(value = "update_user_id")
    private String updateUserId;
    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;
    /**
     * 是否删除 1:已删除 0:未删除
     */
    @TableLogic
    @TableField(value = "is_deleted")
    private Boolean deleted;


}

