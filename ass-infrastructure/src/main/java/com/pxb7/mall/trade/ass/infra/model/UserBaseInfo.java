package com.pxb7.mall.trade.ass.infra.model;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
public class UserBaseInfo {

    /**
     * 用户id
     */
    private String userId;
    /**
     * 用户名称
     */
    private String userName;

    /**
     * 是否商家
     */
    private Boolean isMerchant;

    /**
     * 商家ID
     */
    private String merchantId;


    /**
     * 是否为admin
     */
    private Boolean isAdmin;

}