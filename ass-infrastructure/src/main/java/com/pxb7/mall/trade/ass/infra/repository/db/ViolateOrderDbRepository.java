package com.pxb7.mall.trade.ass.infra.repository.db;


import java.util.List;
import java.util.Objects;

import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.pxb7.mall.trade.ass.client.enums.violate.ViolateStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.pxb7.mall.trade.ass.infra.model.ViolateOrderReqPO;
import com.pxb7.mall.trade.ass.infra.repository.db.mapper.ViolateOrderMapper;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.ViolateOrder;
import org.springframework.util.CollectionUtils;

/**
 * 违约单(ViolateOrder)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-27 15:47:15
 */
@Slf4j
@Repository
public class ViolateOrderDbRepository extends ServiceImpl<ViolateOrderMapper, ViolateOrder> implements ViolateOrderRepository {

    @Override
    public List<ViolateOrder> list(ViolateOrderReqPO.SearchPO param) {
        LambdaQueryWrapper<ViolateOrder> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(param.getViolateId())) {
            queryWrapper.eq(ViolateOrder::getViolateId, param.getViolateId());
        }
        if (StringUtils.isNotBlank(param.getOrderItemId())) {
            queryWrapper.eq(ViolateOrder::getOrderItemId, param.getOrderItemId());
        }
        if (StringUtils.isNotBlank(param.getRefundVoucherId())) {
            queryWrapper.eq(ViolateOrder::getRefundVoucherId, param.getRefundVoucherId());
        }
        if (StringUtils.isNotBlank(param.getViolateUserId())) {
            queryWrapper.eq(ViolateOrder::getViolateUserId, param.getViolateUserId());
        }
        if (StringUtils.isNotBlank(param.getPromiseUserId())) {
            queryWrapper.eq(ViolateOrder::getPromiseUserId, param.getPromiseUserId());
        }
        if (Objects.nonNull(param.getViolateUserType())) {
            queryWrapper.eq(ViolateOrder::getViolateUserType, param.getViolateUserType());
        }
        if (Objects.nonNull(param.getViolateAmount())) {
            queryWrapper.eq(ViolateOrder::getViolateAmount, param.getViolateAmount());
        }
        if (Objects.nonNull(param.getPromiseAmount())) {
            queryWrapper.eq(ViolateOrder::getPromiseAmount, param.getPromiseAmount());
        }
        if (Objects.nonNull(param.getPlatformAmount())) {
            queryWrapper.eq(ViolateOrder::getPlatformAmount, param.getPlatformAmount());
        }
        if (Objects.nonNull(param.getViolateStatus())) {
            queryWrapper.eq(ViolateOrder::getViolateStatus, param.getViolateStatus());
        }
        if (!CollectionUtils.isEmpty(param.getViolateStatuses())){
            queryWrapper.in(ViolateOrder::getViolateStatus, param.getViolateStatuses());
        }
        if (Objects.nonNull(param.getReceiptStatus())) {
            queryWrapper.eq(ViolateOrder::getReceiptStatus, param.getReceiptStatus());
        }
        if (Objects.nonNull(param.getTransferStatus())) {
            queryWrapper.eq(ViolateOrder::getTransferStatus, param.getTransferStatus());
        }
        return this.list(queryWrapper);
    }

    @Override
    public ViolateOrder find(ViolateOrderReqPO.SearchPO param) {
        LambdaQueryChainWrapper<ViolateOrder> queryWrapper = this.lambdaQuery();
        if (StringUtils.isNotBlank(param.getViolateId())) {
            queryWrapper.eq(ViolateOrder::getViolateId, param.getViolateId());
        }
        if (StringUtils.isNotBlank(param.getOrderItemId())) {
            queryWrapper.eq(ViolateOrder::getOrderItemId, param.getOrderItemId());
        }
        if (StringUtils.isNotBlank(param.getRefundVoucherId())) {
            queryWrapper.eq(ViolateOrder::getRefundVoucherId, param.getRefundVoucherId());
        }
        if (StringUtils.isNotBlank(param.getViolateUserId())) {
            queryWrapper.eq(ViolateOrder::getViolateUserId, param.getViolateUserId());
        }
        if (StringUtils.isNotBlank(param.getPromiseUserId())) {
            queryWrapper.eq(ViolateOrder::getPromiseUserId, param.getPromiseUserId());
        }
        if (Objects.nonNull(param.getViolateUserType())) {
            queryWrapper.eq(ViolateOrder::getViolateUserType, param.getViolateUserType());
        }
        if (Objects.nonNull(param.getViolateAmount())) {
            queryWrapper.eq(ViolateOrder::getViolateAmount, param.getViolateAmount());
        }
        if (Objects.nonNull(param.getPromiseAmount())) {
            queryWrapper.eq(ViolateOrder::getPromiseAmount, param.getPromiseAmount());
        }
        if (Objects.nonNull(param.getPlatformAmount())) {
            queryWrapper.eq(ViolateOrder::getPlatformAmount, param.getPlatformAmount());
        }
        if (Objects.nonNull(param.getViolateStatus())) {
            queryWrapper.eq(ViolateOrder::getViolateStatus, param.getViolateStatus());
        }
        if (!CollectionUtils.isEmpty(param.getViolateStatuses())){
            queryWrapper.in(ViolateOrder::getViolateStatus, param.getViolateStatuses());
        }
        if (Objects.nonNull(param.getReceiptStatus())) {
            queryWrapper.eq(ViolateOrder::getReceiptStatus, param.getReceiptStatus());
        }
        if (Objects.nonNull(param.getTransferStatus())) {
            queryWrapper.eq(ViolateOrder::getTransferStatus, param.getTransferStatus());
        }
        return queryWrapper.last("limit 1").one();
    }


    @Override
    public long count(ViolateOrderReqPO.SearchPO param) {
        LambdaQueryWrapper<ViolateOrder> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(param.getViolateId())) {
            queryWrapper.eq(ViolateOrder::getViolateId, param.getViolateId());
        }
        if (StringUtils.isNotBlank(param.getOrderItemId())) {
            queryWrapper.eq(ViolateOrder::getOrderItemId, param.getOrderItemId());
        }
        if (StringUtils.isNotBlank(param.getRefundVoucherId())) {
            queryWrapper.eq(ViolateOrder::getRefundVoucherId, param.getRefundVoucherId());
        }
        if (StringUtils.isNotBlank(param.getViolateUserId())) {
            queryWrapper.eq(ViolateOrder::getViolateUserId, param.getViolateUserId());
        }
        if (StringUtils.isNotBlank(param.getPromiseUserId())) {
            queryWrapper.eq(ViolateOrder::getPromiseUserId, param.getPromiseUserId());
        }
        if (Objects.nonNull(param.getViolateUserType())) {
            queryWrapper.eq(ViolateOrder::getViolateUserType, param.getViolateUserType());
        }
        if (Objects.nonNull(param.getViolateAmount())) {
            queryWrapper.eq(ViolateOrder::getViolateAmount, param.getViolateAmount());
        }
        if (Objects.nonNull(param.getPromiseAmount())) {
            queryWrapper.eq(ViolateOrder::getPromiseAmount, param.getPromiseAmount());
        }
        if (Objects.nonNull(param.getPlatformAmount())) {
            queryWrapper.eq(ViolateOrder::getPlatformAmount, param.getPlatformAmount());
        }
        if (Objects.nonNull(param.getViolateStatus())) {
            queryWrapper.eq(ViolateOrder::getViolateStatus, param.getViolateStatus());
        }
        if (!CollectionUtils.isEmpty(param.getViolateStatuses())){
            queryWrapper.in(ViolateOrder::getViolateStatus, param.getViolateStatuses());
        }
        if (Objects.nonNull(param.getReceiptStatus())) {
            queryWrapper.eq(ViolateOrder::getReceiptStatus, param.getReceiptStatus());
        }
        if (Objects.nonNull(param.getTransferStatus())) {
            queryWrapper.eq(ViolateOrder::getTransferStatus, param.getTransferStatus());
        }
        return this.count(queryWrapper);
    }
}
