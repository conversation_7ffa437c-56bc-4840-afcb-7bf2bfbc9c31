package com.pxb7.mall.trade.ass.infra.repository.db;

import com.baomidou.mybatisplus.extension.service.IService;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.ComplaintWork;

import java.util.List;

/**
 * 客诉工单(ComplaintWork)表服务接口
 *
 * <AUTHOR>
 * @since 2024-09-20 17:55:43
 */
public interface ComplaintWorkRepository extends IService<ComplaintWork> {

    boolean finishWorkOrder(ComplaintWork complaintWork);

    boolean transferWorkOrder(ComplaintWork complaintWork);

    ComplaintWork queryById(String complaintWorkId);

    List<ComplaintWork> listByUserId(String userId, String phone);
}

