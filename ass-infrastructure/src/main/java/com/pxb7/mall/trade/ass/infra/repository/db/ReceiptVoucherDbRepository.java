package com.pxb7.mall.trade.ass.infra.repository.db;

import java.util.List;

import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.ReceiptVoucher;
import com.pxb7.mall.trade.ass.infra.repository.db.mapper.ReceiptVoucherMapper;
import com.pxb7.mall.trade.order.client.enums.order.ReceiptVoucherStatusEnum;

import lombok.extern.slf4j.Slf4j;

/**
 * 收款单(ReceiptVoucher)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-09-25 16:16:37
 */
@Slf4j
@Repository
public class ReceiptVoucherDbRepository extends ServiceImpl<ReceiptVoucherMapper, ReceiptVoucher>
    implements ReceiptVoucherRepository {
    @Override
    public List<ReceiptVoucher> getSuccessByReceiptId(List<String> receiptVoucherIds) {
        return this.lambdaQuery().in(ReceiptVoucher::getReceiptVoucherId, receiptVoucherIds)
            .eq(ReceiptVoucher::getStatus, ReceiptVoucherStatusEnum.SUCCESS.getValue()).list();
    }
}
