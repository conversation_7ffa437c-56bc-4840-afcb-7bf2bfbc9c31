package com.pxb7.mall.trade.ass.infra.repository.db.entity;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.*;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 售后问答问题方案配置
 *
 * <AUTHOR>
 **/
@Getter
@Setter
@ToString
@Accessors(chain = true)
@TableName(value = "ass_question_option")
public class AssQuestionOption implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 自增id
     */
    @TableField(value = "id")
    private Long id;

    /**
     * 业务主键id
     */
    @TableId(value = "option_id", type = IdType.INPUT)
    private String optionId;

    /**
     * 方案名称
     */
    @TableField(value = "`name`")
    private String name;

    /**
     * 问题列表
     */
    @TableField(value = "question_list")
    private String questionList;

    /**
     * 是否启用0否1是
     */
    @TableField(value = "is_enable")
    private Boolean enable;

    /**
     * 创建用户ID
     */
    @TableField(value = "create_user_id")
    private String createUserId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    @TableField(value = "update_user_id")
    private String updateUserId;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", update="now()")
    private LocalDateTime updateTime;

    /**
     * 删除标记
     */
    @TableLogic
    @TableField(value = "is_deleted")
    private Boolean deleted;

}