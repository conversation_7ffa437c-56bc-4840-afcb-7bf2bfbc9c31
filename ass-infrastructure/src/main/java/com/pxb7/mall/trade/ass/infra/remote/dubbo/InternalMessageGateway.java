package com.pxb7.mall.trade.ass.infra.remote.dubbo;

import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.fastjson2.JSON;
import com.pxb7.mall.common.client.api.message.InternalMessageServiceI;
import com.pxb7.mall.common.client.request.message.InternalMessageReqDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
@Slf4j
public class InternalMessageGateway {

    @DubboReference(async = true, retries = 0)
    private InternalMessageServiceI internalMessageService;

    public void sendNotice(String userId, String templateCode, Map<String, String> params, Map<String, String> urlParams) {
        if (StringUtils.isBlank(userId)) {
            return;
        }
        InternalMessageReqDTO reqDTO = new InternalMessageReqDTO();
        reqDTO.setUserId(userId);
        reqDTO.setSceneTemplateId(templateCode);
        reqDTO.setParams(JSON.toJSONString(params));
        reqDTO.setUrlParams(JSON.toJSONString(urlParams));
        reqDTO.setRedirectType(1);
        try {
            SingleResponse<Void> response = internalMessageService.sendNotice(reqDTO);
            if (!response.isSuccess()) {
                log.info("InternalMessageGateway.sendNotice failed, param---{}, response---{}", reqDTO, response);
            }
        } catch (Exception e) {
            log.error("InternalMessageGateway.sendNotice error, param---{}", reqDTO, e);
        }
    }

    public void sendNotice(String userId, String templateCode, Map<String, String> params, String businessId, String businessType) {
        if (StringUtils.isBlank(userId)) {
            return;
        }
        InternalMessageReqDTO reqDTO = new InternalMessageReqDTO();
        reqDTO.setUserId(userId);
        reqDTO.setSceneTemplateId(templateCode);
        reqDTO.setParams(JSON.toJSONString(params));
        reqDTO.setBusinessId(businessId);
        reqDTO.setBusinessType(businessType);
        internalMessageService.sendNotice(reqDTO);
    }

}
