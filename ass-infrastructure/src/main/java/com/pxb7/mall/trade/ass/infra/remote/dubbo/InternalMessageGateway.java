package com.pxb7.mall.trade.ass.infra.remote.dubbo;

import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.fastjson2.JSON;
import com.pxb7.mall.common.client.api.message.InternalMessageServiceI;
import com.pxb7.mall.common.client.request.message.InternalMessageReqDTO;
import com.pxb7.mall.trade.ass.infra.model.InternalMsgReqPO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;

@Component
@Slf4j
public class InternalMessageGateway {
    // 异步
    @DubboReference(async = true, retries = 0)
    private InternalMessageServiceI internalMessageService;
    //同步
    @DubboReference
    private InternalMessageServiceI internalMessageServiceI;

    public void sendNotice(InternalMsgReqPO reqPO) {
        if (StringUtils.isBlank(reqPO.getUserId())) {
            return;
        }
        InternalMessageReqDTO reqDTO = new InternalMessageReqDTO();
        reqDTO.setUserId(reqPO.getUserId());
        reqDTO.setSceneTemplateId(reqPO.getTemplateCode());
        reqDTO.setParams(JSON.toJSONString(reqPO.getParams()));
        reqDTO.setUrlParams(JSON.toJSONString(reqPO.getUrlParams()));
        reqDTO.setRedirectType(reqPO.getRedirectType());
        reqDTO.setRedirectUrlList(reqPO.getRedirectUrlList());
        reqDTO.setBusinessId(reqPO.getBusinessId());
        reqDTO.setBusinessType(reqPO.getBusinessType());
        try {
            SingleResponse<Void> response = internalMessageServiceI.sendNotice(reqDTO);
            if (Objects.isNull(response) || !response.isSuccess()) {
                log.info("InternalMessageGateway.sendNotice failed, param---{}, response---{}", reqDTO, response);
            }
        } catch (Exception e) {
            log.error("InternalMessageGateway.sendNotice error, param---{}", reqDTO, e);
        }
    }

    public void sendNotice(String userId, String templateCode, Map<String, String> params, String businessId, String businessType) {
        if (StringUtils.isBlank(userId)) {
            return;
        }
        InternalMessageReqDTO reqDTO = new InternalMessageReqDTO();
        reqDTO.setUserId(userId);
        reqDTO.setSceneTemplateId(templateCode);
        reqDTO.setParams(JSON.toJSONString(params));
        reqDTO.setBusinessId(businessId);
        reqDTO.setBusinessType(businessType);
        internalMessageService.sendNotice(reqDTO);
    }

}
