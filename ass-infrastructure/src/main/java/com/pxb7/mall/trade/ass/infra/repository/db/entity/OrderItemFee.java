package com.pxb7.mall.trade.ass.infra.repository.db.entity;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.*;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 订单行-手续费表(OrderItemFee)实体类
 *
 * <AUTHOR>
 * @since 2024-10-09 17:07:41
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
@TableName(value = "order_item_fee")
public class OrderItemFee implements Serializable {
    private static final long serialVersionUID = 664893355376768722L;
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 子订单id
     */
    @TableField(value = "order_item_id")
    private String orderItemId;
    /**
     * 承担方 0买家 1卖家
     */
    @TableField(value = "responsible_user")
    private Integer responsibleUser;
    /**
     * 承担方用户id
     */
    @TableField(value = "responsible_user_id")
    private String responsibleUserId;
    /**
     * 手续费比例
     */
    @TableField(value = "fee_ratio")
    private Integer feeRatio;
    /**
     * 1 按比例收费 2 固定金额收费
     */
    @TableField(value = "fee_type")
    private Integer feeType;
    /**
     * 手续费金额
     */
    @TableField(value = "fee_amount")
    private Long feeAmount;
    /**
     * 手续费上限
     */
    @TableField(value = "fee_amount_max")
    private Long feeAmountMax;
    /**
     * 手续费下限
     */
    @TableField(value = "fee_amount_min")
    private Long feeAmountMin;
    /**
     * 手续费折扣
     */
    @TableField(value = "fee_discount")
    private Long feeDiscount;
    /**
     * 真实手续费金额
     */
    @TableField(value = "fee_real_amount")
    private Long feeRealAmount;
    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    @TableField(value = "update_time", update="now()")
    private LocalDateTime updateTime;
    /**
     * 逻辑删除，0:删除，1:正常
     */
    @TableLogic
    @TableField(value = "is_deleted")
    private Boolean deleted;

}
