package com.pxb7.mall.trade.ass.infra.repository.db.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.OrderItemFee;

/**
 * 订单行-手续费表(OrderItemFee)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-10-09 17:07:42
 */
@Mapper
public interface OrderItemFeeMapper extends BaseMapper<OrderItemFee> {
    /**
     * 批量新增数据
     *
     * @param entities List<OrderItemFee> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<OrderItemFee> entities);

}
