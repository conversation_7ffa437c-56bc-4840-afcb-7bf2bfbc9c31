package com.pxb7.mall.trade.ass.infra.util;


import com.alibaba.cola.dto.Response;
import com.alibaba.cola.exception.BizException;
import com.pxb7.mall.trade.order.client.dto.ErrorCode;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;
import java.util.function.Supplier;

@Slf4j
public class DubboCatchUtil {

    public static <T extends Response> T catchException(Supplier<T> supplier, String errorMsg) {
        try {
            return supplier.get();// 可能抛dubbo 异常
        } catch (Throwable e) {
            // dubbo 抛异常了
            log.warn(errorMsg, e);
        }

        return null;
    }

    // 不进行返回值判空校验
    public static <T extends Response> T catchDubboException(Supplier<T> supplier, String errorCode, String errorMsg) {
        try {
            T res = supplier.get();// 可能抛dubbo 异常
            assertSuccess(res, errorCode, errorMsg);
            return res;
        } catch (BizException e) {
            throw new BizException(e.getErrCode(), e.getMessage());
        } catch (Exception e) {
            // dubbo 抛异常了
            log.error(errorMsg, e);
            throw new BizException(errorCode, errorMsg);
        }
    }

    private static void assertSuccess(Response result, String errorCode, String errorMsg) {
        if (Objects.isNull(result)) {
            log.warn("dubbo 调用获取结果为空, {}", errorMsg);
            throw new BizException(ErrorCode.MESSAGE_GET_ERROR.getErrCode(), ErrorCode.MESSAGE_GET_ERROR.getErrDesc());
        }

        if (!result.isSuccess()) {
            log.warn("dubbo 调用发生异常, {}", errorMsg);
            throw new BizException(result.getErrCode(), result.getErrMessage());
        }

    }


}
