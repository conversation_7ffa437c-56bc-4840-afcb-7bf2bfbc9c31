package com.pxb7.mall.trade.ass.infra.remote.dubbo;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.cloud.commons.lang.StringUtils;
import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.pxb7.mall.trade.ass.infra.enums.ErrorCode;
import com.pxb7.mall.trade.ass.infra.exception.BusinessException;
import com.pxb7.mall.trade.ass.infra.util.DubboResultAssert;
import com.pxb7.mall.user.api.SysUserServiceI;
import com.pxb7.mall.user.api.UserServiceI;
import com.pxb7.mall.user.dto.response.sys.CustomerCareInfoRespDTO;
import com.pxb7.mall.user.dto.response.user.UserCertInfoRespDTO;
import com.pxb7.mall.user.dto.response.user.UserShortInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * Copyright (C), 2002-2025, 螃蟹游戏服务网
 *
 * @fileName: UserRpcGateway.java
 * @description: 用户dubbo接口
 * @author: guominqiang
 * @email: <EMAIL>
 * @date: 2025/4/11 17:52
 * @history: //修改记录
 * <author> <time> <version> <desc>
 * 修改人姓名 修改时间 版本号 描述
 */
@Component
@Slf4j
public class UserRpcGateway {
    @DubboReference(providedBy = "user-c")
    private UserServiceI userServiceI;

    @DubboReference(providedBy = "user-c")
    private SysUserServiceI sysUserServiceI;

    /**
     * 查询个人用户实名信息
     *
     * @param userId 用户id
     * @return 个人用户认证的信息
     */
    public UserCertInfoRespDTO getPersonalUserInfo(String userId) {
        log.info(">>>>>>>getPersonalUserInfo: userId {}", userId);
        MultiResponse<UserCertInfoRespDTO> response = userServiceI.getUserCertInfoList(Collections.singletonList(userId));
        if (!response.isSuccess() || CollUtil.isEmpty(response.getData())) {
            throw new BusinessException(ErrorCode.RPC_GET_USER_CERT_INFO_ERROR);
        }
        return response.getData().get(0);
    }

    /**
     * 批量查询个人用户实名信息
     *
     * @param userIds 用户id
     * @return 个人用户认证的信息
     */
    public List<UserCertInfoRespDTO> findPersonalUserInfoList(List<String> userIds) {
        log.info(">>>>>>>findPersonalUserInfoList: userIds {}", userIds);
        MultiResponse<UserCertInfoRespDTO> response = userServiceI.getUserCertInfoList(userIds);
        if (!response.isSuccess()) {
            throw new BusinessException(ErrorCode.RPC_GET_USER_CERT_INFO_ERROR);
        }
        return response.getData();
    }

    /**
     * 查询个人用户注册信息
     *
     * @param userId 用户id
     * @return 个人用户认证的信息
     */
    public UserShortInfoDTO getBaseUserInfo(String userId) {
        MultiResponse<UserShortInfoDTO> response = DubboResultAssert.wrapException(() -> userServiceI.getUserShortInfo(Collections.singletonList(userId)), ErrorCode.RPC_GET_USER_BASE_INFO_ERROR);
        return response.getData()
            .stream()
            .findFirst()
            .orElseThrow(() -> new BusinessException(ErrorCode.GET_USER_BASE_INFO_ERROR));
    }

    /**
     * 查询用户注册信息
     *
     * @param userIds 用户ids
     * @return 个人用户认证的信息
     */
    public List<UserShortInfoDTO> findBaseUserInfoList(List<String> userIds) {
        List<UserShortInfoDTO> userList = Collections.emptyList();
        try {
            MultiResponse<UserShortInfoDTO> response = userServiceI.getUserShortInfo(userIds);
            if (!response.isSuccess()) {
                throw new BusinessException(ErrorCode.GET_USER_BASE_INFO_ERROR);
            }
            return response.getData();
        } catch (Exception e) {
            log.error("get user short info error ", e);
            return userList;
        }
    }

    /**
     * 获取系统用户信息
     *
     * @param sysUserId
     * @return
     */
    public CustomerCareInfoRespDTO getSystemUserInfo(String sysUserId) {
        SingleResponse<CustomerCareInfoRespDTO> response = DubboResultAssert.wrapException(() -> sysUserServiceI.sysUserId2Info(sysUserId), ErrorCode.RPC_GET_SYS_USER_INFO_ERROR);
        return response.getData();
    }

    /**
     * 根据用户id获取手机号
     * @param userId 用户ID
     * @return 手机号
     */
    public String getPhoneByUserId(String userId) {
        if (StringUtils.isEmpty(userId)) {
            return "";
        }
        return getBaseUserInfo(userId).getPhone();
    }
}
