package com.pxb7.mall.trade.ass.infra.repository.db.entity;

import java.io.Serializable;
import java.util.Date;
import java.time.*;
import java.math.BigDecimal;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import com.baomidou.mybatisplus.annotation.*;

/**
 * 驳回原因配置表(AssRejectReasonConfig)实体类
 *
 * <AUTHOR>
 * @since 2025-07-30 13:45:09
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "ass_reject_reason_config")
@ToString
public class AssRejectReasonConfig implements Serializable {
    private static final long serialVersionUID = -97953573600889959L;
    /**
     * 自增id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 业务主键
     */
    @TableField(value = "reason_config_id")
    private String reasonConfigId;
    /**
     * 售后类型1找回2纠纷
     */
    @TableField(value = "ass_type")
    private Integer assType;
    /**
     * 驳回原因文案
     */
    @TableField(value = "reason")
    private String reason;
    /**
     * 用户测文案
     */
    @TableField(value = "user_desc")
    private String userDesc;
    /**
     * 排序
     */
    @TableField(value = "sort")
    private Integer sort;
    /**
     * 创建人id
     */
    @TableField(value = "create_user_id")
    private String createUserId;
    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;
    /**
     * 更新人id
     */
    @TableField(value = "update_user_id")
    private String updateUserId;
    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;
    /**
     * 是否删除 1:已删除 0:未删除
     */
    @TableLogic
    @TableField(value = "is_deleted")
    private Boolean deleted;


}

