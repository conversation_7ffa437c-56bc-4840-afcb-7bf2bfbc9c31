package com.pxb7.mall.trade.ass.infra.repository.db;


import com.baomidou.mybatisplus.extension.service.IService;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.Payment;

import java.util.List;

/**
 * 交易中心表(Payment)表服务接口
 *
 * <AUTHOR>
 * @since 2024-09-03 13:43:22
 */
public interface PaymentRepository extends IService<Payment> {



    List<Payment> getPaymentListByOrderId(String orderId);

    List<Payment> getOrderItemPaymentList(String orderId);

    List<Payment> getListById(List<String> paymentIds);

    Boolean updatePaymentStatus(Payment payment);

    /**
     * 查询支付成功的 、 支付进行中的 payment
     * @param historyPaymentIdList
     * @return
     */
    List<Payment> listPayment(List<String> historyPaymentIdList);

    Payment getByPaymentId(String paymentId);

    Payment getByVoucherId(String receiptVoucherId);

    List<Payment> getListByPaymentIds(List<String> paymentIds);
}

