package com.pxb7.mall.trade.ass.infra.repository.db;


import java.util.List;
import java.util.Objects;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.pxb7.mall.trade.ass.infra.model.AssRetrieveGameConfigReqPO;
import com.pxb7.mall.trade.ass.infra.repository.db.mapper.AssRetrieveGameConfigMapper;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.AssRetrieveGameConfig;
import com.pxb7.mall.trade.ass.infra.repository.db.AssRetrieveGameConfigRepository;

/**
 * 售后找回游戏配置(AssRetrieveGameConfig)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-08-01 14:11:28
 */
@Slf4j
@Repository
public class AssRetrieveGameConfigDbRepository extends ServiceImpl<AssRetrieveGameConfigMapper, AssRetrieveGameConfig> implements AssRetrieveGameConfigRepository {

    @Override
    public boolean insert(AssRetrieveGameConfigReqPO.AddPO param) {
        AssRetrieveGameConfig entity = new AssRetrieveGameConfig();
        entity.setGameConfigId(param.getGameConfigId());
        entity.setRetrieveDays(param.getRetrieveDays());
        entity.setGroupOpen(param.getGroupOpen());
        entity.setCreateUserId(param.getCreateUserId());
        entity.setUpdateUserId(param.getUpdateUserId());
        return this.save(entity);
    }

    @Override
    public boolean update(AssRetrieveGameConfigReqPO.UpdatePO param) {
        LambdaUpdateWrapper<AssRetrieveGameConfig> updateWrapper = new LambdaUpdateWrapper<>();
        //where
        updateWrapper.eq(AssRetrieveGameConfig::getId, param.getId());
        //set
        if (StringUtils.isNotBlank(param.getGameConfigId())) {
            updateWrapper.set(AssRetrieveGameConfig::getGameConfigId, param.getGameConfigId());
        }
        if (Objects.nonNull(param.getRetrieveDays())) {
            updateWrapper.set(AssRetrieveGameConfig::getRetrieveDays, param.getRetrieveDays());
        }
        if (Objects.nonNull(param.getGroupOpen())) {
            updateWrapper.set(AssRetrieveGameConfig::getGroupOpen, param.getGroupOpen());
        }
        if (StringUtils.isNotBlank(param.getCreateUserId())) {
            updateWrapper.set(AssRetrieveGameConfig::getCreateUserId, param.getCreateUserId());
        }
        if (StringUtils.isNotBlank(param.getUpdateUserId())) {
            updateWrapper.set(AssRetrieveGameConfig::getUpdateUserId, param.getUpdateUserId());
        }
        return this.update(updateWrapper);
    }

    @Override
    public boolean deleteById(AssRetrieveGameConfigReqPO.DelPO param) {
        return this.removeById(param.getId());
    }

    @Override
    public AssRetrieveGameConfig findById(Long id) {
        return this.getById(id);
    }

    @Override
    public List<AssRetrieveGameConfig> list(AssRetrieveGameConfigReqPO.SearchPO param) {
        LambdaQueryWrapper<AssRetrieveGameConfig> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(param.getGameConfigId())) {
            queryWrapper.eq(AssRetrieveGameConfig::getGameConfigId, param.getGameConfigId());
        }
        if (Objects.nonNull(param.getRetrieveDays())) {
            queryWrapper.eq(AssRetrieveGameConfig::getRetrieveDays, param.getRetrieveDays());
        }
        if (Objects.nonNull(param.getGroupOpen())) {
            queryWrapper.eq(AssRetrieveGameConfig::getGroupOpen, param.getGroupOpen());
        }
        if (StringUtils.isNotBlank(param.getCreateUserId())) {
            queryWrapper.eq(AssRetrieveGameConfig::getCreateUserId, param.getCreateUserId());
        }
        if (StringUtils.isNotBlank(param.getUpdateUserId())) {
            queryWrapper.eq(AssRetrieveGameConfig::getUpdateUserId, param.getUpdateUserId());
        }
        return this.list(queryWrapper);
    }

    @Override
    public Page<AssRetrieveGameConfig> page(AssRetrieveGameConfigReqPO.PagePO param) {
        LambdaQueryWrapper<AssRetrieveGameConfig> queryWrapper = new LambdaQueryWrapper<>();
        // 返回分页查询结果
        if (StringUtils.isNotBlank(param.getGameConfigId())) {
            queryWrapper.eq(AssRetrieveGameConfig::getGameConfigId, param.getGameConfigId());
        }
        if (Objects.nonNull(param.getRetrieveDays())) {
            queryWrapper.eq(AssRetrieveGameConfig::getRetrieveDays, param.getRetrieveDays());
        }
        if (Objects.nonNull(param.getGroupOpen())) {
            queryWrapper.eq(AssRetrieveGameConfig::getGroupOpen, param.getGroupOpen());
        }
        if (StringUtils.isNotBlank(param.getCreateUserId())) {
            queryWrapper.eq(AssRetrieveGameConfig::getCreateUserId, param.getCreateUserId());
        }
        if (StringUtils.isNotBlank(param.getUpdateUserId())) {
            queryWrapper.eq(AssRetrieveGameConfig::getUpdateUserId, param.getUpdateUserId());
        }
        return this.page(new Page<>(param.getPageIndex(), param.getPageSize()), queryWrapper);
    }
}
