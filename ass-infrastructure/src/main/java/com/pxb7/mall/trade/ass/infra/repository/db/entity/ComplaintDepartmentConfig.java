package com.pxb7.mall.trade.ass.infra.repository.db.entity;

import java.io.Serializable;
import java.util.Date;
import java.time.*;
import java.math.BigDecimal;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import com.baomidou.mybatisplus.annotation.*;

/**
 * 问题部门-问题类型配置(ComplaintDepartmentConfig)实体类
 *
 * <AUTHOR>
 * @since 2024-09-20 16:45:27
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
@TableName(value = "complaint_department_config")
public class ComplaintDepartmentConfig implements Serializable {
    private static final long serialVersionUID = 341903651338640089L;
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 客诉部门id
     */
    @TableField(value = "complaint_department_id")
    private String complaintDepartmentId;
    /**
     * 部门名称
     */
    @TableField(value = "department_name")
    private String departmentName;
    /**
     * 问题类型
     */
    @TableField(value = "question_type")
    private String questionType;
    /**
     * 排序值
     */
    @TableField(value = "question_sort")
    private Integer questionSort;
    /**
     * 创建用户id
     */
    @TableField(value = "create_user_id")
    private String createUserId;
    /**
     * 更新用户id
     */
    @TableField(value = "update_user_id")
    private String updateUserId;
    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    @TableField(value = "update_time", update="now()")
    private LocalDateTime updateTime;
    /**
     * 是否删除
     */
    @TableLogic
    @TableField(value = "is_deleted")
    private Boolean deleted;

}

