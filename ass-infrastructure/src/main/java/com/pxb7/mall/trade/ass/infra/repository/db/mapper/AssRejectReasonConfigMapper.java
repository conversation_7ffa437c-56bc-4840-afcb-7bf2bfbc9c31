package com.pxb7.mall.trade.ass.infra.repository.db.mapper;

import com.pxb7.mall.trade.ass.infra.repository.db.entity.AssRejectReasonConfig;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 驳回原因配置表(AssRejectReasonConfig)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-07-30 13:45:11
 */
@Mapper
public interface AssRejectReasonConfigMapper extends BaseMapper<AssRejectReasonConfig> {
    /**
     * 批量新增数据
     *
     * @param entities List<AssRejectReasonConfig> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<AssRejectReasonConfig> entities);

}

