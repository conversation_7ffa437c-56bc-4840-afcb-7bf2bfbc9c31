package com.pxb7.mall.trade.ass.infra.repository.db;


import java.util.List;
import java.util.Objects;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.pxb7.mall.trade.ass.infra.model.AssWorkOrderReqPO;
import com.pxb7.mall.trade.ass.infra.repository.db.mapper.AssWorkOrderMapper;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.AssWorkOrder;

/**
 * 售后工单(AssWorkOrder)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-31 11:52:42
 */
@Slf4j
@Repository
public class AssWorkOrderDbRepository extends ServiceImpl<AssWorkOrderMapper, AssWorkOrder> implements AssWorkOrderRepository {

    @Override
    public boolean insert(AssWorkOrderReqPO.AddPO param) {
        AssWorkOrder entity = new AssWorkOrder();
        entity.setWorkOrderId(param.getWorkOrderId());
        entity.setOrderItemId(param.getOrderItemId());
        entity.setRoomId(param.getRoomId());
        entity.setAssType(param.getAssType());
        entity.setRelOrderId(param.getRelOrderId());
        entity.setAssStatus(param.getAssStatus());
        entity.setAssStatusMemo(param.getAssStatusMemo());
        entity.setApplyTime(param.getApplyTime());
        entity.setExpectedTime(param.getExpectedTime());
        entity.setCompleteTime(param.getCompleteTime());
        entity.setReadFlag(param.getReadFlag());
        entity.setCreateUserId(param.getCreateUserId());
        entity.setUpdateUserId(param.getUpdateUserId());
        entity.setScheduleId(param.getScheduleId());
        entity.setSourceType(param.getSourceType());
        entity.setProposerType(param.getProposerType());
        return this.save(entity);
    }

    @Override
    public boolean update(AssWorkOrderReqPO.UpdatePO param) {
        LambdaUpdateWrapper<AssWorkOrder> updateWrapper = new LambdaUpdateWrapper<>();
        //where
        updateWrapper.eq(AssWorkOrder::getId, param.getId());
        //set
        if (StringUtils.isNotBlank(param.getWorkOrderId())) {
            updateWrapper.set(AssWorkOrder::getWorkOrderId, param.getWorkOrderId());
        }
        if (StringUtils.isNotBlank(param.getScheduleId())) {
            updateWrapper.set(AssWorkOrder::getScheduleId, param.getScheduleId());
        }
        if (StringUtils.isNotBlank(param.getOrderItemId())) {
            updateWrapper.set(AssWorkOrder::getOrderItemId, param.getOrderItemId());
        }
        if (StringUtils.isNotBlank(param.getRoomId())) {
            updateWrapper.set(AssWorkOrder::getRoomId, param.getRoomId());
        }
        if (Objects.nonNull(param.getAssType())) {
            updateWrapper.set(AssWorkOrder::getAssType, param.getAssType());
        }
        if (StringUtils.isNotBlank(param.getRelOrderId())) {
            updateWrapper.set(AssWorkOrder::getRelOrderId, param.getRelOrderId());
        }
        if (Objects.nonNull(param.getAssStatus())) {
            updateWrapper.set(AssWorkOrder::getAssStatus, param.getAssStatus());
        }
        if (StringUtils.isNotBlank(param.getAssStatusMemo())) {
            updateWrapper.set(AssWorkOrder::getAssStatusMemo, param.getAssStatusMemo());
        }
        if (Objects.nonNull(param.getApplyTime())) {
            updateWrapper.set(AssWorkOrder::getApplyTime, param.getApplyTime());
        }
        if (Objects.nonNull(param.getExpectedTime())) {
            updateWrapper.set(AssWorkOrder::getExpectedTime, param.getExpectedTime());
        }
        if (Objects.nonNull(param.getCompleteTime())) {
            updateWrapper.set(AssWorkOrder::getCompleteTime, param.getCompleteTime());
        }
        if (Objects.nonNull(param.getReadFlag())) {
            updateWrapper.set(AssWorkOrder::getReadFlag, param.getReadFlag());
        }
        if (StringUtils.isNotBlank(param.getCreateUserId())) {
            updateWrapper.set(AssWorkOrder::getCreateUserId, param.getCreateUserId());
        }
        if (StringUtils.isNotBlank(param.getUpdateUserId())) {
            updateWrapper.set(AssWorkOrder::getUpdateUserId, param.getUpdateUserId());
        }
        return this.update(updateWrapper);
    }

    @Override
    public boolean deleteById(AssWorkOrderReqPO.DelPO param) {
        return this.removeById(param.getId());
    }

    @Override
    public AssWorkOrder findById(Long id) {
        return this.getById(id);
    }

    public AssWorkOrder findByOrderItemId(String orderItemId) {
        if (StrUtil.isBlank(orderItemId)) {
            return null;
        }
        LambdaQueryWrapper<AssWorkOrder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AssWorkOrder::getOrderItemId, orderItemId);
        queryWrapper.orderByDesc(AssWorkOrder::getId);
        queryWrapper.last("limit 1");
        return this.getOne(queryWrapper);
    }

    @Override
    public AssWorkOrder findByScheduleId(String scheduleId) {
        if (StrUtil.isBlank(scheduleId)) {
            return null;
        }
        LambdaQueryWrapper<AssWorkOrder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AssWorkOrder::getScheduleId, scheduleId);
        queryWrapper.orderByDesc(AssWorkOrder::getId);
        return this.getOne(queryWrapper);
    }

    //乐观锁更新
    public boolean updateWithOpt(Long id, Integer originStatus, Integer targetStatus, String assStatusMemo,Integer readFlag) {
        return this.baseMapper.updateWithOpt(id, originStatus, targetStatus, assStatusMemo,readFlag) > 0;
    }

    @Override
    public boolean updateReadFlag(String workOrderId, Integer readFlag) {
        return this.baseMapper.updateReadFlag(workOrderId, readFlag) > 0;
    }

    @Override
    public boolean updateRelOrderIdAndFlag(String workOrderId, Integer readFlag, String relOrderId) {
        return this.baseMapper.updateRelOrderIdAndFlag(workOrderId, readFlag,relOrderId) > 0;
    }


    @Override
    public List<AssWorkOrder> list(AssWorkOrderReqPO.SearchPO param) {
        LambdaQueryWrapper<AssWorkOrder> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(param.getWorkOrderId())) {
            queryWrapper.eq(AssWorkOrder::getWorkOrderId, param.getWorkOrderId());
        }
        if (StringUtils.isNotBlank(param.getScheduleId())) {
            queryWrapper.eq(AssWorkOrder::getScheduleId, param.getScheduleId());
        }
        if (StringUtils.isNotBlank(param.getOrderItemId())) {
            queryWrapper.eq(AssWorkOrder::getOrderItemId, param.getOrderItemId());
        }
        if (StringUtils.isNotBlank(param.getRoomId())) {
            queryWrapper.eq(AssWorkOrder::getRoomId, param.getRoomId());
        }
        if (Objects.nonNull(param.getAssType())) {
            queryWrapper.eq(AssWorkOrder::getAssType, param.getAssType());
        }
        if (StringUtils.isNotBlank(param.getRelOrderId())) {
            queryWrapper.eq(AssWorkOrder::getRelOrderId, param.getRelOrderId());
        }
        if (Objects.nonNull(param.getAssStatus())) {
            queryWrapper.eq(AssWorkOrder::getAssStatus, param.getAssStatus());
        }
        if (StringUtils.isNotBlank(param.getAssStatusMemo())) {
            queryWrapper.eq(AssWorkOrder::getAssStatusMemo, param.getAssStatusMemo());
        }
        if (Objects.nonNull(param.getApplyTime())) {
            queryWrapper.eq(AssWorkOrder::getApplyTime, param.getApplyTime());
        }
        if (Objects.nonNull(param.getExpectedTime())) {
            queryWrapper.eq(AssWorkOrder::getExpectedTime, param.getExpectedTime());
        }
        if (Objects.nonNull(param.getCompleteTime())) {
            queryWrapper.eq(AssWorkOrder::getCompleteTime, param.getCompleteTime());
        }
        if (Objects.nonNull(param.getReadFlag())) {
            queryWrapper.eq(AssWorkOrder::getReadFlag, param.getReadFlag());
        }
        if (StringUtils.isNotBlank(param.getCreateUserId())) {
            queryWrapper.eq(AssWorkOrder::getCreateUserId, param.getCreateUserId());
        }
        if (StringUtils.isNotBlank(param.getUpdateUserId())) {
            queryWrapper.eq(AssWorkOrder::getUpdateUserId, param.getUpdateUserId());
        }
        return this.list(queryWrapper);
    }

    @Override
    public Page<AssWorkOrder> page(AssWorkOrderReqPO.PagePO param) {
        LambdaQueryWrapper<AssWorkOrder> queryWrapper = new LambdaQueryWrapper<>();
        // 返回分页查询结果
        if (StringUtils.isNotBlank(param.getWorkOrderId())) {
            queryWrapper.eq(AssWorkOrder::getWorkOrderId, param.getWorkOrderId());
        }
        if (StringUtils.isNotBlank(param.getScheduleId())) {
            queryWrapper.eq(AssWorkOrder::getScheduleId, param.getScheduleId());
        }
        if (StringUtils.isNotBlank(param.getOrderItemId())) {
            queryWrapper.eq(AssWorkOrder::getOrderItemId, param.getOrderItemId());
        }
        if (StringUtils.isNotBlank(param.getRoomId())) {
            queryWrapper.eq(AssWorkOrder::getRoomId, param.getRoomId());
        }
        if (Objects.nonNull(param.getAssType())) {
            queryWrapper.eq(AssWorkOrder::getAssType, param.getAssType());
        }
        if (StringUtils.isNotBlank(param.getRelOrderId())) {
            queryWrapper.eq(AssWorkOrder::getRelOrderId, param.getRelOrderId());
        }
        if (Objects.nonNull(param.getAssStatus())) {
            queryWrapper.eq(AssWorkOrder::getAssStatus, param.getAssStatus());
        }
        if (StringUtils.isNotBlank(param.getAssStatusMemo())) {
            queryWrapper.eq(AssWorkOrder::getAssStatusMemo, param.getAssStatusMemo());
        }
        if (Objects.nonNull(param.getApplyTime())) {
            queryWrapper.eq(AssWorkOrder::getApplyTime, param.getApplyTime());
        }
        if (Objects.nonNull(param.getExpectedTime())) {
            queryWrapper.eq(AssWorkOrder::getExpectedTime, param.getExpectedTime());
        }
        if (Objects.nonNull(param.getCompleteTime())) {
            queryWrapper.eq(AssWorkOrder::getCompleteTime, param.getCompleteTime());
        }
        if (Objects.nonNull(param.getReadFlag())) {
            queryWrapper.eq(AssWorkOrder::getReadFlag, param.getReadFlag());
        }
        if (StringUtils.isNotBlank(param.getCreateUserId())) {
            queryWrapper.eq(AssWorkOrder::getCreateUserId, param.getCreateUserId());
        }
        if (StringUtils.isNotBlank(param.getUpdateUserId())) {
            queryWrapper.eq(AssWorkOrder::getUpdateUserId, param.getUpdateUserId());
        }
        return this.page(new Page<>(param.getPageIndex(), param.getPageSize()), queryWrapper);
    }

}
