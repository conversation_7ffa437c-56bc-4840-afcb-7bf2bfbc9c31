package com.pxb7.mall.trade.ass.infra.repository.db.entity;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.*;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 售后问题回答记录
 *
 * <AUTHOR>
 * @since: 2024-08-13 17:06
 **/

@Getter
@Setter
@ToString
@Accessors(chain = true)
@TableName(value = "ass_answer")
public class AssAnswer implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 自增id
     */
    @TableField(value = "id")
    private Long id;

    /**
     * 主键id
     */
    @TableId(value = "answer_id", type = IdType.INPUT)
    private String answerId;

    /**
     * 工单id
     */
    @TableField(value = "work_order_id")
    private String workOrderId;

    /**
     * 订单编号
     */
    @TableField(value = "order_item_id")
    private String orderItemId;

    /**
     * 答案
     */
    @TableField(value = "answer")
    private String answer;

    /**
     * 创建用户ID
     */
    @TableField(value = "create_user_id")
    private String createUserId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    @TableField(value = "update_user_id")
    private String updateUserId;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", update="now()")
    private LocalDateTime updateTime;

    /**
     * 删除标记
     */
    @TableLogic
    @TableField(value = "is_deleted")
    private Boolean deleted;

}