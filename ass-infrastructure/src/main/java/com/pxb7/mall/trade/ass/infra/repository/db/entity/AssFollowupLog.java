package com.pxb7.mall.trade.ass.infra.repository.db.entity;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.*;
import com.pxb7.mall.trade.ass.client.enums.AssLogAddWay;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 售后工单跟进记录
 *
 * <AUTHOR>
 **/
@Getter
@Setter
@ToString
@Accessors(chain = true)
@TableName(value = "ass_followup_log")
public class AssFollowupLog implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 自增id
     */
    @TableField(value = "id")
    private Long id;

    /**
     * 主键id
     */
    @TableId(value = "followup_id", type = IdType.INPUT)
    private String followupId;

    /**
     * 售后工单id
     */
    @TableField(value = "work_order_id")
    private String workOrderId;

    /**
     * 售后类型1找回2纠纷
     */
    @TableField(value = "ass_type")
    private Integer assType;

    /**
     * 标题
     */
    @TableField(value = "title")
    private String title;

    /**
     * 操作内容
     */
    @TableField(value = "content")
    private String content;

    /**
     * 跟进图片
     */
    @TableField(value = "image")
    private String image;

    /**
     * 日志添加方式1:系统自动产生的日志 2:手动添加的日志
     *
     * @see AssLogAddWay
     */
    @TableField(value = "add_way")
    private Integer addWay;

    /**
     * 跟进用户
     */
    @TableField(value = "create_user_id")
    private String createUserId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    @TableField(value = "update_user_id")
    private String updateUserId;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", update="now()")
    private LocalDateTime updateTime;

    /**
     * 删除标记
     */
    @TableLogic
    @TableField(value = "is_deleted")
    private Boolean deleted;

    /**
     * 飞书名称
     */
    @TableField(value = "feishu_name")
    private String feiShuName;

}