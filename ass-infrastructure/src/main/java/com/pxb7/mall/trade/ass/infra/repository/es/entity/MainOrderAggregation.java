package com.pxb7.mall.trade.ass.infra.repository.es.entity;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.DateFormat;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 主订单聚合数据
 */
@Data
@Document(indexName = "main_order_aggregation")
public class MainOrderAggregation {

    /**
     * 订单号
     */
    @Id
    @Field(name = "order_id",type = FieldType.Keyword)
    private String orderId;

    /**
     * 下单时间
     */
    @Field(name = "create_time",type = FieldType.Date, format = DateFormat.date_hour_minute_second_fraction)
    private LocalDateTime createTime;

    /**
     * 需支付金额
     */
    private Long needPayAmount;

    @Field(name = "order_status",type = FieldType.Integer)
    private Integer orderStatus;

    /**
     * 是否聚合支付 0 否 1是
     */
    private Integer isAggregatePay;

    /**
     * 交易模式
     */
    @Field(name = "service_mode",type = FieldType.Integer)
    private Integer serviceMode;

    /**
     * 交易模式
     */
    private String serviceModeName;

    /**
     * 订单来源
     */
    @Field(name = "order_source", type = FieldType.Integer)
    private Integer orderSource;


    /**
     * 订单来源
     */
    private String orderSourceName;


    /**
     * 主订单金额
     */
    private Long mainOrderAmount;

    /**
     * 订单交付节点列表
     */
    private List<DeliverNodeDoc> deliverNodeDocList;

    /**
     * 订单聚合信息
     */
    @Field(name = "order_aggregation_list", type = FieldType.Nested)
    private List<OrderItemAggregation> orderAggregationList;

    /**
     * 支付类型
     */
    private Integer paymentType;

    /**
     * 交付节点
     */
    private List<DeliverNodeDoc> orderNodeList;

    /**
     * 商品名称
     */
    @Field(name = "product_name", type = FieldType.Text)
    private String productName;

    /**
     * 群聊房间id
     */
    @Field(name = "group_room_id",type = FieldType.Keyword)
    private String groupRoomId;

    /**
     * 交易客服名称
     */
    @Field(name = "trade_customer", type = FieldType.Text)
    private String tradeCustomer;

    /**
     * 交付客服名称
     */
    @Field(name = "delivery_customer", type = FieldType.Text)
    private String deliveryCustomer;

    /**
     * 买家手机号
     */
    @Field(name = "buyer_phone", type = FieldType.Keyword)
    private String buyerPhone;

    /**
     * 买家身份
     */
    @Field(name = "buyer_identity", type = FieldType.Integer)
    private Integer buyerIdentity;

    /**
     * 卖家身份
     */
    @Field(name = "seller_identity", type = FieldType.Integer)
    private Integer sellerIdentity;

    /**
     * 卖家手机号
     */
    @Field(name = "seller_phone", type = FieldType.Keyword)
    private String sellerPhone;

    /**
     * 是否诚心卖
     */
    @Field(name = "sincerely_sell", type = FieldType.Integer)
    private Integer sincerelySell;

    /**
     * 交易状态
     */
    @Field(name = "order_item_status", type = FieldType.Integer)
    private Integer orderItemStatus;


    /**
     * 退款状态
     */
    @Field(name = "refund_status", type = FieldType.Integer)
    private Integer refundStatus;
    /**
     * 放款状态
     *
     */
    @Field(name = "payout_status", type = FieldType.Integer)
    private Integer payoutStatus;

    /**
     * 收款状态
     */
    @Field(name = "collection_status", type = FieldType.Integer)
    private Integer collectionStatus;

    /**
     * 商品成交价
     */
    private Long productSalePrice;

    @Field(name = "Seller_id", type = FieldType.Keyword)
    private String sellerId;

    @Field(value = "buyer_id", type = FieldType.Keyword)
    private String buyerId;

    /**
     * 买家订单状态
     */
    @Field(name = "buyer_status", type = FieldType.Integer)
    private Integer buyerStatus;

    /**
     * 卖家订单状态
     */
    @Field(name = "seller_status", type = FieldType.Integer)
    private Integer sellerStatus;

    /**
     * 游戏id
     */
    @Field(name = "game_id", type = FieldType.Keyword)
    private String gameId;

    /**
     * 订单id
     */
    @Field(name = "order_item_id", type = FieldType.Text)
    private String orderItemId;

    /**
     * 商品id
     */
    @Field(name = "product_id", type = FieldType.Text)
    private String productId;

    /**
     * 订单类型 0 账号订单 1 装备订单 2 金币订单 3 代肝订单 4 自抽号订单
     */
    private String productTypeName;

    @Field(name = "product_type", type = FieldType.Keyword)
    private Integer productType;

    /**
     * 商品编码
     */
    private String productUniqueNo;

    /**
     * 游戏账号
     */
    private String gameAccount;

    /**
     * 游戏名称
     */
    private String gameName;


}
