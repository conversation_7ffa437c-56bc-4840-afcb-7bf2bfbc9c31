package com.pxb7.mall.trade.ass.infra.remote.service.http.dto.request;

import com.alibaba.cola.dto.DTO;
import com.pxb7.mall.trade.order.client.constants.JdPayConstant;
import com.pxb7.mall.trade.order.client.constants.PayTimeOutConstant;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import static com.pxb7.mall.trade.order.client.constants.JdPayConstant.*;

/**
 * 京东支付 三方请求入参
 */
public class JdPayReqPO {

    /**
     * 京东支付：加密后接口统一入参
     */
    @Data
    public static class BaseParam {
        /**
         * 商户号
         */
        private String merchantNo;
        /**
         * 请求唯一标识
         */
        private String reqNo;
        /**
         * 字符集，固定值：UTF-8
         */
        private String charset = JdPayConstant.CHARSET_UTF8;
        /**
         * 数据类型，固定值：JSON
         */
        private String formatType = JdPayConstant.DATA_FORMAT_TYPE_JSON;
        /**
         * 签名类型，固定值：SHA-256
         */
        private String signType = JdPayConstant.SING_TYPE;
        /**
         * 加密类型，固定值：AP7
         */
        private String encType = JdPayConstant.ENCRYPT_TYPE_AP7;
        /**
         * 签名数据
         */
        private String signData ;
        /**
         * 加密数据
         */
        private String encData ;
    }

    /**
     * 京东支付：查询一键签约银行
     */
    @Data
    public static class QuerySignBanksParam {
        /**
         * 请求唯一标识
         */
        private String requestNo;
        /**
         * 商户号
         */
        private String merchantNo;
    }

    /**
     * 签约 入参
     */
    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString(callSuper = true)
    public static class AgreementSignParam extends DTO {
        /**
         * 签名请求号
         */
        private String signReqNo;
        /**
         * 模板编号
         */
        private String templateNo;
        /**
         * 持卡人姓名
         */
        private String idName;
        /**
         * 证件类型，固定值：ID
         */
        private String idType =  "ID";
        /**
         * 证件号码
         */
        private String idNo;
        /**
         * 银行卡类型，固定值：DE 借记卡；CR 信用卡
         */
        private String cardType;
        /**
         * 银行卡简码
         */
        private String bankCode;
        /**
         * 业务回跳地址
         */
        private String returnUrl;
        /**
         * 签约描述
         */
        private String tradeSubject = JD_BANK_TRADE_SUBJECT;
        /**
         * 商户通知地址: 签约完成后，异步通知商户的地址
         */
        private String notifyUrl;

    }

    /**
     * 协议支付 入参
     */
    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString(callSuper = true)
    public static class AgreementPayParam extends DTO {
        /**
         * 签约协议号 --必传
         */
        private String agreementNo;
        /**
         * 商户订单号 --必传
         */
        private String outTradeNo;
        /**
         * 交易金额，单位：分 --必传
         */
        private String tradeAmount;
        /**
         * 交易币种,固定值：CNY
         */
        private String currency = CURRENCY_CNY;
        /**
         * 交易描述 --必传
         */
        private String tradeSubject;
        /**
         * 业务类型
         */
        private String bizTp = BIZ_TYPE;
        /**
         * 商户通知地址: 协议支付完成后，异步通知商户结果 --必传
         */
        private String notifyUrl;
        /**
         * 交易过期关单时间，单位：分钟,范围 30-2880，如果该字段不传，则默认为2880 分钟（即 2 天后)
         */
        private String tradeExpiryTime = String.valueOf(PayTimeOutConstant.PAY_TIME_OUT_ONLINE);
        /**
         * 产品标识,固定值：protocolPay
         */
        private String attributedProduct = ATTRIBUTED_PRODUCT;
    }

    /**
     * 协议支付查询 入参
     */
    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString(callSuper = true)
    public static class AgreementPayQueryParam extends DTO {
        /**
         * 商户订单号 --必传
         */
        private String outTradeNo;
    }

    /**
     * 协议查询 入参
     */
    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString(callSuper = true)
    public static class AgreementQueryParam extends DTO {
        /**
         * 签名请求号 --必传
         */
        private String signReqNo;
        /**
         * 签约协议号 --必传
         */
        private String agreementNo;
    }

    /**
     * 退款 入参
     */
    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString(callSuper = true)
    public static class RefundParam extends DTO {
        /**
         * 商户退款订单号 --必传
         */
        private String outTradeNo;
        /**
         * 商户原订单号（正向支付时传的唯一交易流水号） --必传
         */
        private String originalOutTradeNo;
        /**
         * 商户通知地址: 退款完成后，异步通知商户的地址 --必传
         */
        private String notifyUrl;
        /**
         * 退款金额，单位：分，大于 0，支持部分退款 --必传
         */
        private String tradeAmount;
        /**
         * 交易币种,固定值：CNY
         */
        private String currency = CURRENCY_CNY;
        /**
         * 回传参数 --非必传
         */
        private String returnParams;

    }

    /**
     * 退款 入参
     */
    @Getter
    @Setter
    @Accessors(chain = true)
    @ToString(callSuper = true)
    public static class RefundQueryParam extends DTO {
        /**
         * 商户退款订单号 --必传
         */
        private String outTradeNo;

    }

}
