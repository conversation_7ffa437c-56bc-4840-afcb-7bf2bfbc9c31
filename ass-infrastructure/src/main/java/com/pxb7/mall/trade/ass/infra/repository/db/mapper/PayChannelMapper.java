package com.pxb7.mall.trade.ass.infra.repository.db.mapper;

import com.pxb7.mall.trade.ass.infra.repository.db.entity.PayChannel;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 支付渠道表(PayChannel)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-09-27 10:10:55
 */
@Mapper
public interface PayChannelMapper extends BaseMapper<PayChannel> {
    /**
     * 批量新增数据
     *
     * @param entities List<PayChannel> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<PayChannel> entities);

}

