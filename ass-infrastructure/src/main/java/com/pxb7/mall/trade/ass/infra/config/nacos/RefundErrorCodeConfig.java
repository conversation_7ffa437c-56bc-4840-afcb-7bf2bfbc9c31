package com.pxb7.mall.trade.ass.infra.config.nacos;

import com.pxb7.mall.trade.ass.infra.util.StringUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;

@Slf4j
@Component
@Data
public class RefundErrorCodeConfig implements NacosYamlConfigListener {

    /**
     * 退款失败的错误码，匹配到这些错误码直接关闭退款单
     */
    private List<String> failErrorCodes;

    @Override
    public String configPath() {
        return "refund_fail_code";
    }
    @Override
    public void onRefresh(Object newConfig) {
        String failCodes = newConfig.toString();
        if (StringUtil.isNotBlank(failCodes)) {
            this.failErrorCodes = StringUtil.convertStringToList(failCodes);
            log.info("refund_fail_code: {}", failErrorCodes);
        }
    }


}
