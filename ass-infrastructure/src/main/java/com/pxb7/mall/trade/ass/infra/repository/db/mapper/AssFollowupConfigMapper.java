package com.pxb7.mall.trade.ass.infra.repository.db.mapper;

import com.pxb7.mall.trade.ass.infra.repository.db.entity.AssFollowupConfig;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 跟进结果类型配置表(AssFollowupConfig)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-07-30 11:51:54
 */
@Mapper
public interface AssFollowupConfigMapper extends BaseMapper<AssFollowupConfig> {
    /**
     * 批量新增数据
     *
     * @param entities List<AssFollowupConfig> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<AssFollowupConfig> entities);

}

