package com.pxb7.mall.trade.ass.infra.repository.db.entity;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.*;
import com.pxb7.mall.trade.ass.client.enums.AssScheduleSourceTypeEnums;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 售后流程进度(AssSchedule)实体类
 *
 * <AUTHOR>
 * @since 2024-08-12 10:59:09
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
@TableName(value = "ass_schedule")
public class AssSchedule implements Serializable {

    @Serial
    private static final long serialVersionUID = 647168881307210696L;

    /**
     * 自增id
     */
    @TableField(value = "id")
    private Long id;

    /**
     * 主键id
     */
    @TableId(value = "schedule_id", type = IdType.INPUT)
    private String scheduleId;

    /**
     * 订单ID
     */
    @TableField(value = "order_item_id")
    private String orderItemId;

    /**
     * 工单id
     */
    @TableField(value = "work_order_id")
    private String workOrderId;

    /**
     * 售后类型1找回2纠纷
     */
    @TableField(value = "ass_type")
    private Integer assType;

    /**
     * 接待客服id
     */
    @TableField(value = "recv_customer_id")
    private String recvCustomerId;

    /**
     * 审核客服id
     */
    @TableField(value = "audit_customer_id")
    private String auditCustomerId;

    /**
     * 房间id
     */
    @TableField(value = "room_id")
    private String roomId;

    /**
     * 是否完结
     */
    @TableField(value = "is_finish")
    private Boolean finish;

    /**
     * 来源1:c端用户 2:客服 3:admin用户
     * 
     * @see AssScheduleSourceTypeEnums
     */
    @TableField(value = "source_type")
    private Integer sourceType;

    /**
     * 创建用户ID
     */
    @TableField(value = "create_user_id")
    private String createUserId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    @TableField(value = "update_user_id")
    private String updateUserId;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", update="now()")
    private LocalDateTime updateTime;

    /**
     * 删除标记
     */
    @TableLogic
    @TableField(value = "is_deleted")
    private Boolean deleted;

}
