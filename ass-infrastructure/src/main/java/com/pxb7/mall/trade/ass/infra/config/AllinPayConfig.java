package com.pxb7.mall.trade.ass.infra.config;

import org.springframework.context.annotation.Configuration;

@Configuration
public class AllinPayConfig {

    public static String unitOrderHost = "https://syb-test.allinpay.com";

    public static String businessId = "5505810078000YD";
    public static String appId = "00002811";

    // SM2私钥
    public static String privateKey =
        "MIGTAgEAMBMGByqGSM49AgEGCCqBHM9VAYItBHkwdwIBAQQgKq+zfelNxrtYkJh3zX4b3miICXYN3ki3ZTREqUXSBfSgCgYIKoEcz1UBgi2hRANCAATSXuGjTUMlEvvUwl+BEXfzzNZ8CXMKjUza8taH1mCxUPc91TFzqK1FGcQflWTWn68b+H0RLQnv/TySkL5GfDJi";
    // SM2公钥
    public static String allinPayPublicKey =
        "MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAE/BnA8BawehBtH0ksPyayo4pmzL/u1FQ2sZcqwOp6bjVqQX4tjo930QAvHZPJ2eez8sCz/RYghcqv4LvMq+kloQ==";

}
