package com.pxb7.mall.trade.ass.infra.remote.dubbo;

import cn.hutool.json.JSONUtil;
import com.alibaba.cola.dto.SingleResponse;
import com.pxb7.mall.common.client.api.message.SmsServiceI;
import com.pxb7.mall.common.client.request.message.SendSmsReqDTO;
import com.pxb7.mall.trade.ass.infra.enums.notis.SmsEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 功能描述:发送短信消息 
 * 作者：白春韬
 * 创建日期：2025/08/05 
 * 公司名称：金华博淳网络科技有限公司 
 * 域名： www.pxb7.com
 */
@Service
@Slf4j
public class SmsGateway {

    @DubboReference(providedBy = "common-support")
    private SmsServiceI smsServiceI;

    /**
     * 发短信
     */
    public void sendSms(String assRetrieveId, String phone, SmsEnum smsEnum, Map<String, String> msgParams) {
        if (StringUtils.isEmpty(phone)) {
            return;
        }
        SendSmsReqDTO reqDTO = new SendSmsReqDTO();
        reqDTO.setBusinessId(assRetrieveId);
        reqDTO.setBusinessType(smsEnum.getBusinessType());
        reqDTO.setTemplateCode(smsEnum.getSmsCode());
        reqDTO.setPhone(phone);
        reqDTO.setTemplateParams(JSONUtil.toJsonStr(msgParams));
        try {
            SingleResponse<Void> response = smsServiceI.sendSmsV2(reqDTO);
            if (!response.isSuccess()) {
                log.info("SmsGateway.sendSms failed, param---{}, response---{}", reqDTO, response);
            }
        } catch (Exception e) {
            log.error("SmsGateway.sendSms error, param---{}", reqDTO, e);
        }
    }
}
