package com.pxb7.mall.trade.ass.infra.remote.model.refund;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
public class OrderItemReceiptVoucherPO {

    /**
     * 支付模式 1线上支付 2线下支付 3挂账
     */
    private Integer payMode;
    /**
     * 实收金额,收款单的总金额，注意可能多笔子订单合一个收款单单支付
     */
    private Long actualPayAmount;
    // 以上字段from ReceiptVoucher

    /**
     * todo 不能包含红包 子订单最大可退款金额，productAmount+indemnityAmount+feeAmount+redPacketAmount
     */
    private Long maxRefundAmount;

    /**
     * 已退款金额，只有线上的能关联，用于计算该笔收款单在线原路退款的剩余可退金额
     */
    private Long alreadyRefundAmount;

    // 以下字段from ReceiptVoucherDetail
    /**
     * 支付中心id
     */
    private String paymentId;
    /**
     * 子订单id
     */
    private String orderItemId;
    /**
     * 收款单id
     */
    private String receiptVoucherId;
    /**
     * 商品金额
     */
    private Long productAmount;
    /**
     * 包赔金额
     */
    private Long indemnityAmount;
    /**
     * 手续费金额
     */
    private Long feeAmount;
    /**
     * 红包金额
     */
    private Long redPacketAmount;
    /**
     * 优惠券金额
     */
    private Long couponAmount;
    /**
     * 承担方 0买家 1卖家
     */
    private Integer feeResponsibleUser;
    /**
     * 承担方 0买家 1卖家
     */
    private Integer indemnityResponsibleUser;
}
