package com.pxb7.mall.trade.ass.infra.repository.db.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.ReceiptVoucherDetail;

/**
 * 收款单-资金明细(ReceiptVoucherDetail)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-09-25 16:16:37
 */
@Mapper
public interface ReceiptVoucherDetailMapper extends BaseMapper<ReceiptVoucherDetail> {
    /**
     * 批量新增数据
     *
     * @param entities List<ReceiptVoucherDetail> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<ReceiptVoucherDetail> entities);

}
