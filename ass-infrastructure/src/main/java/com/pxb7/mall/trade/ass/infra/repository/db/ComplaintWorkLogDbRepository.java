package com.pxb7.mall.trade.ass.infra.repository.db;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.ComplaintWorkLog;
import com.pxb7.mall.trade.ass.infra.repository.db.mapper.ComplaintWorkLogMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 客诉工单操作日志表(ComplaintWorkLog)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-09-20 16:52:42
 */
@Slf4j
@Repository
public class ComplaintWorkLogDbRepository extends ServiceImpl<ComplaintWorkLogMapper, ComplaintWorkLog> implements ComplaintWorkLogRepository {

    @Override
    public List<ComplaintWorkLog> queryByWorkId(String complaintWorkId) {
        LambdaQueryWrapper<ComplaintWorkLog> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ComplaintWorkLog::getComplaintWorkId, complaintWorkId);
        return baseMapper.selectList(queryWrapper);
    }
}
