package com.pxb7.mall.trade.ass.infra.repository.db;

import com.baomidou.mybatisplus.extension.service.IService;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.AssRetrieveWo;

/**
 * 售后找回工单
 *
 * <AUTHOR>
 * @since: 2024-08-13 16:48
 **/
public interface AssRetrieveWoRepository extends IService<AssRetrieveWo> {
    Boolean setDealUser(String assRetrieveId, String dealUserId);

    Boolean closeRetrieveWo(String assRetrieveId);


    Boolean userCancel(String assRetrieveId);

}
