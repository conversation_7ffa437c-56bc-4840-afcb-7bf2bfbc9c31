package com.pxb7.mall.trade.ass.infra.repository.db.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.RefundVoucher;

/**
 * 退款单(RefundVoucher)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-09-25 15:02:33
 */
@Mapper
public interface RefundVoucherMapper extends BaseMapper<RefundVoucher> {
    /**
     * 批量新增数据
     *
     * @param entities List<RefundVoucher> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<RefundVoucher> entities);
}
