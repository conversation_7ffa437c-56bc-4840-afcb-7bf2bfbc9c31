package com.pxb7.mall.trade.ass.infra.config;

import org.apache.commons.lang3.SystemUtils;
import org.springframework.context.annotation.Condition;
import org.springframework.context.annotation.ConditionContext;
import org.springframework.core.type.AnnotatedTypeMetadata;

import java.util.Optional;

public class AssertIdeaCondition implements Condition {
    @Override
    public boolean matches(ConditionContext context, AnnotatedTypeMetadata metadata) {

        // 默认认为 -Dallow.idea=false
        Boolean allow = context.getEnvironment().getProperty("allow.idea", Boolean.class);
        allow = Optional.ofNullable(allow).orElse(Boolean.FALSE);

        if (Boolean.TRUE.equals(allow)) {
            return Boolean.TRUE;  // true代表 允许
        }

        if (Boolean.FALSE.equals(isLocal()) || runWithJarLauncher()) {
            return Boolean.TRUE;
        }

        return Boolean.FALSE;
    }

    public static boolean runWithJarLauncher() {
        // 当前可能没有这个类, 不能用 JarLauncher.class.getName 方式获取, 用的地方可能有
        return isPresent("org.springframework.boot.loader.launch.JarLauncher");
    }

    public static boolean isPresent(String className) {
        try {
            Class.forName(className);
            return true;
        } catch (Exception ignore) {
            return false;
        }
    }

    public static Boolean isLocal() {
        // 服务器不会是windows和mac
        return SystemUtils.IS_OS_WINDOWS || SystemUtils.IS_OS_MAC;
    }


}
