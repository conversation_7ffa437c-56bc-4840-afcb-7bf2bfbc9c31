package com.pxb7.mall.trade.ass.infra.remote.dubbo;

import com.alibaba.cola.dto.SingleResponse;
import com.pxb7.mall.common.client.api.CensorServiceI;
import com.pxb7.mall.common.client.request.censor.CensorSingleTextReqDTO;
import com.pxb7.mall.common.client.response.censor.CensorRespDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

/**
 * Copyright (C), 2002-2025, 螃蟹游戏服务网
 *
 * @fileName: CommonSupportRpcGateway.java
 * @description: 公共支撑dubbo接口
 * @author: guominqiang
 * @email: <EMAIL>
 * @date: 2025/4/16 13:56
 * @history: //修改记录
 * <author> <time> <version> <desc>
 * 修改人姓名 修改时间 版本号 描述
 */
@Component
@Slf4j
public class CommonSupportRpcGateway {

    @DubboReference
    private CensorServiceI censorService;

    /**
     * 文本审核
     *
     * @param content 文本内容
     */
    public CensorRespDTO censorSingleText(String content) {

        CensorSingleTextReqDTO censorSingleTextReqDTO = new CensorSingleTextReqDTO();
        censorSingleTextReqDTO.setText(content);
        SingleResponse<CensorRespDTO> singleResponse = censorService.censorSingleText(censorSingleTextReqDTO);
        if (singleResponse.isSuccess()) {
            return singleResponse.getData();
        }
        return null;
    }
}
