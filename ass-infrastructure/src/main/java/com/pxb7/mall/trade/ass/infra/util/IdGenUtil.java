package com.pxb7.mall.trade.ass.infra.util;

import com.alibaba.cola.exception.BizException;
import com.pxb7.mall.components.idgen.IdGen;
import com.pxb7.mall.trade.order.client.dto.ErrorCode;
import com.pxb7.mall.trade.order.client.utils.ActiveUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import static com.pxb7.mall.trade.order.client.constants.OrderNumberConstant.*;

@Slf4j
public class IdGenUtil {
    //单号倒数第五位 = 2， 代表2.0 订单
    private final static String CURRENT_SYSTEM_NO = "2";
    //单号后四位取分库键后四位
    private final static Integer SPILT_NUM = 4;
    //环境变量
    private final static String active = SpringUtils.getActiveProfile();
    //雪花算法 ID 工具类
    private static final IdGen idGen = SpringUtils.getBean(IdGen.class);

    /**
     * 获取ID
     */
    public static String generateId() {
        String uat = ActiveUtil.isUatOrTest2(active) ? UAT_ORDER : "";
        return uat + idGen.nextId();
    }

    /**
     * 获取 分库ID
     * 传递 带有分库键id后四位的参数
     */
    public static String getShardingColumnId(String shardingId) {
        return getHeadShardingId(shardingId, "");
    }

    /**
     * 获取退款单ID
     */
    public static String getTKOrderId(String shardingStr) {
        return getHeadShardingId(shardingStr, REFUND_VOUCHER_NO);
    }

    /**
     * 生成 前缀业务主键
     */
    public static String getHeadShardingId(String shardingStr, String head) {
        //TODO 临时日志
        log.info("getHeadShardingId shardingStr = {}, head = {}, active = {}", shardingStr, head, active);
        String uat = ActiveUtil.isUatOrTest2(active) ? UAT_ORDER : "";
        return uat + head + getShardingId(shardingStr);
    }

    /**
     * 生成 业务主键
     */
    private static String getShardingId(String shardingId) {
        if (StringUtils.isBlank(shardingId)) {
            log.error("生成sharding key失败，shardingId：{}", shardingId);
            throw new BizException(ErrorCode.SHARDING_KEY_ERROR.getErrCode(), ErrorCode.SHARDING_KEY_ERROR.getErrDesc());
        }
        if (shardingId.length() < 4) {
            shardingId = "0000".substring(shardingId.length()) + shardingId;
        }
        return String.valueOf(idGen.nextId()).concat(CURRENT_SYSTEM_NO + shardingId.substring(shardingId.length() - SPILT_NUM));
    }

}
