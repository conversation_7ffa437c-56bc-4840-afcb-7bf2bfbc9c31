package com.pxb7.mall.trade.ass.infra.remote.interceptor;

import com.alibaba.cola.exception.BizException;
import com.pxb7.mall.trade.ass.infra.constant.PayConstant;
import com.pxb7.mall.trade.ass.infra.enums.ErrorCode;
import com.pxb7.mall.trade.ass.infra.util.lianliansecurity.LLianPayAccpSignature;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Interceptor;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import okio.Buffer;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * 连连支付请求头添加签名拦截
 */
@Slf4j
@Component
public class LLianSignatureInterceptor implements Interceptor {

    private String privateKey;

    public void setPrivateKey(String privateKey) {
        this.privateKey = privateKey;
    }

    @NotNull
    @Override
    public Response intercept(@NotNull Chain chain) throws IOException {
        Request request = chain.request();
        // 生成签名
        RequestBody body = request.body();
        Buffer buffer = new Buffer();
        body.writeTo(buffer);

        String bodyStr = buffer.readUtf8Line();
        String sign = LLianPayAccpSignature.sign(privateKey, bodyStr);

        if (StringUtils.isNotBlank(sign)) {
            Request.Builder requestBuilder = request.newBuilder();
            Request signedRequest = requestBuilder.addHeader("Content-Type", "application/json;charset=utf-8")
                .addHeader("Signature-Type", PayConstant.SIGN_TYPE_RSA).addHeader("Signature-Data", sign).build();

            return chain.proceed(signedRequest);
        } else {
            // 签名失败
            throw new BizException(ErrorCode.SIGN_ERROR.getErrDesc());
        }
    }
}
