package com.pxb7.mall.trade.ass.infra.repository.db.entity;

import java.io.Serializable;
import java.util.Date;
import java.time.*;
import java.math.BigDecimal;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import com.baomidou.mybatisplus.annotation.*;

/**
 * 售后工单日志明细(AssWorkOrderLog)实体类
 *
 * <AUTHOR>
 * @since 2025-07-31 10:19:30
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "ass_work_order_log")
@ToString
public class AssWorkOrderLog implements Serializable {
    private static final long serialVersionUID = -80441516498390581L;
    /**
     * 自增id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 售后工单号关联ass_work_order.work_order_id
     */
    @TableField(value = "work_order_id")
    private String workOrderId;
    /**
     * 日志id
     */
    @TableField(value = "work_order_log_id")
    private String workOrderLogId;
    /**
     * 订单ID
     */
    @TableField(value = "order_item_id")
    private String orderItemId;
    /**
     * 标题
     */
    @TableField(value = "title")
    private String title;
    /**
     * 售后类型1找回2纠纷
     */
    @TableField(value = "ass_type")
    private Integer assType;
    /**
     * 0:全展示 1:用户端展示 2:后台展示
     */
    @TableField(value = "show_type")
    private Integer showType;
    /**
     * 用户端展示操作内容
     */
    @TableField(value = "content")
    private String content;
    /**
     * admin展示操作内容
     */
    @TableField(value = "admin_content")
    private String adminContent;
    /**
     * 日志添加方式1系统自动产生的日志 2手动添加的日志
     */
    @TableField(value = "add_way")
    private Integer addWay;
    /**
     * 节点id
     */
    @TableField(value = "node_id")
    private String nodeId;
    /**
     * 当前节点状态描述
     */
    @TableField(value = "node_desc")
    private String nodeDesc;
    /**
     * 创建人id
     */
    @TableField(value = "create_user_id")
    private String createUserId;
    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;
    /**
     * 更新人id
     */
    @TableField(value = "update_user_id")
    private String updateUserId;
    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;
    /**
     * 是否删除 1:已删除 0:未删除
     */
    @TableLogic
    @TableField(value = "is_deleted")
    private Boolean deleted;

    /**
     * 通知类型 0:不通知 1:追回账号,待卖家换绑2:追回号款,待卖家提供收款账号3:售后到期,待买家接受赔付
     */
    @TableField(value = "notice_type")
    private Integer noticeType;

    @TableField(value = "join_group_msg")
    private String joinGroupMsg;



}

