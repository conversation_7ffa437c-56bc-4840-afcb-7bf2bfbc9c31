package com.pxb7.mall.trade.ass.infra.repository.db.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 主订单(MainOrder)实体类
 *
 * <AUTHOR>
 * @since 2024-07-23 09:51:24
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
@TableName(value = "ext_voucher")
public class ExtVoucher implements Serializable {
    private static final long serialVersionUID = -36813869405182610L;
    /**
     * 主键
     */
    @TableField(value = "id")
    private Long id;

    /**
     * 订单id
     */
    @TableField(value = "ext_voucher_id")
    private String extVoucherId;

    /**
     * 订单业务id
     */
    @TableField(value = "order_id")
    private String orderId;

    /**
     * 订单业务id
     */
    @TableField(value = "order_item_id")
    private String orderItemId;

    /**
     * 放款单类型 1 放款 2补放款
     */
    @TableField(value = "ext_payment_type")
    private int extPaymentType = 1;

    /**
     * 放款方式 1支付宝 2微信 3银行卡
     */
    @TableField(value = "ext_type")
    private int extType = 0;

    /**
     * 审核方式 1自动审核 2人工审核
     */
    @TableField(value = "auto_examine_status")
    private Integer autoExamineStatus;

    /**
     * 订单金额
     */
    @TableField(value = "order_amount")
    private Long orderAmount = 0L;

    /**
     * 应放金额
     */
    @TableField(value = "need_amount")
    private Long needAmount = 0L;

    /**
     * 实放金额
     */
    @TableField(value = "actual_amount")
    private Long actualAmount = 0L;

    /**
     * 收款的系统用户id
     */
    @TableField(value = "user_id")
    private String userId;

    /**
     * 收款人手机号
     */
    @TableField(value = "user_telphone")
    private String userTelphone;

    /**
     * 收款账户id
     */
    @TableField(value = "user_account_id")
    private String userAccountId;

    /**
     * 收款人
     */
    @TableField(value = "user_name")
    private String userName;

    /**
     * 收款账号
     */
    @TableField(value = "user_account")
    private String userAccount;

    /**
     * 银行编码
     */
    @TableField(value = "bank_code")
    private String bankCode;

    /**
     * 开户行
     */
    @TableField(value = "bank_name")
    private String bankName;

    /**
     * 公司打款商户ud
     */
    @TableField(value = "merchant_id")
    private String merchantId;

    /**
     * 公司打款款账号
     */
    @TableField(value = "company_account")
    private String companyAccount;

    /**
     * 外部交易号
     */
    @TableField(value = "out_trade_no")
    private String outTradeNo;

    /**
     * 打款状态 1 已制单 2 放款中  3放款成功  4放款失败
     */
    @TableField(value = "ext_payment_status")
    private int extPaymentStatus = 1;

    /**
     * 审核状态  1 审核中  2 审核通过  3 审核失败
     */
    @TableField(value = "review_status")
    private int reviewStatus = 1;

    /**
     * 审核备注
     */
    @TableField(value = "review_remark")
    private String reviewRemark;

    /**
     * 完成时间
     */
    @TableField(value = "complete_time")
    private Date completeTime;

    /**
     * 用户申请时间
     */
    @TableField(value = "apply_time")
    private LocalDateTime applyTime;

    /**
     * 是否公用账号
     */
    @TableField(value = "is_common_account")
    private int isCommonAccount;

    /**
     * 是否修改账号
     */
    @TableField(value = "is_change_account")
    private int isChangeAccount;

    /**
     * 是否高价
     */
    @TableField(value = "is_high_amount")
    private int isHighAmount = 0;

    /**
     * 数据来源 1 主站
     */
    @TableField(value = "data_source")
    private int dataSource = 1;

    /**
     * 创建用户id(客服)
     */
    @TableField(value = "create_user_id")
    private String createUserId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * 更新用户id
     */
    @TableField(value = "update_user_id")
    private String updateUserId;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", update="now()")
    private Date updateTime;

    /**
     * 是否删除 1:已删除 0:未删除
     */
    @TableField(value = "is_deleted")
    private int isDeleted;

}
