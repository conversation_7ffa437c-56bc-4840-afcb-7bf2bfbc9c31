package com.pxb7.mall.trade.ass.infra.repository.db.mapper;

import com.pxb7.mall.trade.ass.infra.repository.db.entity.AssWorkOrder;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 售后工单(AssWorkOrder)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-07-31 11:52:39
 */
@Mapper
public interface AssWorkOrderMapper extends BaseMapper<AssWorkOrder> {
    /**
     * 批量新增数据
     *
     * @param entities List<AssWorkOrder> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<AssWorkOrder> entities);

    /**
     * 乐观锁更新
     * @param id 主键
     * @param originStatus 源状态
     * @param targetStatus 目标状态
     * @return 返回影响行数
     */
    int updateWithOpt(@Param("id") Long id,@Param("originStatus") Integer originStatus,@Param("targetStatus") Integer targetStatus,@Param("assStatusMemo") String assStatusMemo,@Param("readFlag")Integer readFlag);

    /**
     * 更新已读状态
     */
    int updateReadFlag(@Param("workOrderId") String workOrderId, @Param("readFlag") Integer readFlag);

    int updateRelOrderIdAndFlag(@Param("workOrderId") String workOrderId, @Param("readFlag")Integer readFlag, @Param("relOrderId")String relOrderId);
}

