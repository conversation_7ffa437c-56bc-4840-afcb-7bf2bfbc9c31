package com.pxb7.mall.trade.ass.infra.repository.db.entity;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.*;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 收款单-资金明细(ReceiptVoucherDetail)实体类
 *
 * <AUTHOR>
 * @since 2024-09-30 15:25:31
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
@TableName(value = "receipt_voucher_detail")
public class ReceiptVoucherDetail implements Serializable {
    private static final long serialVersionUID = 676795431050452526L;
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 子订单id
     */
    @TableField(value = "order_item_id")
    private String orderItemId;
    /**
     * 收款单id
     */
    @TableField(value = "receipt_voucher_id")
    private String receiptVoucherId;
    /**
     * 商品金额
     */
    @TableField(value = "product_amount")
    private Long productAmount;
    /**
     * 包赔金额
     */
    @TableField(value = "indemnity_amount")
    private Long indemnityAmount;
    /**
     * 手续费金额
     */
    @TableField(value = "fee_amount")
    private Long feeAmount;
    /**
     * 红包金额
     */
    @TableField(value = "red_packet_amount")
    private Long redPacketAmount;
    /**
     * 优惠券金额
     */
    @TableField(value = "coupon_amount")
    private Long couponAmount;
    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    @TableField(value = "update_time", update="now()")
    private LocalDateTime updateTime;
    /**
     * 0 否 1 是
     */
    @TableLogic
    @TableField(value = "is_deleted")
    private Boolean deleted;
    /**
     * 承担方 0买家 1卖家
     */
    @TableField(value = "fee_responsible_user")
    private Integer feeResponsibleUser;
    /**
     * 承担方 0买家 1卖家
     */
    @TableField(value = "indemnity_responsible_user")
    private Integer indemnityResponsibleUser;

}
