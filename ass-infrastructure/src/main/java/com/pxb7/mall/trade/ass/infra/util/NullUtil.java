package com.pxb7.mall.trade.ass.infra.util;

import java.util.Collection;
import java.util.Map;

/**
 * 判空工具类
 *
 * <AUTHOR>
 * @since 7/22/24 8:09 PM
 */

public class NullUtil {

    /**
     * 检查对象是否为null。
     *
     * @param obj 需要检查的对象
     * @return 如果对象为null，则返回true；否则返回false。
     */
    public static boolean isNull(Object obj) {
        return obj == null;
    }

    /**
     * 检查对象是否不为null。
     *
     * @param obj 需要检查的对象
     * @return 如果对象不为null，则返回true；否则返回false。
     */
    public static boolean isNotNull(Object obj) {
        return obj != null;
    }

    /**
     * 检查字符串是否为空或空白。
     *
     * @param str 需要检查的字符串
     * @return 如果字符串为空或空白，则返回true；否则返回false。
     */
    public static boolean isBlank(String str) {
        return str == null || str.trim().isEmpty();
    }

    /**
     * 检查字符串是否非空且非空白。
     *
     * @param str 需要检查的字符串
     * @return 如果字符串非空且非空白，则返回true；否则返回false。
     */
    public static boolean isNotBlank(String str) {
        return str != null && !str.trim().isEmpty();
    }

    /**
     * 检查数组是否为空。
     *
     * @param array 需要检查的数组
     * @return 如果数组为空或null，则返回true；否则返回false。
     */
    public static <T> boolean isArrayEmpty(T[] array) {
        return array == null || array.length == 0;
    }

    /**
     * 检查数组是否非空。
     *
     * @param array 需要检查的数组
     * @return 如果数组非空，则返回true；否则返回false。
     */
    public static <T> boolean isArrayNotEmpty(T[] array) {
        return array != null && array.length > 0;
    }

    /**
     * 检查集合是否为空。
     *
     * @param collection 需要检查的集合
     * @return 如果集合为空或null，则返回true；否则返回false。
     */
    public static <T> boolean isCollectionEmpty(Collection<T> collection) {
        return collection == null || collection.isEmpty();
    }

    /**
     * 检查集合是否非空。
     *
     * @param collection 需要检查的集合
     * @return 如果集合非空，则返回true；否则返回false。
     */
    public static <T> boolean isCollectionNotEmpty(Collection<T> collection) {
        return collection != null && !collection.isEmpty();
    }

    /**
     * 检查Map是否为空。
     *
     * @param map 需要检查的Map
     * @return 如果Map为空或null，则返回true；否则返回false。
     */
    public static <K, V> boolean isMapEmpty(Map<K, V> map) {
        return map == null || map.isEmpty();
    }

    /**
     * 检查Map是否非空。
     *
     * @param map 需要检查的Map
     * @return 如果Map非空，则返回true；否则返回false。
     */
    public static <K, V> boolean isMapNotEmpty(Map<K, V> map) {
        return map != null && !map.isEmpty();
    }
}