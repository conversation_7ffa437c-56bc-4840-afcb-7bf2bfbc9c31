package com.pxb7.mall.trade.ass.infra.repository.db;




import java.util.List;
import java.util.Objects;

import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.pxb7.mall.trade.ass.infra.model.ViolatePayRecordReqPO;
import com.pxb7.mall.trade.ass.infra.repository.db.mapper.ViolatePayRecordMapper;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.ViolatePayRecord;

/**
 * 违约支付单表(ViolatePayRecord)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-31 15:07:26
 */
@Slf4j
@Repository
public class ViolatePayRecordDbRepository extends ServiceImpl<ViolatePayRecordMapper, ViolatePayRecord> implements ViolatePayRecordRepository {

       @Override
       public List<ViolatePayRecord> list(ViolatePayRecordReqPO.SearchPO param){
          LambdaQueryWrapper<ViolatePayRecord> queryWrapper = new LambdaQueryWrapper<>();
                   if (StringUtils.isNotBlank(param.getRecordId())){
                      queryWrapper.eq(ViolatePayRecord::getRecordId,param.getRecordId());
                   }
                   if (StringUtils.isNotBlank(param.getViolateId())){
                      queryWrapper.eq(ViolatePayRecord::getViolateId,param.getViolateId());
                   }
                   if (StringUtils.isNotBlank(param.getOrderItemId())){
                      queryWrapper.eq(ViolatePayRecord::getOrderItemId,param.getOrderItemId());
                   }
                   if (StringUtils.isNotBlank(param.getUserId())){
                      queryWrapper.eq(ViolatePayRecord::getUserId,param.getUserId());
                   }
                   if ( Objects.nonNull(param.getType())){
                      queryWrapper.eq(ViolatePayRecord::getType,param.getType());
                   }
                   if ( Objects.nonNull(param.getChannel())){
                      queryWrapper.eq(ViolatePayRecord::getChannel,param.getChannel());
                   }
                   if ( Objects.nonNull(param.getAmount())){
                      queryWrapper.eq(ViolatePayRecord::getAmount,param.getAmount());
                   }
                   if (StringUtils.isNotBlank(param.getOutTradeNo())){
                      queryWrapper.eq(ViolatePayRecord::getOutTradeNo,param.getOutTradeNo());
                   }
                   if (StringUtils.isNotBlank(param.getRemark())){
                      queryWrapper.eq(ViolatePayRecord::getRemark,param.getRemark());
                   }
                   if ( Objects.nonNull(param.getStatus())){
                      queryWrapper.eq(ViolatePayRecord::getStatus,param.getStatus());
                   }
                   if ( Objects.nonNull(param.getCompletedTime())){
                      queryWrapper.eq(ViolatePayRecord::getCompletedTime,param.getCompletedTime());
                   }
                   if ( Objects.nonNull(param.getCancelTime())){
                      queryWrapper.eq(ViolatePayRecord::getCancelTime,param.getCancelTime());
                   }
                   if( Objects.nonNull(param.getStatusList())){
                       queryWrapper.in(ViolatePayRecord::getStatus, param.getStatusList());
                   }
           return  this.list(queryWrapper);
       }
       


    @Override
    public ViolatePayRecord find(ViolatePayRecordReqPO.SearchPO param) {
        LambdaQueryChainWrapper<ViolatePayRecord> queryWrapper = this.lambdaQuery();
        if (StringUtils.isNotBlank(param.getRecordId())){
            queryWrapper.eq(ViolatePayRecord::getRecordId,param.getRecordId());
        }
        if (StringUtils.isNotBlank(param.getViolateId())){
            queryWrapper.eq(ViolatePayRecord::getViolateId,param.getViolateId());
        }
        if (StringUtils.isNotBlank(param.getOrderItemId())){
            queryWrapper.eq(ViolatePayRecord::getOrderItemId,param.getOrderItemId());
        }
        if (StringUtils.isNotBlank(param.getUserId())){
            queryWrapper.eq(ViolatePayRecord::getUserId,param.getUserId());
        }
        if ( Objects.nonNull(param.getType())){
            queryWrapper.eq(ViolatePayRecord::getType,param.getType());
        }
        if ( Objects.nonNull(param.getChannel())){
            queryWrapper.eq(ViolatePayRecord::getChannel,param.getChannel());
        }
        if ( Objects.nonNull(param.getAmount())){
            queryWrapper.eq(ViolatePayRecord::getAmount,param.getAmount());
        }
        if (StringUtils.isNotBlank(param.getOutTradeNo())){
            queryWrapper.eq(ViolatePayRecord::getOutTradeNo,param.getOutTradeNo());
        }
        if (StringUtils.isNotBlank(param.getRemark())){
            queryWrapper.eq(ViolatePayRecord::getRemark,param.getRemark());
        }
        if ( Objects.nonNull(param.getStatus())){
            queryWrapper.eq(ViolatePayRecord::getStatus,param.getStatus());
        }
        if ( Objects.nonNull(param.getCompletedTime())){
            queryWrapper.eq(ViolatePayRecord::getCompletedTime,param.getCompletedTime());
        }
        if ( Objects.nonNull(param.getCancelTime())){
            queryWrapper.eq(ViolatePayRecord::getCancelTime,param.getCancelTime());
        }
        if( Objects.nonNull(param.getStatusList())){
            queryWrapper.in(ViolatePayRecord::getStatus, param.getStatusList());
        }
        return queryWrapper.last("limit 1").one();
    }
}
