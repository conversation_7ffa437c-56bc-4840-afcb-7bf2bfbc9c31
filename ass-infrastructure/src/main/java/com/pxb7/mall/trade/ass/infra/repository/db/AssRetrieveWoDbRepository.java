package com.pxb7.mall.trade.ass.infra.repository.db;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.pxb7.mall.trade.ass.client.enums.AssRetrieveDealResult;
import com.pxb7.mall.trade.ass.infra.constant.AfcConstant;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pxb7.mall.trade.ass.client.enums.AssStatus;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.AssRetrieveWo;
import com.pxb7.mall.trade.ass.infra.repository.db.mapper.AssRetrieveWoMapper;

import java.time.LocalDateTime;

/**
 * 售后找回工单
 *
 * <AUTHOR>
 * @since: 2024-08-13 16:48
 **/
@Service
public class AssRetrieveWoDbRepository extends ServiceImpl<AssRetrieveWoMapper, AssRetrieveWo>
    implements AssRetrieveWoRepository {
    @Override
    public Boolean setDealUser(String assRetrieveId, String dealUserId) {
        if (StringUtils.isBlank(assRetrieveId) || StringUtils.isBlank(dealUserId)) {
            return Boolean.FALSE;
        }
        LambdaUpdateWrapper<AssRetrieveWo> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(AssRetrieveWo::getAssRetrieveId, assRetrieveId);
        updateWrapper.set(AssRetrieveWo::getDealUserId, dealUserId);
        updateWrapper.set(AssRetrieveWo::getStatus, AssStatus.PROCESSING.getCode());
        return this.update(updateWrapper);
    }

    @Override
    public Boolean closeRetrieveWo(String assRetrieveId) {
        if (StringUtils.isBlank(assRetrieveId)) {
            return Boolean.FALSE;
        }
        LambdaUpdateWrapper<AssRetrieveWo> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(AssRetrieveWo::getAssRetrieveId, assRetrieveId);
        updateWrapper.set(AssRetrieveWo::getStatus, AssStatus.CLOSE.getCode());
        return this.update(updateWrapper);
    }

    @Override
    public Boolean userCancel(String assRetrieveId) {
        if (StringUtils.isBlank(assRetrieveId)) {
            return Boolean.FALSE;
        }
        LambdaUpdateWrapper<AssRetrieveWo> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(AssRetrieveWo::getAssRetrieveId, assRetrieveId);
        updateWrapper.set(AssRetrieveWo::getStatus, AssStatus.FINISH.getCode());
        updateWrapper.set(AssRetrieveWo::getCompleteTime, LocalDateTime.now());
        updateWrapper.set(AssRetrieveWo::getDealResult, AssRetrieveDealResult.NONE.getCode());
        updateWrapper.set(AssRetrieveWo::getClassifyFirst, AfcConstant.ASS_CANCEL);
        updateWrapper.set(AssRetrieveWo::getClassifySecond, AfcConstant.ASS_CANCEL_REASON_SECOND);
        return this.update(updateWrapper);
    }

    @Override
    public AssRetrieveWo getByAssRetrieveId(String assRetrieveId) {
        if (StringUtils.isBlank(assRetrieveId)) {
            return null;
        }
        LambdaQueryWrapper<AssRetrieveWo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AssRetrieveWo::getAssRetrieveId, assRetrieveId);
        return this.getBaseMapper().selectOne(queryWrapper);
    }

}
