package com.pxb7.mall.trade.ass.infra.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ErrorCode {
    REPEAT_OPERATION("302", "重复操作"), RPC_ERROR("00010", "RPC调用失败,数据为空"), INNER_SYSTEM_ERROR("00011", "系统内部调用异常"),

    NO_DATA_MSG("500", "数据不存在或已被删除"), SYSTEM_LIMIT_ERROR("00002", "系统繁忙，请稍后再试"),

    CALL_FAIL("mara500", "rpc调用失败"), QUESTION_NOT_CONFIG("32001", "当前游戏未配置"), PARAM_EMPTY("00015", "参数为空"),
    PARAM_VAL_ERR("00016", "参数取值错误"), ORDER_NOT_EXIST("00017", "查询订单信息失败"), NO_LOGIN("00019", "登录失败，无法访问系统资源"),

    /**
     * 退款错误码
     */
    ORDER_STATUS_FAIL("tra11001", "当前订单状态不允许退款"), ORDER_HAVE_OTHER_REFUND_RECEIPT_FAIL("tra11002", "当前订单有其他退款单正在进行中"),
    SELLER_ACCOUNT_EXPOSE_FAIL("tra11003", "卖家账号信息已经暴露，不允许退款"), SEND_SERVICE_CARD_FAIL("tra11004", "发送客服确认卡片失败，请重新尝试"),
    ORDER_HAVE_OTHER_EXT_PAYMENT_FAIL("tra11005", "当前订单有其他放款单正在进行中"),
    ORDER_HAVE_OTHER_COLLECTION_RECEIPT_FAIL("tra11006", "当前订单有其他收款单正在进行中"),
    DELIVERY_CUSTOMER_NOT_MATCH_ERROR("tra11009", "您不是交付客服，不能操作退款"),
    SEND_BUYER_COLLECT_CARD_ERROR("tra11010", "发送买家信息收集卡片失败"), NO_REFUND_TYPE_WAS_MATCHED_ERROR("tra11011", "未匹配到退款类型"),
    NOT_FOUND_PAY_MODEL_ERROR("tra11012", "未匹配到订单支付模式，请确认订单已完成付款"), REFUND_ERROR("tra11013", "退款异常"),
    REFUND_CHECK_ERROR("tra11013", "退款状态异常"),
    REFUND_PAYMENT_NOT_FOUND_ERROR("tra11014", "未匹配到支付中心收款信息"), REFUND_TYPE_VALID_FAIL_ERROR("tra11015", "退款类型选择校验失败"),
    ORDER_REFUND_REASON_IS_NULL_ERROR("tra11016", "未匹配到退款原因"),
    REFUND_AMOUNT_ERROR("tra11017", "退款金额不合法,如需将剩余号价退完，请选择全额退款"), WHOLE_REFUND_IS_ERROR("tra11018", "退款类型不合法"),
    REFUND_DB_SAVE_ERROR("tra11019", "退款数据保存或更新失败"), REFUND_DB_UPDATE_ERROR("tra11019", "退款数据更新失败"),
    REFUND_NOT_FOUND_ERROR("tra11020", "退款数据不存在"), REFUND_THIRD_FAIL("tra11021", "退款三方请求失败"),
    PAY_CHANNEL_NOT_EXIST_ERROR("tra11022", "不支持该付款方式"), SIGN_ERROR("tra11023", "加签失败"),
    RECEIPT_VOUCHER_NOT_FOUND("tra11024", "找不到当前订单成功的收款单"), AMOUNT_INVALID_FAIL("tra11025", "当前订单退款金额不足"),
    REFUND_BUYER_ACCOUNT_EXIST("tra11026", "退款单买家账号信息已提交"), REFUND_BUYER_CANCEL_ERROR("tra11027", "该退款单状态不允许取消退款"),
    ORDER_FEE_NOT_FOUND_ERROR("tra11028", "该退款单order_item_fee不存在"),
    ORDER_ITEM_NOT_FOUND_ERROR("tra11029", "该退款单order_item不存在"),
    ORDER_ITEM_EXTEND_NOT_FOUND_ERROR("tra11030", "该退款单order_item_extend不存在"),
    REFUND_AUDIT_STATUS_ERROR("tra11031", "退款单审核状态异常"), SERVICE_ROOM_NOT_FOUND_ERROR("tra11032", "im客服群不存在,无法发起退款，请重试"),
    RECHARGE_SELLER_STATUS_ERROR("tra11033", "充值订单非待发货状态，无法申请退款"),
    REFUND_PRODUCT_TYPE_ERROR("tra11034", "退款商品类型非法，客服无法操作退款"), REFUND_TIME_LIMIT_ERROR("tra11035", "下单6小时后可以申请退款"),
    REFUND_VOUCHER_ORDER_ITEM_ERROR("tra11036", "订单号与退款单号不匹配，无法审核退款，请刷新后重试"),
    ORDER_EXPIRE_REFUND_FAIL("tra11037", "当前订单已过在线退款有效期，请走线下退款"),

    EXIST_PROCESSING_TRAD("tra11038", "存在进行中的收退放单据"),

    REFUND_CANCEL_FAIL("tra11039", "退款单取消失败"),

    ASS_SCHEDULE_CREATE_ROOM("afc30001", "获取客服失败"), ASS_SCHEDULE_CREATE_EXITS("afc30002", "售后处理中请勿重复操作"),
    ES_UPDATE_COMPLAINT_ERROR("afc30003", "操作%s 数据ES失败"), ES_NOT_EXISTS_COMPLAINT_WORK("afc30004", "不存在客诉工单"),
    COMPLAINT_NOT_CONFIG_DEPARTMENT("afc30005", "问题部门未配置"), COMPLAINT_NOT_CONFIG_EMPLOYEE("afc30006", "工单处理人员未配置"),
    COMPLAINT_WORK_ORDER_ALREADY_FINISH("afc30007", "工单已完结"),
    COMPLAINT_WO_CANNOT_TRANSFERRED_ONESELF("afc30008", "工单无法转交给自己"),
    COMPLAINT_WO_CANNOT_TRANSFERRED_OTHER("afc30009", "工单无法转交给他人"), TRANSFEREE_NOT_EXISTS("afc30010", "转交人不存在"),
    COMPLAINT_WORK_LOG_NOT_EXISTS("afc30011", "客诉工单日志记录不存在"), GET_USER_BASE_INFO_ERROR("afc30012", "获取用户基础信息失败"),
    RPC_GET_SYS_USER_INFO_ERROR("afc30013", "获取系统用户信息失败"), NO_ORDER("afc30014", "未查询到订单"),
    PULL_CUSTOMER_ERROR("afc30015", "排班分流获取客服失败,请去管理后台配置"), ROOM_NOT_EXISTS("afc30016", "交付房间不存在"),
    ORDER_NOT_DEAL("afc30017", "未查询到群聊中的已成交订单信息"), DEAL_WORK_ORDER_ERROR("afc30018", "处理工单失败"),
    WORD_ORDER_NOT_EXIST("afc30019", "工单不存在"), EXIST_CLAIM_INFO("afc30020", "当前工单存在未完结的赔付单,不允许完结"),
    DISPUTE_ORDER_FINISH_ERROR("afc30021", "当前工单已完结，不允许完结"), GROUP_ORDER_NOT_EXIST("afc30022", "未查询到群聊中的订单信息"),
    ORDER_NO_DEAL("afc30023", "订单未完结--无法申请售后"), WORKORDER_NOT_EXIST("afc30024", "工单不存在"),
    USER_PULL_CUSTOMER_ERROR("afc30025", "获取客服失败"), REPEAT_SUBMIT("afc30026", "重复提交,请稍后再试"),
    ASS_EXIST("afc30027", "重复申请售后"), PHONE_NOT_EXISTS("afc30028", "请输入正确的手机号"),
    GET_USER_PHONE_STATUS_ERROR("afc30029", "核验手机号注册状态失败"), TS_REFUND_CHANNEL_SYS_ERROR("refund500", "退款渠道系统异常"),
    REFUND_NEED_RETRY("refund501", "退款需要重试"), CHANNEL_ERROR("refund501", "三方渠道异常"),
    AFC_QUESTION_NOT_EXISTS("afc30037", "售后问题不存在"), RPC_GET_USER_CERT_INFO_ERROR("afc30030", "获取用户实名认证信息失败"),
    RPC_GET_USER_BASE_INFO_ERROR("afc30031", "获取用户基础信息失败"), SAVE_AFC_APPLY_RECORD_ERROR("afc30032", "保存售后申请记录失败"),
    TEXT_CENSOR_NO_PASS("afc30033", "文本审核不通过"), PLEASE_ENTER_OTHER_SPECIFIC_QUESTION("afc30034", "请输入其他问题"),
    OTHER_QUESTION_TOO_LONG("afc30035", "其他问题不能超过50个字"), LINK_URL_ERROR("afc30036", "链接错误"),
    COMPLAINT_WO_CREATE_ERROR("afc30037", "创建客诉工单失败"),
    COMPLAINT_WO_FINISHED_ERROR("afc30038", "完结工单失败"), ANSWER_EMPTY("afc30039", "答案不能为空"),
    /**
     * 违约金
     */
    VIOLATE_NOT_WHOLE_ERROR("vio40001", "非整单退,不可申请违约金"), VIOLATE_AMOUNT_EXCESS_ERROR("vio40002", "违约金超出最大金额"),
    VIOLATE_PARAM_LACK_ERROR("vio40003", "违约金参数缺失,请补全"), VIOLATE_PROMISE_AMOUNT_ERROR("vio40004", "守约金不可大于总违约金"),
    VIOLATE_REFUND_STATUS_ERROR("vio40005", "整单退款成功后才可操作违约金"), VIOLATE_CREATE_ERROR("vio40006", "创建违约单失败"),
    VIOLATE_UPDATE_STATUS_ILLEGAL_ERROR("vio40007", "违约金状态非收款失败,不可编辑"),
    VIOLATE_STATUS_ILLEGAL_ERROR("vio40008", "违约金状态有误"), VIOLATE_RECEIPT_STOP_ERROR("vio40009", "收款中断时间暂未到"),
    VIOLATE_UPDATE_REPEAT_DEDUCTION_ERROR("vio40010", "违约金已存在扣款单"),
    VIOLATE_AMOUNT_ONE_ERROR("vio40011", "总违约金必须>=守约金额"), VIOLATE_AMOUNT_TWO_ERROR("vio40012", "总违约金必须<剩余可退金额"),
    VIOLATE_UPDATE_AUTH_ERROR("vio40013", "您不是交付客服,不可操作"), VIOLATE_UPDATE_STATUS_ERROR("vio40014", "更新违约单状态失败"),
    VIOLATE_UPDATE_T_STATUS_ERROR("vio40015", "更新违约单打款状态失败"), VIOLATE_CREATE_PAY_RECORD_ERROR("vio40016", "创建违约支付单失败"),
    VIOLATE_REPEAT_PAY_RECORD_ERROR("vio40017", "已存在重复单据"), VIOLATE_CANCEL_PAY_RECORD_ERROR("vio40018", "取消违约支付单失败"),
    VIOLATE_UPDATE_PAY_RECORD_ERROR("vio40019", "更新违约支付单状态失败"), VIOLATE_TRANSFER_CALLBACK_ERROR("vio40020", "打款回调更新失败"),
    VIOLATE_WALLET_REQ_ERROR("vio40021", "钱包扣款请求失败,等待重试"), VIOLATE_WALLET_TRADE_REQ_ERROR("vio40022", "钱包扣款请求失败,等待重试"),
    VIOLATE_WALLET_WAIT_ERROR("vio40023", "钱包扣款处理中,等待重试"),
    VIOLATE_EXIST_ERROR("vio40024", "已有违约金,请勿重复申请"),
    VIOLATE_IN_PROCESS_ERROR("vio40025", "存在未处理完成的违约金,请勿重复申请"),
    VIOLATE_HAND_CREATE_ERROR("vio40026", "创建违约收款失败,请稍后再试"),
    VIOLATE_HAND_DEAL_ERROR("vio40027", "处理违约收款失败,请稍后再试"),
    VIOLATE_RECEIPT_UPDATE_ORDER_ERROR("vio40028", "违约收款失败,更新订单状态失败"),
    VIOLATE_EDIT_ERROR("vio40029", "编辑失败,请稍后再试"),

    // pay dubbo error
    PAY_GATEWAY_WALLET_QUERY_ERROR("viop50000", "dubbo请求钱包查询扣款结果失败"),
    PAY_GATEWAY_WALLET_TRADE_ERROR("viop50001", "dubbo请求钱包扣款失败"),

    INDEMNITY_CHANGE_QUERY_FAIL("indemnity00001", "包赔变更查询异常"),

    ASS_CANCEL_UN_VALID("afc60001", "当前售后状态不支持取消"),
    ASS_HAVE_OTHER_FAIL("afc60002", "当前订单存在其它售后行为"),
    ASS_USER_GET_FAIL("afc60003", "当前用户获取失败")

    ;

    private final String errCode;
    private final String errDesc;

}
