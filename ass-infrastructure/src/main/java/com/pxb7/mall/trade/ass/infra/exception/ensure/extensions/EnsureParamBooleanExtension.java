package com.pxb7.mall.trade.ass.infra.exception.ensure.extensions;

import com.pxb7.mall.trade.ass.infra.enums.ErrorCode;
import com.pxb7.mall.trade.ass.infra.exception.ExceptionFactory;

import org.apache.commons.lang3.BooleanUtils;

/**
 * 断言工具类
 *
 * <AUTHOR>
 * @date 2024/08/07 15:08
 **/
public class EnsureParamBooleanExtension extends EnsureParamObjectExtension<Boolean> {

    private final Boolean condition;

    public EnsureParamBooleanExtension(Boolean condition) {
        super(condition);
        this.condition = condition;
    }

    /**
     * null 值不是false 如果断定 为空或false @see isNotTrue
     */
    public EnsureParamBooleanExtension isFalse(ErrorCode errorCode) {
        if (BooleanUtils.isTrue(condition)) {
            throw ExceptionFactory.create(errorCode.getErrCode(), errorCode.getErrDesc());
        } else {
            return this;
        }
    }

    public EnsureParamBooleanExtension isTrue(ErrorCode errorCode) {
        if (BooleanUtils.isFalse(condition)) {
            throw ExceptionFactory.create(errorCode.getErrCode(), errorCode.getErrDesc());
        } else {
            return this;
        }
    }

}
