package com.pxb7.mall.trade.ass.infra.model;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 金钱类
 * 只支持小数点后两位(元为单位)
 */
@Getter
@Setter
@Accessors(chain = true)
@ToString
public class Money implements Serializable {

    /**
     * 单位为分
     */
    private long value;

    public Money() {

    }

    public Money(Long fen) {
        this.value = fen;
    }

    public Money(String yuan) {
        this.value = new BigDecimal(yuan).movePointRight(2).longValue();
    }

    public void setValue(long value) {
        this.value = value;
    }

    public long getFen() {
        return value;
    }

    public String getYuan() {
        return new BigDecimal(value).movePointLeft(2).toPlainString();
    }

    public boolean isEmpty() {
        return value == 0;
    }
}
