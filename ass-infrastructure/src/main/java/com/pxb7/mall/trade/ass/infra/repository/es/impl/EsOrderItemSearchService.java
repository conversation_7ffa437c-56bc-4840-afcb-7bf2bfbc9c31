package com.pxb7.mall.trade.ass.infra.repository.es.impl;

import java.util.List;

import org.springframework.data.elasticsearch.core.SearchHit;

import com.pxb7.mall.trade.ass.infra.repository.es.entity.OrderItemAggregation;

public interface EsOrderItemSearchService {
    OrderItemAggregation findOneByRoomId(String roomId);

    List<SearchHit<OrderItemAggregation>> orderItemSearchByOrderItemIds(List<String> orderItemIds);
}
