package com.pxb7.mall.trade.ass.infra.repository.db.mapper;

import com.pxb7.mall.trade.ass.infra.repository.db.entity.AssWorkOrderLog;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 售后工单日志明细(AssWorkOrderLog)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-07-31 10:19:33
 */
@Mapper
public interface AssWorkOrderLogMapper extends BaseMapper<AssWorkOrderLog> {
    /**
     * 批量新增数据
     *
     * @param entities List<AssWorkOrderLog> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<AssWorkOrderLog> entities);

}

