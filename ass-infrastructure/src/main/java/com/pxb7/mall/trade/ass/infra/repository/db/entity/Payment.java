package com.pxb7.mall.trade.ass.infra.repository.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.pxb7.mall.trade.order.client.enums.pay.TradeStatusEnum;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 交易中心表(Payment)实体类
 *
 * <AUTHOR>
 * @since 2024-09-03 13:43:22
 */
@Data
@Accessors(chain = true)
@TableName(value = "payment")
public class Payment implements Serializable {
    private static final long serialVersionUID = -92653270791349043L;
    /**
     * 主键
     */
    @TableField(value = "id")
    private Long id;
    /**
     * 业务主键, 内部交易凭证
     */
    @TableId(value = "payment_id", type = IdType.INPUT)
    private String paymentId;

    /**
     * 订单id
     */
    @TableField(value = "order_id")
    private String orderId;
    /**
     * 订单行id
     */
    @TableField(value = "order_item_id")
    private String orderItemId;
    /**
     * 单据id: 根据不同业务存饭：收款单ID、保证金ID、售后单ID
     */
    @TableField(value = "voucher_id")
    private String voucherId;
    /**
     * 订单域单据业务类型 关联: 1 退款 2放款 3赔付 4收
     * @see com.pxb7.mall.trade.order.client.enums.pay.VoucherTypeEnum
     */
    @TableField(value = "voucher_type")
    private Integer voucherType;
    /**
     * 支付模式: 1线上支付 2线下支付 3线上原路退款
     * @see com.pxb7.mall.trade.order.client.enums.pay.TradeModeEnum
     */
    @TableField(value = "trade_mode")
    private Integer tradeMode;
    /**
     * 支付域业务id
     */
    @TableField(value = "business_id")
    private String businessId;
    /**
     * 支付域业务类型 关联枚举: 支付/原路退/打款/挂账
     */
    @TableField(value = "business_type")
    private Integer businessType;
    /**
     * 支付用户ID
     */
    @TableField(value = "pay_user_id")
    private String payUserId;
    /**
     * 外部交易凭证
     */
    @TableField(value = "out_trade_no")
    private String outTradeNo;
    /**
     * 交易金额
     */
    @TableField(value = "trade_amount")
    private Long tradeAmount;
    /**
     * 实际金额, 下游回填的值
     */
    @TableField(value = "actual_amount")
    private Long actualAmount;
    /**
     * 交易状态: 1待执行 2执行中 3执行成功 4执行失败 5取消执行
     * @see TradeStatusEnum
     */
    @TableField(value = "trade_status")
    private Integer tradeStatus;
    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    @TableField(value = "update_time", update="now()")
    private LocalDateTime updateTime;
    /**
     * 逻辑删除，0:删除，1:正常
     */
    @TableField(value = "is_deleted")
    @TableLogic
    private Boolean deleted;


}

