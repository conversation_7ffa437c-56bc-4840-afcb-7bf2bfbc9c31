package com.pxb7.mall.trade.ass.infra.remote.dubbo;

import com.alibaba.cola.dto.SingleResponse;
import com.pxb7.mall.pay.client.api.PayAccountTradeInfoService;
import com.pxb7.mall.pay.client.api.PayTradeService;
import com.pxb7.mall.pay.client.dto.model.AccountTradeExtDTO;
import com.pxb7.mall.pay.client.dto.model.PayAccountTradeDTO;
import com.pxb7.mall.pay.client.dto.response.AccountTradeInfoRespDTO;
import com.pxb7.mall.pay.client.dto.response.PayAccountTradeResp;
import com.pxb7.mall.trade.ass.infra.util.DubboResultAssert;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import static com.pxb7.mall.trade.ass.infra.enums.ErrorCode.PAY_GATEWAY_WALLET_QUERY_ERROR;
import static com.pxb7.mall.trade.ass.infra.enums.ErrorCode.PAY_GATEWAY_WALLET_TRADE_ERROR;

@Component
@Slf4j
public class PayServiceGateway {

    @DubboReference
    private PayAccountTradeInfoService payAccountTradeInfoServiceI;
    @DubboReference
    private PayTradeService payTradeServiceI;

    // 查询钱包扣款
    public SingleResponse<AccountTradeInfoRespDTO> queryWalletDeduction(String voucherId, String userId) {
        SingleResponse<AccountTradeInfoRespDTO> response =
                DubboResultAssert.wrapExceptionV2(() -> payAccountTradeInfoServiceI.queryTradeInfo(voucherId, userId)
                        , PAY_GATEWAY_WALLET_QUERY_ERROR.getErrCode()
                        , PAY_GATEWAY_WALLET_QUERY_ERROR.getErrDesc());
        log.info("[PayServiceGateway.queryWalletDeduction] 入参:voucherId:{},userId:{};response:{}", voucherId, userId, response);
        return response;
    }

    // 请求钱包扣款
    public PayAccountTradeResp walletDeduction(String orderId, String voucherId, String userId, Long amount, Integer type, AccountTradeExtDTO tradeExt) {
        PayAccountTradeDTO tradeDTO = new PayAccountTradeDTO();
        tradeDTO.setOrderId(orderId);
        tradeDTO.setVoucherId(voucherId);
        tradeDTO.setAmount(amount);
        tradeDTO.setUserId(userId);
        tradeDTO.setTradeType(type);
        tradeDTO.setTradeExt(tradeExt);
        SingleResponse<PayAccountTradeResp> response = DubboResultAssert.wrapException(() -> payTradeServiceI.trade(tradeDTO)
                , PAY_GATEWAY_WALLET_TRADE_ERROR.getErrCode()
                , PAY_GATEWAY_WALLET_TRADE_ERROR.getErrDesc());

        log.info("[PayServiceGateway.walletDeduction] 入参:voucherId:{},userId:{};response:{}", voucherId, userId, response);

        if (response.isSuccess()) {
            return response.getData();
        }

        return null;

    }
}
