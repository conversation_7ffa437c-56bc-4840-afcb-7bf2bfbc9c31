package com.pxb7.mall.trade.ass.infra.constant;

/**
 * <AUTHOR>
 * @date 2024/9/27 2:17 PM
 */
public interface OrderIndexFieldConstant {

    // 索引名称
    String indexName = "order_item_aggregation";


    /**
     * 创建时间
     */
    String create_time = "create_time";

    /**
     * 买家ID
     */
    String buyer_id = "buyer_id";
    /**
     * 卖家ID
     */
    String seller_id = "seller_id";

    // 手机号： 可能是卖家、可能是买家
    String phone = "phone";
    /**
     * 买家手机号
     */
    String buyer_phone = "buyer_phone";
    /**
     * “
     * 卖家手机号
     */
    String seller_phone = "seller_phone";

    /**
     * 买家状态
     */
    String buyer_status = "buyer_status";

    /**
     * 卖家状态
     */
    String seller_status = "seller_status";

    /**
     * 商品名称
     */
    String product_name = "product_name";

    /**
     * 子订单ID
     */
    String order_item_id = "order_item_id";

    /**
     * 外部渠道订单ID
     */
    String out_order_id = "out_order_id";

    /**
     * 1.0订单ID
     */
    String old_order_item_id = "old_order_item_id";
    /**
     * 订单状态
     */
    String order_item_status = "order_item_status";
    /**
     * 商品ID
     */
    String product_id = "product_id";

    /**
     * 商品编号
     */
    String product_unique_no = "product_unique_no";
    /**
     * 商品类型
     */
    String product_type = "product_type";

    /**
     * 游戏ID
     */
    String game_id = "game_id";
    String game_name = "game_name";
    String game_name_keyword = "game_name.keyword";

    String game_account ="game_account";

    /**
     * 交付房间ID
     */
    String delivery_room_id = "delivery_room_id";

    /**
     * 交付房间ID
     */
    String room_id = "room_id";


    /**
     * 充值卖家状态
     */
    String recharge_seller_status = "recharge_seller_status";


    /**
     * 商品成交价
     */
    String product_sale_price = "product_sale_price";

    /**
     * 成交时间
     */
    String complete_time = "complete_time";

    /**
     * 订单来源
     */
    String order_source = "order_source";


    String ass_apply_time = "ass_apply_time";


    String buyer_merchant_id = "buyer_merchant_id";

}
