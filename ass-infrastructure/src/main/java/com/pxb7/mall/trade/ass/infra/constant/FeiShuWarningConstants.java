package com.pxb7.mall.trade.ass.infra.constant;

import lombok.Getter;

/**
 * 飞书预警消息模板
 */
@Getter
public class FeiShuWarningConstants {

    /**
     * 发起退款失败 预警文案模板
     */
    public final static String REFUND_ERROR_MESSAGE_TEMPLATE="发起退款异常：\n\n退款渠道：%s\n平台请求单号：%s\ntraceID：%s\n异常描述：%s";
    /**
     * 发起退款失败 预警文案模板V2
     */
    public final static String REFUND_ERROR_MESSAGE_TEMPLATE_V2="发起退款异常V2：\n\n退款渠道：%s\n平台请求单号：%s\ntraceID：%s\n异常描述：%s";

    /**
     * 退款查询失败 预警文案模板
     */
    public final static String REFUND_QUERY_ERROR_MESSAGE_TEMPLATE="退款查询异常：\n\nrefundLogId：%s\n异常描述：最后一次退款查询后依然处于退款中";

}
