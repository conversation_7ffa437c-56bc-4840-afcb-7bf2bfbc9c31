package com.pxb7.mall.trade.ass.infra.repository.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 售后问题配置(AfcQuestionConf)实体类
 *
 * <AUTHOR>
 * @since 2025-04-11 13:54:03
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "afc_question_conf")
public class AfcQuestionConf implements Serializable {
    private static final long serialVersionUID = 368045862549297787L;
    /**
     * 自增id
     */
    @TableField(value = "id")
    private Long id;
    /**
     * 问题id
     */
    @TableId(value = "question_id", type = IdType.INPUT)
    private String questionId;
    /**
     * 问题名称
     */
    @TableField(value = "question_name")
    private String questionName;
    /**
     * 问题类型 1找回 2纠纷
     */
    @TableField(value = "question_type")
    private Integer questionType;
    /**
     * 排序值
     */
    @TableField(value = "sort")
    private Integer sort;
    /**
     * 创建人ID
     */
    @TableField(value = "create_user_id")
    private String createUserId;
    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;
    /**
     * 更新人id
     */
    @TableField(value = "update_user_id")
    private String updateUserId;
    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;
    /**
     * 是否删除 1:已删除 0:未删除
     */
    @TableLogic
    @TableField(value = "is_deleted")
    private Boolean deleted;

}

