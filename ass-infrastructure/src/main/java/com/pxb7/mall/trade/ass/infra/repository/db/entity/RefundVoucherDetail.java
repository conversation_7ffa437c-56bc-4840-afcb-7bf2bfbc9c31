package com.pxb7.mall.trade.ass.infra.repository.db.entity;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.*;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 退款单金额详情(RefundVoucherDetail)实体类
 *
 * <AUTHOR>
 * @since 2024-09-25 16:35:53
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
@TableName(value = "refund_voucher_detail")
public class RefundVoucherDetail implements Serializable {
    private static final long serialVersionUID = -98388090315979430L;
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 退款单业务主键
     */
    @TableField(value = "refund_voucher_id")
    private String refundVoucherId;
    /**
     * 订单行id
     */
    @TableField(value = "order_item_id")
    private String orderItemId;
    /**
     * 商品号价退款金额
     */
    @TableField(value = "product_amount")
    private Long productAmount;
    /**
     * 包赔金额
     */
    @TableField(value = "indemnity_amount")
    private Long indemnityAmount;
    /**
     * 手续费金额
     */
    @TableField(value = "fee_amount")
    private Long feeAmount;
    /**
     * 买家违约金额
     */
    @TableField(value = "violate_amount")
    private Long violateAmount;
    /**
     * 红包
     */
    @TableField(value = "red_packet_amount")
    private Long redPacketAmount;
    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    @TableField(value = "update_time", update="now()")
    private LocalDateTime updateTime;
    /**
     * 是否删除
     */
    @TableLogic
    @TableField(value = "is_deleted")
    private Boolean deleted;
    /**
     * 包赔承担方 0买家 1卖家
     */
    @TableField(value = "indemnity_responsible_user")
    private Integer indemnityResponsibleUser;

    /**
     * 包赔承担方 0买家 1卖家
     */
    @TableField(value = "fee_responsible_user")
    private Integer feeResponsibleUser;



    /**
     * 退款--包赔信息列表的 json串
     * List<RefundIndemnityJsonBO> -> json string
     *
     */
    @TableField(value = "extra_param")
    private String extraPram;

}
