package com.pxb7.mall.trade.ass.infra.repository.db.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.ReceiptVoucher;

/**
 * 收款单(ReceiptVoucher)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-09-25 16:16:36
 */
@Mapper
public interface ReceiptVoucherMapper extends BaseMapper<ReceiptVoucher> {
    /**
     * 批量新增数据
     *
     * @param entities List<ReceiptVoucher> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<ReceiptVoucher> entities);

}
