package com.pxb7.mall.trade.ass.infra.repository.es.entity;


import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.Document;

import java.time.LocalDateTime;

/**
 * 订单交付节点信息（无查询需求）
 */

@Data
@Document(indexName = "main_order_aggregation")
public class DeliverNodeDoc {

    /**
     * 订单id
     */
    private String orderId;

    /**
     * 订单行id
     */
    private String orderItemId;

    /**
     * 订单交付节点id
     */
    @Id
    private String orderDeliverNodeId;

    /**
     * 交付节点
     */
    private String node;

    /**
     * 节点排序
     */
    private Integer sortIndex;

    /**
     * 节点时间
     */
    private LocalDateTime nodeTime;
}
