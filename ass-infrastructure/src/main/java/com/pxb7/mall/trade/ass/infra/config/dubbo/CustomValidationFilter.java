package com.pxb7.mall.trade.ass.infra.config.dubbo;

import java.util.List;
import java.util.Set;

import org.apache.dubbo.common.constants.CommonConstants;
import org.apache.dubbo.common.constants.FilterConstants;
import org.apache.dubbo.common.extension.Activate;
import org.apache.dubbo.common.utils.ConfigUtils;
import org.apache.dubbo.rpc.*;
import org.apache.dubbo.validation.Validation;
import org.apache.dubbo.validation.Validator;

import com.alibaba.cola.exception.BizException;
import com.google.common.collect.Lists;

import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import jakarta.validation.ValidationException;
import lombok.extern.slf4j.Slf4j;

/**
 * dubbo参数校验统一拦截
 */
@Slf4j
@Activate(group = {CommonConstants.PROVIDER}, value = FilterConstants.VALIDATION_KEY, order = 100)
public class CustomValidationFilter implements Filter {

    private static final String METHOD_PREFIX = "$";

    private Validation validation;

    /**
     * Sets the validation instance for ValidationFilter
     *
     * @param validation Validation instance injected by dubbo framework based on "validation" attribute value.
     */
    public void setValidation(Validation validation) {
        this.validation = validation;
    }

    /**
     * Perform the validation of before invoking the actual method based on <b>validation</b> attribute value.
     *
     * @param invoker service
     * @param invocation invocation.
     * @return Method invocation result
     * @throws RpcException Throws RpcException if validation failed or any other runtime exception occurred.
     */
    @Override
    public Result invoke(Invoker<?> invoker, Invocation invocation) throws RpcException {
        if (validation != null && !invocation.getMethodName().startsWith(METHOD_PREFIX) && ConfigUtils.isNotEmpty(
            invoker.getUrl().getMethodParameter(invocation.getMethodName(), FilterConstants.VALIDATION_KEY))) {
            try {
                Validator validator = validation.getValidator(invoker.getUrl());
                if (validator != null) {
                    validator.validate(invocation.getMethodName(), invocation.getParameterTypes(),
                        invocation.getArguments());
                }
            } catch (RpcException e) {
                throw e;
            } catch (ConstraintViolationException e) {
                // 添加catch ConstraintViolationException用于实现自定义的参数校验异常处理逻辑
                Set<ConstraintViolation<?>> constraintViolations = e.getConstraintViolations();
                // 可能有多个参数校验不通过，一次全部返回
                List<String> messages = Lists.newArrayList();
                for (ConstraintViolation<?> firsConstraintViolation : constraintViolations) {
                    messages.add(firsConstraintViolation.getMessage());
                }
                return AsyncRpcResult.newDefaultAsyncResult(new BizException("tra5001", String.join("; ", messages)),
                    invocation);
            } catch (ValidationException e) {
                return AsyncRpcResult.newDefaultAsyncResult(new ValidationException(e.getMessage()), invocation);
            } catch (Throwable t) {
                return AsyncRpcResult.newDefaultAsyncResult(t, invocation);
            }
        }
        return invoker.invoke(invocation);
    }

}
