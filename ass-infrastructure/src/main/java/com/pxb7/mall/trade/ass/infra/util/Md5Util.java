package com.pxb7.mall.trade.ass.infra.util;

import org.apache.commons.lang3.StringUtils;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * 用于泰山支付加签验签
 */
public class Md5Util {

    /**
     * md5加签
     * @return
     */
    public static String digest(String input){
        // 获取MD5 MessageDigest实例
        MessageDigest md;
        try {
            md = MessageDigest.getInstance("MD5");
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        }

        // 执行哈希计算
        byte[] messageDigest = md.digest(input.getBytes());

        // 将字节数组转换为十六进制字符串
        StringBuilder hexString = new StringBuilder();
        for (byte b : messageDigest) {
            String hex = Integer.toHexString(0xff & b);
            if (hex.length() == 1) {
                hexString.append('0');
            }
            hexString.append(hex);
        }
        return hexString.toString().toUpperCase();
    }


    /**
     *
     * @param md5Key md5Key
     * @param timeStamp 时间戳
     * @param signature 泰山返回的签名
     * @return
     */
    public static Boolean tspaySign(String md5Key,String timeStamp,String signature){
        String input = md5Key + ":" + timeStamp;
        String digest = digest(input);
        return StringUtils.isNotBlank(signature) && StringUtils.isNotBlank(input) && signature.equals(digest);
    }




}
