package com.pxb7.mall.trade.ass.infra.remote.service.http;

import com.alibaba.cola.dto.DTO;
import com.alibaba.cola.exception.BizException;
import com.alibaba.fastjson.JSON;
import com.jd.open.api.sdk.DefaultJdClient;
import com.jd.open.api.sdk.domain.jdxcx.MiniAppOrderDetailsRpcService.response.orderInfo.OrderStatusInfo;
import com.jd.open.api.sdk.domain.jdxcx.MiniAppTransactionClient.response.refundQuery.QueryRefundResult;
import com.jd.open.api.sdk.internal.util.JsonUtil;
import com.jd.open.api.sdk.request.AbstractRequest;
import com.jd.open.api.sdk.request.JdRequest;
import com.jd.open.api.sdk.request.jdxcx.MiniappOrderCancelRequest;
import com.jd.open.api.sdk.request.jdxcx.MiniappOrderOrderInfoRequest;
import com.jd.open.api.sdk.request.jdxcx.MiniappRefundQueryRequest;
import com.jd.open.api.sdk.request.jdxcx.MiniappRefundRequest;
import com.jd.open.api.sdk.response.AbstractResponse;
import com.jd.open.api.sdk.response.jdxcx.MiniappOrderCancelResponse;
import com.jd.open.api.sdk.response.jdxcx.MiniappOrderCreatedResponse;
import com.jd.open.api.sdk.response.jdxcx.MiniappOrderOrderInfoResponse;
import com.jd.open.api.sdk.response.jdxcx.MiniappRefundQueryResponse;
import com.jd.open.api.sdk.response.jdxcx.MiniappRefundResponse;
import com.pxb7.mall.trade.ass.infra.remote.model.refund.JdRefundPO;
import com.pxb7.mall.trade.order.client.dto.ErrorCode;
import com.pxb7.mall.trade.order.client.enums.order.OrderItemStatusEnum;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2025/4/21
 */
@Service
@Slf4j
@Data
public class JdApiService {

    private static final String SERVER_URL = "https://api.jd.com/routerjson";

    private static final String TEST_JD_SKU_ID = "10151187469076";

    @Value("${channel.jd.appKey}")
    private String appKey;

    @Value("${channel.jd.appSecret}")
    private String appSecret;

    /**
     * 查询订单详情
     *
     * @param jdOrderId 京东订单编号
     * @param xidBuyer 京东用户编号
     */
    public JdOrder getJdOrder(Long jdOrderId, String xidBuyer) {
        MiniappOrderOrderInfoRequest request = new MiniappOrderOrderInfoRequest();
        request.setXidBuyer(xidBuyer);
        request.setOrderId(jdOrderId);
        MiniappOrderOrderInfoResponse response = this.execute(request);
        if (response != null && response.getJsfResponseResult() != null &&
                response.getJsfResponseResult().getOrderStatusInfo() != null) {
            return new JdOrder(response.getJsfResponseResult().getOrderStatusInfo());
        }
        log.error("京东订单查询异常, request:[{}], response:[{}]", request, response);
        throw new BizException("京东订单查询异常");
    }

    /**
     * 查询部分退款详情
     *
     * @param jdOrderId      京东订单编号
     * @param refundUuid 退款唯一标识
     */
    public JdRefund getRefund(Long jdOrderId, String refundUuid) throws Exception {
        MiniappRefundQueryRequest request = new MiniappRefundQueryRequest();
        request.setOrderId(jdOrderId);
        request.setRefundUuid(refundUuid);
        MiniappRefundQueryResponse response = this.execute(request);
        if (response != null && response.getReturnType() != null &&
                response.getReturnType().getSuccess() != null) {
            return new JdRefund(response.getReturnType().getSuccess());
        }
        log.error("京东退款查询异常, request:[{}], response:[{}]", request, response);
        throw new BizException("京东退款查询异常");
    }

    /**
     * 部分退款
     * @param refundRequest 退款请求对象
     */
    public Boolean refund(JdRefundPO refundRequest) throws Exception {
        MiniappRefundRequest request = new MiniappRefundRequest();
        request.setXidBuyer(refundRequest.getJdBuyerId());
        request.setOrderId(refundRequest.getJdOrderId());
        request.setIsHalfRefund(refundRequest.getPartRefund());
        request.setRefundUuid(refundRequest.getRefundUuid());
        request.setRefundAmount(refundRequest.getRefundAmount());
        MiniappRefundResponse response = this.execute(request);
        log.info("发起京东部分退款, request:[{}], response:[{}]",
            JSON.toJSONString(request), JSON.toJSONString(response));
        if (response != null && response.getReturnType().getSuccess() != null && response.getReturnType().getSuccess()) {
            return true;
        }
        throw new BizException("京东部分退款发起失败");
    }

    public <T extends AbstractResponse> T execute(JdRequest<T> request) {
        T response = null;
        try {
            DefaultJdClient client = new DefaultJdClient(SERVER_URL, null, this.getAppKey(), this.getAppSecret());
            response = client.execute(request);
            return response;
        } catch (Exception e) {
            log.error("京东接口调用发生异常, request:[{" + JSON.toJSONString(request) + "}], response:[{" + JSON.toJSONString(response) + "}]", e);
            throw new BizException(ErrorCode.SYSTEM_ERROR.getErrCode(), ErrorCode.SYSTEM_ERROR.getErrDesc());
        }
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class JdOrder extends DTO {
        private Long orderId;
        private JdOrderStatusEnum orderStatus;
        private Long amount;
        private Long refundAmount;

        public JdOrder(OrderStatusInfo o) {
            this.orderId = o.getOrderId();
            this.orderStatus = JdOrderStatusEnum.get(o.getOrderStatus());
            this.amount = o.getAmount();
            this.refundAmount = o.getRefundAmount();
        }
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    @ToString
    public static class JdRefund extends DTO {
        private Long orderId;
        private LocalDateTime refundTime;
        private LocalDateTime created;
        private JdRefundStatusEnum refundStatus;
        private String jdRefundMemo;
        private Long refundMoney;

        public JdRefund(QueryRefundResult o) {
            this.orderId = o.getOrderId();
            this.refundTime = this.parse(o.getRefundTime());
            this.created = this.parse(o.getCreated());
            this.refundStatus = JdRefundStatusEnum.getByCode(o.getRefundStatus());
            this.jdRefundMemo = o.getJdRefundMemo();
            this.refundMoney = o.getRefundMoney().getCent();
        }

        private LocalDateTime parse(Date date) {
            if (date == null) {
                return null;
            }
            return date.toInstant()
                    .atZone(ZoneId.systemDefault())
                    .toLocalDateTime();
        }
    }

    @AllArgsConstructor
    @Getter
    public enum JdRefundStatusEnum {

        REFUND_WAIT(1, "等待退款", "用户已发起退款，等待商家处理"),
        REFUND_REVIEW(2, "退款审核中", "商家正在审核退款申请"),
        REFUND_SUCCESS(3, "退款成功", "退款已完成并到账"),
        REFUND_REJECTED(4, "退款被驳回", "商家拒绝退款申请");

        private final Integer code;
        private final String name;
        private final String description;

        private static final Map<Integer, JdRefundStatusEnum> MAPPING = new HashMap<>();

        static {
            for (JdRefundStatusEnum status : JdRefundStatusEnum.values()) {
                MAPPING.put(status.getCode(), status);
            }
        }

        public static JdRefundStatusEnum getByCode(Integer code) {
            if (code == null) {
                throw new IllegalArgumentException("退款状态码不可为空");
            }
            JdRefundStatusEnum status = MAPPING.get(code);
            if (status == null) {
                throw new IllegalArgumentException("未知的退款状态码: " + code);
            }
            return status;
        }
    }


    @AllArgsConstructor
    @Getter
    public enum JdOrderStatusEnum {

        WAIT_TO_PAY(1, "等待支付", "用户提交订单，未支付", OrderItemStatusEnum.WAIT_PAY),
        PAY_SUCCESS(64, "已完成", "订单完成", OrderItemStatusEnum.DEALING),
        CANCEL(256, "已取消", "用户取消订单，或未支付超时取消", OrderItemStatusEnum.DEAL_CANCEL),
        WAIT_TO_REFUND(8256, "支付成功，退款处理中", "支付成功后，发起退款", OrderItemStatusEnum.DEALING),
        REFUND_FAIL(16448, "支付成功，退款异常", "退款处理异常，中间态，最终会被置为完成", OrderItemStatusEnum.DEALING),
        REFUND_SUCCESS(32832, "支付成功，退款成功", "退款成功", OrderItemStatusEnum.REFUND_CANCEL),
        ;
        private final Integer code;
        private final String name;
        private final String desc;
        private final OrderItemStatusEnum orderItemStatus;

        public static JdOrderStatusEnum get(Integer code) {
            if (code == null) {
                throw new IllegalArgumentException("code is null");
            }
            for (JdOrderStatusEnum value : JdOrderStatusEnum.values()) {
                if (value.getCode().equals(code)) {
                    return value;
                }
            }
            throw new IllegalArgumentException("code is undefined, code:" + code);
        }
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class MiniappOrderSubmitMultiSkuOrderRequest extends AbstractRequest implements JdRequest<MiniappOrderCreatedResponse> {

        private Long amount;
        private Long quantity;
        private Long sku;
        private String xidBuyer;
        private String callBackUrl;
        private String trackerId;
        private Long totalFee;

        public String getApiMethod() {
            return "jingdong.submitMultiSkuOrder";
        }

        public String getAppJsonParams() throws IOException {
            Map<String, Object> pmap = new TreeMap<>();
            pmap.put("amount", this.amount);
            pmap.put("quantity", this.quantity);
            pmap.put("sku", this.sku);
            pmap.put("xid_buyer", this.xidBuyer);
            pmap.put("callBackUrl", this.callBackUrl);
            pmap.put("trackerId", this.trackerId);
            pmap.put("totalFee", this.totalFee);
            return JsonUtil.toJson(pmap);
        }

        public Class<MiniappOrderCreatedResponse> getResponseClass() {
            return MiniappOrderCreatedResponse.class;
        }
    }

}
