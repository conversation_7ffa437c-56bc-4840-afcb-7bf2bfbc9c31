package com.pxb7.mall.trade.ass.infra.repository.es.entity;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.DateFormat;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 子订单-优惠表(OrderItemPromotionDoc)
 *
 * <AUTHOR>
 * @since 2024-09-03 15:24:30
 */

@Data
@Document(indexName = "order_item_promotion_doc")
public class OrderItemPromotionDoc implements Serializable {

    /**
     * 订单行id
     */
    @Field(type = FieldType.Keyword, value = "order_item_id")
    private String orderItemId;
    /**
     * 1：商品优惠券 2：包赔优惠券 3：红包
     */
    @Field(value = "promotion_type")
    private Integer promotionType;
    /**
     * 优惠金额
     */
    @Field(value = "promotion_amount")
    private Long promotionAmount;
    /**
     * 券/红包id
     */
    @Id
    @Field(type = FieldType.Keyword, value = "promotion_id")
    private String promotionId;
    /**
     * 优惠归属 1平台 2商家
     */
    @Field(value = "promotion_belong")
    private Short promotionBelong;
    /**
     * 创建时间
     */
    @Field(type = FieldType.Date,format = DateFormat.date_hour_minute_second_fraction,value = "create_time")
    private LocalDateTime createTime;
}
