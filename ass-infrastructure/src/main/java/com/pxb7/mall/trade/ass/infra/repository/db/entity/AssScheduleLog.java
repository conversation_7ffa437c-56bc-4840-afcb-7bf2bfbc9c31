package com.pxb7.mall.trade.ass.infra.repository.db.entity;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.*;
import com.pxb7.mall.trade.ass.client.enums.AssScheduleNode;
import com.pxb7.mall.trade.ass.client.enums.AssScheduleSourceTypeEnums;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 售后流程进度日志(AssScheduleLog)实体类
 *
 * <AUTHOR>
 * @since 2024-08-12 10:59:47
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
@TableName(value = "ass_schedule_log")
public class AssScheduleLog implements Serializable {

    @Serial
    private static final long serialVersionUID = -34462354060685370L;

    /**
     * 自增id
     */
    @TableField(value = "id")
    private Long id;

    /**
     * 主键id
     */
    @TableId(value = "schedule_log_id", type = IdType.INPUT)
    private String scheduleLogId;

    /**
     * 售后流程进度id
     */
    @TableField(value = "schedule_id")
    private String scheduleId;

    /**
     * 其他数据
     */
    @TableField(value = "data")
    private String data;

    /**
     * 节点id
     * 
     * @see AssScheduleNode
     */
    @TableField(value = "node_id")
    private String nodeId;

    /**
     * 当前节点状态描述
     */
    @TableField(value = "node_desc")
    private String nodeDesc;

    /**
     * 是否展示
     */
    @TableField(value = "is_show")
    private Boolean isShow;

    /**
     * 来源1:c端用户 2:客服 3:admin用户
     *
     * @see AssScheduleSourceTypeEnums
     */
    @TableField(value = "source_type")
    private Integer sourceType;

    /**
     * 创建用户ID
     */
    @TableField(value = "create_user_id")
    private String createUserId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    @TableField(value = "update_user_id")
    private String updateUserId;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", update="now()")
    private LocalDateTime updateTime;

    /**
     * 删除标记
     */
    @TableLogic
    @TableField(value = "is_deleted")
    private Boolean deleted;

}
