package com.pxb7.mall.trade.ass.infra.util;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.cola.exception.BizException;
import com.pxb7.mall.trade.ass.infra.enums.ErrorCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.springframework.core.env.Environment;

import java.util.Arrays;
import java.util.Objects;
import java.util.function.Supplier;

@Slf4j
public class DubboResultAssert {

    private static final Environment environment = SpringUtils.getBean(Environment.class);

    private static final String DEV_NAME = "dev";

    private static void assertSuccess(Response result, String errorCode, String errorMsg) {
        if (result == null) {
            log.error("dubbo 调用获取结果为空, {}", errorMsg);
            throw new BizException(ErrorCode.RPC_ERROR.getErrCode(), ErrorCode.RPC_ERROR.getErrDesc());
        }

        if (!result.isSuccess()) {
            log.error("dubbo 调用发生异常, {}", errorMsg);
            throw new BizException(result.getErrCode(), result.getErrMessage());
        }

        if (result instanceof SingleResponse) {
            SingleResponse res = (SingleResponse)result;
            if (Objects.isNull(res.getData())) {
                throw new BizException(errorCode, errorMsg + " 返回结果是空" + devErrorMsg());
            }
        }

        if (result instanceof MultiResponse) {
            MultiResponse res = (MultiResponse)result;
            if (CollectionUtils.isEmpty(res.getData())) {
                throw new BizException(errorCode, errorMsg + " 返回结果是空" + devErrorMsg());
            }
        }

    }

    public static <T extends Response> T wrapException(Supplier<T> supplier, String errorCode, String errorMsg) {
        try {
            T res = supplier.get();// 可能抛dubbo 异常
            assertSuccess(res, errorCode, errorMsg);
            return res;
        } catch (BizException e) {
            throw new BizException(e.getErrCode(), e.getMessage());
        } catch (Exception e) {
            // dubbo 抛异常了
            log.error(errorMsg, e);
            throw new BizException(errorCode, errorMsg + devErrorMsg());
        }
    }

    public static <T extends Response> T wrapException(Supplier<T> supplier, ErrorCode errorCode) {
        try {
            T res = supplier.get();// 可能抛dubbo 异常
            assertSuccess(res, errorCode.getErrCode(), errorCode.getErrDesc());
            return res;
        } catch (BizException e) {
            throw new BizException(e.getErrCode(), e.getMessage());
        } catch (Exception e) {
            // dubbo 抛异常了
            log.error(errorCode.getErrDesc(), e);
            throw new BizException(String.valueOf(errorCode), errorCode.getErrDesc() + devErrorMsg());
        }
    }


    private static void assertSuccess(Response result) {
        if (result == null) {
            log.error("dubbo 调用获取结果为空");
            throw new BizException(ErrorCode.RPC_ERROR.getErrCode(), ErrorCode.RPC_ERROR.getErrDesc());
        }

        if (!result.isSuccess()) {
            log.error("dubbo 调用发生异常");
            throw new BizException(result.getErrCode(), result.getErrMessage());
        }

    }

    public static <T extends Response> T wrapExceptionV2(Supplier<T> supplier, String errorCode, String errorMsg) {
        try {
            T res = supplier.get();// 可能抛dubbo 异常
            assertSuccess(res);
            return res;
        } catch (BizException e) {
            throw new BizException(e.getErrCode(), e.getMessage());
        } catch (Exception e) {
            // dubbo 抛异常了
            log.error(errorMsg, e);
            throw new BizException(errorCode, errorMsg + devErrorMsg());
        }
    }

    public static String devErrorMsg() {
        return isDevActive() ? ", 请联系对应业务域!" : "";
    }

    public static boolean isDevActive() {
        return Arrays.asList(environment.getActiveProfiles()).contains(DEV_NAME);
    }
}
