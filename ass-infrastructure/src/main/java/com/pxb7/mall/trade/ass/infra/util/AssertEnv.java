package com.pxb7.mall.trade.ass.infra.util;

import org.springframework.core.env.Environment;

import java.util.Arrays;

public class AssertEnv {

    private static final String DEV_NAME = "dev";
    private static final String PROD_NAME = "prod";

    private static final String TEST2_NAME = "test2";

    private static final Environment environment = SpringUtils.getBean(Environment.class);

    public static boolean isDevActive() {
        return Arrays.asList(environment.getActiveProfiles()).contains(DEV_NAME);
    }

    public static boolean isTest2Active() {
        return Arrays.asList(environment.getActiveProfiles()).contains(TEST2_NAME);
    }

    public static boolean isProdActive() {
        return Arrays.asList(environment.getActiveProfiles()).contains(PROD_NAME);
    }

    public static boolean isCustomActive(String envName) {
        return Arrays.asList(environment.getActiveProfiles()).contains(envName);
    }


}
