package com.pxb7.mall.trade.ass.infra.config.nacos;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <p>功能描述:售后订单同步任务开关 为true时是开启，为false时是关闭</p>
 * 作者：杨学玺
 * 创建日期：2025/08/12
 * 公司名称：金华博淳网络科技有限公司
 * 域名：www.pxb7.com
 */
@Slf4j
@Component
@Data
public class AssWorkOrderInitConfig implements NacosYamlConfigListener {

    /**
     * 数据同步开关
     */
    private boolean initSwitch = false;

    @Override
    public void onRefresh(Object newConfig) {
        if (Objects.isNull(newConfig)) {
            this.initSwitch = false;
            return;
        }

        this.initSwitch = (boolean) newConfig;
    }

    @Override
    public String configPath() {
        return "order_init_switch";
    }
}
