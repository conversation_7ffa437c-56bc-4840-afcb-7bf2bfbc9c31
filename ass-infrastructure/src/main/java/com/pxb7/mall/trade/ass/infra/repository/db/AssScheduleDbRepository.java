package com.pxb7.mall.trade.ass.infra.repository.db;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

import cn.hutool.core.util.ObjectUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pxb7.mall.trade.ass.client.dto.request.AssStatusReqDTO;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.AssSchedule;
import com.pxb7.mall.trade.ass.infra.repository.db.mapper.AssScheduleMapper;

import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 售后流程进度(AssSchedule)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-08-12 10:59:09
 */
@Slf4j
@Repository
public class AssScheduleDbRepository extends ServiceImpl<AssScheduleMapper, AssSchedule>
    implements AssScheduleRepository {

    @Override
    public AssSchedule getByRoomId(String roomId) {
        if (StringUtils.isBlank(roomId)) {
            return null;
        }
        LambdaQueryWrapper<AssSchedule> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AssSchedule::getRoomId, roomId);
        queryWrapper.orderByDesc(AssSchedule::getId);
        queryWrapper.last("limit 1");
        return this.getOne(queryWrapper);
    }

    @Override
    public boolean updateWorkOrderIdAndAssTypeAndFinishById(Long id, String workOrderId, Integer assType,
        Boolean finish, String updateUserId) {
        if (Objects.isNull(id) || StringUtils.isBlank(workOrderId) || Objects.isNull(assType) || Objects.isNull(finish)
            || StringUtils.isBlank(updateUserId)) {
            return false;
        }
        return baseMapper.updateWorkOrderIdAndAssTypeAndFinishById(id, workOrderId, assType, finish, updateUserId);
    }

    @Override
    public boolean updateFinishById(Long id, Boolean finish, String updateUserId) {
        if (Objects.isNull(id) || Objects.isNull(finish) || StringUtils.isBlank(updateUserId)) {
            return false;
        }
        return baseMapper.updateFinishById(id, finish, updateUserId);
    }

    @Override
    public AssSchedule findOneByOrderItemIdAndRoomId(String orderItemId, String roomId) {
        if (StringUtils.isBlank(orderItemId) || StringUtils.isBlank(roomId)) {
            return null;
        }
        return this.lambdaQuery().eq(AssSchedule::getOrderItemId, orderItemId).eq(AssSchedule::getRoomId, roomId)
            .orderByDesc(AssSchedule::getId).last("limit 1").one();
    }

    @Override
    public List<AssSchedule> findListByOrderItemIdAndRoomId(List<AssStatusReqDTO> list) {
        if (CollUtil.isEmpty(list)) {
            return CollUtil.newArrayList();
        }
        return baseMapper.findListByOrderItemIdAndRoomId(list);
    }

    @Override
    public List<AssSchedule> listNoFinishAndMaxId(Long minId, Long maxId) {
        return baseMapper.listNoFinishAndMaxId(minId, maxId);
    }
}
