package com.pxb7.mall.trade.ass.infra.repository.db;

import java.util.Objects;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.AssQuestionOptionRelation;
import com.pxb7.mall.trade.ass.infra.repository.db.mapper.AssQuestionOptionRelationMapper;

/**
 * 售后问答问题游戏关联
 *
 * <AUTHOR>
 * @since: 2024-08-13 16:03
 **/
@Service
public class AssQuestionOptionRelationDbRepository
    extends ServiceImpl<AssQuestionOptionRelationMapper, AssQuestionOptionRelation>
    implements AssQuestionOptionRelationRepository {
    @Override
    public String getOptionId(String gameId) {
        if (StringUtils.isBlank(gameId)) {
            return null;
        }
        LambdaQueryWrapper<AssQuestionOptionRelation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AssQuestionOptionRelation::getGameId, gameId);
        AssQuestionOptionRelation relation = this.getOne(queryWrapper);
        if (Objects.isNull(relation)) {
            return null;
        }
        return relation.getOptionId();
    }
}
