package com.pxb7.mall.trade.ass.infra.repository.db;

import java.util.List;

import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.ReceiptVoucherDetail;
import com.pxb7.mall.trade.ass.infra.repository.db.mapper.ReceiptVoucherDetailMapper;

import lombok.extern.slf4j.Slf4j;

/**
 * 收款单-资金明细(ReceiptVoucherDetail)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-09-25 16:16:37
 */
@Slf4j
@Repository
public class ReceiptVoucherDetailDbRepository extends ServiceImpl<ReceiptVoucherDetailMapper, ReceiptVoucherDetail>
    implements ReceiptVoucherDetailRepository {

    @Override
    public List<ReceiptVoucherDetail> getListByOrderItemId(String orderItemId) {
        return this.lambdaQuery().eq(ReceiptVoucherDetail::getOrderItemId, orderItemId).list();
    }
}
