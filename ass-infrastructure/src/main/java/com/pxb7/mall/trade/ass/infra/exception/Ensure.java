package com.pxb7.mall.trade.ass.infra.exception;

import com.pxb7.mall.trade.ass.infra.exception.ensure.extensions.EnsureParamBooleanExtension;
import com.pxb7.mall.trade.ass.infra.exception.ensure.extensions.EnsureParamCollectionExtension;
import com.pxb7.mall.trade.ass.infra.exception.ensure.extensions.EnsureParamIntegerExtension;
import com.pxb7.mall.trade.ass.infra.exception.ensure.extensions.EnsureParamObjectExtension;
import com.pxb7.mall.trade.ass.infra.exception.ensure.extensions.EnsureParamStringExtension;

import java.util.Collection;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * 断言工具类
 *
 * <AUTHOR>
 * @date 2024/08/07 15:03
 **/
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class Ensure {

    public static EnsureParamObjectExtension<Object> that(Object obj) {
        return new EnsureParamObjectExtension<>(obj);
    }

    public static EnsureParamBooleanExtension that(Boolean obj) {
        return new EnsureParamBooleanExtension(obj);
    }

    public static EnsureParamStringExtension that(String str) {
        return new EnsureParamStringExtension(str);
    }

    public static EnsureParamCollectionExtension that(Collection<?> collection) {
        return new EnsureParamCollectionExtension(collection);
    }

    public static EnsureParamIntegerExtension that(Integer value) {
        return new EnsureParamIntegerExtension(value);
    }

}
