package com.pxb7.mall.trade.ass.infra.model;

import lombok.Data;

@Data
public class RefundIndemnityDetailPO {

    /**
     * 订单包赔id
     */
    private String orderItemIndemnityId;

    /**
     * 包赔实付金额
     */
    private Long indemnityRealAmount;

    /**
     * 包赔号商折扣金额
     */
    private Long indemnityMerchantCoupon;

    /**
     * 包赔原价
     */
    private Long indemnityAmount;

    /**
     * 承担方 0买家 1卖家
     */
    private Integer responsibleUser;
}
