package com.pxb7.mall.trade.ass.infra.repository.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.OrderItemPromotion;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 订单行 -优惠表(OrderItemPromotion)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-07-22 20:59:37
 */
@Mapper
public interface OrderItemPromotionMapper extends BaseMapper<OrderItemPromotion> {

    /**
     * 获取订单行上不同类型的优惠券金额
     * 
     * @param orderItemId 订单行
     * @param couponBelong 优惠券归属
     * @return 优惠券金额
     */
//    Long getOrderItemPromotionTotal(@Param("orderItemId") String orderItemId,
//        @Param("couponBelong") Integer couponBelong);

}
