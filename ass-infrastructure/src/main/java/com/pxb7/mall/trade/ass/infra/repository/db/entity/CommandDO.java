package com.pxb7.mall.trade.ass.infra.repository.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 指令表
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "trade_command")
@ToString
public class CommandDO implements Serializable {

    @Serial
    private static final long serialVersionUID = 6827694945415884476L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 指令ID
     */
    @TableField(value = "command_id")
    private String commandId;

    /**
     * 业务单据ID
     */
    @TableField(value = "biz_id")
    private String bizId;

    /**
     * 指令类型：代发结果 ...
     */
    @TableField(value = "command_type")
    private String commandType;

    /**
     * 指令数据内容
     */
    @TableField(value = "command_content")
    private String commandContent;

    /**
     * 指令状态 0:未处理 1:已处理
     */
    @TableField(value = "command_status")
    private Integer commandStatus;

    /**
     * 失败次数
     */
    @TableField(value = "fail_count")
    private Integer failCount;


    @TableField(value = "remark")
    private String remark;

    /**
     * 环境
     */
    @TableField(value = "env")
    private String env;

    /**
     * 执行时间
     */
    @TableField(value = "execute_time")
    private LocalDateTime executeTime;

    /**
     * 创建用户id
     */
    @TableField(value = "create_user_id")
    private String createUserId;
    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;
    /**
     * 更新用户id
     */
    @TableField(value = "update_user_id")
    private String updateUserId;
    /**
     * 更新时间
     */
    @TableField(value = "update_time", update = "now()")
    private LocalDateTime updateTime;
    /**
     * 删除标记
     */
    @TableLogic
    @TableField(value = "is_deleted")
    private Boolean deleted;
}
