package com.pxb7.mall.trade.ass.infra.model;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;


/**
 * (RefundViolateRelation)实体类
 *
 * <AUTHOR>
 * @since 2025-03-31 17:44:30
 */
public class RefundViolateRelationReqPO {
     
    @Getter
    @Setter
    @Accessors(chain = true)
    public static class AddPO  {
    
       
         private String  refundVoucherId;

       
         private String  orderItemId;

       
         private Integer  violateUserType;

       
         private Long  violateAmout;

       
         private Long  promiseAmount;

       
         private Boolean  status;

    }

    @Getter
    @Setter
    @Accessors(chain = true)
    public static class UpdatePO {
     
      
         private Integer  id;
         
     
      
         private String  refundVoucherId;
         
     
      
         private String  orderItemId;
         
     
      
         private Integer  violateUserType;
     
      
         private Long  violateAmout;
         
     
      
         private Long  promiseAmount;
         
     
      
         private Boolean  status;
         
     
    }


    @Getter
    @Setter
    @Accessors(chain = true)
    public  static class  DelPO{
        private  Integer  id;
    }

    @Getter
    @Setter
    @Accessors(chain = true)
    public static class  SearchPO{
       
         private String  refundVoucherId;

       
         private String  orderItemId;

       
         private Integer  violateUserType;
       
         private Long  violateAmout;

       
         private Long  promiseAmount;

       
         private Boolean  status;

    }

    @Getter
    @Setter
    @Accessors(chain = true)
    public static class PagePO{
    
       
         private String  refundVoucherId;

       
         private String  orderItemId;

       
         private Integer  violateUserType;
       
         private Long  violateAmout;

       
         private Long  promiseAmount;

       
         private Boolean  status;


        /**
         * 页码，从1开始
         */
        private Integer pageIndex;
        /**
         * 每页数量
         */
        private Integer pageSize;

    }

}

