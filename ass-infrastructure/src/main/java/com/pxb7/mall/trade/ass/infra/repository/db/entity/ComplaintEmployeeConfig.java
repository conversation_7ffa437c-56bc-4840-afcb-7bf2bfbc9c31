package com.pxb7.mall.trade.ass.infra.repository.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 工单处理人员信息维护配置(ComplaintEmployeeConfig)实体类
 *
 * <AUTHOR>
 * @since 2024-09-20 16:45:28
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
@TableName(value = "complaint_employee_config")
public class ComplaintEmployeeConfig implements Serializable {
    private static final long serialVersionUID = 128067352259585671L;
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 工单处理人员信息配置id
     */
    @TableField(value = "complaint_employee_id")
    private String complaintEmployeeId;
    /**
     * 问题部门配置id
     */
    @TableField(value = "complaint_department_id")
    private String complaintDepartmentId;
    /**
     * 手机号
     */
    @TableField(value = "mobile")
    private String mobile;
    /**
     * 后台用户ID
     */
    @TableField(value = "user_id")
    private String userId;
    /**
     * 账号(员工姓名)
     */
    @TableField(value = "user_name")
    private String userName;
    /**
     * 创建用户id
     */
    @TableField(value = "create_user_id")
    private String createUserId;
    /**
     * 更新用户id
     */
    @TableField(value = "update_user_id")
    private String updateUserId;
    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    @TableField(value = "update_time", update="now()")
    private LocalDateTime updateTime;
    /**
     * 是否删除
     */
    @TableLogic
    @TableField(value = "is_deleted")
    private Boolean deleted;

}

