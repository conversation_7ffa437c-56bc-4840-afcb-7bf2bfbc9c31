package com.pxb7.mall.trade.ass.infra.remote.dubbo;


import com.alibaba.cola.dto.SingleResponse;
import com.pxb7.mall.merchant.client.api.MerchantAccountServiceI;
import com.pxb7.mall.merchant.client.dto.response.account.MerchantAccountIdentityRespDTO;
import com.pxb7.mall.trade.ass.infra.util.DubboResultAssert;
import com.pxb7.mall.trade.order.client.dto.ErrorCode;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

/**
 * <p>功能描述:</p>
 * 作者：xuexiyang
 * 创建日期：2025/08/07
 * 公司名称：金华博淳网络科技有限公司
 * 域名： www.pxb7.com
 */
@Component
public class MerchantAccountGateway {


    @DubboReference
    private MerchantAccountServiceI merchantAccountService;

    /**
     * 查询号商信息 以及子账号信息
     *
     * @param merchantUserId
     * @return
     */
    public MerchantAccountIdentityRespDTO queryMerchantAccountIdentity(String merchantUserId) {
        if (StringUtils.isBlank(merchantUserId)) {
            return null;
        }
        // 查询商户身份信息
        SingleResponse<MerchantAccountIdentityRespDTO> merchantAccountIdentity =
                DubboResultAssert.wrapException(() -> merchantAccountService.queryMerchantAccountIdentity(merchantUserId),
                        ErrorCode.CONNECT_DUBBO_FAIL.getErrCode(), "查询号商用户信息异常");

        return merchantAccountIdentity.getData();
    }



}
