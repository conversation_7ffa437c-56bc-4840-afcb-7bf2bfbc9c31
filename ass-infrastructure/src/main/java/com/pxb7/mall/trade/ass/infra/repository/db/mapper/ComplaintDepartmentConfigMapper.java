package com.pxb7.mall.trade.ass.infra.repository.db.mapper;

import com.pxb7.mall.trade.ass.infra.repository.db.entity.ComplaintDepartmentConfig;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 问题部门-问题类型配置(ComplaintDepartmentConfig)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-09-20 16:45:27
 */
@Mapper
public interface ComplaintDepartmentConfigMapper extends BaseMapper<ComplaintDepartmentConfig> {
    /**
     * 批量新增数据
     *
     * @param entities List<ComplaintDepartmentConfig> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<ComplaintDepartmentConfig> entities);

}

