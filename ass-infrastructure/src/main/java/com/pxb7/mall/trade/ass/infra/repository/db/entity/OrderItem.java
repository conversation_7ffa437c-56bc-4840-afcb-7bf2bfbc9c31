package com.pxb7.mall.trade.ass.infra.repository.db.entity;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.*;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 订单行(OrderItem)实体类
 *
 * <AUTHOR>
 * @since 2024-10-24 10:24:10
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
@TableName(value = "order_item")
public class OrderItem implements Serializable {
    private static final long serialVersionUID = 678958508224315241L;
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 主订单id
     */
    @TableField(value = "order_id")
    private String orderId;
    /**
     * 子订单id,业务主键
     */
    @TableField(value = "order_item_id")
    private String orderItemId;
    /**
     * 商品业务主键id
     */
    @TableField(value = "product_id")
    private String productId;
    /**
     * 游戏ID
     */
    @TableField(value = "game_id")
    private String gameId;
    /**
     * 买家id
     */
    @TableField(value = "buyer_id")
    private String buyerId;
    /**
     * 卖家id,诚心卖的卖家是系统
     */
    @TableField(value = "seller_id")
    private String sellerId;
    /**
     * 商品类型: 1账号 2充值 3金币 4装备 5初始号 20诚心卖曝光劵 21诚心卖服务
     */
    @TableField(value = "product_type")
    private Integer productType;
    /**
     * 卖家商品类型: 1账号 2充值 3金币 4装备 5初始号 10金币出售 11 金币回收 20诚心卖曝光劵 21诚心卖服务
     */
    @TableField(value = "seller_product_type")
    private Integer sellerProductType;
    /**
     * 1待付款 2交易中 3待结算 4已成交 5已取消 6退款后取消 (待支付是买家, 其他都是卖家状态)
     */
    @TableField(value = "order_item_status")
    private Integer orderItemStatus;
    /**
     * 买家订单状态: 1待付款 2交易中 3已成交 4已取消 5退款后已取消
     */
    @TableField(value = "buyer_status")
    private Integer buyerStatus;
    /**
     * 卖家订单状态: 0初始值（不能展示在卖家订单列表） 1交易中 2待结算 3已成交 4已取消 5退款后已取消
     */
    @TableField(value = "seller_status")
    private Integer sellerStatus;

    /**
     * 订单行金额（商品售价 + 买家承担的包赔原费用 + 买家承担的手续费原费用）
     */
    @TableField(value = "order_item_amount")
    private Long orderItemAmount;
    /**
     * 订单行应付款金额（商品实付价格+包赔实付费用+买家实际承担手续费）
     */
    @TableField(value = "order_item_pay_amount")
    private Long orderItemPayAmount;
    /**
     * 订单行实付金额
     */
    @TableField(value = "order_item_actual_pay_amount")
    private Long orderItemActualPayAmount;
    /**
     * 商品原价(不包含包赔)
     */
    @TableField(value = "product_original_price")
    private Long productOriginalPrice;
    /**
     * 是否议价: 0否 1是
     */
    @TableField(value = "is_bargain")
    private Boolean bargain;
    /**
     * 商品销售价格(求降价之后的价格)
     */
    @TableField(value = "product_sale_price")
    private Long productSalePrice;
    /**
     * 商品优惠金额, 是号价优惠了多少钱
     */
    @TableField(value = "product_coupon_amount")
    private Long productCouponAmount;
    /**
     * 商品应付价格(实际支付的商品的钱)(是商品不包含包赔)
     */
    @TableField(value = "product_pay_amount")
    private Long productPayAmount;
    /**
     * 购买的商品数量(诚心卖服务订单以天为单位)
     */
    @TableField(value = "product_quantity")
    private Integer productQuantity;
    /**
     * 当前可放款金额
     */
    @TableField(value = "payout_amount")
    private Long payoutAmount;
    /**
     * 最大可放款金额（买家实付-买家退款，包含手续费和包赔的金额）
     */
    @TableField(value = "max_payout_amount")
    private Long maxPayoutAmount;
    /**
     * 订单完结时间
     */
    @TableField(value = "complete_time")
    private LocalDateTime completeTime;
    /**
     * 订单取消时间
     */
    @TableField(value = "cancel_time")
    private LocalDateTime cancelTime;
    /**
     * 最新收款单状态 1待收款 2进行中 3 已收款 4 收款失败
     */
    @TableField(value = "receipt_status")
    private Integer receiptStatus;
    /**
     * 最新退款单状态 1待审核, 2退款中, 3退款成功, 4退款失败, 5退款关闭
     */
    @TableField(value = "refund_status")
    private Integer refundStatus;
    /**
     * 最新放款单状态 1 已制单 2 放款中 3放款成功 4放款失败
     */
    @TableField(value = "payout_status")
    private Integer payoutStatus;
    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    @TableField(value = "update_time", update="now()")
    private LocalDateTime updateTime;
    /**
     * 逻辑删除，0:删除，1:正常
     */
    @TableLogic
    @TableField(value = "is_deleted")
    private Boolean deleted;

}
