package com.pxb7.mall.trade.ass.infra.repository.db;

import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.RefundReason;
import com.pxb7.mall.trade.ass.infra.repository.db.mapper.RefundReasonMapper;

import lombok.extern.slf4j.Slf4j;

/**
 * 退款原因管理(RefundReason)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-09-25 15:02:33
 */
@Slf4j
@Repository
public class RefundReasonDbRepository extends ServiceImpl<RefundReasonMapper, RefundReason>
    implements RefundReasonRepository {
    @Override
    public RefundReason getOneByReasonId(String refundReasonId) {
        return this.lambdaQuery().eq(RefundReason::getReasonRefundId, refundReasonId).one();
    }
}
