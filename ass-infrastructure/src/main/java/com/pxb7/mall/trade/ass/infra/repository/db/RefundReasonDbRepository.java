package com.pxb7.mall.trade.ass.infra.repository.db;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.RefundReason;
import com.pxb7.mall.trade.ass.infra.repository.db.mapper.RefundReasonMapper;

import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Objects;

/**
 * 退款原因管理(RefundReason)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-09-25 15:02:33
 */
@Slf4j
@Repository
public class RefundReasonDbRepository extends ServiceImpl<RefundReasonMapper, RefundReason> implements RefundReasonRepository {

    @Override
    public RefundReason getOneByReasonId(String refundReasonId) {
        return this.lambdaQuery().eq(RefundReason::getReasonRefundId, refundReasonId).one();
    }

    @Override
    public String getReasonContent(String refundReasonId) {
        if (StringUtils.isBlank(refundReasonId)) {
            return "";
        }
        RefundReason reason = getOneByReasonId(refundReasonId);
        return Objects.nonNull( reason) ? reason.getReasonRefundContent() : "";
    }

    @Override
    public List<RefundReason> getListForBuyer() {
        return this.lambdaQuery().in(RefundReason::getReasonRange, List.of(1, 3))
                .eq(RefundReason::getRefundType, 1)
                .list();
    }

    @Override
    public List<RefundReason> getListForCustomer(String reasonRefundId,Integer refundType) {
        return this.lambdaQuery().in(RefundReason::getReasonRange, List.of(2, 3))
                .eq(StringUtils.isNotBlank(reasonRefundId), RefundReason::getPid, reasonRefundId)
                .eq(refundType != null, RefundReason::getRefundType, refundType)
                .list();
    }

    @Override
    public RefundReason getOneByReasonIdIncludeDeleted(String reasonId) {
        if (StringUtils.isBlank(reasonId)) {
            return null;
        }
        return baseMapper.getOneByReasonIdIncludeDeleted(reasonId);
    }

    @Override
    public String getReasonContentIncludeDeleted(String reasonId) {
        if (StringUtils.isBlank(reasonId)) {
            return "";
        }
        RefundReason reason = getOneByReasonIdIncludeDeleted(reasonId);
        return Objects.nonNull( reason) ? reason.getReasonRefundContent() : "";
    }
}
