package com.pxb7.mall.trade.ass.infra.repository.es.entity;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.DateFormat;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 子订单支付记录表(OrderItemPaymentDoc) es
 *
 * <AUTHOR>
 * @since 2024-09-03 15:21:52
 */
@Data
@Document(indexName = "order_item_payment_doc")
public class OrderItemPaymentDoc implements Serializable {

    /**
     * 内部交易凭证
     */
    @Id
    @Field(value = "payment_id")
    private String paymentId;
    /**
     * 主订单id
     */
    @Field(type = FieldType.Keyword, value = "order_id")
    private String orderId;
    /**
     * 订单行id
     */
    @Field(type = FieldType.Keyword, value = "order_item_id")
    private String orderItemId;
    /**
     * 支付域业务类型 关联枚举: 支付/原路退/打款/挂账
     */
    @Field(value = "business_type")
    private Integer businessType;
    /**
     * 收款单业务id
     */
    @Field(type = FieldType.Keyword, value = "receipt_voucher_id")
    private String receiptVoucherId;
    /**
     * 退款单业务id
     */
    @Field(type = FieldType.Keyword, value = "refund_voucher_id")
    private String refundVoucherId;
    /**
     * 放款单业务id
     */
    @Field(type = FieldType.Keyword, value = "payout_voucher_id")
    private String payoutVoucherId;
    /**
     * 处理状态: 1处理中 2处理成功 3处理失败
     */
    @Field(value = "status")
    private Integer status;
    /**
     * 交易金额
     */
    @Field(value = "trade_amount")
    private Long tradeAmount;
    /**
     * 创建时间
     */
    @Field(type = FieldType.Date,format = DateFormat.date_hour_minute_second_fraction,value = "create_time")
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    @Field(type = FieldType.Date,format = DateFormat.date_hour_minute_second_fraction,value = "update_time")
    private LocalDateTime updateTime;

}
