package com.pxb7.mall.trade.ass.infra.repository.db;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pxb7.mall.trade.ass.infra.model.ViolatePayRecordReqPO;
import com.pxb7.mall.trade.ass.infra.repository.db.entity.ViolatePayRecord;

/**
 * 违约支付单表(ViolatePayRecord)表服务接口
 *
 * <AUTHOR>
 * @since 2025-03-31 15:07:26
 */
public interface ViolatePayRecordRepository extends IService<ViolatePayRecord> {

    List<ViolatePayRecord> list(ViolatePayRecordReqPO.SearchPO param);

    ViolatePayRecord find(ViolatePayRecordReqPO.SearchPO param);


}

