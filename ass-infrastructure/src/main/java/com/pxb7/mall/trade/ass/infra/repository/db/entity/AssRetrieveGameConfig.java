package com.pxb7.mall.trade.ass.infra.repository.db.entity;

import java.io.Serializable;
import java.util.Date;
import java.time.*;
import java.math.BigDecimal;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import com.baomidou.mybatisplus.annotation.*;

/**
 * 售后找回游戏配置(AssRetrieveGameConfig)实体类
 *
 * <AUTHOR>
 * @since 2025-08-01 14:11:23
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "ass_retrieve_game_config")
@ToString
public class AssRetrieveGameConfig implements Serializable {
    private static final long serialVersionUID = 598199211065347764L;
    /**
     * 自增id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 业务主键
     */
    @TableField(value = "game_config_id")
    private String gameConfigId;
    /**
     * 找回处理时间单位天
     */
    @TableField(value = "retrieve_days")
    private Integer retrieveDays;
    /**
     * 是否开启群通知 1:已开启 0:未开启
     */
    @TableField(value = "is_group_open")
    private Boolean groupOpen;
    /**
     * 创建人id
     */
    @TableField(value = "create_user_id")
    private String createUserId;
    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;
    /**
     * 更新人id
     */
    @TableField(value = "update_user_id")
    private String updateUserId;
    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;
    /**
     * 是否删除 1:已删除 0:未删除
     */
    @TableLogic
    @TableField(value = "is_deleted")
    private Boolean deleted;


}

