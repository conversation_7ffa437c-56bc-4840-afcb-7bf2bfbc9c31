package com.pxb7.mall.trade.ass.infra.model;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.data.elasticsearch.annotations.Field;

@Getter
@Setter
@Accessors(chain = true)
public class RiskAddBlackRecordPO {

    /**
     * 游戏账号
     */
    private String gameAccount;


    /**
     * 违约方手机号 （买家或者买家的手机号）
     */
    private String phone;

    /**
     * 违约方身份证号 （买家或者买家的身份证）
     */
    private String certNo;

    /**
     * 黑号类型
     */
    //private Integer balckType;

    /**
     *  业务凭证id
     */
    private String voucherId;
}
