package com.pxb7.mall.trade.ass.infra.repository.db.entity;

import java.io.Serializable;
import java.util.Date;
import java.time.*;
import java.math.BigDecimal;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import com.baomidou.mybatisplus.annotation.*;

/**
 * 客诉工单操作日志表(ComplaintWorkLog)实体类
 *
 * <AUTHOR>
 * @since 2024-09-20 16:52:42
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
@TableName(value = "complaint_work_log")
public class ComplaintWorkLog implements Serializable {
    private static final long serialVersionUID = -11781296874641127L;
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 客诉工单日志业务ID
     */
    @TableField(value = "complaint_work_log_id")
    private String complaintWorkLogId;
    /**
     * 房间id
     */
    @TableField(value = "room_id")
    private String roomId;
    /**
     * 工单编号
     */
    @TableField(value = "complaint_work_id")
    private String complaintWorkId;
    /**
     * 日志类型（1:创建；2:转交；3:完结）
     */
    @TableField(value = "log_type")
    private Integer logType;
    /**
     * 当前处理人ID
     */
    @TableField(value = "current_processor_id")
    private String currentProcessorId;
    /**
     * 被转交人id(log_type=2时有值)
     */
    @TableField(value = "transferee_id")
    private String transfereeId;
    /**
     * 转交备注
     */
    @TableField(value = "transfer_note")
    private String transferNote;
    /**
     * 转交备注
     */
    @TableField(value = "modification")
    private String modification;
    /**
     * 创建用户id
     */
    @TableField(value = "create_user_id")
    private String createUserId;
    /**
     * 更新用户id
     */
    @TableField(value = "update_user_id")
    private String updateUserId;
    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    @TableField(value = "update_time", update="now()")
    private LocalDateTime updateTime;
    /**
     * 是否删除
     */
    @TableLogic
    @TableField(value = "is_deleted")
    private Boolean deleted;

}

