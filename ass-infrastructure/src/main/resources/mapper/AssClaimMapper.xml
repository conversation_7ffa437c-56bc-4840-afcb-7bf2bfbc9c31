<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pxb7.mall.trade.ass.infra.repository.db.mapper.AssClaimMapper">

    <resultMap id="BaseResultMap" type="com.pxb7.mall.trade.ass.infra.repository.db.entity.AssClaim">
        <result column="id" jdbcType="BIGINT" property="id"/>
        <result column="ass_claim_id" jdbcType="VARCHAR" property="assClaimId"/>
        <result column="claim_no" jdbcType="VARCHAR" property="claimNo"/>
        <result column="claim_amount" jdbcType="BIGINT" property="claimAmount"/>
        <result column="ass_type" jdbcType="INTEGER" property="assType"/>
        <result column="product_id" jdbcType="VARCHAR" property="productId"/>
        <result column="product_unique_no" jdbcType="VARCHAR" property="productUniqueNo"/>
        <result column="order_item_id" jdbcType="VARCHAR" property="orderItemId"/>
        <result column="work_order_id" jdbcType="VARCHAR" property="workOrderId"/>
        <result column="work_order_no" jdbcType="VARCHAR" property="workOrderNo"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="examine_user_id" jdbcType="VARCHAR" property="examineUserId"/>
        <result column="examine_time" jdbcType="TIMESTAMP" property="examineTime"/>
        <result column="examine_reason" jdbcType="VARCHAR" property="examineReason"/>
        <result column="proposer_user_id" jdbcType="VARCHAR" property="proposerUserId"/>
        <result column="proposer_reason" jdbcType="VARCHAR" property="proposerReason"/>
        <result column="account_type" jdbcType="INTEGER" property="accountType"/>
        <result column="bank_code" jdbcType="VARCHAR" property="bankCode"/>
        <result column="payment_channel" jdbcType="INTEGER" property="paymentChannel"/>
        <result column="payer_name" jdbcType="VARCHAR" property="payerName"/>
        <result column="account" jdbcType="VARCHAR" property="account"/>
        <result column="pay_way" jdbcType="INTEGER" property="payWay"/>
        <result column="qr_code" jdbcType="VARCHAR" property="qrCode"/>
        <result column="payment_account_id" jdbcType="VARCHAR" property="paymentAccountId"/>
        <result column="finance_account_id" jdbcType="VARCHAR" property="financeAccountId"/>
        <result column="finance_time" jdbcType="TIMESTAMP" property="financeTime"/>
        <result column="reject_reason" jdbcType="VARCHAR" property="rejectReason"/>
        <result column="complete_time" jdbcType="TIMESTAMP" property="completeTime"/>
        <result column="proof_list" jdbcType="VARCHAR" property="proofList"/>
        <result column="source_type" jdbcType="INTEGER" property="sourceType"/>
        <result column="create_user_id" jdbcType="VARCHAR" property="createUserId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_user_id" jdbcType="VARCHAR" property="updateUserId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="is_deleted" jdbcType="BOOLEAN" property="deleted"/>
    </resultMap>

</mapper>

