<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pxb7.mall.trade.ass.infra.repository.db.mapper.AssFollowupConfigMapper">

    <resultMap id="BaseResultMap" type="com.pxb7.mall.trade.ass.infra.repository.db.entity.AssFollowupConfig">
        <result column="id" jdbcType="INTEGER" property="id"/>
        <result column="followup_config_id" jdbcType="VARCHAR" property="followupConfigId"/>
        <result column="ass_type" jdbcType="INTEGER" property="assType"/>
        <result column="result_desc" jdbcType="VARCHAR" property="resultDesc"/>
        <result column="progress_desc" jdbcType="VARCHAR" property="progressDesc"/>
        <result column="join_group_desc" jdbcType="VARCHAR" property="joinGroupDesc"/>
        <result column="notice_type" jdbcType="INTEGER" property="noticeType"/>
        <result column="sort" jdbcType="INTEGER" property="sort"/>
        <result column="create_user_id" jdbcType="VARCHAR" property="createUserId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_user_id" jdbcType="VARCHAR" property="updateUserId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="is_deleted" jdbcType="BOOLEAN" property="deleted"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into
        ass_followup_config(followup_config_id,ass_type,result_desc,progress_desc,join_group_desc,notice_type,sort,create_user_id,create_time,update_user_id,update_time,is_deleted)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.followupConfigId},#{entity.assType},#{entity.resultDesc},#{entity.progressDesc},#{entity.joinGroupDesc},#{entity.noticeType},#{entity.sort},#{entity.createUserId},#{entity.createTime},#{entity.updateUserId},#{entity.updateTime},#{entity.deleted})
        </foreach>
    </insert>
</mapper>

