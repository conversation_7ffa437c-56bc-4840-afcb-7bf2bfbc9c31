<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pxb7.mall.trade.ass.infra.repository.db.mapper.ComplaintWorkLogMapper">

    <resultMap id="BaseResultMap" type="com.pxb7.mall.trade.ass.infra.repository.db.entity.ComplaintWorkLog">
        <result column="id" jdbcType="INTEGER" property="id"/>
        <result column="complaint_work_log_id" jdbcType="VARCHAR" property="complaintWorkLogId"/>
        <result column="room_id" jdbcType="VARCHAR" property="roomId"/>
        <result column="complaint_work_id" jdbcType="VARCHAR" property="complaintWorkId"/>
        <result column="log_type" jdbcType="INTEGER" property="logType"/>
        <result column="current_processor_id" jdbcType="VARCHAR" property="currentProcessorId"/>
        <result column="transferee_id" jdbcType="VARCHAR" property="transfereeId"/>
        <result column="transfer_note" jdbcType="VARCHAR" property="transferNote"/>
        <result column="create_user_id" jdbcType="VARCHAR" property="createUserId"/>
        <result column="update_user_id" jdbcType="VARCHAR" property="updateUserId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="is_deleted" jdbcType="BOOLEAN" property="deleted"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into
        complaint_work_log(complaint_work_log_id,room_id,complaint_work_id,log_type,current_processor_id,transferee_id,transfer_note,create_user_id,update_user_id,create_time,update_time,is_deleted)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.complaintWorkLogId},#{entity.roomId},#{entity.complaintWorkId},#{entity.logType},#{entity.currentProcessorId},#{entity.transfereeId},#{entity.transferNote},#{entity.createUserId},#{entity.updateUserId},#{entity.createTime},#{entity.updateTime},#{entity.deleted})
        </foreach>
    </insert>
</mapper>

