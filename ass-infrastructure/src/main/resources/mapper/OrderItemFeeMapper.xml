<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pxb7.mall.trade.ass.infra.repository.db.mapper.OrderItemFeeMapper">

    <resultMap id="BaseResultMap" type="com.pxb7.mall.trade.ass.infra.repository.db.entity.OrderItemFee">
        <result column="id" jdbcType="BIGINT" property="id"/>
        <result column="order_item_id" jdbcType="VARCHAR" property="orderItemId"/>
        <result column="responsible_user" jdbcType="INTEGER" property="responsibleUser"/>
        <result column="responsible_user_id" jdbcType="VARCHAR" property="responsibleUserId"/>
        <result column="fee_ratio" jdbcType="INTEGER" property="feeRatio"/>
        <result column="fee_type" jdbcType="INTEGER" property="feeType"/>
        <result column="fee_amount" jdbcType="BIGINT" property="feeAmount"/>
        <result column="fee_amount_max" jdbcType="BIGINT" property="feeAmountMax"/>
        <result column="fee_amount_min" jdbcType="BIGINT" property="feeAmountMin"/>
        <result column="fee_discount" jdbcType="BIGINT" property="feeDiscount"/>
        <result column="fee_real_amount" jdbcType="BIGINT" property="feeRealAmount"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="is_deleted" jdbcType="BOOLEAN" property="deleted"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into
        order_item_fee(order_item_id,responsible_user,responsible_user_id,fee_ratio,fee_type,fee_amount,fee_amount_max,fee_amount_min,fee_discount,fee_real_amount,create_time,update_time,is_deleted)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.orderItemId},#{entity.responsibleUser},#{entity.responsibleUserId},#{entity.feeRatio},#{entity.feeType},#{entity.feeAmount},#{entity.feeAmountMax},#{entity.feeAmountMin},#{entity.feeDiscount},#{entity.feeRealAmount},#{entity.createTime},#{entity.updateTime},#{entity.deleted})
        </foreach>
    </insert>
</mapper>

