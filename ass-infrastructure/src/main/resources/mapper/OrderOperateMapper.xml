<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pxb7.mall.trade.ass.infra.repository.db.mapper.OrderOperateMapper">

    <resultMap id="BaseResultMap" type="com.pxb7.mall.trade.ass.infra.repository.db.entity.OrderOperate">
        <result column="id" jdbcType="BIGINT" property="id"/>
        <result column="order_id" jdbcType="VARCHAR" property="orderId"/>
        <result column="order_item_id" jdbcType="VARCHAR" property="orderItemId"/>
        <result column="opt_type" jdbcType="INTEGER" property="optType"/>
        <result column="opt_content" jdbcType="VARCHAR" property="optContent"/>
        <result column="opt_user_type" jdbcType="INTEGER" property="optUserType"/>
        <result column="opt_user_id" jdbcType="VARCHAR" property="optUserId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="is_deleted" jdbcType="BOOLEAN" property="deleted"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into
        order_operate(order_id,order_item_id,opt_type,opt_content,opt_user_type,opt_user_id,create_time,update_time,is_deleted)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.orderId},#{entity.orderItemId},#{entity.optType},#{entity.optContent},#{entity.optUserType},#{entity.optUserId},#{entity.createTime},#{entity.updateTime},#{entity.deleted})
        </foreach>
    </insert>
</mapper>

