<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pxb7.mall.trade.ass.infra.repository.db.mapper.AssPaymentMapper">

    <resultMap id="BaseResultMap" type="com.pxb7.mall.trade.ass.infra.repository.db.entity.AssPayment">
        <result column="id" jdbcType="BIGINT" property="id"/>
        <result column="ass_payment_id" jdbcType="VARCHAR" property="assPaymentId"/>
        <result column="payment_no" jdbcType="VARCHAR" property="paymentNo"/>
        <result column="payment_amount" jdbcType="BIGINT" property="paymentAmount"/>
        <result column="ass_type" jdbcType="INTEGER" property="assType"/>
        <result column="work_order_id" jdbcType="VARCHAR" property="workOrderId"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="proposer_user_id" jdbcType="VARCHAR" property="proposerUserId"/>
        <result column="complete_time" jdbcType="TIMESTAMP" property="completeTime"/>
        <result column="proposer_reason" jdbcType="VARCHAR" property="proposerReason"/>
        <result column="product_id" jdbcType="VARCHAR" property="productId"/>
        <result column="product_unique_no" jdbcType="VARCHAR" property="productUniqueNo"/>
        <result column="order_item_id" jdbcType="VARCHAR" property="orderItemId"/>
        <result column="payment_account" jdbcType="VARCHAR" property="paymentAccount"/>
        <result column="account_id" jdbcType="VARCHAR" property="accountId"/>
        <result column="payment_channel" jdbcType="INTEGER" property="paymentChannel"/>
        <result column="qr_code" jdbcType="VARCHAR" property="qrCode"/>
        <result column="expire_time" jdbcType="TIMESTAMP" property="expireTime"/>
        <result column="payment_proof" jdbcType="VARCHAR" property="paymentProof"/>
        <result column="pay_way" jdbcType="INTEGER" property="payWay"/>
        <result column="source_type" jdbcType="INTEGER" property="sourceType"/>
        <result column="notes" jdbcType="VARCHAR" property="notes"/>
        <result column="finance" jdbcType="VARCHAR" property="finance"/>
        <result column="finance_id" jdbcType="VARCHAR" property="financeId"/>
        <result column="create_user_id" jdbcType="VARCHAR" property="createUserId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_user_id" jdbcType="VARCHAR" property="updateUserId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="is_deleted" jdbcType="BOOLEAN" property="deleted"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into
        ass_payment(ass_payment_id,payment_no,payment_amount,ass_type,work_order_id,status,proposer_user_id,complete_time,proposer_reason,product_id,order_item_id,payment_account,account_id,payment_channel,qr_code,payment_proof,pay_way,source_type,notes,finance,finance_id,create_user_id,create_time,update_user_id,update_time,is_deleted)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.assPaymentId},#{entity.paymentNo},#{entity.paymentAmount},#{entity.assType},#{entity.workOrderId},#{entity.status},#{entity.proposerUserId},#{entity.completeTime},#{entity.proposerReason},#{entity.productId},#{entity.orderItemId},#{entity.paymentAccount},#{entity.accountId},#{entity.paymentChannel},#{entity.qrCode},#{entity.paymentProof},#{entity.payWay},#{entity.sourceType},#{entity.notes},#{entity.finance},#{entity.financeId},#{entity.createUserId},#{entity.createTime},#{entity.updateUserId},#{entity.updateTime},#{entity.deleted})
        </foreach>
    </insert>
</mapper>

