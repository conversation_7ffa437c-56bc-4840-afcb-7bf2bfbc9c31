<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pxb7.mall.trade.ass.infra.repository.db.mapper.AssFollowupLogMapper">
  <resultMap id="BaseResultMap" type="com.pxb7.mall.trade.ass.infra.repository.db.entity.AssFollowupLog">
    <!--@mbg.generated-->
    <!--@Table ass_followup_log-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="followup_id" jdbcType="VARCHAR" property="followupId" />
    <result column="work_order_id" jdbcType="VARCHAR" property="workOrderId" />
    <result column="ass_type" jdbcType="SMALLINT" property="assType" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="content" jdbcType="LONGVARCHAR" property="content" />
    <result column="image" jdbcType="LONGVARCHAR" property="image" />
    <result column="add_way" jdbcType="SMALLINT" property="addWay" />
    <result column="create_user_id" jdbcType="VARCHAR" property="createUserId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user_id" jdbcType="VARCHAR" property="updateUserId" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="is_deleted" jdbcType="BOOLEAN" property="deleted" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, followup_id, work_order_id, ass_type, title, content, image, add_way, create_user_id, 
    create_time, update_user_id, update_time, is_deleted
  </sql>
</mapper>