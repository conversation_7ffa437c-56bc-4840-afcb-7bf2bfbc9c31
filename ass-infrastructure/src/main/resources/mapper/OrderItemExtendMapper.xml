<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pxb7.mall.trade.ass.infra.repository.db.mapper.OrderItemExtendMapper">

    <resultMap id="BaseResultMap" type="com.pxb7.mall.trade.ass.infra.repository.db.entity.OrderItemExtend">
        <result column="id" jdbcType="BIGINT" property="id"/>
        <result column="order_item_id" jdbcType="VARCHAR" property="orderItemId"/>
        <result column="trade_customer_id" jdbcType="VARCHAR" property="tradeCustomerId"/>
        <result column="delivery_customer_id" jdbcType="VARCHAR" property="deliveryCustomerId"/>
        <result column="delivery_customer" jdbcType="VARCHAR" property="deliveryCustomer"/>
        <result column="trade_customer" jdbcType="VARCHAR" property="tradeCustomer"/>
        <result column="product_name" jdbcType="VARCHAR" property="productName"/>
        <result column="product_pic" jdbcType="VARCHAR" property="productPic"/>
        <result column="product_attr" jdbcType="VARCHAR" property="productAttr"/>
        <result column="game_account" jdbcType="VARCHAR" property="gameAccount"/>
        <result column="product_highlight" jdbcType="VARCHAR" property="productHighlight"/>
        <result column="game_name" jdbcType="VARCHAR" property="gameName"/>
        <result column="game_attr" jdbcType="VARCHAR" property="gameAttr"/>
        <result column="is_easy_buy" jdbcType="BOOLEAN" property="easyBuy"/>
        <result column="room_id" jdbcType="VARCHAR" property="roomId"/>
        <result column="delivery_room_id" jdbcType="VARCHAR" property="deliveryRoomId"/>
        <result column="buyer_phone" jdbcType="VARCHAR" property="buyerPhone"/>
        <result column="buyer_user_type" jdbcType="INTEGER" property="buyerUserType"/>
        <result column="seller_phone" jdbcType="VARCHAR" property="sellerPhone"/>
        <result column="seller_user_type" jdbcType="INTEGER" property="sellerUserType"/>
        <result column="is_sincerity_sell" jdbcType="BOOLEAN" property="sinceritySell"/>
        <result column="buyer_merchant_id" jdbcType="VARCHAR" property="buyerMerchantId"/>
        <result column="buyer_merchant_dept_id" jdbcType="VARCHAR" property="buyerMerchantDeptId"/>
        <result column="seller_merchant_id" jdbcType="VARCHAR" property="sellerMerchantId"/>
        <result column="seller_merchant_dept_id" jdbcType="VARCHAR" property="sellerMerchantDeptId"/>
        <result column="is_add_contract" jdbcType="BOOLEAN" property="addContract"/>
        <result column="buyer_receipt_time" jdbcType="TIMESTAMP" property="buyerReceiptTime"/>
        <result column="auto_receipt_time" jdbcType="TIMESTAMP" property="autoReceiptTime"/>
        <result column="buyer_remark" jdbcType="VARCHAR" property="buyerRemark"/>
        <result column="seller_remark" jdbcType="VARCHAR" property="sellerRemark"/>
        <result column="customer_remark" jdbcType="VARCHAR" property="customerRemark"/>
        <result column="is_guarantee" jdbcType="BOOLEAN" property="guarantee"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="is_deleted" jdbcType="BOOLEAN" property="deleted"/>
        <result column="product_unique_no" jdbcType="VARCHAR" property="productUniqueNo"/>
        <result column="recharge_seller_status" jdbcType="INTEGER" property="rechargeSellerStatus"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into
        order_item_extend(order_item_id,trade_customer_id,delivery_customer_id,delivery_customer,trade_customer,product_name,product_pic,product_attr,game_account,product_highlight,game_name,game_attr,is_easy_buy,room_id,delivery_room_id,buyer_phone,buyer_user_type,seller_phone,seller_user_type,is_sincerity_sell,buyer_merchant_id,buyer_merchant_dept_id,seller_merchant_id,seller_merchant_dept_id,is_add_contract,buyer_receipt_time,auto_receipt_time,buyer_remark,seller_remark,customer_remark,is_guarantee,create_time,update_time,is_deleted,product_unique_no,recharge_seller_status)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.orderItemId},#{entity.tradeCustomerId},#{entity.deliveryCustomerId},#{entity.deliveryCustomer},#{entity.tradeCustomer},#{entity.productName},#{entity.productPic},#{entity.productAttr},#{entity.gameAccount},#{entity.productHighlight},#{entity.gameName},#{entity.gameAttr},#{entity.easyBuy},#{entity.roomId},#{entity.deliveryRoomId},#{entity.buyerPhone},#{entity.buyerUserType},#{entity.sellerPhone},#{entity.sellerUserType},#{entity.sinceritySell},#{entity.buyerMerchantId},#{entity.buyerMerchantDeptId},#{entity.sellerMerchantId},#{entity.sellerMerchantDeptId},#{entity.addContract},#{entity.buyerReceiptTime},#{entity.autoReceiptTime},#{entity.buyerRemark},#{entity.sellerRemark},#{entity.customerRemark},#{entity.guarantee},#{entity.createTime},#{entity.updateTime},#{entity.deleted},#{entity.productUniqueNo},#{entity.rechargeSellerStatus})
        </foreach>
    </insert>
</mapper>

