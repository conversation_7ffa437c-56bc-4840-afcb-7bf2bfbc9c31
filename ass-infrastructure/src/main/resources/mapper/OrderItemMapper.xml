<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pxb7.mall.trade.ass.infra.repository.db.mapper.OrderItemMapper">

    <resultMap id="BaseResultMap" type="com.pxb7.mall.trade.ass.infra.repository.db.entity.OrderItem">
        <result column="id" jdbcType="BIGINT" property="id"/>
        <result column="order_id" jdbcType="VARCHAR" property="orderId"/>
        <result column="order_item_id" jdbcType="VARCHAR" property="orderItemId"/>
        <result column="product_id" jdbcType="VARCHAR" property="productId"/>
        <result column="game_id" jdbcType="VARCHAR" property="gameId"/>
        <result column="buyer_id" jdbcType="VARCHAR" property="buyerId"/>
        <result column="seller_id" jdbcType="VARCHAR" property="sellerId"/>
        <result column="product_type" jdbcType="INTEGER" property="productType"/>
        <result column="seller_product_type" jdbcType="SMALLINT" property="sellerProductType"/>
        <result column="order_item_status" jdbcType="INTEGER" property="orderItemStatus"/>
        <result column="buyer_status" jdbcType="INTEGER" property="buyerStatus"/>
        <result column="seller_status" jdbcType="INTEGER" property="sellerStatus"/>
        <result column="order_item_amount" jdbcType="BIGINT" property="orderItemAmount"/>
        <result column="order_item_pay_amount" jdbcType="BIGINT" property="orderItemPayAmount"/>
        <result column="order_item_actual_pay_amount" jdbcType="BIGINT" property="orderItemActualPayAmount"/>
        <result column="product_original_price" jdbcType="BIGINT" property="productOriginalPrice"/>
        <result column="is_bargain" jdbcType="BOOLEAN" property="bargain"/>
        <result column="product_sale_price" jdbcType="BIGINT" property="productSalePrice"/>
        <result column="product_coupon_amount" jdbcType="BIGINT" property="productCouponAmount"/>
        <result column="product_pay_amount" jdbcType="BIGINT" property="productPayAmount"/>
        <result column="product_quantity" jdbcType="INTEGER" property="productQuantity"/>
        <result column="payout_amount" jdbcType="BIGINT" property="payoutAmount"/>
        <result column="max_payout_amount" jdbcType="BIGINT" property="maxPayoutAmount"/>
        <result column="complete_time" jdbcType="TIMESTAMP" property="completeTime"/>
        <result column="cancel_time" jdbcType="TIMESTAMP" property="cancelTime"/>
        <result column="receipt_status" jdbcType="INTEGER" property="receiptStatus"/>
        <result column="refund_status" jdbcType="INTEGER" property="refundStatus"/>
        <result column="payout_status" jdbcType="INTEGER" property="payoutStatus"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="is_deleted" jdbcType="BOOLEAN" property="deleted"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into
        order_item(order_id,order_item_id,product_id,game_id,buyer_id,seller_id,product_type,seller_product_type,order_item_status,buyer_status,seller_status,order_item_amount,order_item_pay_amount,order_item_actual_pay_amount,product_original_price,is_bargain,product_sale_price,product_coupon_amount,product_pay_amount,product_quantity,payout_amount,max_payout_amount,complete_time,cancel_time,receipt_status,refund_status,payout_status,create_time,update_time,is_deleted)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.orderId},#{entity.orderItemId},#{entity.productId},#{entity.gameId},#{entity.buyerId},#{entity.sellerId},#{entity.productType},#{entity.sellerProductType},#{entity.orderItemStatus},#{entity.buyerStatus},#{entity.sellerStatus},#{entity.orderItemAmount},#{entity.orderItemPayAmount},#{entity.orderItemActualPayAmount},#{entity.productOriginalPrice},#{entity.bargain},#{entity.productSalePrice},#{entity.productCouponAmount},#{entity.productPayAmount},#{entity.productQuantity},#{entity.payoutAmount},#{entity.maxPayoutAmount},#{entity.completeTime},#{entity.cancelTime},#{entity.receiptStatus},#{entity.refundStatus},#{entity.payoutStatus},#{entity.createTime},#{entity.updateTime},#{entity.deleted})
        </foreach>
    </insert>
</mapper>

