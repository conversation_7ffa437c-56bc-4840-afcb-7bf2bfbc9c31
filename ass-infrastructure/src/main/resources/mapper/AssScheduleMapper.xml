<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pxb7.mall.trade.ass.infra.repository.db.mapper.AssScheduleMapper">

    <resultMap id="BaseResultMap" type="com.pxb7.mall.trade.ass.infra.repository.db.entity.AssSchedule">
        <result column="id" jdbcType="BIGINT" property="id"/>
        <result column="schedule_id" jdbcType="VARCHAR" property="scheduleId"/>
        <result column="order_item_id" jdbcType="VARCHAR" property="orderItemId"/>
        <result column="work_order_id" jdbcType="VARCHAR" property="workOrderId"/>
        <result column="ass_type" jdbcType="INTEGER" property="assType"/>
        <result column="recv_customer_id" jdbcType="VARCHAR" property="recvCustomerId"/>
        <result column="audit_customer_id" jdbcType="VARCHAR" property="auditCustomerId"/>
        <result column="room_id" jdbcType="VARCHAR" property="roomId"/>
        <result column="is_finish" jdbcType="BOOLEAN" property="finish"/>
        <result column="source_type" jdbcType="VARCHAR" property="sourceType"/>
        <result column="create_user_id" jdbcType="VARCHAR" property="createUserId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_user_id" jdbcType="VARCHAR" property="updateUserId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="is_deleted" jdbcType="BOOLEAN" property="deleted"/>
    </resultMap>

    <update id="updateWorkOrderIdAndAssTypeAndFinishById">
        update ass_schedule
        set work_order_id=#{workOrderId},
            ass_type=#{assType},
            is_finish=#{finish},
            update_user_id=#{updateUserId}
        where id = #{id}
    </update>
    <update id="updateFinishById">
        update ass_schedule
        set is_finish=#{finish},
            update_user_id=#{updateUserId}
        where id = #{id}
    </update>
    <select id="findListByOrderItemIdAndRoomId" resultMap="BaseResultMap">
        select * from ass_schedule
        where
        <foreach collection="list" item="condition" open="(" separator=" OR " close=")">
            (is_deleted = 0 and order_item_id = #{condition.orderItemId}
            and room_id = #{condition.roomId}
            )
        </foreach>
    </select>
</mapper>

