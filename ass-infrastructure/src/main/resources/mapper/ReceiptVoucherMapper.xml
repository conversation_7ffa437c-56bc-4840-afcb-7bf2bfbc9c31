<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pxb7.mall.trade.ass.infra.repository.db.mapper.ReceiptVoucherMapper">

    <resultMap id="BaseResultMap" type="com.pxb7.mall.trade.ass.infra.repository.db.entity.ReceiptVoucher">
        <result column="id" jdbcType="BIGINT" property="id"/>
        <result column="order_id" jdbcType="VARCHAR" property="orderId"/>
        <result column="order_item_id" jdbcType="VARCHAR" property="orderItemId"/>
        <result column="receipt_voucher_id" jdbcType="VARCHAR" property="receiptVoucherId"/>
        <result column="payment_id" jdbcType="VARCHAR" property="paymentId"/>
        <result column="pay_mode" jdbcType="INTEGER" property="payMode"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="review_status" jdbcType="INTEGER" property="reviewStatus"/>
        <result column="need_pay_amount" jdbcType="BIGINT" property="needPayAmount"/>
        <result column="actual_pay_amount" jdbcType="BIGINT" property="actualPayAmount"/>
        <result column="receipt_type" jdbcType="INTEGER" property="receiptType"/>
        <result column="submitter" jdbcType="VARCHAR" property="submitter"/>
        <result column="submitter_name" jdbcType="VARCHAR" property="submitterName"/>
        <result column="submit_remark" jdbcType="VARCHAR" property="submitRemark"/>
        <result column="company_account" jdbcType="VARCHAR" property="companyAccount"/>
        <result column="pay_screenshot" jdbcType="VARCHAR" property="payScreenshot"/>
        <result column="reviewer_id" jdbcType="VARCHAR" property="reviewerId"/>
        <result column="reviewer_name" jdbcType="VARCHAR" property="reviewerName"/>
        <result column="review_remark" jdbcType="VARCHAR" property="reviewRemark"/>
        <result column="channel_id" jdbcType="VARCHAR" property="channelId"/>
        <result column="payment_time" jdbcType="TIMESTAMP" property="paymentTime"/>
        <result column="company_account_id" jdbcType="VARCHAR" property="companyAccountId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="is_deleted" jdbcType="BOOLEAN" property="deleted"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into
        receipt_voucher(order_id,order_item_id,receipt_voucher_id,payment_id,pay_mode,status,review_status,need_pay_amount,actual_pay_amount,receipt_type,submitter,submitter_name,submit_remark,company_account,pay_screenshot,reviewer_id,reviewer_name,review_remark,channel_id,payment_time,company_account_id,create_time,update_time,is_deleted)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.orderId},#{entity.orderItemId},#{entity.receiptVoucherId},#{entity.paymentId},#{entity.payMode},#{entity.status},#{entity.reviewStatus},#{entity.needPayAmount},#{entity.actualPayAmount},#{entity.receiptType},#{entity.submitter},#{entity.submitterName},#{entity.submitRemark},#{entity.companyAccount},#{entity.payScreenshot},#{entity.reviewerId},#{entity.reviewerName},#{entity.reviewRemark},#{entity.channelId},#{entity.paymentTime},#{entity.companyAccountId},#{entity.createTime},#{entity.updateTime},#{entity.deleted})
        </foreach>
    </insert>
</mapper>

