<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pxb7.mall.trade.ass.infra.repository.db.mapper.AssScheduleLogMapper">

    <resultMap id="BaseResultMap" type="com.pxb7.mall.trade.ass.infra.repository.db.entity.AssScheduleLog">
        <result column="id" jdbcType="BIGINT" property="id"/>
        <result column="schedule_log_id" jdbcType="VARCHAR" property="scheduleLogId"/>
        <result column="schedule_id" jdbcType="VARCHAR" property="scheduleId"/>
        <result column="node_id" jdbcType="VARCHAR" property="nodeId"/>
        <result column="node_desc" jdbcType="VARCHAR" property="nodeDesc"/>
        <result column="is_show" jdbcType="BOOLEAN" property="show"/>
        <result column="data" jdbcType="VARCHAR" property="data"/>
        <result column="create_user_id" jdbcType="VARCHAR" property="createUserId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_user_id" jdbcType="VARCHAR" property="updateUserId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="is_deleted" jdbcType="BOOLEAN" property="deleted"/>
        <result column="source_type" jdbcType="VARCHAR" property="sourceType"/>
    </resultMap>

    <update id="delByScheduleIdAndNodeIds">
        update ass_schedule_log
        set is_deleted = 1
        where schedule_id = #{scheduleId}
        and node_id in
        <foreach collection="nodeIds" item="nodeId" separator="," open="(" close=")">
            #{nodeId}
        </foreach>
    </update>
</mapper>

