<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pxb7.mall.trade.ass.infra.repository.db.mapper.AssDisputeWoMapper">
    <resultMap id="BaseResultMap" type="com.pxb7.mall.trade.ass.infra.repository.db.entity.AssDisputeWo">
        <!--@mbg.generated-->
        <!--@Table ass_dispute_work-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="ass_dispute_id" jdbcType="VARCHAR" property="assDisputeId"/>
        <result column="work_order_no" jdbcType="VARCHAR" property="workOrderNo"/>
        <result column="order_item_id" jdbcType="VARCHAR" property="orderItemId"/>
        <result column="product_id" jdbcType="VARCHAR" property="productId"/>
        <result column="product_unique_no" jdbcType="VARCHAR" property="productUniqueNo"/>
        <result column="product_type" jdbcType="INTEGER" property="productType"/>
        <result column="game_id" jdbcType="VARCHAR" property="gameId"/>
        <result column="status" jdbcType="SMALLINT" property="status"/>
        <result column="proposer_user_id" jdbcType="VARCHAR" property="proposerUserId"/>
        <result column="proposer_type" jdbcType="SMALLINT" property="proposerType"/>
        <result column="dispute_user_id" jdbcType="VARCHAR" property="disputeUserId"/>
        <result column="handle_status" jdbcType="SMALLINT" property="handleStatus"/>
        <result column="claim_amount" jdbcType="BIGINT" property="claimAmount"/>
        <result column="claim_status" jdbcType="SMALLINT" property="claimStatus"/>
        <result column="sponsor" jdbcType="SMALLINT" property="sponsor"/>
        <result column="complete_time" jdbcType="TIMESTAMP" property="completeTime"/>
        <result column="classify_first" jdbcType="VARCHAR" property="classifyFirst"/>
        <result column="classify_second" jdbcType="VARCHAR" property="classifySecond"/>
        <result column="recover_amount" jdbcType="BIGINT" property="recoverAmount"/>
        <result column="recover_status" jdbcType="SMALLINT" property="recoverStatus"/>
        <result column="data_source" jdbcType="SMALLINT" property="dataSource"/>
        <result column="source_type" jdbcType="SMALLINT" property="sourceType"/>
        <result column="create_user_id" jdbcType="VARCHAR" property="createUserId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_user_id" jdbcType="VARCHAR" property="updateUserId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="is_deleted" jdbcType="TINYINT" property="deleted"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, ass_dispute_id, work_order_no, order_item_id, product_id, game_id, `status`,
        proposer_user_id, proposer_type, dispute_user_id, handle_status, claim_amount, claim_status,
        sponsor, complete_time, classify_first, classify_second, recover_amount, recover_status,
        data_source, source_type, create_user_id, create_time, update_user_id, update_time,
        is_deleted
    </sql>
</mapper>