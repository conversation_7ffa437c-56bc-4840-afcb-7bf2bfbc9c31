<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pxb7.mall.trade.ass.infra.repository.db.mapper.AssQuestionOptionRelationMapper">
  <resultMap id="BaseResultMap" type="com.pxb7.mall.trade.ass.infra.repository.db.entity.AssQuestionOptionRelation">
    <!--@mbg.generated-->
    <!--@Table ass_question_option_relation-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <id column="relation_id" jdbcType="VARCHAR" property="relationId" />
    <result column="option_id" jdbcType="VARCHAR" property="optionId" />
    <result column="game_id" jdbcType="VARCHAR" property="gameId" />
    <result column="create_user_id" jdbcType="VARCHAR" property="createUserId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user_id" jdbcType="VARCHAR" property="updateUserId" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, relation_id, option_id, game_id, create_user_id, create_time, update_user_id, 
    update_time, is_deleted
  </sql>
</mapper>