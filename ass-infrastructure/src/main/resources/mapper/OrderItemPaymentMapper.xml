<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pxb7.mall.trade.ass.infra.repository.db.mapper.OrderItemPaymentMapper">

    <resultMap id="BaseResultMap" type="com.pxb7.mall.trade.ass.infra.repository.db.entity.OrderItemPayment">
        <result column="id" jdbcType="INTEGER" property="id"/>
        <result column="order_id" jdbcType="VARCHAR" property="orderId"/>
        <result column="order_item_id" jdbcType="VARCHAR" property="orderItemId"/>
        <result column="business_type" jdbcType="INTEGER" property="businessType"/>
        <result column="receipt_voucher_id" jdbcType="VARCHAR" property="receiptVoucherId"/>
        <result column="refund_voucher_id" jdbcType="VARCHAR" property="refundVoucherId"/>
        <result column="payout_voucher_id" jdbcType="VARCHAR" property="payoutVoucherId"/>
        <result column="payment_id" jdbcType="VARCHAR" property="paymentId"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="trade_amount" jdbcType="INTEGER" property="tradeAmount"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="is_deleted" jdbcType="BOOLEAN" property="deleted"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into
        order_item_payment(order_id,order_item_id,business_type,receipt_voucher_id,refund_voucher_id,payout_voucher_id,payment_id,status,trade_amount,create_time,update_time,is_deleted)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.orderId},#{entity.orderItemId},#{entity.businessType},#{entity.receiptVoucherId},#{entity.refundVoucherId},#{entity.payoutVoucherId},#{entity.paymentId},#{entity.status},#{entity.tradeAmount},#{entity.createTime},#{entity.updateTime},#{entity.deleted})
        </foreach>
    </insert>
</mapper>

