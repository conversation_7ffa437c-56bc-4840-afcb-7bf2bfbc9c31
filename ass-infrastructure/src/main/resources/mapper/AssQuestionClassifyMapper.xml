<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pxb7.mall.trade.ass.infra.repository.db.mapper.AssQuestionClassifyMapper">
  <resultMap id="BaseResultMap" type="com.pxb7.mall.trade.ass.infra.repository.db.entity.AssQuestionClassify">
    <!--@mbg.generated-->
    <!--@Table ass_question_classify-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <id column="classify_id" jdbcType="VARCHAR" property="classifyId" />
    <result column="ass_type" jdbcType="SMALLINT" property="assType" />
    <result column="first_level_directory" jdbcType="VARCHAR" property="firstLevelDirectory" />
    <result column="second_level_directory" jdbcType="VARCHAR" property="secondLevelDirectory" />
    <result column="sort_index" jdbcType="SMALLINT" property="sortIndex" />
    <result column="create_user_id" jdbcType="VARCHAR" property="createUserId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user_id" jdbcType="VARCHAR" property="updateUserId" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="is_deleted" jdbcType="BOOLEAN" property="deleted" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, classify_id, ass_type, first_level_directory, second_level_directory, sort_index, 
    create_user_id, create_time, update_user_id, update_time, is_deleted
  </sql>
</mapper>