<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pxb7.mall.trade.ass.infra.repository.db.mapper.AssAnswerMapper">
  <resultMap id="BaseResultMap" type="com.pxb7.mall.trade.ass.infra.repository.db.entity.AssAnswer">
    <!--@mbg.generated-->
    <!--@Table ass_answer-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="answer_id" jdbcType="VARCHAR" property="answerId" />
    <result column="work_order_id" jdbcType="VARCHAR" property="workOrderId" />
    <result column="order_item_id" jdbcType="VARCHAR" property="orderItemId" />
    <result column="answer" jdbcType="LONGVARCHAR" property="answer" />
    <result column="create_user_id" jdbcType="VARCHAR" property="createUserId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user_id" jdbcType="VARCHAR" property="updateUserId" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, answer_id, work_order_id, order_item_id, answer, create_user_id, create_time, 
    update_user_id, update_time, is_deleted
  </sql>
</mapper>