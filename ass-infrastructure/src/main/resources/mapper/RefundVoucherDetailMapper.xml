<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pxb7.mall.trade.ass.infra.repository.db.mapper.RefundVoucherDetailMapper">

    <resultMap id="BaseResultMap"
               type="com.pxb7.mall.trade.ass.infra.repository.db.entity.RefundVoucherDetail">
        <result column="id" jdbcType="BIGINT" property="id"/>
        <result column="order_item_id" jdbcType="VARCHAR" property="orderItemId"/>
        <result column="receipt_voucher_id" jdbcType="VARCHAR" property="receiptVoucherId"/>
        <result column="product_amount" jdbcType="BIGINT" property="productAmount"/>
        <result column="indemnity_amount" jdbcType="BIGINT" property="indemnityAmount"/>
        <result column="fee_amount" jdbcType="BIGINT" property="feeAmount"/>
        <result column="red_packet_amount" jdbcType="BIGINT" property="redPacketAmount"/>
        <result column="coupon_amount" jdbcType="BIGINT" property="couponAmount"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="is_deleted" jdbcType="BOOLEAN" property="deleted"/>
        <result column="fee_responsible_user" jdbcType="INTEGER" property="feeResponsibleUser"/>
        <result column="indemnity_responsible_user" jdbcType="INTEGER" property="indemnityResponsibleUser"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into
        receipt_voucher_detail(order_item_id,receipt_voucher_id,product_amount,indemnity_amount,fee_amount,red_packet_amount,coupon_amount,create_time,update_time,is_deleted,fee_responsible_user,indemnity_responsible_user)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.orderItemId},#{entity.receiptVoucherId},#{entity.productAmount},#{entity.indemnityAmount},#{entity.feeAmount},#{entity.redPacketAmount},#{entity.couponAmount},#{entity.createTime},#{entity.updateTime},#{entity.deleted},#{entity.feeResponsibleUser},#{entity.indemnityResponsibleUser})
        </foreach>
    </insert>
</mapper>

