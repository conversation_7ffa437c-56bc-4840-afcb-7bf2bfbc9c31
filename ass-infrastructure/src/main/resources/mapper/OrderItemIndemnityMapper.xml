<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pxb7.mall.trade.ass.infra.repository.db.mapper.OrderItemIndemnityMapper">
    <resultMap id="BaseResultMap"
               type="com.pxb7.mall.trade.ass.infra.repository.es.entity.OrderItemIndemnity">
        <result column="id" jdbcType="BIGINT" property="id"/>
        <result column="indemnity_id" jdbcType="VARCHAR" property="indemnityId"/>
        <result column="indemnity_name" jdbcType="VARCHAR" property="indemnityName"/>
        <result column="indemnity_type_lev1" jdbcType="SMALLINT" property="indemnityTypeLev1"/>
        <result column="indemnity_type_lev2" jdbcType="SMALLINT" property="indemnityTypeLev2"/>
        <result column="percent_compensation" jdbcType="SMALLINT" property="percentCompensation"/>
        <result column="product_id" jdbcType="VARCHAR" property="productId"/>
        <result column="order_item_id" jdbcType="VARCHAR" property="orderItemId"/>
        <result column="policy_id" jdbcType="VARCHAR" property="policyId"/>
        <result column="responsible_user" jdbcType="INTEGER" property="responsibleUser"/>
        <result column="responsible_user_id" jdbcType="VARCHAR" property="responsibleUserId"/>
        <result column="indemnity_status" jdbcType="INTEGER" property="indemnityStatus"/>
        <result column="indemnity_buy_ratio" jdbcType="INTEGER" property="indemnityBuyRatio"/>
        <result column="indemnity_amount" jdbcType="BIGINT" property="indemnityAmount"/>
        <result column="indemnity_coupon" jdbcType="BIGINT" property="indemnityCoupon"/>
        <result column="indemnity_merchant_coupon" jdbcType="BIGINT" property="indemnityMerchantCoupon"/>
        <result column="indemnity_real_amount" jdbcType="BIGINT" property="indemnityRealAmount"/>
        <result column="price_min" jdbcType="BIGINT" property="priceMin"/>
        <result column="price_max" jdbcType="BIGINT" property="priceMax"/>
        <result column="insurance_name" jdbcType="VARCHAR" property="insuranceName"/>
        <result column="insurance_sku" jdbcType="VARCHAR" property="insuranceSku"/>
        <result column="indemnity_desc" jdbcType="VARCHAR" property="indemnityDesc"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="is_deleted" jdbcType="BOOLEAN" property="deleted"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into
        order_item_indemnity(indemnity_id,indemnity_name,indemnity_type_lev1,indemnity_type_lev2,percent_compensation,product_id,order_item_id,policy_id,responsible_user,responsible_user_id,indemnity_status,indemnity_buy_ratio,indemnity_amount,indemnity_coupon,indemnity_merchant_coupon,indemnity_real_amount,price_min,price_max,insurance_name,insurance_sku,indemnity_desc,create_time,update_time,is_deleted)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.indemnityId},#{entity.indemnityName},#{entity.indemnityTypeLev1},#{entity.indemnityTypeLev2},#{entity.percentCompensation},#{entity.productId},#{entity.orderItemId},#{entity.policyId},#{entity.responsibleUser},#{entity.responsibleUserId},#{entity.indemnityStatus},#{entity.indemnityBuyRatio},#{entity.indemnityAmount},#{entity.indemnityCoupon},#{entity.indemnityMerchantCoupon},#{entity.indemnityRealAmount},#{entity.priceMin},#{entity.priceMax},#{entity.insuranceName},#{entity.insuranceSku},#{entity.indemnityDesc},#{entity.createTime},#{entity.updateTime},#{entity.deleted})
        </foreach>
    </insert>

    <select id="getOrderItemIndemnityTotal" resultType="java.lang.Long">
        SELECT
        sum(indemnity_real_amount)
        FROM `order_item_indemnity`
        where order_item_id = #{orderItemId}
        and indemnity_status = #{indemnityStatus}
        <if test="userId !=null and userId != ''">
            and responsible_user_id = #{userId}
        </if>
        and is_deleted = 0
    </select>
</mapper>

