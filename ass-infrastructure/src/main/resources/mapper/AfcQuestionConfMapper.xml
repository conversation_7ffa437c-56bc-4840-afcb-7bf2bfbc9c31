<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pxb7.mall.trade.ass.infra.repository.db.mapper.AfcQuestionConfMapper">

    <resultMap id="BaseResultMap" type="com.pxb7.mall.trade.ass.infra.repository.db.entity.AfcQuestionConf">
        <result column="id" jdbcType="INTEGER" property="id"/>
        <result column="question_id" jdbcType="VARCHAR" property="questionId"/>
        <result column="question_name" jdbcType="VARCHAR" property="questionName"/>
        <result column="question_type" jdbcType="INTEGER" property="questionType"/>
        <result column="sort" jdbcType="INTEGER" property="sort"/>
        <result column="create_user_id" jdbcType="VARCHAR" property="createUserId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_user_id" jdbcType="VARCHAR" property="updateUserId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="is_deleted" jdbcType="INTEGER" property="isDeleted"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into
        afc_question_conf(question_id,question_name,question_type,sort,create_user_id,create_time,update_user_id,update_time,is_deleted)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.questionId},#{entity.questionName},#{entity.questionType},#{entity.sort},#{entity.createUserId},#{entity.createTime},#{entity.updateUserId},#{entity.updateTime},#{entity.isDeleted})
        </foreach>
    </insert>
</mapper>

