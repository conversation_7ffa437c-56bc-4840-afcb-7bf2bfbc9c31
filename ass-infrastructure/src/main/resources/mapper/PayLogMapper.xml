<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pxb7.mall.trade.ass.infra.repository.db.mapper.PayLogMapper">

    <resultMap id="BaseResultMap" type="com.pxb7.mall.trade.ass.infra.repository.db.entity.PayLog">
        <result column="id" jdbcType="INTEGER" property="id"/>
        <result column="pay_log_id" jdbcType="VARCHAR" property="payLogId"/>
        <result column="pay_channel_id" jdbcType="VARCHAR" property="payChannelId"/>
        <result column="pay_merchant_id" jdbcType="VARCHAR" property="payMerchantId"/>
        <result column="payment_id" jdbcType="VARCHAR" property="paymentId"/>
        <result column="pay_status" jdbcType="INTEGER" property="payStatus"/>
        <result column="trade_mode" jdbcType="INTEGER" property="tradeMode"/>
        <result column="pay_offline_image_url" jdbcType="VARCHAR" property="payOfflineImageUrl"/>
        <result column="user_pay_account" jdbcType="VARCHAR" property="userPayAccount"/>
        <result column="out_trade_no" jdbcType="VARCHAR" property="outTradeNo"/>
        <result column="request_param" jdbcType="VARCHAR" property="requestParam"/>
        <result column="response_url" jdbcType="VARCHAR" property="responseUrl"/>
        <result column="callback_result" jdbcType="VARCHAR" property="callbackResult"/>
        <result column="trade_amount" jdbcType="INTEGER" property="tradeAmount"/>
        <result column="biz_type" jdbcType="INTEGER" property="bizType"/>
        <result column="pay_client_type" jdbcType="INTEGER" property="payClientType"/>
        <result column="payment_type" jdbcType="INTEGER" property="paymentType"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="is_deleted" jdbcType="BOOLEAN" property="deleted"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into
        pay_log(pay_log_id,pay_channel_id,pay_merchant_id,payment_id,pay_status,trade_mode,pay_offline_image_url,user_pay_account,out_trade_no,request_param,response_url,callback_result,trade_amount,biz_type,pay_client_type,payment_type,create_time,update_time,is_deleted)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.payLogId},#{entity.payChannelId},#{entity.payMerchantId},#{entity.paymentId},#{entity.payStatus},#{entity.tradeMode},#{entity.payOfflineImageUrl},#{entity.userPayAccount},#{entity.outTradeNo},#{entity.requestParam},#{entity.responseUrl},#{entity.callbackResult},#{entity.tradeAmount},#{entity.bizType},#{entity.payClientType},#{entity.paymentType},#{entity.createTime},#{entity.updateTime},#{entity.deleted})
        </foreach>
    </insert>
</mapper>

