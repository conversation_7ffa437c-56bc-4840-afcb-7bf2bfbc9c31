<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pxb7.mall.trade.ass.infra.repository.db.mapper.PayCompanyAccountMapper">

    <resultMap id="BaseResultMap" type="com.pxb7.mall.trade.ass.infra.repository.db.entity.PayCompanyAccount">
        <result column="id" jdbcType="INTEGER" property="id"/>
        <result column="company_account_id" jdbcType="VARCHAR" property="companyAccountId"/>
        <result column="pay_mode" jdbcType="INTEGER" property="payMode"/>
        <result column="account_name" jdbcType="VARCHAR" property="accountName"/>
        <result column="company_account" jdbcType="VARCHAR" property="companyAccount"/>
        <result column="transfer_type" jdbcType="INTEGER" property="transferType"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="payout_info" jdbcType="VARCHAR" property="payoutInfo"/>
        <result column="qr_code_url" jdbcType="VARCHAR" property="qrCodeUrl"/>
        <result column="out_payment_param" jdbcType="VARCHAR" property="outPaymentParam"/>
        <result column="create_user_id" jdbcType="VARCHAR" property="createUserId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_user_id" jdbcType="VARCHAR" property="updateUserId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into
        pay_company_account(company_account_id,pay_mode,account_name,company_account,transfer_type,status,payout_info,qr_code_url,out_payment_param,create_user_id,create_time,update_user_id,update_time)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.companyAccountId},#{entity.payMode},#{entity.accountName},#{entity.companyAccount},#{entity.transferType},#{entity.status},#{entity.payoutInfo},#{entity.qrCodeUrl},#{entity.outPaymentParam},#{entity.createUserId},#{entity.createTime},#{entity.updateUserId},#{entity.updateTime})
        </foreach>
    </insert>
</mapper>

