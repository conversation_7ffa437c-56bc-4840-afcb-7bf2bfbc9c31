<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pxb7.mall.trade.ass.infra.repository.db.mapper.RefundReasonMapper">

    <resultMap id="BaseResultMap" type="com.pxb7.mall.trade.ass.infra.repository.db.entity.RefundReason">
        <result column="id" jdbcType="BIGINT" property="id"/>
        <result column="reason_refund_id" jdbcType="VARCHAR" property="reasonRefundId"/>
        <result column="reason_refund_content" jdbcType="VARCHAR" property="reasonRefundContent"/>
        <result column="reason_intercept" jdbcType="INTEGER" property="reasonIntercept"/>
        <result column="auto_grounding" jdbcType="VARCHAR" property="autoGrounding"/>
        <result column="reason_range" jdbcType="INTEGER" property="reasonRange"/>
        <result column="reason_level" jdbcType="INTEGER" property="reasonLevel"/>
        <result column="pid" jdbcType="VARCHAR" property="pid"/>
        <result column="refund_type" jdbcType="VARCHAR" property="refundType"/>
        <result column="create_user_id" jdbcType="VARCHAR" property="createUserId"/>
        <result column="update_user_id" jdbcType="VARCHAR" property="updateUserId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="is_deleted" jdbcType="BOOLEAN" property="deleted"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into
        refund_reason(reason_refund_id,reason_refund_content,reason_intercept,auto_grounding,reason_range,reason_level,pid,refund_type,create_user_id,update_user_id,create_time,update_time,is_deleted)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.reasonRefundId},#{entity.reasonRefundContent},#{entity.reasonIntercept},#{entity.autoGrounding},#{entity.reasonRange},#{entity.reasonLevel},#{entity.pid},#{entity.refundType},#{entity.createUserId},#{entity.updateUserId},#{entity.createTime},#{entity.updateTime},#{entity.deleted})
        </foreach>
    </insert>
</mapper>

