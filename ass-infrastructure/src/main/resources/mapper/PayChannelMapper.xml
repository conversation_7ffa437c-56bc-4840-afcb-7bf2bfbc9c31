<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pxb7.mall.trade.ass.infra.repository.db.mapper.PayChannelMapper">

    <resultMap id="BaseResultMap" type="com.pxb7.mall.trade.ass.infra.repository.db.entity.PayChannel">
        <result column="id" jdbcType="INTEGER" property="id"/>
        <result column="channel_id" jdbcType="VARCHAR" property="channelId"/>
        <result column="pay_company_account_id" jdbcType="VARCHAR" property="payCompanyAccountId"/>
        <result column="pay_type" jdbcType="INTEGER" property="payType"/>
        <result column="pay_product" jdbcType="INTEGER" property="payProduct"/>
        <result column="channel_name" jdbcType="VARCHAR" property="channelName"/>
        <result column="channel" jdbcType="INTEGER" property="channel"/>
        <result column="channel_remark" jdbcType="VARCHAR" property="channelRemark"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="adaptation_client" jdbcType="VARCHAR" property="adaptationClient"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into
        pay_channel(channel_id,pay_company_account_id,pay_type,pay_product,channel_name,channel,channel_remark,status,create_time,update_time,adaptation_client)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.channelId},#{entity.payCompanyAccountId},#{entity.payType},#{entity.payProduct},#{entity.channelName},#{entity.channel},#{entity.channelRemark},#{entity.status},#{entity.createTime},#{entity.updateTime},#{entity.adaptationClient})
        </foreach>
    </insert>
</mapper>

