<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pxb7.mall.trade.ass.infra.repository.db.mapper.AssWorkOrderMapper">

    <resultMap id="BaseResultMap" type="com.pxb7.mall.trade.ass.infra.repository.db.entity.AssWorkOrder">
        <result column="id" jdbcType="INTEGER" property="id"/>
        <result column="work_order_id" jdbcType="VARCHAR" property="workOrderId"/>
        <result column="schedule_id" jdbcType="VARCHAR" property="scheduleId"/>
        <result column="order_item_id" jdbcType="VARCHAR" property="orderItemId"/>
        <result column="room_id" jdbcType="VARCHAR" property="roomId"/>
        <result column="ass_type" jdbcType="INTEGER" property="assType"/>
        <result column="rel_order_id" jdbcType="VARCHAR" property="relOrderId"/>
        <result column="ass_status" jdbcType="INTEGER" property="assStatus"/>
        <result column="ass_status_memo" jdbcType="VARCHAR" property="assStatusMemo"/>
        <result column="apply_time" jdbcType="TIMESTAMP" property="applyTime"/>
        <result column="expected_time" jdbcType="TIMESTAMP" property="expectedTime"/>
        <result column="complete_time" jdbcType="TIMESTAMP" property="completeTime"/>
        <result column="read_flag" jdbcType="VARCHAR" property="readFlag"/>
        <result column="create_user_id" jdbcType="VARCHAR" property="createUserId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_user_id" jdbcType="VARCHAR" property="updateUserId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="is_deleted" jdbcType="BOOLEAN" property="deleted"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into
        ass_work_order(work_order_id,schedule_id,order_item_id,room_id,ass_type,rel_order_id,ass_status,ass_status_memo,apply_time,expected_time,complete_time,read_flag,create_user_id,create_time,update_user_id,update_time,is_deleted)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.workOrderId},#{entity.scheduleId},#{entity.orderItemId},#{entity.roomId},#{entity.assType},#{entity.relOrderId},#{entity.assStatus},#{entity.assStatusMemo},#{entity.applyTime},#{entity.expectedTime},#{entity.completeTime},#{entity.readFlag},#{entity.createUserId},#{entity.createTime},#{entity.updateUserId},#{entity.updateTime},#{entity.deleted})
        </foreach>
    </insert>


    <update id="updateWithOpt">
        UPDATE ass_work_order
        SET ass_status = #{targetStatus},
        ass_status_memo = #{assStatusMemo}
        WHERE id = #{id} AND ass_status = #{originStatus} and is_deleted = 0
    </update>

</mapper>

