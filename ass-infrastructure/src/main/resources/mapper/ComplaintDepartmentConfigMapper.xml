<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pxb7.mall.trade.ass.infra.repository.db.mapper.ComplaintDepartmentConfigMapper">

    <resultMap id="BaseResultMap" type="com.pxb7.mall.trade.ass.infra.repository.db.entity.ComplaintDepartmentConfig">
        <result column="id" jdbcType="INTEGER" property="id"/>
        <result column="complaint_department_id" jdbcType="VARCHAR" property="complaintDepartmentId"/>
        <result column="department_name" jdbcType="VARCHAR" property="departmentName"/>
        <result column="question_type" jdbcType="VARCHAR" property="questionType"/>
        <result column="question_sort" jdbcType="INTEGER" property="questionSort"/>
        <result column="create_user_id" jdbcType="VARCHAR" property="createUserId"/>
        <result column="update_user_id" jdbcType="VARCHAR" property="updateUserId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="is_deleted" jdbcType="INTEGER" property="isDeleted"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into
        complaint_department_config(complaint_department_id,department_name,question_type,question_sort,create_user_id,update_user_id,create_time,update_time,is_deleted)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.complaintDepartmentId},#{entity.departmentName},#{entity.questionType},#{entity.questionSort},#{entity.createUserId},#{entity.updateUserId},#{entity.createTime},#{entity.updateTime},#{entity.isDeleted})
        </foreach>
    </insert>
</mapper>

