<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pxb7.mall.trade.ass.infra.repository.db.mapper.AssRetrieveWoMapper">
    <resultMap id="BaseResultMap" type="com.pxb7.mall.trade.ass.infra.repository.db.entity.AssRetrieveWo">
        <!--@mbg.generated-->
        <!--@Table ass_retrieve_work-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="ass_retrieve_id" jdbcType="VARCHAR" property="assRetrieveId"/>
        <result column="work_order_no" jdbcType="VARCHAR" property="workOrderNo"/>
        <result column="order_item_id" jdbcType="VARCHAR" property="orderItemId"/>
        <result column="product_id" jdbcType="VARCHAR" property="productId"/>
        <result column="product_unique_no" jdbcType="VARCHAR" property="productUniqueNo"/>
        <result column="product_type" jdbcType="INTEGER" property="productType"/>
        <result column="game_id" jdbcType="VARCHAR" property="gameId"/>
        <result column="proposer_user_id" jdbcType="VARCHAR" property="proposerUserId"/>
        <result column="recv_customer_id" jdbcType="VARCHAR" property="recvCustomerId"/>
        <result column="recv_feishu_name" jdbcType="VARCHAR" property="recvFeishuName"/>
        <result column="audit_customer_id" jdbcType="VARCHAR" property="auditCustomerId"/>
        <result column="audit_feishu_name" jdbcType="VARCHAR" property="auditFeishuName"/>
        <result column="deal_user_id" jdbcType="VARCHAR" property="dealUserId"/>
        <result column="status" jdbcType="SMALLINT" property="status"/>
        <result column="deal_result" jdbcType="SMALLINT" property="dealResult"/>
        <result column="recover_amount" jdbcType="BIGINT" property="recoverAmount"/>
        <result column="recover_status" jdbcType="SMALLINT" property="recoverStatus"/>
        <result column="claim_amount" jdbcType="BIGINT" property="claimAmount"/>
        <result column="claim_status" jdbcType="SMALLINT" property="claimStatus"/>
        <result column="expected_time" jdbcType="TIMESTAMP" property="expectedTime"/>
        <result column="sponsor" jdbcType="SMALLINT" property="sponsor"/>
        <result column="complete_time" jdbcType="TIMESTAMP" property="completeTime"/>
        <result column="is_rotate" jdbcType="TINYINT" property="isRotate"/>
        <result column="classify_first" jdbcType="VARCHAR" property="classifyFirst"/>
        <result column="classify_second" jdbcType="VARCHAR" property="classifySecond"/>
        <result column="data_source" jdbcType="SMALLINT" property="dataSource"/>
        <result column="source_type" jdbcType="SMALLINT" property="sourceType"/>
        <result column="insure" jdbcType="SMALLINT" property="insure"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="create_user_id" jdbcType="VARCHAR" property="createUserId"/>
        <result column="create_feishu_name" jdbcType="VARCHAR" property="createFeishuName"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="update_user_id" jdbcType="VARCHAR" property="updateUserId"/>
        <result column="is_deleted" jdbcType="TINYINT" property="deleted"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, ass_retrieve_id, work_order_no, order_item_id, product_id, game_id, proposer_user_id,
        recv_customer_id,recv_feishu_name, audit_customer_id,audit_feishu_name, deal_user_id, `status`, deal_result,
        recover_amount,
        recover_status, claim_amount, claim_status, expected_time, sponsor, complete_time,
        is_rotate, classify_first, classify_second, data_source, source_type, insure, create_time,
        create_user_id, create_feishu_name, update_time, update_user_id, is_deleted
    </sql>
</mapper>