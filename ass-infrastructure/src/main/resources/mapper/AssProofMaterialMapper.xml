<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pxb7.mall.trade.ass.infra.repository.db.mapper.AssProofMaterialMapper">
  <resultMap id="BaseResultMap" type="com.pxb7.mall.trade.ass.infra.repository.db.entity.AssProofMaterial">
    <!--@mbg.generated-->
    <!--@Table ass_proof_material-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="proof_id" jdbcType="VARCHAR" property="proofId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="link_url" jdbcType="VARCHAR" property="linkUrl" />
    <result column="ass_type" jdbcType="SMALLINT" property="assType" />
    <result column="work_order_id" jdbcType="VARCHAR" property="workOrderId" />
    <result column="create_user_id" jdbcType="VARCHAR" property="createUserId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user_id" jdbcType="VARCHAR" property="updateUserId" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, proof_id, `name`, link_url, ass_type, work_order_id, create_user_id, create_time, 
    update_user_id, update_time, is_deleted
  </sql>
</mapper>