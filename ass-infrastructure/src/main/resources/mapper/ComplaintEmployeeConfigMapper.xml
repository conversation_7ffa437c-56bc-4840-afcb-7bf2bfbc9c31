<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pxb7.mall.trade.ass.infra.repository.db.mapper.ComplaintEmployeeConfigMapper">

    <resultMap id="BaseResultMap" type="com.pxb7.mall.trade.ass.infra.repository.db.entity.ComplaintEmployeeConfig">
        <result column="id" jdbcType="INTEGER" property="id"/>
        <result column="complaint_employee_id" jdbcType="VARCHAR" property="complaintEmployeeId"/>
        <result column="complaint_department_id" jdbcType="VARCHAR" property="complaintDepartmentId"/>
        <result column="mobile" jdbcType="VARCHAR" property="mobile"/>
        <result column="user_id" jdbcType="VARCHAR" property="userId"/>
        <result column="user_name" jdbcType="VARCHAR" property="userName"/>
        <result column="create_user_id" jdbcType="VARCHAR" property="createUserId"/>
        <result column="update_user_id" jdbcType="VARCHAR" property="updateUserId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="is_deleted" jdbcType="BOOLEAN" property="deleted"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into
        complaint_employee_config(complaint_employee_id,complaint_department_id,question_name,user_id,user_name,create_user_id,update_user_id,create_time,update_time,is_deleted)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.complaintEmployeeId},#{entity.complaintDepartmentId},#{entity.questionName},#{entity.userId},#{entity.userName},#{entity.createUserId},#{entity.updateUserId},#{entity.createTime},#{entity.updateTime},#{entity.deleted})
        </foreach>
    </insert>
</mapper>

