<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pxb7.mall.trade.ass.infra.repository.db.mapper.ViolateOrderMapper">

    <resultMap id="BaseResultMap" type="com.pxb7.mall.trade.ass.infra.repository.db.entity.ViolateOrder" >
     <result  column="id" jdbcType="INTEGER" property="id" />
     <result  column="violate_id" jdbcType="VARCHAR" property="violateId" />
     <result  column="order_item_id" jdbcType="VARCHAR" property="orderItemId" />
     <result  column="refund_voucher_id" jdbcType="VARCHAR" property="refundVoucherId" />
     <result  column="violate_user_id" jdbcType="VARCHAR" property="violateUserId" />
     <result  column="promise_user_id" jdbcType="VARCHAR" property="promiseUserId" />
     <result  column="violate_user_type" jdbcType="INTEGER" property="violateUserType" />
     <result  column="violate_amount" jdbcType="BIGINT" property="violateAmount" />
     <result  column="promise_amount" jdbcType="BIGINT" property="promiseAmount" />
     <result  column="platform_amount" jdbcType="BIGINT" property="platformAmount" />
     <result  column="violate_status" jdbcType="INTEGER" property="violateStatus" />
     <result  column="receipt_status" jdbcType="INTEGER" property="receiptStatus" />
     <result  column="transfer_status" jdbcType="INTEGER" property="transferStatus" />
     <result  column="create_time" jdbcType="VARCHAR" property="createTime" />
     <result  column="update_time" jdbcType="VARCHAR" property="updateTime" />
     <result  column="is_deleted" jdbcType="INTEGER" property="isDeleted" />
    </resultMap>
        
    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into violate_order(violate_id,order_item_id,refund_voucher_id,violate_user_id,promise_user_id,violate_user_type,violate_amount,promise_amount,platform_amount,violate_status,receipt_status,transfer_status,create_time,update_time,is_deleted)
        values
        <foreach collection="entities" item="entity" separator=",">
        (#{entity.violateId},#{entity.orderItemId},#{entity.refundVoucherId},#{entity.violateUserId},#{entity.promiseUserId},#{entity.violateUserType},#{entity.violateAmount},#{entity.promiseAmount},#{entity.platformAmount},#{entity.violateStatus},#{entity.receiptStatus},#{entity.transferStatus},#{entity.createTime},#{entity.updateTime},#{entity.isDeleted})
        </foreach>
    </insert>
</mapper>

