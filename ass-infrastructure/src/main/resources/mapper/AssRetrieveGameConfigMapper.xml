<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pxb7.mall.trade.ass.infra.repository.db.mapper.AssRetrieveGameConfigMapper">

    <resultMap id="BaseResultMap" type="com.pxb7.mall.trade.ass.infra.repository.db.entity.AssRetrieveGameConfig">
        <result column="id" jdbcType="INTEGER" property="id"/>
        <result column="game_config_id" jdbcType="VARCHAR" property="gameConfigId"/>
        <result column="retrieve_days" jdbcType="INTEGER" property="retrieveDays"/>
        <result column="is_group_open" jdbcType="BOOLEAN" property="groupOpen"/>
        <result column="create_user_id" jdbcType="VARCHAR" property="createUserId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_user_id" jdbcType="VARCHAR" property="updateUserId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="is_deleted" jdbcType="BOOLEAN" property="deleted"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into
        ass_retrieve_game_config(game_config_id,retrieve_days,is_group_open,create_user_id,create_time,update_user_id,update_time,is_deleted)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.gameConfigId},#{entity.retrieveDays},#{entity.groupOpen},#{entity.createUserId},#{entity.createTime},#{entity.updateUserId},#{entity.updateTime},#{entity.deleted})
        </foreach>
    </insert>
</mapper>

