<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pxb7.mall.trade.ass.infra.repository.db.mapper.ComplaintWorkMapper">

    <resultMap id="BaseResultMap" type="com.pxb7.mall.trade.ass.infra.repository.db.entity.ComplaintWork">
        <result column="id" jdbcType="INTEGER" property="id"/>
        <result column="complaint_work_id" jdbcType="VARCHAR" property="complaintWorkId"/>
        <result column="room_id" jdbcType="VARCHAR" property="roomId"/>
        <result column="order_item_id" jdbcType="VARCHAR" property="orderItemId"/>
        <result column="complaint_title" jdbcType="VARCHAR" property="complaintTitle"/>
        <result column="complaint_channel" jdbcType="INTEGER" property="complaintChannel"/>
        <result column="complaint_phone" jdbcType="VARCHAR" property="complaintPhone"/>
        <result column="complaint_role" jdbcType="INTEGER" property="complaintRole"/>
        <result column="complaint_level" jdbcType="INTEGER" property="complaintLevel"/>
        <result column="complaint_content" jdbcType="VARCHAR" property="complaintContent"/>
        <result column="complaint_img" jdbcType="VARCHAR" property="complaintImg"/>
        <result column="complaint_state" jdbcType="INTEGER" property="complaintState"/>
        <result column="current_processor_id" jdbcType="VARCHAR" property="currentProcessorId"/>
        <result column="deal_result" jdbcType="VARCHAR" property="dealResult"/>
        <result column="note" jdbcType="VARCHAR" property="note"/>
        <result column="transferee_id" jdbcType="VARCHAR" property="transfereeId"/>
        <result column="finisher_id" jdbcType="VARCHAR" property="finisherId"/>
        <result column="finish_time" jdbcType="TIMESTAMP" property="finishTime"/>
        <result column="responsible_person" jdbcType="VARCHAR" property="responsiblePerson"/>
        <result column="is_responsibility" jdbcType="BOOLEAN" property="responsibility"/>
        <result column="department_name" jdbcType="VARCHAR" property="departmentName"/>
        <result column="question_type" jdbcType="VARCHAR" property="questionType"/>
        <result column="create_user_id" jdbcType="VARCHAR" property="createUserId"/>
        <result column="update_user_id" jdbcType="VARCHAR" property="updateUserId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="is_deleted" jdbcType="BOOLEAN" property="deleted"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into
        complaint_work(complaint_work_id,room_id,order_item_id,complaint_title,complaint_channel,complaint_phone,complaint_role,complaint_level,complaint_content,complaint_img,complaint_state,current_processor_id,deal_result,note,transferee_id,finisher_id,finish_time,responsible_person,is_responsibility,department_name,question_type,create_user_id,update_user_id,create_time,update_time,is_deleted)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.complaintWorkId},#{entity.roomId},#{entity.orderItemId},#{entity.complaintTitle},#{entity.complaintChannel},#{entity.complaintPhone},#{entity.complaintRole},#{entity.complaintLevel},#{entity.complaintContent},#{entity.complaintImg},#{entity.complaintState},#{entity.currentProcessorId},#{entity.dealResult},#{entity.note},#{entity.transfereeId},#{entity.finisherId},#{entity.finishTime},#{entity.responsiblePerson},#{entity.responsibility},#{entity.departmentName},#{entity.questionType},#{entity.createUserId},#{entity.updateUserId},#{entity.createTime},#{entity.updateTime},#{entity.deleted})
        </foreach>
    </insert>
</mapper>

