<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pxb7.mall.trade.ass.infra.repository.db.mapper.RefundVoucherMapper">

    <resultMap id="BaseResultMap" type="com.pxb7.mall.trade.ass.infra.repository.db.entity.RefundVoucher">
        <result column="id" jdbcType="BIGINT" property="id"/>
        <result column="order_item_id" jdbcType="VARCHAR" property="orderItemId"/>
        <result column="refund_voucher_id" jdbcType="VARCHAR" property="refundVoucherId"/>
        <result column="product_type" jdbcType="INTEGER" property="productType"/>
        <result column="refund_status" jdbcType="SMALLINT" property="refundStatus"/>
        <result column="refund_reason_id" jdbcType="VARCHAR" property="refundReasonId"/>
        <result column="refund_reason" jdbcType="VARCHAR" property="refundReason"/>
        <result column="is_whole_refund" jdbcType="INTEGER" property="wholeRefund"/>
        <result column="refund_type" jdbcType="INTEGER" property="refundType"/>
        <result column="receipt_voucher_id" jdbcType="VARCHAR" property="receiptVoucherId"/>
        <result column="payment_id" jdbcType="VARCHAR" property="paymentId"/>
        <result column="refund_amount" jdbcType="BIGINT" property="refundAmount"/>
        <result column="actual_refund_amount" jdbcType="BIGINT" property="actualRefundAmount"/>
        <result column="submitter" jdbcType="VARCHAR" property="submitter"/>
        <result column="submitter_type" jdbcType="SMALLINT" property="submitterType"/>
        <result column="close_type" jdbcType="SMALLINT" property="closeType"/>
        <result column="reject_reason" jdbcType="VARCHAR" property="rejectReason"/>
        <result column="apply_time" jdbcType="TIMESTAMP" property="applyTime"/>
        <result column="finish_time" jdbcType="TIMESTAMP" property="finishTime"/>
        <result column="refund_channel" jdbcType="INTEGER" property="refundChannel"/>
        <result column="refund_account" jdbcType="VARCHAR" property="refundAccount"/>
        <result column="buyer_name" jdbcType="VARCHAR" property="buyerName"/>
        <result column="audit_status" jdbcType="SMALLINT" property="auditStatus"/>
        <result column="audit_user" jdbcType="VARCHAR" property="auditUser"/>
        <result column="audit_remark" jdbcType="VARCHAR" property="auditRemark"/>
        <result column="audit_img" jdbcType="VARCHAR" property="auditImg"/>
        <result column="execute_user" jdbcType="VARCHAR" property="executeUser"/>
        <result column="account_name" jdbcType="VARCHAR" property="accountName"/>
        <result column="company_account" jdbcType="VARCHAR" property="companyAccount"/>
        <result column="ext_info" jdbcType="VARCHAR" property="extInfo"/>
        <result column="create_user_id" jdbcType="VARCHAR" property="createUserId"/>
        <result column="update_user_id" jdbcType="VARCHAR" property="updateUserId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="is_deleted" jdbcType="BOOLEAN" property="deleted"/>
    </resultMap>
</mapper>

