<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pxb7.mall.trade.ass.infra.repository.db.mapper.AssWorkOrderLogMapper">

    <resultMap id="BaseResultMap" type="com.pxb7.mall.trade.ass.infra.repository.db.entity.AssWorkOrderLog">
        <result column="id" jdbcType="INTEGER" property="id"/>
        <result column="work_order_id" jdbcType="VARCHAR" property="workOrderId"/>
        <result column="work_order_log_id" jdbcType="VARCHAR" property="workOrderLogId"/>
        <result column="order_item_id" jdbcType="VARCHAR" property="orderItemId"/>
        <result column="title" jdbcType="VARCHAR" property="title"/>
        <result column="ass_type" jdbcType="INTEGER" property="assType"/>
        <result column="show_type" jdbcType="INTEGER" property="showType"/>
        <result column="content" jdbcType="VARCHAR" property="content"/>
        <result column="admin_content" jdbcType="VARCHAR" property="adminContent"/>
        <result column="add_way" jdbcType="INTEGER" property="addWay"/>
        <result column="node_id" jdbcType="VARCHAR" property="nodeId"/>
        <result column="node_desc" jdbcType="VARCHAR" property="nodeDesc"/>
        <result column="create_user_id" jdbcType="VARCHAR" property="createUserId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_user_id" jdbcType="VARCHAR" property="updateUserId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="is_deleted" jdbcType="BOOLEAN" property="deleted"/>
        <result column="notice_type" jdbcType="INTEGER" property="noticeType"/>
        <result column="join_group_msg" jdbcType="VARCHAR" property="joinGroupMsg"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into
        ass_work_order_log(work_order_id,work_order_log_id,order_item_id,title,ass_type,show_type,content,admin_content,add_way,node_id,node_desc,create_user_id,create_time,update_user_id,update_time,is_deleted,notice_type,join_group_msg)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.workOrderId},#{entity.workOrderLogId},#{entity.orderItemId},#{entity.title},#{entity.assType},#{entity.showType},#{entity.content},#{entity.adminContent},#{entity.addWay},#{entity.nodeId},#{entity.nodeDesc},#{entity.createUserId},#{entity.createTime},#{entity.updateUserId},#{entity.updateTime},#{entity.deleted},#{entity.noticeType},#{entity.joinGroupMsg})
        </foreach>
    </insert>
</mapper>

