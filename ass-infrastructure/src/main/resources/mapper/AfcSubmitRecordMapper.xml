<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pxb7.mall.trade.ass.infra.repository.db.mapper.AfcSubmitRecordMapper">

    <resultMap id="BaseResultMap" type="com.pxb7.mall.trade.ass.infra.repository.db.entity.AfcSubmitRecord">
        <result column="id" jdbcType="INTEGER" property="id"/>
        <result column="submit_record_id" jdbcType="VARCHAR" property="submitRecordId"/>
        <result column="order_item_id" jdbcType="VARCHAR" property="orderItemId"/>
        <result column="room_id" jdbcType="VARCHAR" property="roomId"/>
        <result column="game_id" jdbcType="VARCHAR" property="gameId"/>
        <result column="game_name" jdbcType="VARCHAR" property="gameName"/>
        <result column="product_id" jdbcType="VARCHAR" property="productId"/>
        <result column="product_unique_no" jdbcType="VARCHAR" property="productUniqueNo"/>
        <result column="afc_type" jdbcType="INTEGER" property="afcType"/>
        <result column="question_id" jdbcType="VARCHAR" property="questionId"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="submitter_account" jdbcType="VARCHAR" property="submitterAccount"/>
        <result column="create_user_id" jdbcType="VARCHAR" property="createUserId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_user_id" jdbcType="VARCHAR" property="updateUserId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="is_deleted" jdbcType="INTEGER" property="isDeleted"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into
        afc_submit_record(submit_record_id,order_item_id,room_id,game_id,game_name,product_id,product_unique_no,afc_type,question_id,remark,submitter_account,create_user_id,create_time,update_user_id,update_time,is_deleted)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.submitRecordId},#{entity.orderItemId},#{entity.roomId},#{entity.gameId},#{entity.gameName},#{entity.productId},#{entity.productUniqueNo},#{entity.afcType},#{entity.questionId},#{entity.remark},#{entity.submitterAccount},#{entity.createUserId},#{entity.createTime},#{entity.updateUserId},#{entity.updateTime},#{entity.isDeleted})
        </foreach>
    </insert>
</mapper>

