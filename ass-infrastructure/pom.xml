<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.pxb7.mall.trade</groupId>
        <artifactId>ass</artifactId>
        <version>${revision}</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>ass-infrastructure</artifactId>
    <packaging>jar</packaging>
    <name>ass-infrastructure</name>
    <properties>
        <maven.deploy.skip>true</maven.deploy.skip>
    </properties>
    <dependencies>

        <dependency>
            <groupId>com.jd.open.api</groupId>
            <artifactId>open-api-sdk</artifactId>
        </dependency>

        <dependency>
            <groupId>com.pxb7.mall.components</groupId>
            <artifactId>app-config-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.pxb7.mall.trade</groupId>
            <artifactId>ass-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pxb7.mall.im</groupId>
            <artifactId>im-c-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pxb7.mall.merchant</groupId>
            <artifactId>merchant-admin-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pxb7.mall.user</groupId>
            <artifactId>user-c-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pxb7.mall.common</groupId>
            <artifactId>common-support-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pxb7.mall.trade</groupId>
            <artifactId>order-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pxb7.mall.common</groupId>
            <artifactId>common-support-client</artifactId>
        </dependency>
        <!-- 京东银行卡支付 -->
        <dependency>
            <groupId>com.wangyin.plat-arch</groupId>
            <artifactId>wyaks-security</artifactId>
        </dependency>
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcprov-jdk15on</artifactId>
        </dependency>
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcpkix-jdk15on</artifactId>
        </dependency>

        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.fastjson2</groupId>
            <artifactId>fastjson2</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
        </dependency>
        <dependency>
            <groupId>com.squareup.retrofit2</groupId>
            <artifactId>retrofit</artifactId>
        </dependency>
        <dependency>
            <groupId>com.squareup.retrofit2</groupId>
            <artifactId>converter-jackson</artifactId>
        </dependency>
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>logging-interceptor</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba.cola</groupId>
            <artifactId>cola-component-exception</artifactId>
        </dependency>

        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.dubbo</groupId>
            <artifactId>dubbo-spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.dubbo</groupId>
            <artifactId>dubbo-common</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.pxb7.mall.components</groupId>
            <artifactId>dubbo-instance-register-starter</artifactId>
        </dependency>
        <!--redisson-->
        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pxb7.mall.components</groupId>
            <artifactId>px-cache-redisson-util-starter</artifactId>
        </dependency>
        <!--        <dependency>-->
        <!--            <groupId>com.pxb7.mall.components</groupId>-->
        <!--            <artifactId>px-cache-starter-redisson</artifactId>-->
        <!--        </dependency>-->
        <dependency>
            <groupId>org.apache.shardingsphere</groupId>
            <artifactId>shardingsphere-jdbc</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.shardingsphere</groupId>
                    <artifactId>shardingsphere-test-util</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.shardingsphere</groupId>
                    <artifactId>shardingsphere-parser-sql-oracle</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.shardingsphere</groupId>
                    <artifactId>shardingsphere-parser-sql-sqlserver</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.shardingsphere</groupId>
                    <artifactId>shardingsphere-parser-sql-opengauss</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.pxb7.mall.components</groupId>
            <artifactId>idgen-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-elasticsearch</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pxb7.mall.components</groupId>
            <artifactId>px-elasticsearch-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.pxb7.mall.components</groupId>
            <artifactId>exception-spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.rocketmq</groupId>
            <artifactId>rocketmq-v5-client-spring-boot-starter</artifactId>
        </dependency>

        <!--mq幂等-->
        <!--        <dependency>-->
        <!--            <groupId>com.pxb7.mall.components</groupId>-->
        <!--            <artifactId>pxmq-client</artifactId>-->
        <!--        </dependency>-->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pxb7.mall.components</groupId>
            <artifactId>logback-springboot3-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pxb7.mall.components</groupId>
            <artifactId>sentryext-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pxb7.mall.components</groupId>
            <artifactId>sentinel-component-starter</artifactId>
        </dependency>

        <!--  YeePay start -->
        <dependency>
            <groupId>com.yeepay.yop.sdk</groupId>
            <artifactId>yop-java-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yeepay.yop.sdk</groupId>
            <artifactId>yop-java-sdk-crypto-inter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yeepay.yop.sdk</groupId>
            <artifactId>yop-java-sdk-crypto-gm</artifactId>
        </dependency>
        <!--  YeePay end -->

        <dependency>
            <groupId>com.pxb7.mall.user</groupId>
            <artifactId>user-c-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pxb7.mall.pay</groupId>
            <artifactId>pay-client</artifactId>
        </dependency>

    </dependencies>
</project>
