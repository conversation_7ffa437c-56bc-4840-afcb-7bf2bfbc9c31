## dev的
#nacos:
#  address: ************:8848
#  username: develop
#  password: develop2024.
#  namespace: pxb7_dev

#### test2的
#nacos:
#  address: ************:8848
#  username: pxb7_test
#  password: pxb7_test
#  namespace: pxb7_test2

nacos:
  address: ************:8848
  username: pxb7_test
  password: pxb7_test
  namespace: pxb7_test



spring:
  main:
    allow-circular-references: true
    banner-mode: off
  dynamic:
    tp:
      enabledBanner: false  # 不打印banner图
  application:
    name: ass
  profiles:
    active: test
  datasource:
    driver-class-name: org.apache.shardingsphere.driver.ShardingSphereDriver
    url: jdbc:shardingsphere:classpath:sharding-jdbc-${spring.profiles.active}.yaml
    # 这边是需要配置的
    hikari:
      pool-name: ass-pool
      minimum-idle: 150
      maximum-pool-size: 2500
  cloud:
    nacos:
      username: ${nacos.username}
      password: ${nacos.password}
      discovery:
        server-addr: ${nacos.address}
        namespace: ${nacos.namespace}
      config:
        server-addr: ${nacos.address}
        # 配置文件格式
        file-extension: yaml
        # 共享配置
        shared-configs:
          - dataId: ${spring.application.name}-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
          - dataId: trade-common-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
            refresh: false  # 必须配置，否则自动刷新不生效
        extension-configs:
          - dataId: ${spring.application.name}-dtp-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
            refresh: false  # 必须配置，否则自动刷新不生效
        refresh-enabled: false
        namespace: ${nacos.namespace}
  config:
    import: classpath:sentinel-component.yaml
#server:
#  port: 8086
#  tomcat:
#    uri-encoding: UTF-8
#  servlet:
#    # 项目级别公共前缀
#    context-path: /ass

#dubbo 具体配置待定
dubbo:
  protocol:
    # dubbo 协议
    name: dubbo
    # dubbo 协议端口
    port: -1
  registry:
    address: nacos://${nacos.address} #注册中心地址，相当于nacos的服务地址127.0.0.1:8848
    use-as-config-center: false
    use-as-metadata-center: false
    username: ${nacos.username}
    password: ${nacos.password}
    parameters:
      namespace: ${nacos.namespace}
  application:
    qos-enable: false #dubbo运维服务是否开启
  consumer:
    check: false #启动时是否检查依赖的服务
  provider:
    filter: -exception,-dubboExceptionFilter

mybatis-plus:
  mapper-locations: classpath*:/mapper/**.xml
  global-config:
    banner: false

management:
  endpoints:
    web:
      exposure:
        include: health,info
  endpoint:
    health:
      show-details: always

# 日志配置信息
logging:
  config: classpath:logback-spring.xml

# 动态日志级别变更
logback:
  enabled: true
  config:
    fileName: ${spring.application.name}-logback-dynamic.json
    levelKey: level


server:
  port: 8086
  shutdown: graceful
  tomcat:
    uri-encoding: UTF-8
  servlet:
    # 项目级别公共前缀
    context-path: /api/ass

# 飞书预警机器人
feishu:
  payBot:
    hook:
      url: https://open.feishu.cn/open-apis/bot/v2/hook/4ec42670-fe11-41d8-86e9-175eee19b995
      secret.key: 97LoGFcZxD17a9zkByOB0g

aes:
  secretKey: qjGjK5hKxIpN27giOZjsHQ==