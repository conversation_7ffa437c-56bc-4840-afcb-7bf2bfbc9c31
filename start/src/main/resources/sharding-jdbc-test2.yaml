dataSources:
  trade_db:
    url: *************************************************************************************************************************************************************************************************
    username: pxb7_test2
    password: pxb7_test2#aCNBbsvj
    driverClassName: com.mysql.cj.jdbc.Driver
    dataSourceClassName: com.zaxxer.hikari.HikariDataSource
    validationTimeout: 5000
    connectionTimeout: 30000
    maximumPoolSize: 50
    minimumIdle: 1
    idleTimeout: 600000
    leakDetectionThreshold: 0
    keepAliveTime: 30000
  db0:
    url: ****************************************************************************************************************************************************************************************************
    username: pxb7_test2
    password: pxb7_test2#aCNBbsvj
    driverClassName: com.mysql.cj.jdbc.Driver
    dataSourceClassName: com.zaxxer.hikari.HikariDataSource
    validationTimeout: 5000
    connectionTimeout: 30000
    maximumPoolSize: 50
    minimumIdle: 1
    idleTimeout: 600000
    leakDetectionThreshold: 0
    keepAliveTime: 30000
  db1:
    url: ****************************************************************************************************************************************************************************************************
    username: pxb7_test2
    password: pxb7_test2#aCNBbsvj
    driverClassName: com.mysql.cj.jdbc.Driver
    dataSourceClassName: com.zaxxer.hikari.HikariDataSource
    validationTimeout: 5000
    connectionTimeout: 30000
    maximumPoolSize: 50
    minimumIdle: 1
    idleTimeout: 600000
    leakDetectionThreshold: 0
    keepAliveTime: 30000
  db2:
    url: ****************************************************************************************************************************************************************************************************
    username: pxb7_test2
    password: pxb7_test2#aCNBbsvj
    driverClassName: com.mysql.cj.jdbc.Driver
    dataSourceClassName: com.zaxxer.hikari.HikariDataSource
    validationTimeout: 5000
    connectionTimeout: 30000
    maximumPoolSize: 50
    minimumIdle: 1
    idleTimeout: 600000
    leakDetectionThreshold: 0
    keepAliveTime: 30000
  db3:
    url: ****************************************************************************************************************************************************************************************************
    username: pxb7_test2
    password: pxb7_test2#aCNBbsvj
    driverClassName: com.mysql.cj.jdbc.Driver
    dataSourceClassName: com.zaxxer.hikari.HikariDataSource
    validationTimeout: 5000
    connectionTimeout: 30000
    maximumPoolSize: 50
    minimumIdle: 1
    idleTimeout: 600000
    leakDetectionThreshold: 0
    keepAliveTime: 30000
  db4:
    url: ****************************************************************************************************************************************************************************************************
    username: pxb7_test2
    password: pxb7_test2#aCNBbsvj
    driverClassName: com.mysql.cj.jdbc.Driver
    dataSourceClassName: com.zaxxer.hikari.HikariDataSource
    validationTimeout: 5000
    connectionTimeout: 30000
    maximumPoolSize: 50
    minimumIdle: 1
    idleTimeout: 600000
    leakDetectionThreshold: 0
    keepAliveTime: 30000
  db5:
    url: ****************************************************************************************************************************************************************************************************
    username: pxb7_test2
    password: pxb7_test2#aCNBbsvj
    driverClassName: com.mysql.cj.jdbc.Driver
    dataSourceClassName: com.zaxxer.hikari.HikariDataSource
    validationTimeout: 5000
    connectionTimeout: 30000
    maximumPoolSize: 50
    minimumIdle: 1
    idleTimeout: 600000
    leakDetectionThreshold: 0
    keepAliveTime: 30000
  db6:
    url: ****************************************************************************************************************************************************************************************************
    username: pxb7_test2
    password: pxb7_test2#aCNBbsvj
    driverClassName: com.mysql.cj.jdbc.Driver
    dataSourceClassName: com.zaxxer.hikari.HikariDataSource
    validationTimeout: 5000
    connectionTimeout: 30000
    maximumPoolSize: 50
    minimumIdle: 1
    idleTimeout: 600000
    leakDetectionThreshold: 0
    keepAliveTime: 30000
  db7:
    url: ****************************************************************************************************************************************************************************************************
    username: pxb7_test2
    password: pxb7_test2#aCNBbsvj
    driverClassName: com.mysql.cj.jdbc.Driver
    dataSourceClassName: com.zaxxer.hikari.HikariDataSource
    validationTimeout: 5000
    connectionTimeout: 30000
    maximumPoolSize: 50
    minimumIdle: 1
    idleTimeout: 600000
    leakDetectionThreshold: 0
    keepAliveTime: 30000
  db8:
    url: ****************************************************************************************************************************************************************************************************
    username: pxb7_test2
    password: pxb7_test2#aCNBbsvj
    driverClassName: com.mysql.cj.jdbc.Driver
    dataSourceClassName: com.zaxxer.hikari.HikariDataSource
    validationTimeout: 5000
    connectionTimeout: 30000
    maximumPoolSize: 50
    minimumIdle: 1
    idleTimeout: 600000
    leakDetectionThreshold: 0
    keepAliveTime: 30000
  db9:
    url: ****************************************************************************************************************************************************************************************************
    username: pxb7_test2
    password: pxb7_test2#aCNBbsvj
    driverClassName: com.mysql.cj.jdbc.Driver
    dataSourceClassName: com.zaxxer.hikari.HikariDataSource
    validationTimeout: 5000
    connectionTimeout: 30000
    maximumPoolSize: 50
    minimumIdle: 1
    idleTimeout: 600000
    leakDetectionThreshold: 0
    keepAliveTime: 30000
  db10:
    url: ****************************************************************************************************************************************************************************************************
    username: pxb7_test2
    password: pxb7_test2#aCNBbsvj
    driverClassName: com.mysql.cj.jdbc.Driver
    dataSourceClassName: com.zaxxer.hikari.HikariDataSource
    validationTimeout: 5000
    connectionTimeout: 30000
    maximumPoolSize: 50
    minimumIdle: 1
    idleTimeout: 600000
    leakDetectionThreshold: 0
    keepAliveTime: 30000
  db11:
    url: ****************************************************************************************************************************************************************************************************
    username: pxb7_test2
    password: pxb7_test2#aCNBbsvj
    driverClassName: com.mysql.cj.jdbc.Driver
    dataSourceClassName: com.zaxxer.hikari.HikariDataSource
    validationTimeout: 5000
    connectionTimeout: 30000
    maximumPoolSize: 50
    minimumIdle: 1
    idleTimeout: 600000
    leakDetectionThreshold: 0
    keepAliveTime: 30000
  db12:
    url: ****************************************************************************************************************************************************************************************************
    username: pxb7_test2
    password: pxb7_test2#aCNBbsvj
    driverClassName: com.mysql.cj.jdbc.Driver
    dataSourceClassName: com.zaxxer.hikari.HikariDataSource
    validationTimeout: 5000
    connectionTimeout: 30000
    maximumPoolSize: 50
    minimumIdle: 1
    idleTimeout: 600000
    leakDetectionThreshold: 0
    keepAliveTime: 30000
  db13:
    url: ****************************************************************************************************************************************************************************************************
    username: pxb7_test2
    password: pxb7_test2#aCNBbsvj
    driverClassName: com.mysql.cj.jdbc.Driver
    dataSourceClassName: com.zaxxer.hikari.HikariDataSource
    validationTimeout: 5000
    connectionTimeout: 30000
    maximumPoolSize: 50
    minimumIdle: 1
    idleTimeout: 600000
    leakDetectionThreshold: 0
    keepAliveTime: 30000
  db14:
    url: ****************************************************************************************************************************************************************************************************
    username: pxb7_test2
    password: pxb7_test2#aCNBbsvj
    driverClassName: com.mysql.cj.jdbc.Driver
    dataSourceClassName: com.zaxxer.hikari.HikariDataSource
    validationTimeout: 5000
    connectionTimeout: 30000
    maximumPoolSize: 50
    minimumIdle: 1
    idleTimeout: 600000
    leakDetectionThreshold: 0
    keepAliveTime: 30000
  db15:
    url: ****************************************************************************************************************************************************************************************************
    username: pxb7_test2
    password: pxb7_test2#aCNBbsvj
    driverClassName: com.mysql.cj.jdbc.Driver
    dataSourceClassName: com.zaxxer.hikari.HikariDataSource
    validationTimeout: 5000
    connectionTimeout: 30000
    maximumPoolSize: 50
    minimumIdle: 1
    idleTimeout: 600000
    leakDetectionThreshold: 0
    keepAliveTime: 30000


mode:
  type: Standalone

rules:
  - !SINGLE
    tables:
      - "trade_db.*"
  - !SHARDING
    tables:
      # 子订单表
      order_item:
        actualDataNodes: db$->{0..15}.order_item
      # 子订单扩展表
      order_item_extend:
        actualDataNodes: db$->{0..15}.order_item_extend
      # 子订单手续费表
      order_item_fee:
        actualDataNodes: db$->{0..15}.order_item_fee
      # 收款单
      receipt_voucher:
        actualDataNodes: db$->{0..15}.receipt_voucher
        databaseStrategy:
          standard: #  standard 标准、complex 复合、HINT
            shardingColumn: receipt_voucher_id
            shardingAlgorithmName: receipt_voucher_inline
      # 收款单资金明细
      receipt_voucher_detail:
        actualDataNodes: db$->{0..15}.receipt_voucher_detail
      # 订单操作记录表
      order_operate:
        actualDataNodes: db$->{0..15}.order_operate
      # 支付记录表
      pay_log:
        actualDataNodes: db$->{0..15}.pay_log
        databaseStrategy:
          complex:
            shardingColumns: payment_id,pay_log_id
            shardingAlgorithmName: customer_complex_sharding_algorithm
      payment:
        actualDataNodes: db$->{0..15}.payment
        databaseStrategy:
          complex:
            shardingColumns: payment_id,order_id,voucher_id,business_id
            shardingAlgorithmName: customer_complex_sharding_algorithm
      trade_command:
        actualDataNodes: db$->{0..15}.trade_command
        databaseStrategy:
          standard:
            shardingColumn: command_id
            shardingAlgorithmName: trade_command_inline
      # 违约金
      violate_order:
        actualDataNodes: db$->{0..15}.violate_order
        databaseStrategy:
          complex:
            shardingColumns: order_item_id,refund_voucher_id,violate_id
            shardingAlgorithmName: customer_complex_sharding_algorithm
      # 违约支付单表
      violate_pay_record:
        actualDataNodes: db$->{0..15}.violate_pay_record
        databaseStrategy:
          complex:
            shardingColumns: order_item_id,violate_id,record_id
            shardingAlgorithmName: customer_complex_sharding_algorithm
    defaultDatabaseStrategy:
      standard: #  standard 标准、complex 复合、HINT
        shardingColumn: order_item_id
        shardingAlgorithmName: order_item_inline
    defaultAuditStrategy: # 默认审计策略
      auditorNames:
        - sharding_key_required_auditor
      allowHintDisable: true
    auditors:
      sharding_key_required_auditor:
        type: CUSTOMER_SHARDING_AUDIT_ALGORITHM
    #    bindingTables:
    #      - order_item, ext_voucher,ext_voucher_detail, order_item_extend, order_item_fee, order_item_indemnity, order_item_promotion, order_item_sincerity_sell_service,order_deliver_node,order_item_payment
    #      - main_order,receipt_voucher,receipt_voucher_detail
    #      - payment, pay_log, pay_transfer
    shardingAlgorithms:
      receipt_voucher_inline:
        type: INLINE
        props:
          algorithm-expression: db$->{Long.parseLong(receipt_voucher_id[-4..-1]) % 16} # 分片算法表达式
          allow-range-query-with-inline-sharding: true # 允许范围查询
      order_item_inline:
        type: INLINE
        props:
          algorithm-expression: db$->{Long.parseLong(order_item_id[-4..-1]) % 16} # 分片算法表达式
          allow-range-query-with-inline-sharding: true # 允许范围查询
      trade_command_inline:
        type: INLINE
        props:
          algorithm-expression: db$->{Long.parseLong(command_id[-4..-1]) % 16} # 分片算法表达式
          allow-range-query-with-inline-sharding: true # 允许范围查询
      customer_complex_sharding_algorithm:
        type: CLASS_BASED
        props:
          strategy: COMPLEX  #  STANDARD 标准、COMPLEX 复合、HINT
          algorithmClassName: com.pxb7.mall.trade.ass.infra.config.OrderComplexShardingAlgorithm # 自定义类
          DB_NAME: db  # db名称
          allow-range-query-with-inline-sharding: false # 自定义属性 （是否允许范围查询）, 没空写 有必要搞这个吗?

# 文档: 用户手册, 通用配置, 属性配置
props:
  sql-show: false

