USE
pxb7_trade;
SET NAMES utf8mb4;

DROP TABLE IF EXISTS `afc_question_conf`;
CREATE TABLE `afc_question_conf`
(
    `id`             bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '自增id',
    `question_id`    varchar(64) NOT NULL DEFAULT '' COMMENT '问题id',
    `question_name`  varchar(64) NOT NULL DEFAULT '' COMMENT '问题名称',
    `question_type`  smallint unsigned NOT NULL DEFAULT '0' COMMENT '问题类型 1找回 2纠纷',
    `sort`           smallint unsigned NOT NULL DEFAULT '0' COMMENT '排序值',
    `create_user_id` varchar(64) NOT NULL COMMENT '创建人ID',
    `create_time`    datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP (3) COMMENT '创建时间',
    `update_user_id` varchar(64) NOT NULL DEFAULT '' COMMENT '更新人id',
    `update_time`    datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP (3) ON UPDATE CURRENT_TIMESTAMP (3) COMMENT '更新时间',
    `is_deleted`     tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除 1:已删除 0:未删除',
    PRIMARY KEY (`id`) USING BTREE,
    KEY              `idx_question_id` (`question_id`) USING BTREE
) ENGINE=InnoDB COMMENT='售后问题配置';


DROP TABLE IF EXISTS `afc_submit_record`;
CREATE TABLE `afc_submit_record`
(
    `id`                bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '自增id',
    `submit_record_id`  varchar(64) NOT NULL DEFAULT '' COMMENT '提交记录id',
    `order_item_id`     varchar(64) NOT NULL DEFAULT '' COMMENT '订单id',
    `room_id`           varchar(64) NOT NULL DEFAULT '' COMMENT '房间id',
    `game_id`           varchar(64) NOT NULL DEFAULT '' COMMENT '游戏ID',
    `game_name`         varchar(90) NOT NULL DEFAULT '' COMMENT '游戏名称',
    `product_id`        varchar(90) NOT NULL DEFAULT '' COMMENT '商品id',
    `product_unique_no` varchar(64) NOT NULL COMMENT '商品编码',
    `nickname`          varchar(64) NOT NULL DEFAULT '' COMMENT '用户昵称',
    `phone`             varchar(32) NOT NULL DEFAULT '' COMMENT '手机号',
    `afc_type`          smallint unsigned NOT NULL DEFAULT '0' COMMENT '售后类型 1找回 2纠纷',
    `question_id`       varchar(64) NOT NULL DEFAULT '' COMMENT '售后问题id',
    `remark`            varchar(500)         DEFAULT NULL COMMENT '备注(其他问题，用户输入的内容)',
    `create_user_id`    varchar(64) NOT NULL COMMENT '创建人ID',
    `create_time`       datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP (3) COMMENT '创建时间',
    `update_user_id`    varchar(64) NOT NULL DEFAULT '' COMMENT '更新人id',
    `update_time`       datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP (3) ON UPDATE CURRENT_TIMESTAMP (3) COMMENT '更新时间',
    `is_deleted`        tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除 1:已删除 0:未删除',
    PRIMARY KEY (`id`) USING BTREE,
    KEY                 `idx_question_id` (`question_id`) USING BTREE,
    KEY                 `idx_order_item_id` (`order_item_id`) USING BTREE,
    KEY                 `idx_game_id` (`game_id`) USING BTREE,
    KEY                 `idx_product_unique_no` (`product_unique_no`) USING BTREE,
    KEY                 `idx_room_id` (`room_id`) USING BTREE,
    KEY                 `idx_create_time` (`create_time`) USING BTREE
) ENGINE=InnoDB COMMENT='售后提交记录';



