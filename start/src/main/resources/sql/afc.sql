USE pxb7_trade;
SET NAMES utf8mb4;

CREATE TABLE `ass_answer`
(
    `id`             bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '自增id',
    `answer_id`      varchar(64) NOT NULL DEFAULT '' COMMENT '主键id',
    `work_order_id`  varchar(64) NOT NULL DEFAULT '' COMMENT '工单id',
    `order_item_id`  varchar(64) NOT NULL DEFAULT '' COMMENT '订单编号',
    `answer`         text        NOT NULL COMMENT '答案',
    `create_time`    datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP (3) COMMENT '创建时间',
    `update_user_id` varchar(32) NOT NULL DEFAULT '' COMMENT '更新人id',
    `update_time`    datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP (3) ON UPDATE CURRENT_TIMESTAMP (3) COMMENT '更新时间',
    `is_deleted`     tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除 1:已删除 0:未删除',
    `create_user_id` varchar(32) NOT NULL DEFAULT '' COMMENT '创建人id',
    PRIMARY KEY (`id`) USING BTREE,
    KEY              `idx_answer_id` (`answer_id`),
    KEY              `idx_order_item_id` (`order_item_id`),
    KEY              `idx_work_order_id` (`work_order_id`)
) ENGINE = InnoDB  COMMENT = '售后问题回答记录';

CREATE TABLE `ass_claim`
(
    `id`                 bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '自增id',
    `ass_claim_id`       varchar(64)  NOT NULL DEFAULT '' COMMENT '主键ID',
    `claim_no`           varchar(255) NOT NULL DEFAULT '' COMMENT '赔付编号',
    `claim_amount`       bigint       NOT NULL DEFAULT '0' COMMENT '赔付金额',
    `ass_type`           smallint unsigned NOT NULL DEFAULT '0' COMMENT '售后类型1找回2纠纷',
    `game_id`            varchar(64)  NOT NULL DEFAULT '' COMMENT '游戏业务ID',
    `game_name`          varchar(255) NOT NULL DEFAULT '' COMMENT '游戏名称',
    `product_id`         varchar(64)  NOT NULL DEFAULT '' COMMENT '商品id',
    `product_unique_no`  varchar(64)  NOT NULL DEFAULT '' COMMENT '商品编码',
    `order_item_id`      varchar(64)  NOT NULL DEFAULT '' COMMENT '订单id',
    `work_order_id`      varchar(64)  NOT NULL DEFAULT '' COMMENT '工单ID',
    `work_order_no`      varchar(128) NOT NULL DEFAULT '' COMMENT '工单编号',
    `status`             smallint unsigned NOT NULL DEFAULT '1' COMMENT '放款状态1待赔付审核2待财务审核3线上打款中4打款成功5打款失败',
    `examine_user_id`    varchar(64)           DEFAULT NULL COMMENT '审核人ID',
    `examine_time`       datetime(3) DEFAULT NULL COMMENT '审核时间',
    `examine_reason`     varchar(255) NOT NULL DEFAULT '' COMMENT '审核驳回原因',
    `proposer_user_id`   varchar(64)  NOT NULL DEFAULT '' COMMENT '申请人ID',
    `proposer_reason`    varchar(255) NOT NULL DEFAULT '' COMMENT '申请原因',
    `account_type`       smallint unsigned NOT NULL DEFAULT '0' COMMENT '账户类型1个人2公司',
    `bank_code`          varchar(32)  NOT NULL DEFAULT '' COMMENT '银行code',
    `payment_channel`    smallint unsigned NOT NULL DEFAULT '1' COMMENT '支付渠道1线上2线下',
    `payer_name`         varchar(90)  NOT NULL DEFAULT '' COMMENT '收款姓名',
    `account`            varchar(90)  NOT NULL DEFAULT '' COMMENT '收款账号',
    `pay_way`            smallint unsigned NOT NULL DEFAULT '0' COMMENT '收款方式1支付宝2微信3银行卡',
    `qr_code`            varchar(255) NOT NULL DEFAULT '' COMMENT '用户的微信二维码',
    `payment_account_id` varchar(64)  NOT NULL DEFAULT '' COMMENT '付款账户ID',
    `finance_account_id` varchar(64)  NOT NULL DEFAULT '' COMMENT '财务ID',
    `finance_time`       datetime(3) DEFAULT NULL COMMENT '财务审核时间',
    `reject_reason`      varchar(255) NOT NULL DEFAULT '' COMMENT '财务驳回原因',
    `complete_time`      datetime(3) DEFAULT NULL COMMENT '完成时间',
    `proof_list`         text COMMENT '放款凭证列表',
    `cashier_account_id` varchar(64)  NOT NULL DEFAULT '' COMMENT '出纳ID',
    `cashier_time`       datetime(3) DEFAULT NULL COMMENT '出纳审核时间',
    `cashier_reason`     varchar(255) NOT NULL DEFAULT '' COMMENT '出纳驳回原因',
    `source_type`        smallint unsigned NOT NULL DEFAULT '1' COMMENT '运营渠道1螃蟹2支付宝小程序',
    `create_user_id`     varchar(64)  NOT NULL DEFAULT '' COMMENT '创建人id',
    `create_time`        datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP (3) COMMENT '创建时间',
    `update_user_id`     varchar(64)  NOT NULL DEFAULT '' COMMENT '更新人id',
    `update_time`        datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP (3) ON UPDATE CURRENT_TIMESTAMP (3) COMMENT '更新时间',
    `is_deleted`         tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除 1:已删除 0:未删除',
    PRIMARY KEY (`id`) USING BTREE,
    KEY                  `idx_work_order_id` (`work_order_id`) USING BTREE,
    KEY                  `idx_ass_claim_id` (`ass_claim_id`),
    KEY                  `idx_claim_no` (`claim_no`),
    KEY                  `idx_order_item_id` (`order_item_id`),
    KEY                  `idx_product_id` (`product_id`),
    KEY                  `idx_product_unique_no` (`product_unique_no`),
    KEY                  `idx_work_order_no` (`work_order_no`)
) ENGINE = InnoDB COMMENT = '售后赔付管理';


CREATE TABLE `ass_dispute_work`
(
    `id`                bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '自增id',
    `ass_dispute_id`    varchar(64)  NOT NULL DEFAULT '' COMMENT '主键id',
    `work_order_no`     varchar(64)  NOT NULL DEFAULT '' COMMENT '工单编号',
    `order_item_id`     varchar(64)  NOT NULL DEFAULT '' COMMENT '订单ID',
    `product_id`        varchar(64)  NOT NULL DEFAULT '' COMMENT '商品id',
    `product_unique_no` varchar(64)  NOT NULL DEFAULT '' COMMENT '商品编码',
    `product_type`      smallint     NOT NULL COMMENT '商品类型: 1账号 2充值 3金币 4装备 5初始号 20诚心卖',
    `game_id`           varchar(64)  NOT NULL DEFAULT '' COMMENT '游戏ID',
    `status`            smallint unsigned NOT NULL DEFAULT '1' COMMENT '工单状态1处理中2处理完成3已关闭',
    `proposer_user_id`  varchar(64)  NOT NULL DEFAULT '' COMMENT '申请人id',
    `proposer_type`     smallint unsigned NOT NULL DEFAULT '1' COMMENT '申请人1用户主动发起2客服发起',
    `dispute_user_id`   varchar(64)  NOT NULL DEFAULT '' COMMENT '纠纷售后人员',
    `handle_status`     smallint unsigned NOT NULL DEFAULT '0' COMMENT '处理状态1正常完结2平台规则完结3赔付安抚完结',
    `claim_amount`      bigint unsigned NOT NULL DEFAULT '0' COMMENT '赔付金额',
    `claim_status`      smallint unsigned NOT NULL DEFAULT '0' COMMENT '赔付状态0无1待审核2已赔付',
    `sponsor`           smallint unsigned NOT NULL DEFAULT '1' COMMENT '工单发起来源1用户2客服',
    `complete_time`     datetime(3) DEFAULT NULL COMMENT '完成时间',
    `classify_first`    varchar(255) NOT NULL DEFAULT '' COMMENT '问题归类一级目录',
    `classify_second`   varchar(255) NOT NULL DEFAULT '' COMMENT '问题归类二级目录',
    `recover_amount`    bigint       NOT NULL DEFAULT '0' COMMENT '追回金额',
    `recover_status`    smallint unsigned NOT NULL DEFAULT '0' COMMENT '追回号款状态0无1部分追回2全追回',
    `data_source`       smallint unsigned NOT NULL DEFAULT '1' COMMENT '数据来源1IM私聊2企业微信3投诉转交4其他',
    `source_type`       smallint unsigned NOT NULL DEFAULT '1' COMMENT '渠道来源1螃蟹2支付宝小程序',
    `create_user_id`    varchar(64)  NOT NULL DEFAULT '' COMMENT '创建人id',
    `create_time`       datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP (3) COMMENT '创建时间',
    `update_user_id`    varchar(64)  NOT NULL DEFAULT '' COMMENT '更新人id',
    `update_time`       datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP (3) ON UPDATE CURRENT_TIMESTAMP (3) COMMENT '更新时间',
    `is_deleted`        tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除 1:已删除 0:未删除',
    `feishu_name`       varchar(325) NOT NULL DEFAULT '' COMMENT '纠纷售后飞书名称',
    PRIMARY KEY (`id`) USING BTREE,
    KEY                 `idx_work_order_no` (`work_order_no`),
    KEY                 `idx_order_item_id` (`order_item_id`),
    KEY                 `idx_product_unique_no` (`product_unique_no`),
    KEY                 `idx_ass_dispute_id` (`ass_dispute_id`),
    KEY                 `idx_feishu_name` (`feishu_name`),
    KEY                 `idx_product_id` (`product_id`),
    KEY                 `idx_dispute_user_id` (`dispute_user_id`),
    KEY                 `idx_create_time` (`create_time`)
) ENGINE = InnoDB  COMMENT = '售后纠纷工单';

CREATE TABLE `ass_followup_log`
(
    `id`             bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '自增id',
    `followup_id`    varchar(64)  NOT NULL DEFAULT '' COMMENT '主键id',
    `work_order_id`  varchar(64)  NOT NULL DEFAULT '' COMMENT '售后工单id',
    `ass_type`       smallint unsigned NOT NULL DEFAULT '0' COMMENT '售后类型1找回2纠纷',
    `title`          varchar(255) NOT NULL DEFAULT '' COMMENT '标题',
    `content`        text COMMENT '操作内容',
    `image`          text COMMENT '跟进图片',
    `add_way`        smallint unsigned NOT NULL DEFAULT '0' COMMENT '日志添加方式1系统自动产生的日志 2手动添加的日志',
    `create_user_id` varchar(64)  NOT NULL DEFAULT '' COMMENT '创建人id',
    `create_time`    datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP (3) COMMENT '创建时间',
    `update_user_id` varchar(64)  NOT NULL DEFAULT '' COMMENT '更新人id',
    `update_time`    datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP (3) ON UPDATE CURRENT_TIMESTAMP (3) COMMENT '更新时间',
    `is_deleted`     tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除 1:已删除 0:未删除',
    `feishu_name`    varchar(325) NOT NULL DEFAULT '' COMMENT '纠纷售后飞书名称',
    PRIMARY KEY (`id`) USING BTREE,
    KEY              `idx_work_order_id` (`work_order_id`) USING BTREE,
    KEY              `idx_followup_id` (`followup_id`)
) ENGINE = InnoDB COMMENT = '售后工单跟进记录';

CREATE TABLE `ass_insurance_claim`
(
    `id`                bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `claim_id`          varchar(64)  NOT NULL DEFAULT '' COMMENT '业务主键id',
    `policy_no`         varchar(255) NOT NULL DEFAULT '' COMMENT '保险单号',
    `outside_policy_no` varchar(255) NOT NULL DEFAULT '' COMMENT '外部保险单号',
    `order_item_id`     varchar(64)  NOT NULL DEFAULT '' COMMENT '订单编号',
    `product_id`        varchar(64)  NOT NULL DEFAULT '' COMMENT '商品id -商品域',
    `product_unique_no` varchar(64)  NOT NULL DEFAULT '' COMMENT '商品编码',
    `claim_report_no`   varchar(128) NOT NULL DEFAULT '' COMMENT '外部赔付单号',
    `claim_no`          varchar(128) NOT NULL DEFAULT '' COMMENT '赔付单号',
    `work_order_id`     varchar(64)  NOT NULL DEFAULT '' COMMENT '售后工单id',
    `work_order_no`     varchar(128) NOT NULL DEFAULT '' COMMENT '工单编号',
    `is_timely_claim`   tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否36小时内赔付 0:否 1是',
    `claim_fee`         bigint unsigned NOT NULL DEFAULT '0' COMMENT '保司实际理赔金额',
    `apply_amount`      bigint unsigned NOT NULL DEFAULT '0' COMMENT '理赔金额(保额已追回金额)',
    `start_time`        datetime(3) NOT NULL DEFAULT '1000-01-01 00:00:00.000' COMMENT '理赔发起时间',
    `finish_time`       datetime(3) NOT NULL DEFAULT '1000-01-01 00:00:00.000' COMMENT '理赔完成时间',
    `status`            smallint unsigned NOT NULL DEFAULT '0' COMMENT '理赔状态 0 已创建 1 理赔中（待审核） 2 已完成 3 同步中（废除） 4 理赔失败（不予受理） 5 已退回（待补充材料）',
    `fail_reason`       varchar(255) NOT NULL DEFAULT '' COMMENT '失败原因',
    `remark`            varchar(500) NOT NULL DEFAULT '' COMMENT '备注',
    `debit_account`     varchar(64)  NOT NULL DEFAULT '' COMMENT '打款账户',
    `create_user_id`    varchar(64)  NOT NULL DEFAULT '' COMMENT '创建人id',
    `create_time`       datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP (3) COMMENT '创建时间',
    `update_user_id`    varchar(64)  NOT NULL DEFAULT '' COMMENT '更新人id',
    `update_time`       datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP (3) ON UPDATE CURRENT_TIMESTAMP (3) COMMENT '更新时间',
    `is_deleted`        tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除 1:已删除 0:未删除',
    `compensation_no`   varchar(50)  NOT NULL DEFAULT '' COMMENT '唯一编码',
    PRIMARY KEY (`id`) USING BTREE,
    KEY                 `idx_order_item_id` (`order_item_id`) USING BTREE,
    KEY                 `idx_claim_id` (`claim_id`),
    KEY                 `idx_policy_no` (`policy_no`),
    KEY                 `idx_product_unique_no` (`product_unique_no`),
    KEY                 `idx_claim_no` (`claim_no`),
    KEY                 `idx_work_order_no` (`work_order_no`)
) ENGINE = InnoDB COMMENT = '保险赔付单';

CREATE TABLE `ass_payment`
(
    `id`                bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '自增id',
    `ass_payment_id`    varchar(64)  NOT NULL DEFAULT '' COMMENT '主键ID',
    `payment_no`        varchar(64)  NOT NULL DEFAULT '' COMMENT '收款编号',
    `payment_amount`    bigint unsigned NOT NULL DEFAULT '0' COMMENT '收款金额',
    `ass_type`          smallint unsigned NOT NULL DEFAULT '0' COMMENT '售后类型1找回2纠纷',
    `work_order_id`     varchar(64)  NOT NULL DEFAULT '' COMMENT '售后工单ID',
    `work_order_no`     varchar(128) NOT NULL DEFAULT '' COMMENT '工单编号',
    `status`            smallint unsigned NOT NULL DEFAULT '1' COMMENT '收款状态1待审核2已收款3已关闭',
    `proposer_user_id`  varchar(64)  NOT NULL DEFAULT '' COMMENT '申请人ID',
    `complete_time`     datetime(3) DEFAULT NULL COMMENT '完成时间',
    `proposer_reason`   varchar(255) NOT NULL DEFAULT '' COMMENT '申请原因',
    `game_id`           varchar(64)  NOT NULL DEFAULT '' COMMENT '游戏业务ID',
    `game_name`         varchar(255) NOT NULL DEFAULT '' COMMENT '游戏名称',
    `product_id`        varchar(64)  NOT NULL DEFAULT '' COMMENT '商品id',
    `product_unique_no` varchar(64)  NOT NULL DEFAULT '' COMMENT '商品编码',
    `order_item_id`     varchar(64)  NOT NULL DEFAULT '' COMMENT '订单id',
    `payment_account`   varchar(64)  NOT NULL DEFAULT '' COMMENT '收款账户',
    `account_id`        varchar(64)  NOT NULL DEFAULT '' COMMENT '收款账户ID',
    `payment_channel`   smallint unsigned NOT NULL DEFAULT '1' COMMENT '收款渠道1线上2线下',
    `qr_code`           varchar(255) NOT NULL DEFAULT '' COMMENT '收款二维码',
    `expire_time`       datetime(3) NOT NULL DEFAULT '1000-01-01 00:00:00.000' COMMENT '二维码过期时间',
    `payment_proof`     text COMMENT '收款凭证',
    `pay_way`           smallint unsigned NOT NULL DEFAULT '0' COMMENT '收款方式1支付宝2微信',
    `source_type`       smallint unsigned NOT NULL DEFAULT '1' COMMENT '渠道来源1螃蟹2支付宝小程序',
    `notes`             varchar(255) NOT NULL DEFAULT '' COMMENT '线下收款备注',
    `finance`           varchar(64)  NOT NULL DEFAULT '' COMMENT '财务人员',
    `finance_id`        varchar(64)  NOT NULL DEFAULT '' COMMENT '财务人员ID',
    `inner_trade_no`    varchar(64)  NOT NULL DEFAULT '' COMMENT '平台请求单号（平台内部生成给三方的单号）',
    `out_trade_no`      varchar(64)  NOT NULL DEFAULT '' COMMENT '三方凭证单号（三方生成返回给平台的单号）',
    `create_user_id`    varchar(64)  NOT NULL DEFAULT '' COMMENT '创建人id',
    `create_time`       datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP (3) COMMENT '创建时间',
    `update_user_id`    varchar(64)  NOT NULL DEFAULT '' COMMENT '更新人id',
    `update_time`       datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP (3) ON UPDATE CURRENT_TIMESTAMP (3) COMMENT '更新时间',
    `is_deleted`        tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除 1:已删除 0:未删除',
    PRIMARY KEY (`id`) USING BTREE,
    KEY                 `idx_work_order_id` (`work_order_id`) USING BTREE,
    KEY                 `idx_ass_payment_id` (`ass_payment_id`),
    KEY                 `idx_payment_no` (`payment_no`),
    KEY                 `idx_order_item_id` (`order_item_id`),
    KEY                 `idx_product_id` (`product_id`),
    KEY                 `idx_work_order_no` (`work_order_no`),
    KEY                 `idx_product_unique_no` (`product_unique_no`),
    KEY                 `idx_out_trade_no` (`out_trade_no`) USING BTREE,
    KEY                 `idx_inner_trade_no` (`inner_trade_no`) USING BTREE
) ENGINE = InnoDB COMMENT = '售后收款列表';

CREATE TABLE `ass_proof_material`
(
    `id`             bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '自增id',
    `proof_id`       varchar(64)  NOT NULL DEFAULT '' COMMENT '主键id',
    `name`           varchar(300) NOT NULL DEFAULT '' COMMENT '说明',
    `link_url`       varchar(900) NOT NULL DEFAULT '' COMMENT '链接',
    `ass_type`       smallint unsigned NOT NULL DEFAULT '0' COMMENT '类型1找回2纠纷',
    `work_order_id`  varchar(64)  NOT NULL DEFAULT '' COMMENT '售后工单id',
    `file_id`        varchar(128)          DEFAULT NULL COMMENT '上传到阿里后的文件id',
    `create_user_id` varchar(64)  NOT NULL DEFAULT '' COMMENT '创建人id',
    `create_time`    datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP (3) COMMENT '创建时间',
    `update_user_id` varchar(32)  NOT NULL DEFAULT '' COMMENT '更新人id',
    `update_time`    datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP (3) ON UPDATE CURRENT_TIMESTAMP (3) COMMENT '更新时间',
    `is_deleted`     tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除 1:已删除 0:未删除',
    PRIMARY KEY (`id`) USING BTREE,
    KEY              `idx_work_order_id` (`work_order_id`) USING BTREE,
    KEY              `idx_proof_id` (`proof_id`)
) ENGINE = InnoDB COMMENT = '售后证据材料';

CREATE TABLE `ass_question_classify`
(
    `id`                     bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '自增id',
    `classify_id`            varchar(64)  NOT NULL DEFAULT '' COMMENT '业务主键id',
    `ass_type`               smallint unsigned NOT NULL DEFAULT '0' COMMENT '售后类型1找回2纠纷',
    `first_level_directory`  varchar(32)  NOT NULL DEFAULT '' COMMENT '一级目录',
    `second_level_directory` varchar(900) NOT NULL DEFAULT '' COMMENT '二级目录',
    `sort_index`             smallint unsigned NOT NULL DEFAULT '0' COMMENT '排序',
    `create_user_id`         varchar(64)  NOT NULL COMMENT '创建人ID',
    `create_time`            datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP (3) COMMENT '创建时间',
    `update_user_id`         varchar(32)  NOT NULL DEFAULT '' COMMENT '更新人id',
    `update_time`            datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP (3) ON UPDATE CURRENT_TIMESTAMP (3) COMMENT '更新时间',
    `is_deleted`             tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除 1:已删除 0:未删除',
    PRIMARY KEY (`id`) USING BTREE,
    KEY                      `idx_classify_id` (`classify_id`)
) ENGINE = InnoDB COMMENT = '售后问题归类配置';

CREATE TABLE `ass_question_option`
(
    `id`             bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '自增id',
    `option_id`      varchar(64) NOT NULL DEFAULT '' COMMENT '业务主键id',
    `name`           varchar(90) NOT NULL DEFAULT '' COMMENT '方案名称',
    `question_list`  text        NOT NULL COMMENT '问题列表',
    `is_enable`      tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否启用0否1是',
    `create_user_id` varchar(64) NOT NULL DEFAULT '' COMMENT '创建人id',
    `create_time`    datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP (3) COMMENT '创建时间',
    `update_user_id` varchar(64) NOT NULL DEFAULT '' COMMENT '更新人id',
    `update_time`    datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP (3) ON UPDATE CURRENT_TIMESTAMP (3) COMMENT '更新时间',
    `is_deleted`     tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除 1:已删除 0:未删除',
    PRIMARY KEY (`id`) USING BTREE,
    KEY              `idx_option_id` (`option_id`)
) ENGINE = InnoDB COMMENT = '售后问答问题方案配置';

CREATE TABLE `ass_question_option_relation`
(
    `id`             bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '自增id',
    `relation_id`    varchar(64) NOT NULL DEFAULT '' COMMENT '业务主键id',
    `option_id`      varchar(64) NOT NULL DEFAULT '' COMMENT '配置方案id-ass_question_option',
    `game_id`        varchar(64) NOT NULL DEFAULT '' COMMENT '游戏id',
    `create_user_id` varchar(64) NOT NULL DEFAULT '' COMMENT '创建人id',
    `create_time`    datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP (3) COMMENT '创建时间',
    `update_user_id` varchar(64) NOT NULL DEFAULT '' COMMENT '更新人id',
    `update_time`    datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP (3) ON UPDATE CURRENT_TIMESTAMP (3) COMMENT '更新时间',
    `is_deleted`     tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除 1:已删除 0:未删除',
    PRIMARY KEY (`id`) USING BTREE,
    KEY              `idx_relation_id` (`relation_id`),
    KEY              `idx_game_id_option_id` (`game_id`, `option_id`)
) ENGINE = InnoDB COMMENT = '售后问答问题游戏关联';

CREATE TABLE `ass_retrieve_work`
(
    `id`                bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '自增id',
    `ass_retrieve_id`   varchar(64)  NOT NULL DEFAULT '' COMMENT '主键ID',
    `work_order_no`     varchar(64)  NOT NULL DEFAULT '' COMMENT '工单编号',
    `order_item_id`     varchar(64)  NOT NULL DEFAULT '' COMMENT '订单ID',
    `product_id`        varchar(64)  NOT NULL DEFAULT '' COMMENT '商品id',
    `product_unique_no` varchar(64)  NOT NULL DEFAULT '' COMMENT '商品编码',
    `product_type`      smallint     NOT NULL DEFAULT '0' COMMENT '商品类型: 1账号 2充值 3金币 4装备 5初始号 20诚心卖',
    `game_id`           varchar(64)  NOT NULL DEFAULT '' COMMENT '游戏ID',
    `proposer_user_id`  varchar(64)  NOT NULL DEFAULT '' COMMENT '申请人id',
    `recv_customer_id`  varchar(64)  NOT NULL DEFAULT '' COMMENT '接待客服id',
    `audit_customer_id` varchar(64)  NOT NULL DEFAULT '' COMMENT '审核客服id',
    `deal_user_id`      varchar(64)  NOT NULL DEFAULT '' COMMENT '当前处理人员',
    `status`            smallint unsigned NOT NULL DEFAULT '0' COMMENT '状态0待石锤1处理中2处理完成3已关闭',
    `deal_result`       smallint unsigned NOT NULL DEFAULT '0' COMMENT '处理结果0处理中1追回账号2追回号款3无结果',
    `recover_amount`    bigint unsigned NOT NULL DEFAULT '0' COMMENT '追回金额',
    `recover_status`    smallint unsigned NOT NULL DEFAULT '0' COMMENT '追回号款状态0无1部分追回2全追回',
    `claim_amount`      bigint       NOT NULL DEFAULT '0' COMMENT '赔付金额',
    `claim_status`      smallint unsigned NOT NULL DEFAULT '0' COMMENT '赔付状态0无1待审核2已赔付',
    `expected_time`     datetime(3) DEFAULT NULL COMMENT '预计完成时间',
    `sponsor`           smallint unsigned NOT NULL DEFAULT '1' COMMENT '工单发起来源1用户2客服',
    `complete_time`     datetime(3) DEFAULT NULL COMMENT '完成时间',
    `is_rotate`         tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否轮转',
    `classify_first`    varchar(255) NOT NULL DEFAULT '' COMMENT '问题归类一级目录',
    `classify_second`   varchar(255) NOT NULL DEFAULT '' COMMENT '问题归类二级目录',
    `data_source`       smallint unsigned NOT NULL DEFAULT '1' COMMENT '数据来源1IM私聊2企业微信3投诉转交4其他',
    `source_type`       smallint unsigned NOT NULL DEFAULT '1' COMMENT '渠道来源1螃蟹2支付宝小程序',
    `insure`            smallint unsigned NOT NULL DEFAULT '0' COMMENT '保险 0未购买 1购买',
    `create_user_id`    varchar(64)  NOT NULL DEFAULT '' COMMENT '创建人id',
    `create_time`       datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP (3) COMMENT '创建时间',
    `update_user_id`    varchar(64)  NOT NULL DEFAULT '' COMMENT '更新人id',
    `update_time`       datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP (3) ON UPDATE CURRENT_TIMESTAMP (3) COMMENT '更新时间',
    `is_deleted`        tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除 1:已删除 0:未删除',
    `handle_notes`      varchar(500) NOT NULL DEFAULT '' COMMENT '处理详情备注',
    PRIMARY KEY (`id`) USING BTREE,
    KEY                 `idx_work_order_no` (`work_order_no`),
    KEY                 `idx_product_unique_no` (`product_unique_no`),
    KEY                 `idx_order_item_id` (`order_item_id`),
    KEY                 `idx_ass_retrieve_id` (`ass_retrieve_id`),
    KEY                 `idx_product_id` (`product_id`),
    KEY                 `idx_create_time` (`create_time`)
) ENGINE = InnoDB COMMENT = '售后找回工单';

CREATE TABLE `ass_schedule`
(
    `id`                bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '自增id',
    `schedule_id`       varchar(64) NOT NULL DEFAULT '' COMMENT '主键id',
    `order_item_id`     varchar(64) NOT NULL DEFAULT '' COMMENT '订单编号',
    `work_order_id`     varchar(64) NOT NULL DEFAULT '' COMMENT '工单id',
    `ass_type`          smallint unsigned NOT NULL DEFAULT '0' COMMENT '售后类型1找回2纠纷',
    `recv_customer_id`  varchar(64) NOT NULL DEFAULT '' COMMENT '接待客服id',
    `audit_customer_id` varchar(64) NOT NULL DEFAULT '' COMMENT '审核客服id',
    `room_id`           varchar(64) NOT NULL DEFAULT '' COMMENT '房间id',
    `is_finish`         tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否完结',
    `create_user_id`    varchar(64) NOT NULL DEFAULT '' COMMENT '创建人id',
    `create_time`       datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP (3) COMMENT '创建时间',
    `update_user_id`    varchar(64) NOT NULL DEFAULT '' COMMENT '更新人id',
    `update_time`       datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP (3) ON UPDATE CURRENT_TIMESTAMP (3) COMMENT '更新时间',
    `is_deleted`        tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除 1:已删除 0:未删除',
    `source_type`       tinyint unsigned NOT NULL DEFAULT '1' COMMENT '来源 1:C端用户 2:客服 3:admin用户',
    PRIMARY KEY (`id`) USING BTREE,
    KEY                 `idx_room_id` (`room_id`) USING BTREE,
    KEY                 `idx_schedule_id` (`schedule_id`) USING BTREE,
    KEY                 `idx_order_item_id` (`order_item_id`),
    KEY                 `idx_work_order_id` (`work_order_id`)
) ENGINE = InnoDB COMMENT = '售后流程进度';

CREATE TABLE `ass_schedule_log`
(
    `id`              bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '自增id',
    `schedule_log_id` varchar(64) NOT NULL DEFAULT '' COMMENT '主键id',
    `schedule_id`     varchar(64) NOT NULL DEFAULT '' COMMENT '售后流程进度id',
    `node_id`         varchar(64) NOT NULL DEFAULT '' COMMENT '节点id',
    `node_desc`       varchar(64) NOT NULL DEFAULT '' COMMENT '当前节点状态描述',
    `is_show`         tinyint unsigned NOT NULL DEFAULT '1' COMMENT '是否展示',
    `data`            text COMMENT '其他数据',
    `create_user_id`  varchar(64) NOT NULL DEFAULT '' COMMENT '创建人id',
    `create_time`     datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP (3) COMMENT '创建时间',
    `update_user_id`  varchar(64) NOT NULL DEFAULT '' COMMENT '更新人id',
    `update_time`     datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP (3) ON UPDATE CURRENT_TIMESTAMP (3) COMMENT '更新时间',
    `is_deleted`      tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除 1:已删除 0:未删除',
    `source_type`     tinyint unsigned NOT NULL DEFAULT '1' COMMENT '来源 1:C端用户 2:客服 3:admin用户',
    PRIMARY KEY (`id`) USING BTREE,
    KEY               `idx_schedule_id` (`schedule_id`) USING BTREE,
    KEY               `idx_schedule_log_id` (`schedule_log_id`) USING BTREE
) ENGINE = InnoDB COMMENT = '售后流程进度日志';

CREATE TABLE `complaint_department_config`
(
    `id`                      bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
    `complaint_department_id` varchar(64)   NOT NULL DEFAULT '' COMMENT '客诉部门id',
    `department_name`         varchar(20)   NOT NULL DEFAULT '' COMMENT '部门名称',
    `question_type`           varchar(1000) NOT NULL DEFAULT '' COMMENT '问题类型',
    `question_sort`           smallint unsigned NOT NULL DEFAULT '0' COMMENT '排序值',
    `create_user_id`          varchar(64)            DEFAULT '' COMMENT '创建用户id',
    `update_user_id`          varchar(64)            DEFAULT '' COMMENT '更新用户id',
    `create_time`             datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP (3) COMMENT '创建时间',
    `update_time`             datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP (3) ON UPDATE CURRENT_TIMESTAMP (3) COMMENT '更新时间',
    `is_deleted`              tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB COMMENT = '问题部门-问题类型配置';

CREATE TABLE `complaint_employee_config`
(
    `id`                      bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
    `complaint_employee_id`   varchar(64) NOT NULL DEFAULT '' COMMENT '工单处理人员信息配置id',
    `complaint_department_id` varchar(64) NOT NULL DEFAULT '' COMMENT '问题部门配置id',
    `mobile`                  varchar(20) NOT NULL DEFAULT '' COMMENT '手机号',
    `user_id`                 varchar(64) NOT NULL DEFAULT '' COMMENT '后台用户ID',
    `user_name`               varchar(64) NOT NULL DEFAULT '' COMMENT '账号(员工姓名)',
    `create_user_id`          varchar(64)          DEFAULT '' COMMENT '创建用户id',
    `update_user_id`          varchar(64)          DEFAULT '' COMMENT '更新用户id',
    `create_time`             datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP (3) COMMENT '创建时间',
    `update_time`             datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP (3) ON UPDATE CURRENT_TIMESTAMP (3) COMMENT '更新时间',
    `is_deleted`              tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB COMMENT = '工单处理人员信息维护配置';

CREATE TABLE `complaint_work`
(
    `id`                   bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
    `complaint_work_id`    varchar(64)   NOT NULL DEFAULT '' COMMENT '工单编号(业务id)',
    `room_id`              varchar(64)   NOT NULL DEFAULT '' COMMENT '房间id',
    `order_item_id`        varchar(64)   NOT NULL DEFAULT '' COMMENT '订单行id',
    `complaint_title`      varchar(50)   NOT NULL DEFAULT '' COMMENT '投诉标题',
    `complaint_channel`    smallint unsigned NOT NULL DEFAULT '1' COMMENT '投诉渠道1:IM 2支付宝 3闲鱼 4:12315 5消费宝 6连连支付 7电话 8反诈邮箱 9外部门升级 10黑猫投诉 11工商局 12工信部',
    `complaint_phone`      varchar(32)   NOT NULL DEFAULT '' COMMENT '投诉手机号',
    `complaint_role`       smallint unsigned NOT NULL DEFAULT '0' COMMENT '投诉人身份1买家 2卖家 3其他',
    `user_id`              varchar(64)   NOT NULL COMMENT '用户id',
    `complaint_level`      smallint unsigned NOT NULL DEFAULT '0' COMMENT '投诉级别:1-6',
    `complaint_content`    varchar(500)  NOT NULL DEFAULT '' COMMENT '投诉文字内容',
    `complaint_img`        text COMMENT '投诉图片地址多图,隔开',
    `work_order_status`    smallint unsigned NOT NULL DEFAULT '1' COMMENT '工单状态 1待处理 2已完结',
    `current_processor_id` varchar(64)   NOT NULL DEFAULT '' COMMENT '当前处理人ID',
    `deal_result`          varchar(500)  NOT NULL DEFAULT '' COMMENT '处理结果',
    `note`                 varchar(500)  NOT NULL DEFAULT '' COMMENT '备注',
    `transferee_id`        varchar(64)   NOT NULL DEFAULT '' COMMENT '被转交人id',
    `finisher_id`          varchar(64)   NOT NULL DEFAULT '' COMMENT '完结人id',
    `finish_time`          datetime(3) DEFAULT NULL COMMENT '完结时间',
    `responsible_person`   varchar(64)   NOT NULL DEFAULT '' COMMENT '责任人',
    `is_responsibility`    tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否有责 0:否 1是',
    `department_name`      varchar(20)   NOT NULL DEFAULT '' COMMENT '部门名称',
    `question_type`        varchar(1000) NOT NULL DEFAULT '' COMMENT '问题类型',
    `question_level`       smallint unsigned NOT NULL DEFAULT '0' COMMENT '问题分级 1-6',
    `complaint_source`     smallint unsigned NOT NULL DEFAULT '1' COMMENT '创建来源(1:系统,2:IM)',
    `create_user_id`       varchar(64)            DEFAULT '' COMMENT '创建人id',
    `update_user_id`       varchar(64)            DEFAULT '' COMMENT '更新人id',
    `create_time`          datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP (3) COMMENT '创建时间',
    `update_time`          datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP (3) ON UPDATE CURRENT_TIMESTAMP (3) COMMENT '更新时间',
    `is_deleted`           tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除',
    PRIMARY KEY (`id`) USING BTREE,
    KEY                    `idx_work_order` (`complaint_work_id`, `order_item_id`) USING BTREE,
    KEY                    `idx_complaint_phone` (`complaint_phone`) USING BTREE,
    KEY                    `idx_complaint_channel` (`complaint_channel`) USING BTREE,
    KEY                    `idx_create_user_id` (`create_user_id`) USING BTREE,
    KEY                    `idx_current_processor_id` (`current_processor_id`) USING BTREE,
    KEY                    `idx_finisher_id` (`finisher_id`) USING BTREE,
    KEY                    `idx_create_time` (`create_time`) USING BTREE,
    KEY                    `idx_finish_time` (`finish_time`) USING BTREE,
    KEY                    `idx_status_current_processor` (`work_order_status`, `current_processor_id`) USING BTREE,
    KEY                    `idx_order_item_id` (`order_item_id`) USING BTREE,
    KEY                    `idx_user_id` (`user_id`) USING BTREE
) ENGINE = InnoDB  COMMENT = '客诉工单';


CREATE TABLE `complaint_work_log`
(
    `id`                    bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
    `complaint_work_log_id` varchar(64)  NOT NULL DEFAULT '' COMMENT '客诉工单日志业务ID',
    `room_id`               varchar(64)  NOT NULL DEFAULT '' COMMENT '房间id',
    `complaint_work_id`     varchar(64)  NOT NULL DEFAULT '' COMMENT '工单编号',
    `log_type`              smallint unsigned NOT NULL DEFAULT '1' COMMENT '日志类型（1:创建；2:转交；3:完结）',
    `current_processor_id`  varchar(64)  NOT NULL DEFAULT '' COMMENT '当前处理人ID',
    `transferee_id`         varchar(64)  NOT NULL DEFAULT '' COMMENT '被转交人id(log_type=2时有值)',
    `transfer_note`         varchar(500) NOT NULL DEFAULT '' COMMENT '转交备注',
    `create_user_id`        varchar(64)           DEFAULT '' COMMENT '创建用户id',
    `update_user_id`        varchar(64)           DEFAULT '' COMMENT '更新用户id',
    `create_time`           datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP (3) COMMENT '创建时间',
    `update_time`           datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP (3) ON UPDATE CURRENT_TIMESTAMP (3) COMMENT '更新时间',
    `is_deleted`            tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除',
    PRIMARY KEY (`id`) USING BTREE,
    KEY                     `idx_room_id` (`room_id`) USING BTREE,
    KEY                     `idx_complaint_work_log_id` (`complaint_work_log_id`)
) ENGINE = InnoDB COMMENT = '客诉工单操作日志表';








