package com.pxb7.mall.trade.ass;

import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.dromara.dynamictp.core.spring.EnableDynamicTp;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

/**
 * Spring Boot Starter
 *
 * <AUTHOR>
 */
@SpringBootApplication(scanBasePackages = {"com.pxb7.mall", "com.alibaba.cola"})
@EnableDiscoveryClient
@EnableDubbo
@EnableDynamicTp
// @EnableAsync
@MapperScan(basePackages = {"com.pxb7.mall.trade.ass.infra.repository.db.mapper"})
public class Application {
    public static void main(String[] args) {

        SpringApplication.run(Application.class, args);
    }
}
