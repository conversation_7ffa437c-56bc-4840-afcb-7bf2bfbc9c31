package com.pxb7.mall.trade.ass;

import com.pxb7.mall.trade.ass.client.enums.RiskBlackOperateType;
import com.pxb7.mall.trade.ass.domain.violate.mq.ViolateMessageService;
import jakarta.annotation.Resource;
import org.junit.Test;

public class MqSendTest extends AbstractBaseSpringService{

    @Resource
    private ViolateMessageService violateMessageService;

    @Test
    public void test_sendViolateRiskBlack() {
        violateMessageService.sendViolateRiskBlack("test0001",  RiskBlackOperateType.REMOVE.getCode());
        System.out.println("==");
    }
}
