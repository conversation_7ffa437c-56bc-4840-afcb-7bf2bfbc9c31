package com.pxb7.mall.trade.ass;

import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.pxb7.mall.trade.ass.adapter.consumer.RefundPartlyMessageConsumer;
import com.pxb7.mall.trade.ass.adapter.consumer.RefundQueryDelayMessageConsumer;
import com.pxb7.mall.trade.ass.infra.enums.OrderTypeEnum;
import com.pxb7.mall.trade.ass.infra.model.mq.PartRefundMessage;
import com.pxb7.mall.trade.ass.infra.model.mq.RefundQueryMessage;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.apis.message.MessageId;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.apache.rocketmq.client.java.message.MessageIdImpl;
import org.junit.Test;
import java.nio.ByteBuffer;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025/7/3 17:37
 */
@Slf4j
public class AhsRefundTest extends AbstractBaseSpringService {

    @Resource
    private RefundPartlyMessageConsumer refundPartlyMessageConsumer;

    @Resource
    private RefundQueryDelayMessageConsumer refundQueryDelayMessageConsumer;

    private static final MessageView messageView = new MessageView() {
        @Override
        public MessageId getMessageId() {
            return new MessageIdImpl("", "");
        }

        @Override
        public String getTopic() {
            return "";
        }

        @Override
        public ByteBuffer getBody() {
            return null;
        }

        @Override
        public Map<String, String> getProperties() {
            return Map.of();
        }

        @Override
        public Optional<String> getTag() {
            return Optional.empty();
        }

        @Override
        public Collection<String> getKeys() {
            return List.of();
        }

        @Override
        public Optional<String> getMessageGroup() {
            return Optional.empty();
        }

        @Override
        public Optional<Long> getDeliveryTimestamp() {
            return Optional.empty();
        }

        @Override
        public String getBornHost() {
            return "";
        }

        @Override
        public long getBornTimestamp() {
            return 0;
        }

        @Override
        public int getDeliveryAttempt() {
            return 0;
        }
    };

    @Test
    public void ahsRefund() {
//        PartRefundMessage message =
//                new PartRefundMessage().setPayPaymentId("15993919746051420123")
//                        .setHistoryRefundPaymentIds(Lists.newArrayList())
//                        .setRefundVoucherId("ATK1601264647864330123")
//                        .setRefundAmount(1L)
//                        .setUserId("")
//                        .setOrderType(OrderTypeEnum.AHS_ORDER.getCode());
//        refundPartlyMessageConsumer.consume(messageView, message);
    }

    @Test
    public void ashRefundQuery() {
//        RefundQueryMessage refundQueryMessage = new RefundQueryMessage()
//                .setRefundLogId("16013289900445520123")
//                .setPayLogId("15993919746051520123")
//                .setOrderItemId("")
//                .setRefundVoucherId("")
//                .setChannelType(null)
//                .setChannelExtMap(Map.of());
//        refundQueryDelayMessageConsumer.consume(messageView, refundQueryMessage);
    }

}
