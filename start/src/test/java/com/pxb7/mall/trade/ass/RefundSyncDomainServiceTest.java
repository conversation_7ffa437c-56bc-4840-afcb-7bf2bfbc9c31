package com.pxb7.mall.trade.ass;

import com.pxb7.mall.trade.ass.domain.refund.RefundSyncDomainService;
import com.pxb7.mall.trade.ass.infra.model.mq.RefundQueryMessage;
import com.pxb7.mall.trade.order.client.enums.order.ChannelOrderTypeEnum;
import jakarta.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import org.junit.Test;

/**
 * <AUTHOR>
 * @since 2025/5/12
 */
public class RefundSyncDomainServiceTest extends AbstractBaseSpringService{

    @Resource
    private RefundSyncDomainService refundSyncDomainService;

    private static final String XID_BUYER = "o*AARNRjluc1yfxPWydDzAd-WZOGI3ZR50lqhR5o5a48KUKpWSG6M";

    @Test
    public void testHandleResult(){
        Map<String, Object> ext = new HashMap<>();
        ext.put("outOrderId", "315173193447");
        ext.put("jdBuyerId", XID_BUYER);
        refundSyncDomainService.handleRefundResult(new RefundQueryMessage()
            .setOrderItemId("ZH15072328577851424628")
            .setRefundVoucherId("TK15072425306526824628")
            .setChannelType(ChannelOrderTypeEnum.JD.getValue())
            .setChannelExtMap(ext));
    }

}
