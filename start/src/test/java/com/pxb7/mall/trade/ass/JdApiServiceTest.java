package com.pxb7.mall.trade.ass;

import cn.hutool.core.date.DateUtil;
import com.pxb7.mall.trade.ass.infra.remote.model.refund.JdRefundPO;
import com.pxb7.mall.trade.ass.infra.remote.service.http.JdApiService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

/**
 * <AUTHOR>
 * @since 2025/4/21
 */
@Slf4j
public class JdApiServiceTest extends AbstractBaseSpringService{

    @Resource
    private JdApiService service;

    private static final String XID_BUYER = "o*AARNRjluc1yfxPWydDzAd-WZOGI3ZR50lqhR5o5a48KUKpWSG6M";

//    @Test
//    public void testCreate() throws Exception {
//        Long jdOrderId = service.create("ZH" + DateUtil.now(), 100L);
//        log.info("JD_ORDER_ID = " + jdOrderId);
//    }

//    @Test
//    public void testCancel() throws Exception {
//        service.cancel(313854258236L);
//    }

    //1待支付
    @Test
    public void testGet() throws Exception {
//        JdApiService.JdOrder jdOrder = service.get(314218013156L);
    }

    @Test
    public void testRefund() throws Exception {
        Boolean result = service.refund(new JdRefundPO()
            .setJdBuyerId(XID_BUYER)
            .setJdOrderId(313854258236L)
            .setRefundUuid("TK001")
            .setPartRefund(false)
            .setRefundAmount(100L));
    }

    @Test
    public void testGetRefund() throws Exception {
        JdApiService.JdRefund refund = service.getRefund(312573119229L, "TK001");
    }

    //refund_success


}
