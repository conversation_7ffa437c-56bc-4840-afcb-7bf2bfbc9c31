package com.pxb7.mall.trade.ass.indemnitychange;

import com.alibaba.cola.dto.SingleResponse;
import com.pxb7.mall.trade.ass.AbstractBaseSpringService;
import com.pxb7.mall.trade.ass.client.api.RefundVoucherServiceI;
import com.pxb7.mall.trade.ass.client.dto.model.orderitemimdemnity.OrderItemIndemnityDTO;
import com.pxb7.mall.trade.ass.client.dto.request.refund.CancelRefundVoucherReqDTO;
import com.pxb7.mall.trade.ass.client.dto.request.refund.IndemnityChangeServiceRefundReqDTO;
import com.pxb7.mall.trade.ass.infra.model.Money;
import jakarta.annotation.Resource;
import org.junit.Test;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/22 16:58
 */
public class IndemnityChangeTest extends AbstractBaseSpringService {

    @Resource
    private RefundVoucherServiceI refundVoucherServiceI;


    @Test
    public void testCancelRefund() {
        CancelRefundVoucherReqDTO req = new CancelRefundVoucherReqDTO();
        req.setRefundVoucherId("TK15464175773290124361");
        req.setUserId("1849633861009526878");
        req.setOrderItemId("ZH15463893655552324361");
        req.setUserName("fan");
        SingleResponse<Boolean> response = refundVoucherServiceI.cancelIndemnityChangeRefund(req);

    }

    @Test
    public void submitIndemnityRefund() {

        OrderItemIndemnityDTO add = new OrderItemIndemnityDTO();
        add.setIndemnityAmount(0L);
        add.setIndemnityName("免费包赔");

        OrderItemIndemnityDTO cancel = new OrderItemIndemnityDTO();
        cancel.setIndemnityAmount(46000L);
        cancel.setIndemnityName("实名包赔");

        OrderItemIndemnityDTO cancel2 = new OrderItemIndemnityDTO();
        cancel2.setIndemnityAmount(23000L);
        cancel2.setIndemnityName("单倍包赔");

        IndemnityChangeServiceRefundReqDTO reqDto = new IndemnityChangeServiceRefundReqDTO();
        reqDto.setChangeUser("1851566639754006579");
        reqDto.setRefundAmount(69000L);
        reqDto.setResponseUser(1);
        reqDto.setResponseUserId("18514689322511974772162");

        reqDto.setAddedIndemnityList(List.of(add));
        reqDto.setCanceledIndemnityList(List.of(cancel, cancel2));
        reqDto.setIndemnityChangeId("BPBG15468267933704022162");


        reqDto.setOrderItemId("ZH15466548673746224361");
        reqDto.setPayMode(1);
        reqDto.setRoomId("154665692102702");
        String priceStr = new Money(reqDto.getRefundAmount()).getYuan();


        refundVoucherServiceI.createIndemnityChangeRefund(reqDto);

    }

}
