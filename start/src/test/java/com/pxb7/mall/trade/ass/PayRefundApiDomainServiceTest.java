package com.pxb7.mall.trade.ass;

import com.pxb7.mall.trade.ass.domain.refund.PayRefundApiDomainService;
import com.pxb7.mall.trade.ass.infra.model.mq.PartRefundMessage;
import jakarta.annotation.Resource;
import org.junit.Test;

/**
 * <AUTHOR>
 * @since 2025/5/12
 */
public class PayRefundApiDomainServiceTest extends AbstractBaseSpringService{

    @Resource
    private PayRefundApiDomainService payRefundApiDomainService;

    @Test
    public void test1(){
        PartRefundMessage message = new PartRefundMessage();
        message.setOrderItemId("ZH15072328577851424628");
        message.setRefundVoucherId("TK15072425306526824628");
        message.setRefundAmount(100L);
        payRefundApiDomainService.refundPartly(message);
    }

}
